// Skip waiting and claim clients immediately
self.addEventListener('install', event => {
  self.skipWaiting();
});

self.addEventListener('activate', event => {
  event.waitUntil(clients.claim());
});

// Don't cache admin routes
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // Skip caching for admin routes
  if (url.pathname.includes('/admin')) {
    return event.respondWith(fetch(event.request));
  }
  
  // Your existing caching strategy for other routes
  // ...
}); 