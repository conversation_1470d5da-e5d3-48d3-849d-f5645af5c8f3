{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@popperjs/core": "^2.11.8", "@tailwindcss/forms": "^0.5.2", "@vitejs/plugin-vue": "^5.0.4", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.6.4", "bootstrap": "^5.3.3", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "sass": "^1.84.0", "tailwindcss": "^3.1.0", "vite": "^6.2.1", "vue": "^3.2.31"}, "dependencies": {"@vueuse/core": "^10.11.1", "fuse.js": "^7.0.0", "prismjs": "^1.29.0"}}