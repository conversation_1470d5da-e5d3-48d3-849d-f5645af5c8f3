import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue'; 

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/main.css',
                'resources/css/plugins.css',
                'resources/css/modalboxes.css',
                'resources/css/background-animation.css',
                'resources/css/gradient-svg.css',
                'resources/css/navigation.css',
                'resources/css/snap-scroll.css',
                'resources/js/app.js',
                'resources/js/tools/scroll-spy.js',
                'resources/js/svg-animation.js',
                'resources/js/snap-scroll.js',
                'resources/sass/template.scss',
            ],
            refresh: true,
        }),
        vue({ 
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: { 
        alias: {
            vue: 'vue/dist/vue.esm-bundler.js',
            '@': '/resources/js'
        },
    },
    build: {
        rollupOptions: {
            output: {
                entryFileNames: `assets/[name].[hash].js`,
                chunkFileNames: `assets/[name].[hash].js`,
                assetFileNames: `assets/[name].[hash].[ext]`
            }
        },
        hmr: process.env.NODE_ENV === 'production' ? false : {},
    },
    publicDir: 'public',
    assetsInclude: ['**/*.woff2']
});


    // Add cache busting parameter to all script tags
    document.addEventListener('DOMContentLoaded', () => {
        const timestamp = new Date().getTime();
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            if (!script.src.includes('?')) {
                script.src = script.src + '?v=' + timestamp;
            }
        });
    });

document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    app.mount('#app');
    console.log('Vue app mounted after DOM content loaded');
  }, 50);
});

self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // Skip caching for admin routes
  if (url.pathname.includes('/admin')) {
    return event.respondWith(fetch(event.request));
  }
  
  // Your existing caching strategy for other routes
});

