const CACHE_NAME = 'design-vt-cache-v1';
const STATIC_CACHE_NAME = 'design-vt-static-v2';
const DYNAMIC_CACHE_NAME = 'design-vt-dynamic-v1';

// Assets to cache
const STATIC_ASSETS = [
    '/',
    '/offline.html',
    '/manifest.json',
    // Icons
    '/img/icons/favicon.png',
    '/img/icons/favicon.svg',
    '/img/icons/favicon-32x32.png',
    '/img/icons/favicon-16x16.png',
    '/img/icons/apple-touch-icon.png',
    '/img/icons/safari-pinned-tab.svg',
    '/img/icons/mstile-70x70.png',
    '/img/icons/mstile-144x144.png',
    '/img/icons/mstile-150x150.png',
    '/img/icons/mstile-310x150.png',
    '/img/icons/mstile-310x310.png',
    // Fonts
    'https://fonts.bunny.net/css?family=figtree:400,500,600'
];

// Check if URL is supported for caching
const isURLCacheable = (url) => {
    const supportedSchemes = ['http:', 'https:'];
    try {
        const urlObj = new URL(url);
        return supportedSchemes.includes(urlObj.protocol);
    } catch {
        return false;
    }
};

// Add this before any caching logic
function shouldCache(request) {
    // Only cache GET requests
    if (request.method !== 'GET') return false;

    // Don't cache API or admin routes
    const url = new URL(request.url);
    if (url.pathname.startsWith('/api/') || url.pathname.startsWith('/admin/')) return false;

    // Check if the request explicitly disallows service worker caching
    const swAllowed = request.headers.get('Service-Worker-Allowed');
    if (swAllowed === 'false') return false;

    return true;
}

// Check if the URL is a Vite build asset
function isBuildAsset(url) {
    return url.pathname.startsWith('/build/assets/');
}

// Install event - cache static assets
self.addEventListener('install', event => {
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE_NAME)
                .then(cache => {
                    // Cache files one by one to handle failures gracefully
                    return Promise.allSettled(
                        STATIC_ASSETS.map(url => 
                            fetch(url, { cache: 'no-cache' })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`Failed to fetch ${url}: ${response.status} ${response.statusText}`);
                                    }
                                    return cache.put(url, response);
                                })
                                .catch(error => {
                                    console.warn(`Failed to cache: ${url}`, error.message);
                                    // Continue despite the error
                                    return Promise.resolve();
                                })
                        )
                    );
                }),
            caches.open(CACHE_NAME)
                .then(cache => 
                    fetch('/offline.html')
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to fetch offline page');
                            }
                            return cache.put('/offline.html', response);
                        })
                        .catch(error => {
                            console.warn('Failed to cache offline page:', error.message);
                            // Continue despite the error
                            return Promise.resolve();
                        })
                )
        ])
    );
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(keys => {
            return Promise.all(
                keys.filter(key => {
                    return key !== CACHE_NAME && 
                           key !== STATIC_CACHE_NAME && 
                           key !== DYNAMIC_CACHE_NAME;
                }).map(key => caches.delete(key))
            );
        })
    );
    // Immediately claim any new clients
    return self.clients.claim();
});

// Fetch event - network first, then cache
self.addEventListener('fetch', event => {
    // Only handle GET requests
    if (!shouldCache(event.request)) {
        return;
    }

    // Skip non-cacheable URLs
    if (!isURLCacheable(event.request.url)) {
        return;
    }

    const url = new URL(event.request.url);
    
    // For build assets, use network-first approach
    if (isBuildAsset(url)) {
        event.respondWith(
            fetch(event.request)
                .then(response => {
                    // Cache on successful fetch
                    if (response && response.status === 200) {
                        const responseToCache = response.clone();
                        caches.open(DYNAMIC_CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                    }
                    return response;
                })
                .catch(() => {
                    // Try from cache if network fails
                    return caches.match(event.request);
                })
        );
        return;
    }

    // Handle navigation requests
    if (event.request.mode === 'navigate') {
        event.respondWith(
            fetch(event.request)
                .catch(() => caches.match('/offline.html'))
        );
        return;
    }

    // For other requests, try cache first, then network
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return from cache if found
                if (response) {
                    return response;
                }

                // Otherwise, fetch from network
                return fetch(event.request).then(networkResponse => {
                    // Cache the new response if it's valid
                    if (networkResponse && networkResponse.status === 200) {
                        const responseToCache = networkResponse.clone();
                        caches.open(DYNAMIC_CACHE_NAME).then(cache => {
                            cache.put(event.request, responseToCache);
                        });
                    }
                    return networkResponse;
                });
            })
            // Do not return offline.html for non-navigation requests
            // to avoid MIME type errors on scripts/styles.
    );
}); 