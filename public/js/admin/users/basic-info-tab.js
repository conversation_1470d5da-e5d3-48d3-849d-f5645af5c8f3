/**
 * Basic Info Tab JS
 * Handles the email and birthday edit functionality
 */

/**
 * Toggle email edit form visibility
 * @param {boolean} show - Whether to show or hide the form
 */
function toggleEmailEdit(show) {
    const emailDisplay = document.getElementById('email-display');
    const emailEmpty = document.getElementById('email-empty');
    const emailForm = document.getElementById('email-form');
    
    if (!emailDisplay || !emailForm) {
        console.error('Email elements not found in the DOM');
        return;
    }
    
    if (show) {
        if (emailDisplay) emailDisplay.classList.add('hidden');
        if (emailEmpty) emailEmpty.classList.add('hidden');
        emailForm.classList.remove('hidden');
        const emailInput = document.getElementById('email-input');
        if (emailInput) emailInput.focus();
    } else {
        const emailValue = document.getElementById('email-value');
        const hasEmail = emailValue && emailValue.textContent.trim() !== '';
        
        if (hasEmail) {
            emailDisplay.classList.remove('hidden');
            if (emailEmpty) emailEmpty.classList.add('hidden');
        } else {
            if (emailDisplay) emailDisplay.classList.add('hidden');
            if (emailEmpty) emailEmpty.classList.remove('hidden');
        }
        
        emailForm.classList.add('hidden');
    }
}

/**
 * Toggle birthday edit form visibility
 * @param {boolean} show - Whether to show or hide the form
 */
function toggleBirthdayEdit(show) {
    const birthdayDisplay = document.getElementById('birthday-display');
    const birthdayEmpty = document.getElementById('birthday-empty');
    const birthdayForm = document.getElementById('birthday-form');
    
    if (!birthdayDisplay || !birthdayForm) {
        console.error('Birthday elements not found in the DOM');
        return;
    }
    
    if (show) {
        if (birthdayDisplay) birthdayDisplay.classList.add('hidden');
        if (birthdayEmpty) birthdayEmpty.classList.add('hidden');
        birthdayForm.classList.remove('hidden');
        const birthdayInput = document.getElementById('birthday-input');
        if (birthdayInput) birthdayInput.focus();
    } else {
        const birthdayValue = document.getElementById('birthday-value');
        const hasBirthday = birthdayValue && birthdayValue.textContent.trim() !== '';
        
        if (hasBirthday) {
            birthdayDisplay.classList.remove('hidden');
            if (birthdayEmpty) birthdayEmpty.classList.add('hidden');
        } else {
            if (birthdayDisplay) birthdayDisplay.classList.add('hidden');
            if (birthdayEmpty) birthdayEmpty.classList.remove('hidden');
        }
        
        birthdayForm.classList.add('hidden');
    }
}

// Export functions to global scope
window.toggleEmailEdit = toggleEmailEdit;
window.toggleBirthdayEdit = toggleBirthdayEdit;

// Initialize event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Basic info tab JS loaded');
    
    // Setup event listeners for cancel buttons if they exist
    const cancelEmailBtn = document.getElementById('cancel-email-btn');
    if (cancelEmailBtn) {
        cancelEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            toggleEmailEdit(false);
        });
    }
    
    const cancelBirthdayBtn = document.getElementById('cancel-birthday-btn');
    if (cancelBirthdayBtn) {
        cancelBirthdayBtn.addEventListener('click', function(e) {
            e.preventDefault();
            toggleBirthdayEdit(false);
        });
    }
}); 