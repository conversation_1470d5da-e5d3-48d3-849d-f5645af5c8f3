/**
 * Management Tab JS
 * Handles functionality related to the management tab in the user details page
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Management tab JS loaded');
    
    // Direct event handlers for delete functionality
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    const deleteModal = document.getElementById('deleteModal');
    const closeDeleteBtn = document.getElementById('close-delete-btn');
    const submitDeleteBtn = document.getElementById('submit-delete-btn');
    const deleteForm = document.getElementById('deleteForm');
    
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            if (deleteModal) {
                deleteModal.classList.remove('hidden');
            } else {
                console.error('Delete modal not found');
            }
        });
    }
    
    if (closeDeleteBtn) {
        closeDeleteBtn.addEventListener('click', function() {
            if (deleteModal) {
                deleteModal.classList.add('hidden');
            }
        });
    }
    
    if (submitDeleteBtn) {
        submitDeleteBtn.addEventListener('click', function() {
            if (deleteForm) {
                deleteForm.submit();
            } else {
                console.error('Delete form not found');
            }
        });
    }
});
