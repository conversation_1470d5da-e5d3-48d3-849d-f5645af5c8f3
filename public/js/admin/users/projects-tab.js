/**
 * Projects Tab JS
 * Handles functionality related to the projects tab in the user details page
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Projects tab JS loaded');
    
    // Project selection enhancement
    const projectSelect = document.getElementById('project_id');
    if (projectSelect) {
        // Could enhance with select2 or similar if desired
        projectSelect.addEventListener('change', function() {
            // Any additional actions on project selection
        });
    }
    
    // Font selection enhancement
    const fontSelect = document.getElementById('font_id');
    if (fontSelect) {
        // Could enhance with select2 or similar if desired
        fontSelect.addEventListener('change', function() {
            // Any additional actions on font selection
        });
    }
    
    // Project removal confirmation
    const projectRemoveForms = document.querySelectorAll('form[action*="projects/remove"]');
    projectRemoveForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!confirm('Ste si istý, že chcete odstrániť tento projekt od používateľa?')) {
                e.preventDefault();
            }
        });
    });
    
    // Font removal confirmation
    const fontRemoveForms = document.querySelectorAll('form[action*="fonts/remove"]');
    fontRemoveForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!confirm('Ste si istý, že chcete odstrániť tento font od používateľa?')) {
                e.preventDefault();
            }
        });
    });
}); 