/**
 * Test script for delete functionality
 * This script will be loaded in the browser console to test the delete functionality
 */

function testDeleteFunctionality() {
    console.log('Testing delete functionality');
    
    // Check if the management tab is visible
    const managementTab = document.getElementById('management-content');
    if (managementTab && managementTab.classList.contains('hidden')) {
        console.log('Management tab is hidden, switching to it');
        switchTab('management');
    }
    
    // Check if the delete button exists
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    if (!confirmDeleteBtn) {
        console.error('Delete button not found');
        return;
    }
    
    // Check if the delete modal exists
    const deleteModal = document.getElementById('deleteModal');
    if (!deleteModal) {
        console.error('Delete modal not found');
        return;
    }
    
    // Check if the delete form exists
    const deleteForm = document.getElementById('deleteForm');
    if (!deleteForm) {
        console.error('Delete form not found');
        return;
    }
    
    // Check if the submit delete button exists
    const submitDeleteBtn = document.getElementById('submit-delete-btn');
    if (!submitDeleteBtn) {
        console.error('Submit delete button not found');
        return;
    }
    
    // Check if the close delete button exists
    const closeDeleteBtn = document.getElementById('close-delete-btn');
    if (!closeDeleteBtn) {
        console.error('Close delete button not found');
        return;
    }
    
    console.log('All elements found, testing click events');
    
    // Test clicking the confirm delete button
    console.log('Clicking confirm delete button');
    confirmDeleteBtn.click();
    
    // Check if the modal is visible
    console.log('Modal visible:', !deleteModal.classList.contains('hidden'));
    
    // Test clicking the close button
    console.log('Clicking close button');
    closeDeleteBtn.click();
    
    // Check if the modal is hidden
    console.log('Modal hidden:', deleteModal.classList.contains('hidden'));
    
    console.log('Delete functionality test complete');
}

// Run the test when the script is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, waiting for page to fully initialize');
    setTimeout(testDeleteFunctionality, 1000);
}); 