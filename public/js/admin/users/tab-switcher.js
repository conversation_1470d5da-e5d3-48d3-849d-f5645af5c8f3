/**
 * Tab Switcher for Admin User Detail Page
 * Handles the tab switching logic for the user detail page
 */

/**
 * Switch tab based on tab ID
 * @param {string} tabId - ID of the tab to switch to
 */
function switchTab(tabId) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('[id$="-content"]');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });
    
    // Show selected tab content
    const selectedContent = document.getElementById(`${tabId}-content`);
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    } else {
        console.error(`Tab content #${tabId}-content not found`);
        return;
    }
    
    // Update tab buttons (remove active state from all, add to selected)
    const tabButtons = document.querySelectorAll('[id$="-tab"]');
    tabButtons.forEach(button => {
        // Reset all tabs to inactive state
        button.classList.remove('border-gray-800', 'dark:border-gray-400', 'text-gray-800', 'dark:text-gray-200');
        button.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
        button.setAttribute('aria-selected', 'false');
        
        // Find and update icon color
        const icon = button.querySelector('i');
        if (icon) {
            icon.classList.remove('text-gray-800', 'dark:text-gray-200');
            icon.classList.add('text-gray-500', 'dark:text-gray-400');
        }
    });
    
    // Set active tab
    const activeTab = document.getElementById(`${tabId}-tab`);
    if (activeTab) {
        activeTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
        activeTab.classList.add('border-gray-800', 'dark:border-gray-400', 'text-gray-800', 'dark:text-gray-200');
        activeTab.setAttribute('aria-selected', 'true');
        
        // Update icon color
        const icon = activeTab.querySelector('i');
        if (icon) {
            icon.classList.remove('text-gray-500', 'dark:text-gray-400');
            icon.classList.add('text-gray-800', 'dark:text-gray-200');
        }
    }
    
    // Store active tab in session storage
    sessionStorage.setItem('activeUserDetailTab', tabId);
}

// Export function to global scope
window.switchTab = switchTab;

// Initialize tab switching when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Tab switcher JS loaded');
    
    // Get active tab from session storage or default to 'basic-info'
    const activeTab = sessionStorage.getItem('activeUserDetailTab') || 'basic-info';
    switchTab(activeTab);
}); 