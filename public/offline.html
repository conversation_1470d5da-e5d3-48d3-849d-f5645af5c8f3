<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Design Vermont Dev</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#000000">
    <style>
        body {
            font-family: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
            background: #f3f4f6;
            color: #1f2937;
            display: flex;
            min-height: 100vh;
            margin: 0;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 1rem;
        }
        .container {
            max-width: 28rem;
            padding: 2rem;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            margin: 0 0 1rem;
            font-size: 1.5rem;
            line-height: 2rem;
            font-weight: 600;
        }
        p {
            margin: 0 0 1.5rem;
            color: #4b5563;
        }
        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #000;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .button:hover {
            background: #1a1a1a;
        }
        @media (prefers-color-scheme: dark) {
            body {
                background: #1f2937;
                color: #f3f4f6;
            }
            .container {
                background: #111827;
            }
            p {
                color: #9ca3af;
            }
            .button {
                background: #3b82f6;
            }
            .button:hover {
                background: #2563eb;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📡</div>
        <h1>You're Offline</h1>
        <p>Please check your internet connection and try again.</p>
        <a href="/" class="button">Try Again</a>
    </div>
    <script>
        // Check if we're back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
    </script>
</body>
</html> 