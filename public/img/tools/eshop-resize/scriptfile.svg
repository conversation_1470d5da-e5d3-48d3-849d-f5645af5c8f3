<svg width="400" height="350" viewBox="0 0 400 350" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2009_54)" filter="url(#filter0_dd_2009_54)">
<path d="M265.485 109.943V300.653C265.485 310.909 257.166 319.22 246.918 319.22H75.3039C65.0479 319.22 56.7368 310.909 56.7368 300.653V49.5672C56.7362 39.3111 65.0473 31 75.3039 31H186.542L265.485 109.943Z" fill="url(#paint0_linear_2009_54)"/>
<g filter="url(#filter1_dd_2009_54)">
<path d="M265.485 109.943H205.109C194.853 109.943 186.542 101.632 186.542 91.3756V31L265.485 109.943Z" fill="url(#paint1_linear_2009_54)"/>
</g>
<g filter="url(#filter2_i_2009_54)">
<path d="M212.25 133H109.75C98.4282 133 89.25 142.178 89.25 153.5V266.25C89.25 277.572 98.4282 286.75 109.75 286.75H212.25C223.572 286.75 232.75 277.572 232.75 266.25V153.5C232.75 142.178 223.572 133 212.25 133Z" fill="#DDEBFD"/>
</g>
<g filter="url(#filter3_i_2009_54)">
<path d="M199.438 201.972C198.467 201.061 197.179 200.562 195.848 200.583C194.516 200.604 193.245 201.143 192.304 202.085L170.205 224.189C169.244 225.15 168.704 226.454 168.704 227.813C168.704 229.172 169.244 230.475 170.205 231.436L222.597 283.844C225.678 282.052 228.235 279.485 230.016 276.399C231.797 273.312 232.74 269.814 232.75 266.25V233.281L199.438 201.972Z" fill="#4071F7"/>
</g>
<g filter="url(#filter4_i_2009_54)">
<path d="M176.375 199.625C184.866 199.625 191.75 192.741 191.75 184.25C191.75 175.759 184.866 168.875 176.375 168.875C167.884 168.875 161 175.759 161 184.25C161 192.741 167.884 199.625 176.375 199.625Z" fill="#5392F9"/>
</g>
<g filter="url(#filter5_i_2009_54)">
<path d="M149.248 196.002C148.269 195.076 146.973 194.56 145.625 194.56C144.277 194.56 142.981 195.076 142.002 196.002L89.25 248.753V266.25C89.25 271.687 91.4098 276.901 95.2543 280.746C99.0988 284.59 104.313 286.75 109.75 286.75H212.25C215.813 286.74 219.312 285.797 222.399 284.016C225.485 282.235 228.052 279.678 229.844 276.597L149.248 196.002Z" fill="#5392F9"/>
</g>
</g>
<g filter="url(#filter6_dd_2009_54)">
<path d="M280.252 193.651H361.424C373.73 193.651 383.68 203.856 383.68 216.478V296.508C383.68 309.131 373.73 319.336 361.424 319.336H280.252C267.945 319.336 257.995 309.131 257.995 296.508V216.478C257.995 203.856 267.945 193.651 280.252 193.651Z" fill="#001E36"/>
<path d="M286.275 283.83V229.942C286.275 229.576 286.432 229.366 286.798 229.366C287.688 229.366 288.526 229.366 289.731 229.314C290.988 229.262 292.297 229.262 293.711 229.209C295.125 229.157 296.644 229.157 298.267 229.105C299.89 229.052 301.461 229.052 303.032 229.052C307.327 229.052 310.888 229.576 313.82 230.676C316.439 231.566 318.848 233.032 320.838 234.97C322.514 236.646 323.823 238.688 324.661 240.94C325.446 243.139 325.865 245.391 325.865 247.748C325.865 252.252 324.818 255.97 322.723 258.902C320.628 261.835 317.696 264.034 314.292 265.291C310.731 266.601 306.803 267.072 302.509 267.072C301.252 267.072 300.414 267.072 299.89 267.02C299.367 266.967 298.634 266.967 297.639 266.967V283.777C297.691 284.144 297.429 284.458 297.062 284.511C297.01 284.511 296.958 284.511 296.853 284.511H286.903C286.484 284.511 286.275 284.301 286.275 283.83ZM297.691 239.474V257.069C298.424 257.122 299.105 257.174 299.733 257.174H302.509C304.551 257.174 306.594 256.86 308.531 256.232C310.207 255.76 311.673 254.765 312.825 253.456C313.925 252.147 314.449 250.366 314.449 248.062C314.501 246.439 314.082 244.815 313.244 243.401C312.354 242.04 311.097 240.992 309.579 240.416C307.641 239.631 305.546 239.316 303.399 239.369C302.037 239.369 300.833 239.369 299.838 239.421C298.791 239.369 298.057 239.421 297.691 239.474Z" fill="#31A8FF"/>
<path d="M358.543 253.875C356.972 253.037 355.296 252.461 353.516 252.094C351.578 251.675 349.64 251.413 347.65 251.413C346.603 251.361 345.503 251.518 344.508 251.78C343.827 251.937 343.251 252.304 342.885 252.827C342.623 253.246 342.466 253.77 342.466 254.241C342.466 254.713 342.675 255.184 342.99 255.603C343.461 256.179 344.089 256.65 344.77 257.017C345.975 257.645 347.231 258.221 348.488 258.745C351.316 259.688 354.039 260.997 356.553 262.568C358.281 263.668 359.695 265.134 360.69 266.915C361.528 268.59 361.947 270.423 361.895 272.309C361.947 274.77 361.214 277.231 359.852 279.274C358.386 281.368 356.344 282.992 353.987 283.934C351.421 285.034 348.279 285.61 344.508 285.61C342.099 285.61 339.743 285.401 337.386 284.929C335.553 284.615 333.72 284.039 332.044 283.254C331.678 283.044 331.416 282.678 331.468 282.259V273.146C331.468 272.989 331.521 272.78 331.678 272.675C331.835 272.57 331.992 272.623 332.149 272.728C334.192 273.932 336.339 274.77 338.643 275.294C340.633 275.817 342.728 276.079 344.822 276.079C346.812 276.079 348.226 275.817 349.169 275.346C350.007 274.979 350.583 274.089 350.583 273.146C350.583 272.413 350.164 271.733 349.326 271.052C348.488 270.371 346.76 269.585 344.194 268.59C341.523 267.648 339.062 266.391 336.758 264.82C335.134 263.668 333.773 262.149 332.778 260.369C331.94 258.693 331.521 256.86 331.573 255.027C331.573 252.775 332.202 250.628 333.354 248.69C334.663 246.596 336.601 244.92 338.852 243.872C341.314 242.616 344.403 242.039 348.122 242.039C350.269 242.039 352.468 242.197 354.615 242.511C356.186 242.72 357.705 243.139 359.119 243.715C359.329 243.768 359.538 243.977 359.643 244.187C359.695 244.396 359.748 244.606 359.748 244.815V253.351C359.748 253.561 359.643 253.77 359.486 253.875C359.014 253.98 358.753 253.98 358.543 253.875Z" fill="#31A8FF"/>
</g>
<defs>
<filter id="filter0_dd_2009_54" x="12.3135" y="31" width="297.594" height="302.281" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.68652" operator="erode" in="SourceAlpha" result="effect1_dropShadow_2009_54"/>
<feOffset dy="9.37304"/>
<feGaussianBlur stdDeviation="4.68652"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2009_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.68652" operator="erode" in="SourceAlpha" result="effect2_dropShadow_2009_54"/>
<feOffset dy="4.68652"/>
<feGaussianBlur stdDeviation="4.68652"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2009_54" result="effect2_dropShadow_2009_54"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2009_54" result="shape"/>
</filter>
<filter id="filter1_dd_2009_54" x="177.169" y="31" width="97.6888" height="107.062" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="9.37304" operator="erode" in="SourceAlpha" result="effect1_dropShadow_2009_54"/>
<feOffset dy="18.7461"/>
<feGaussianBlur stdDeviation="9.37304"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2009_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="9.37304" operator="erode" in="SourceAlpha" result="effect2_dropShadow_2009_54"/>
<feOffset dy="9.37304"/>
<feGaussianBlur stdDeviation="7.02978"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2009_54" result="effect2_dropShadow_2009_54"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2009_54" result="shape"/>
</filter>
<filter id="filter2_i_2009_54" x="89.25" y="133" width="143.5" height="155.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.75036"/>
<feGaussianBlur stdDeviation="4.37591"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_2009_54"/>
</filter>
<filter id="filter3_i_2009_54" x="168.704" y="200.583" width="64.046" height="85.0118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.75036"/>
<feGaussianBlur stdDeviation="4.37591"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_2009_54"/>
</filter>
<filter id="filter4_i_2009_54" x="161" y="168.875" width="30.75" height="32.5004" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.75036"/>
<feGaussianBlur stdDeviation="4.37591"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_2009_54"/>
</filter>
<filter id="filter5_i_2009_54" x="89.25" y="194.56" width="140.594" height="93.9405" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.75036"/>
<feGaussianBlur stdDeviation="4.37591"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 0 0.685988 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_2009_54"/>
</filter>
<filter id="filter6_dd_2009_54" x="253.309" y="193.651" width="135.058" height="139.744" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.68652" operator="erode" in="SourceAlpha" result="effect1_dropShadow_2009_54"/>
<feOffset dy="9.37304"/>
<feGaussianBlur stdDeviation="4.68652"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2009_54"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4.68652" operator="erode" in="SourceAlpha" result="effect2_dropShadow_2009_54"/>
<feOffset dy="4.68652"/>
<feGaussianBlur stdDeviation="4.68652"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.152941 0 0 0 0 0.294118 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2009_54" result="effect2_dropShadow_2009_54"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2009_54" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2009_54" x1="55.6635" y1="31" x2="266.557" y2="319.221" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F4F4F4"/>
</linearGradient>
<linearGradient id="paint1_linear_2009_54" x1="226.722" y1="70.8354" x2="186.887" y2="110.671" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7E7E7"/>
<stop offset="1" stop-color="#FEFEFE"/>
</linearGradient>
<clipPath id="clip0_2009_54">
<rect width="288.221" height="288.221" fill="white" transform="translate(17 31)"/>
</clipPath>
</defs>
</svg>
