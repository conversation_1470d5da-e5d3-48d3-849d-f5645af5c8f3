@extends('layouts.application')

@section('content')
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-8 text-gray-900 dark:text-white">Font Icon Debug Page</h1>
    
    <div class="grid grid-cols-1 gap-8">
        <font-icon-debug font-name="design"></font-icon-debug>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const app = Vue.createApp({});
        
        // Register the debug component
        app.component('FontIconDebug', FontIconDebug);
        
        // Mount the app
        app.mount('#app');
    });
</script>
@endpush 