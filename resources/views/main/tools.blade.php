@extends('layouts.application')

@section('content')
    <div id="app" class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }} relative">
        <!-- Main content with the animation contained inside -->
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950 bg-opacity-75 dark:bg-opacity-85 relative" id="icons">
            <!-- Particle animation wrapper - positioned within this container only -->
            
            <div class="designdev_tm_about w-full px-4 sm:px-0 py-8 sm:py-[143px] min-h-[100vh] relative z-10">
                <div class="container w-full relative mx-auto">
                    <!-- Header Section with improved mobile spacing -->
                    <div class="mb-8 mt-16 sm:mt-0">
                        <p class="text-xs sm:text-sm md:text-base opacity-50 dark:text-white mb-2 sm:mb-4 gant-modern-regular">
                            {{ __('messages.tools_description') }}
                        </p>
                        <h1 class="text-4xl sm:text-5xl md:text-6xl xl:text-8xl gant-modern-bold leading-tight tracking-tighter dark:text-gray-200">
                            {{ __('messages.tools') }}
                        </h1>
                    </div>

                    <!-- Tools Grid with adjusted gap for mobile -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                        <!-- Tool Card with improved mobile padding -->

                        <!-- Eshop Resizer -->
                        <a href="{{ route('tools.eshop-resizer.documentation') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/tools/eshop-resize/thumbnail.svg') }}" 
                                     alt="Eshop Resizer Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">EshopResizer.jsx</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 1.0 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Photoshop script</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 text-center ease-out">
                                Photoshop automation script for batch processing and resizing e-commerce images.
                            </p>
                        </a>

                        <!-- InstaResizer.jsx -->
                        <a href="{{ route('tools.insta-resizer.documentation') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/tools/insta-resize/thumbnail.svg') }}" 
                                     alt="Eshop Resizer Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">InstaResizer.jsx</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 1.0 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Photoshop script</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Photoshop automation script for batch processing and resizing e-commerce images.
                            </p>
                        </a>  

                        <!-- Animate.svg -->
                        <a href="{{ route('tools.svg-animation.documentation') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/tools/svg-animate/thumbnail.svg') }}" 
                                     alt="Eshop Resizer Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">Animate.svg</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 0.2 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Browser based</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Photoshop automation script for batch processing and resizing e-commerce images.
                            </p>
                        </a>

                        <!-- OrderGroup.img -->
                        <a href="{{ route('tools.ordergroup-image.documentation') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/tools/ordergroup-image/thumbnail.svg') }}" 
                                     alt="Eshop Resizer Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">OrderGroup.img</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 0.2 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Browser based</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Photoshop automation script for batch processing and resizing e-commerce images.
                            </p>
                        </a>
                        
                        <!-- QR Code Generator -->
                        <a href="{{ route('tools.qr-generator.documentation') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/tools/qr/thumbnail.svg') }}" 
                                     alt="QR Code Generator Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">QRGenerator</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 1.0 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Browser based</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Generate QR codes from URLs or text. Customize size and colors, then download as SVG or PNG.
                            </p>
                        </a>

                        <!-- Barcode Generator -->
                        <a href="{{ route('tools.barcode-generator.documentation') }}"
                            class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <!-- Placeholder: Create a thumbnail img/tools/barcode/thumbnail.svg -->
                                <img src="{{ asset('img/tools/barcode/thumbnail.svg') }}" 
                                     alt="BarcodeGenerator " 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">BarcodeGenerator</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 1.0 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Browser based</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Generate EAN-13, EAN-8, and Code128 barcodes. Customize and download as SVG/PNG.
                            </p>
                        </a>

                        <!-- SVG Optimizer -->
                        <a href="{{ route('tools.svg-optimizer.documentation') }}"
                            class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <!-- Placeholder: Create a thumbnail img/tools/svg-optimizer/thumbnail.svg -->
                                <img src="{{ asset('img/tools/svg-optimizer/thumbnail.svg') }}" 
                                     alt="SVG Optimizer Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg "> <!-- Added placeholder styling -->
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">SVG Optimizer</span>
                                </h1>
                                <span class="text-sm sm:text-md gant-modern-bold text-gray-600 dark:text-gray-300">Version 1.0 <span class="gant-modern-regular text-gray-500 dark:text-gray-400">– Browser based</span></span>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out text-center">
                                Optimize SVG files by removing redundant data and applying best practices. Reduce file size for web use.
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection