@extends('layouts.application')

@section('content')
    <div class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950" id="icons">
            <div class="designdev_tm_about w-full px-0 py-[143px] min-h-[100vh]">
                <div class="container w-full relative px-0 mx-auto">
                    <code-terminal 
                        :code="{{ json_encode($colorsContent) }}"
                        filename="_colors.scss"
                        download-url="{{ route('download-scss', ['file' => '_colors.scss']) }}"
                        language="scss"
                        subheadline="–––––– UTILITIES"
                        title="ColourClasses"
                        excerpt="Dev project colours, used in the design system."
                    ></code-terminal>
                    <code-terminal 
                        :code="{{ json_encode($fontWeightsContent) }}"
                        filename="_fontWeights.scss"
                        download-url="{{ route('download-scss', ['file' => '_fontWeights.scss']) }}"
                        language="scss"
                        subheadline="–––––– UTILITIES"
                        title="FontWeights"
                        excerpt="Font-family implementation for weights, in bootstrap variables (curently for Nunito Sans)."
                    ></code-terminal>
                </div>
            </div>
        </div>
    </div>
@endsection