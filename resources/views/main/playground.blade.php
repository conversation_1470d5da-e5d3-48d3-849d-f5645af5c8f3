@extends('layouts.application')

@section('content')
    <div id="playground-app" class="h-[calc(100vh-64px)] flex flex-col">
        <div class="flex-1 overflow-hidden">
            <div class="bg-white dark:bg-slate-950 h-full">
                <div class="p-4 sm:p-6 md:p-8 h-full">
                    <playground-demo class="h-full"></playground-demo>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const app = createApp({
            methods: {
                handleThemeChange(theme) {
                    if (theme === 'dark') {
                        document.documentElement.classList.add('dark')
                    } else {
                        document.documentElement.classList.remove('dark')
                    }
                    // Store theme preference
                    localStorage.setItem('theme', theme)
                }
            },
            mounted() {
                // Check for stored theme preference
                const storedTheme = localStorage.getItem('theme')
                if (storedTheme) {
                    this.handleThemeChange(storedTheme)
                }
            }
        })
        app.mount('#playground-app')
    })
</script>
@endpush
