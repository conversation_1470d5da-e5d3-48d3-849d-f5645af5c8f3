@extends('layouts.application')

@section('content')
    <div id="hello-app">
        <div class="{{ auth()->check() && auth()->user() && isset(auth()->user()->prefers_dark_mode) && auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
            <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950 relative overflow-hidden" id="hello">
                <!-- Add LavaBalls as background -->
                <div class="absolute inset-0 w-full h-full overflow-hidden pointer-events-none">
                    <lava-balls></lava-balls>
                </div>
                
                <div class="designdev_tm_hero w-full min-h-screen flex items-center justify-center">
                    <div class="container w-full h-full px-2 sm:px-4 lg:px-6 xl:px-8 mx-auto flex flex-col justify-between">
                        <div class="w-full mx-auto text-center py-8 md:py-12 lg:py-16">
                            @if(auth()->check() && auth()->user())
                                <x-greeting :user="auth()->user()" />
                                @php
                                    $devLogins = config('vermont.developer_user_logins', []);
                                    $isDevUser = auth()->user()->login && in_array(auth()->user()->login, $devLogins);
                                @endphp
                                
                                @if($isDevUser)
                                <div class="relative transform scale-110">
                                    <div class="mt-16 alert bg-gray-950 dark:bg-gray-50 shadow-2xl my-2 max-w-3xl mx-auto rounded-xl backdrop-blur-sm opacity-95">
                                        <div class="flex items-center justify-center gap-6 p-12">
                                            <div class="flex-shrink-0 [animation:pulse_1s_cubic-bezier(0.4,0,0.6,1)_infinite]">
                                                <i class="wms wms-alert text-red-600 text-6xl"></i>
                                            </div>
                                            <div class="flex-row">
                                                <span class="block font-bold text-3xl text-white dark:text-black tracking-tight leading-tight">
                                                    Vytvoril si si už classu na placeholderi?
                                                </span>
                                                <span class="block font-bold text-3xl text-red-600 dark:text-red-600 tracking-tight leading-tight">
                                                    .docasny-text {text-color: #FF4343;} ??
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Decorative glow effect -->
                                </div>
                                @endif
                            @else
                                <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-[5vw] 2xl:text-[6vw] gant-modern-bold leading-none tracking-tighter text-gray-900 dark:text-gray-200">
                                    Welcome to Vermont Design
                                </h1>
                            @endif
                            
                            <!-- Segmented Tabs for Commands and Bookmarks -->
                            <div class="mt-8 mb-8">
                                @php
                                    $isAdmin = auth()->check() && auth()->user() && 
                                               auth()->user()->login && 
                                               in_array(auth()->user()->login, config('vermont.admin_user_logins', []));
                                @endphp
                                <hello-tabs :is-admin="{{ $isAdmin ? 'true' : 'false' }}"></hello-tabs>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{-- Tabs render both commands and bookmarks, remove direct bookmarks usage --}}
            {{-- bookmarks section removed in favor of segmented tabs --}}
        </div>
    </div>
@endsection

@push('scripts')
    @vite('resources/js/hello-page.js')
    <style>
        /* Modal stacking fixes */
        body.modal-open [aria-modal="true"] {
            z-index: 9999 !important;
        }
        
        body.modal-open .z-\[50\] {
            z-index: 50 !important;
        }
        
        /* Force the modal to be visible */
        [aria-modal="true"] {
            display: block !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
        }
    </style>
@endpush
