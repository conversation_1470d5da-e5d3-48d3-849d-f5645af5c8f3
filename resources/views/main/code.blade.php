@extends('layouts.application')

@section('content')
    <div class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950" id="code">
            <div class="designdev_tm_about w-full px-0 py-24 md:py-32 min-h-screen">
                <div class="container w-full relative px-4 md:px-6 lg:px-8 mx-auto flex flex-col h-full">
                    <!-- Header Section with improved mobile spacing -->
                    <div class="mb-8 mt-16 sm:mt-0">
                        <p class="text-xs sm:text-sm md:text-base opacity-50 dark:text-white mb-2 sm:mb-4 gant-modern-regular">
                            {{ __('messages.code_description') }}
                        </p>
                        <h1 class="text-4xl sm:text-5xl md:text-6xl xl:text-8xl gant-modern-bold leading-tight tracking-tighter dark:text-gray-200">
                            {{ __('messages.code') }}
                        </h1>
                    </div>

                    <!-- Tools Grid with adjusted gap for mobile -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                        <!-- Tool Card with improved mobile padding -->

                        <!-- scss -->
                        <a href="{{ route('classes') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/code/thumbnail.svg') }}" 
                                     alt="SCSS Classes Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">{{ __('messages.scss_classes') }}</span>
                                </h1>
                                <p class="text-2xs sm:text-xs text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out">
                                    <i class="wms wms-browse-history-outline opacity-50 me-1"></i>
                                    {{ __('messages.scss_classes_subtitle') }}
                                </p>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 text-center ease-out">
                                {{ __('messages.scss_classes_description') }}
                            </p>
                        </a>

                        <!-- Playground -->
                        <a href="{{ route('playground') }}" 
                           class="group p-4 sm:p-6 transition-all duration-200">
                            <div class="relative overflow-hidden rounded-lg">
                                <img src="{{ asset('img/code/thumbnail-ide.svg') }}" 
                                     alt="Playground Interface" 
                                     class="w-full h-auto transform transition-transform duration-500 group-hover:scale-105 drop-shadow-lg">
                            </div>
                            <div class="mb-3 sm:mb-4 text-center mt-4">
                                <h1 class="text-2xl gant-modern-bold dark:text-gray-200 mb-2">
                                    <span class="px-4 py-1 rounded-lg transition-colors duration-300 text-gray-800 dark:text-gray-200 group-hover:bg-blue-500 group-hover:text-white">{{ __('messages.playground_title') }}</span>
                                </h1>
                                <p class="text-2xs sm:text-xs text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 ease-out">
                                    <i class="wms wms-browse-history-outline opacity-50 me-1"></i>
                                    {{ __('messages.playground_subtitle') }}
                                </p>
                            </div>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 gant-modern-regular opacity-0 group-hover:opacity-100 -translate-y-2 group-hover:translate-y-0 transition-all duration-300 text-center ease-out">
                                {{ __('messages.playground_description') }}
                            </p>
                        </a>

                        <!-- InstaResizer.jsx -->
                        

                        <!-- Animate.svg -->
                        

                        <!-- OrderGroup Image -->
                        

                    </div>
                    

                    
                </div>
            </div>
        </div>
    </div>
@endsection
