@extends('layouts.application')

@section('title', 'To do List')

@section('content')
<!-- Pattern SVG Background with lowest z-index and pointer-events-none -->
<div class="fixed inset-0 pointer-events-none z-0 bg-white dark:bg-slate-950">
    <svg class="absolute inset-0 w-full h-full text-gray-900/[0.07] dark:text-gray-100/[0.08]" xmlns="http://www.w3.org/2000/svg" style="background-color: transparent;">
        <defs>
            <pattern id="pattern-whiteboard" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-whiteboard)" style="fill: url(#pattern-whiteboard); background-color: transparent;" />
    </svg>
</div>

<!-- Main content with higher z-index -->
<div class="relative z-10 px-6 sm:px-12 md:px-12 lg:px-16 mt-16 bg-transparent dark:bg-transparent">
    <!-- Header Section - Similar to icons.blade.php -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8 bg-transparent dark:bg-transparent">
        <div>
            <div class="flex items-center gap-3">
                <h2 class="text-3xl md:text-5xl lg:text-6xl xl:text-8xl gant-modern-bold leading-tight md:leading-none tracking-tighter mb-4 dark:text-gray-200">{{ __('messages.whiteboard') }}</h2>
            </div>
        </div>
    </div>

    <!-- Whiteboard Cards Section -->
    <div class="w-full">
        <!-- Vue component for whiteboard -->
        <div id="whiteboard-app" class="bg-transparent dark:bg-transparent">
            <whiteboard-container 
                :is-admin="{{ (auth()->user()->is_admin || in_array(auth()->user()->login, config('vermont.admin_user_logins', []))) ? 'true' : 'false' }}"
            ></whiteboard-container>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Force dark mode application immediately if needed
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        if (isDarkMode) {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark');
            
            // Remove any white backgrounds
            document.querySelectorAll('.bg-white').forEach(el => {
                if (el.classList.contains('dark:bg-slate-950') || 
                    el.classList.contains('dark:bg-transparent')) {
                    el.classList.remove('bg-white');
                    el.classList.add('bg-transparent');
                }
            });
        }
        
        if (window.createApp) {
            const app = window.createApp('#whiteboard-app');
            // No need to provide update methods anymore since we removed the event bindings
        }
    });
</script>
@endpush
