@extends('layouts.application')

@section('content')
    <div class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950" id="icons">
            <div class="designdev_tm_about w-full px-0 py-[143px] min-h-[100vh]">
                <div class="container w-full relative px-0 mx-auto">
                    <p class="opacity-50 dark:text-white">
                        {{ __('messages.typography') }}
                    </p>
                    <h1
                        class="text-5xl lg:text-6xl xl:text-8xl gant-modern-bold leading-none tracking-tighter mb-20 dark:text-gray-200">
                        {{ __('messages.font_families_title') }}
                    </h1>
                    <div class="mb-14">
                    <p class="opacity-50 dark:text-white">
                        {{ __('messages.vermont_main_fontface') }}
                    </p>
                    <p id="gant-modern-demo"
                                class="text-black mb-0 text-4xl lg:text-5xl xl:text-7xl gant-modern-bold leading-none tracking-tighter dark:text-gray-200 ">
                                <i class="design design-text-editor opacity-25"></i>{{ __('messages.gant_modern') }}
                            </p>
                        <div class="flex gap-3 mb-4">
                            <font-download-button 
                                type="scss" 
                                font-family="Gant-Modern"
                                :current-settings="$refs.gantModernControls?.currentValues"
                            />
                            <font-download-button 
                                type="woff2" 
                                font-family="Gant-Modern"
                                :current-settings="$refs.gantModernControls?.currentValues"
                            />
                        </div>
                        <font-controls 
                            ref="gantModernControls"
                            font-family="Gant-Modern" 
                            target-id="gant-modern-demo" 
                        />
                    </div>
                    <div class="mb-14">
                    <p class="opacity-50 dark:text-white">
                        {{ __('messages.vermont_accent_fontface') }}
                    </p>
                    <p id="gant-serif-demo"
                                class="text-black mb-0 text-4xl lg:text-5xl xl:text-7xl gant-serif-condensed-bold leading-none tracking-tighter dark:text-gray-200 ">
                                <i class="design design-text-editor opacity-25"></i>{{ __('messages.gant_serif') }}
                            </p>
                        <div class="flex gap-3 mb-4">
                            <font-download-button 
                                type="scss" 
                                font-family="Gant-Serif"
                                :current-settings="$refs.gantSerifControls?.currentValues"
                            />
                            <font-download-button 
                                type="woff2" 
                                font-family="Gant-Serif"
                                :current-settings="$refs.gantSerifControls?.currentValues"
                            />
                        </div>
                        <font-controls 
                            ref="gantSerifControls"
                            font-family="Gant-Serif" 
                            target-id="gant-serif-demo" 
                        />
                    </div>
                    <div class="mb-14">
                    <p class="opacity-50 dark:text-white">
                        {{ __('messages.internal_development_projects') }}
                    </p>
                    <p id="nunito-demo"
                                class="text-black mb-0 text-4xl lg:text-5xl xl:text-7xl nunito-bold leading-none tracking-tighter dark:text-gray-200 ">
                                <i class="design design-text-editor opacity-25"></i>{{ __('messages.nunito_sans') }}
                            </p>
                        <div class="flex gap-3 mb-4">
                            <font-download-button 
                                type="scss" 
                                font-family="nunito-sans"
                                :current-settings="$refs.nunitoControls?.currentValues"
                            />
                            <font-download-button 
                                type="woff2" 
                                font-family="nunito-sans"
                                :current-settings="$refs.nunitoControls?.currentValues"
                            />
                        </div>
                        <font-controls 
                            ref="nunitoControls"
                            font-family="nunito-sans" 
                            target-id="nunito-demo" 
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection