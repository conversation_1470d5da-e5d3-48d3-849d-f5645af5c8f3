@extends('layouts.application')

@section('content')
    <div class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950" id="animations">
            <div class="designdev_tm_about w-full px-0 py-24 md:py-32 min-h-screen">
                <div class="container w-full relative px-4 md:px-6 lg:px-8 mx-auto flex flex-col h-full">
                    <!-- Header section - made more compact -->
                    <div class="mb-6 md:mb-8">
                        <p class="opacity-50 dark:text-white text-sm md:text-base mb-1 gant-modern-regular">
                            Explore our collection of animations, including SVG icons and Lottie animations.
                        </p>
                        <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl gant-modern-bold leading-tight md:leading-none tracking-tighter dark:text-gray-200">
                            {{ __('messages.animations') }}
                        </h1>
                    </div>
                    
                    <!-- Main content split into two sections -->
                    <div class="flex flex-col md:flex-row gap-6 md:gap-4 flex-1 h-full">
                        <!-- SVG Section -->
                        <div class="w-full md:w-1/2 flex flex-col h-full group transition-all duration-500 ease-in-out hover:scale-[1.02] origin-center md:pr-4">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-2xl md:text-3xl gant-modern-bold dark:text-gray-200">SVG Inline Animations</h2>
                                <i class="design design-vector text-2xl opacity-50 dark:text-white"></i>
                            </div>
                            
                            <!-- SVG Example Container - increased flex priority -->
                            <div class="bg-gray-50 dark:bg-slate-900 rounded-lg p-4 md:p-6 mb-4 flex-grow flex-shrink-0 flex items-center justify-center border border-gray-100 dark:border-slate-800 h-[50vh] md:h-[60vh]">
                                <div class="w-full h-full flex items-center justify-center">
                                    <p class="text-gray-500 dark:text-gray-400 italic gant-modern-regular">SVG example will be placed here</p>
                                </div>
                            </div>
                            
                            <!-- Made smaller to give more space to example -->
                            <div class="mt-auto">
                                <p class="text-gray-600 dark:text-gray-400 mb-3 gant-modern-regular text-sm md:text-base">Scalable vector graphics for clean, crisp icons at any size. Perfect for responsive designs and interactive elements.</p>
                                
                                <!-- Placeholder for future list -->
                                <h3 class="text-lg gant-modern-medium dark:text-gray-300 mb-1">Available SVG Animations:</h3>
                                <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1 pl-2 gant-modern-regular text-sm">
                                    <li>Content to be added later</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Center Divider -->
                        <div class="hidden md:block border-l border-gray-200 dark:border-slate-700 self-stretch mx-2"></div>

                        <!-- Lottie Section -->
                        <div class="w-full md:w-1/2 flex flex-col h-full group transition-all duration-500 ease-in-out hover:scale-[1.02] origin-center md:pl-4">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-2xl md:text-3xl gant-modern-bold dark:text-gray-200">Lottie Animations</h2>
                                <i class="design design-play text-2xl opacity-50 dark:text-white"></i>
                            </div>
                            
                            <!-- Lottie Example Container - increased flex priority -->
                            <div class="bg-gray-50 dark:bg-slate-900 rounded-lg p-4 md:p-6 mb-4 flex-grow flex-shrink-0 flex items-center justify-center border border-gray-100 dark:border-slate-800 h-[50vh] md:h-[60vh]">
                                <div class="w-full h-full flex items-center justify-center">
                                    <p class="text-gray-500 dark:text-gray-400 italic gant-modern-regular">Lottie example will be placed here</p>
                                </div>
                            </div>
                            
                            <!-- Made smaller to give more space to example -->
                            <div class="mt-auto">
                                <p class="text-gray-600 dark:text-gray-400 mb-3 gant-modern-regular text-sm md:text-base">Interactive, lightweight animations for engaging user experiences. Easy to implement and highly customizable.</p>
                                
                                <!-- Placeholder for future list -->
                                <h3 class="text-lg gant-modern-medium dark:text-gray-300 mb-1">Available Lottie Animations:</h3>
                                <ul class="list-disc list-inside text-gray-600 dark:text-gray-400 space-y-1 pl-2 gant-modern-regular text-sm">
                                    <li>Content to be added later</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
