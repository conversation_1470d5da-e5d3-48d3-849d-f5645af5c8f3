@extends('layouts.application')

@section('content')
    <div class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="designdev_tm_section w-full clear-both clearfix bg-white dark:bg-slate-950" id="assets">
        <div class="designdev_tm_about w-full px-0 py-[143px] min-h-[100vh]">
                <div class="w-full relative px-4 md:px-6 lg:px-8 mx-auto">    
                    <p class="opacity-50 dark:text-white text-sm md:text-base mb-4">
                        {{ __('messages.assets_description') }}
                    </p>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-8xl gant-modern-bold leading-tight md:leading-none tracking-tighter mb-6 md:mb-12 dark:text-gray-200">
                        {{ __('messages.project_assets') }}
                    </h1>
                    <div class="about_inner relative grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-y-4 gap-x-4  md:gap-x-8 xl:gap-x-12 2xl:gap-x-16">
                        

                        <!-- Icon Font Cards -->
                        @foreach($fonts as $font)
                        <div class="transform transition-all duration-300">
                            <card-font 
                                :title="'{{ $font->name }}'"
                                :icons="'{{ $font->icon }}'"
                                image-path="{{ asset('img/projects/' . str_replace(' ', '-', strtolower($font->name)) . '.png') }}"
                                :count="'{{ $font->icons_count }}'"
                                :update="'{{ is_null($font->icons_max_updated_at) ? '-' : \Carbon\Carbon::parse($font->icons_max_updated_at)->format('d.m H:i') }}'"
                                :link1="'{{ route($font->hash . '_icons') }}'"
                                :link2="'{{ route('download_font', ['filename' => strtolower($font->name)]) }}'"
                                :link1-text="'{{ __('messages.preview') }}'"
                                :link2-text="'{{ __('messages.cloud') }}'"
                                :scss-content="{{ json_encode(file_get_contents(resource_path('sass/'.$font->path))) }}"
                                class="h-full">
                            </card-font>
                        </div>
                        @endforeach
                        <!-- Font Family Cards -->
                        <div class="transform transition-all duration-300">
                            <card-family
                                title="Gant Fonts"
                                image-path="/img/projects/gant-preview.png"
                                :link="'{{ route('fonts.gant') }}'"
                                icons="design design-font"
                                :weights="'5 Weights'"
                                description="Vermont projects only"
                            />
                        </div>
                        <div class="transform transition-all duration-300">
                            <card-family
                                title="Nunito Sans"
                                image-path="/img/projects/nunito-preview.png"
                                :link="'{{ route('fonts.nunito') }}'"
                                icons="design design-font"
                                :weights="'Variable fonts'"
                                description="Internal Dev projects only"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
