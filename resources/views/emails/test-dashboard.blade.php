@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">Email Testing Dashboard</h2>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="mb-4">
                        <h4>Email Configuration</h4>
                        <div class="bg-light p-3 rounded">
                            <p><strong>Current Mail Driver:</strong> {{ config('mail.default') }}</p>
                            <p><strong>From Address:</strong> {{ config('mail.from.address') }}</p>
                            <p><strong>From Name:</strong> {{ config('mail.from.name') }}</p>
                        </div>
                    </div>

                    <ul class="nav nav-tabs mb-4" id="emailTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="test-tab" data-bs-toggle="tab" href="#test" role="tab">Test Email</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="welcome-tab" data-bs-toggle="tab" href="#welcome" role="tab">Welcome Email</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="notification-tab" data-bs-toggle="tab" href="#notification" role="tab">Notification Email</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="emailTabsContent">
                        <!-- Test Email Form -->
                        <div class="tab-pane fade show active" id="test" role="tabpanel">
                            <h4>Send Test Email</h4>
                            <form method="POST" action="{{ route('emails.send.test') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="email" class="form-label">Recipient Email</label>
                                    <input type="email" class="form-control" id="email" name="email" required value="{{ old('email', Auth::user()->email) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="name" class="form-label">Recipient Name</label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ old('name', Auth::user()->name) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="message" class="form-label">Custom Message</label>
                                    <textarea class="form-control" id="message" name="message">{{ old('message', 'This is a test message from the Design Portal.') }}</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Send Test Email</button>
                            </form>
                        </div>

                        <!-- Welcome Email Form -->
                        <div class="tab-pane fade" id="welcome" role="tabpanel">
                            <h4>Send Welcome Email</h4>
                            <form method="POST" action="{{ route('emails.send.welcome') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="welcome_email" class="form-label">Recipient Email</label>
                                    <input type="email" class="form-control" id="welcome_email" name="email" required value="{{ old('email', Auth::user()->email) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="welcome_name" class="form-label">Recipient Name (Optional)</label>
                                    <input type="text" class="form-control" id="welcome_name" name="name" value="{{ old('name', Auth::user()->name) }}">
                                    <div class="form-text">If left blank, the authenticated user's name will be used.</div>
                                </div>
                                <button type="submit" class="btn btn-primary">Send Welcome Email</button>
                            </form>
                        </div>

                        <!-- Notification Email Form -->
                        <div class="tab-pane fade" id="notification" role="tabpanel">
                            <h4>Send Notification Email</h4>
                            <form method="POST" action="{{ route('emails.send.notification') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="notification_email" class="form-label">Recipient Email</label>
                                    <input type="email" class="form-control" id="notification_email" name="email" required value="{{ old('email', Auth::user()->email) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="title" class="form-label">Notification Title</label>
                                    <input type="text" class="form-control" id="title" name="title" required value="{{ old('title', 'Important Update from Design Portal') }}">
                                </div>
                                <div class="mb-3">
                                    <label for="notification_message" class="form-label">Notification Message</label>
                                    <textarea class="form-control" id="notification_message" name="message" required rows="4">{{ old('message', 'We have important news to share with you regarding your Design Portal account.') }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="action_text" class="form-label">Action Button Text (Optional)</label>
                                    <input type="text" class="form-control" id="action_text" name="action_text" value="{{ old('action_text', 'View Details') }}">
                                </div>
                                <div class="mb-3">
                                    <label for="action_url" class="form-label">Action Button URL (Optional)</label>
                                    <input type="url" class="form-control" id="action_url" name="action_url" value="{{ old('action_url', url('/')) }}">
                                </div>
                                <button type="submit" class="btn btn-primary">Send Notification Email</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 