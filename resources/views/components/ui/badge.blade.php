@props([
    'type' => 'default', // default, success, warning, danger, info
    'icon' => null,
    'size' => 'md', // sm, md, lg
    'pill' => true,
])

@php
    $typeClasses = [
        'default' => 'text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700/50',
        'success' => 'text-green-800 dark:text-green-300 bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800/30',
        'warning' => 'text-yellow-800 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/10 border-yellow-200 dark:border-yellow-800/30',
        'danger' => 'text-red-800 dark:text-red-300 bg-red-50 dark:bg-red-900/10 border-red-200 dark:border-red-800/30',
        'info' => 'text-blue-800 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800/30',
    ];
    
    $sizeClasses = [
        'sm' => 'px-2 py-1 text-xs',
        'md' => 'px-3 py-1.5 text-sm',
        'lg' => 'px-4 py-2 text-base',
    ];
    
    $classes = $typeClasses[$type] ?? $typeClasses['default'];
    $sizeClass = $sizeClasses[$size] ?? $sizeClasses['md'];
    $roundedClass = $pill ? 'rounded-full' : 'rounded-md';
@endphp

<span {{ $attributes->merge(['class' => "inline-flex items-center {$sizeClass} gant-modern-medium {$classes} {$roundedClass} border"]) }}>
    @if($icon)
        <i class="{{ $icon }} mr-1.5"></i>
    @endif
    {{ $slot }}
</span> 