@props([
    'type' => 'info', // info, success, warning, error
    'message' => '',
    'dismissible' => false,
])

@php
    $typeClasses = [
        'info' => 'text-blue-700 dark:text-blue-400 border-blue-500 dark:border-blue-600 bg-blue-50/50 dark:bg-blue-900/10',
        'success' => 'text-green-700 dark:text-green-400 border-green-500 dark:border-green-600 bg-green-50/50 dark:bg-green-900/10',
        'warning' => 'text-yellow-700 dark:text-yellow-400 border-yellow-500 dark:border-yellow-600 bg-yellow-50/50 dark:bg-yellow-900/10',
        'error' => 'text-red-700 dark:text-red-400 border-red-500 dark:border-red-600 bg-red-50/50 dark:bg-red-900/10',
    ];
    
    $iconClasses = [
        'info' => 'design design-info-circle',
        'success' => 'design design-circle-checked',
        'warning' => 'design design-warning',
        'error' => 'design design-circle-remove',
    ];
    
    $classes = $typeClasses[$type] ?? $typeClasses['info'];
    $icon = $iconClasses[$type] ?? $iconClasses['info'];
@endphp

<div {{ $attributes->merge(['class' => "p-4 text-sm gant-modern-regular {$classes} flex items-center border-l-4 pl-5"]) }} role="alert">
    <i class="{{ $icon }} mr-2 text-lg"></i>
    <span>{{ $message }}</span>
    @if($dismissible)
        <button type="button" class="ml-auto text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none" onclick="this.parentElement.remove()">
            <i class="design design-close text-lg"></i>
        </button>
    @endif
</div> 