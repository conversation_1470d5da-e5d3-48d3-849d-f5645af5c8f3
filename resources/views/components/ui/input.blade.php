@props([
    'type' => 'text',
    'name' => '',
    'id' => null,
    'value' => '',
    'label' => null,
    'placeholder' => '',
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'error' => null,
    'helpText' => null,
    'leadingIcon' => null,
    'trailingIcon' => null,
])

@php
    $inputId = $id ?? $name;
    $hasError = $error !== null;
    $hasLeadingIcon = $leadingIcon !== null;
    $hasTrailingIcon = $trailingIcon !== null;
    
    $baseClasses = 'flex h-10 rounded-xl border px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-300 dark:focus:ring-offset-gray-800 gant-modern-regular w-full';
    
    $stateClasses = $hasError
        ? 'border-red-300 dark:border-red-700 focus:border-red-500 dark:focus:border-red-600 focus:ring-red-500 dark:focus:ring-red-600 bg-red-50 dark:bg-red-900/10'
        : 'border-gray-300 dark:border-gray-700 focus:border-gray-500 dark:focus:border-gray-600 focus:ring-gray-500 dark:focus:ring-gray-600 !bg-white dark:!bg-gray-800';
    
    $paddingClasses = '';
    if ($hasLeadingIcon) $paddingClasses .= ' pl-10';
    if ($hasTrailingIcon) $paddingClasses .= ' pr-10';
@endphp

<div {{ $attributes->only(['class']) }}>
    @if($label)
        <label for="{{ $inputId }}" class="block text-sm gant-modern-medium text-gray-600 dark:text-gray-400 mb-1">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif
    
    <div class="relative">
        @if($hasLeadingIcon)
            <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                <i class="{{ $leadingIcon }} text-gray-400 dark:text-gray-600"></i>
            </div>
        @endif
        
        <input
            type="{{ $type }}"
            name="{{ $name }}"
            id="{{ $inputId }}"
            value="{{ $value }}"
            placeholder="{{ $placeholder }}"
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($readonly) readonly @endif
            class="{{ $baseClasses }} {{ $stateClasses }} {{ $paddingClasses }}"
            {{ $attributes->except(['class']) }}
        >
        
        @if($hasTrailingIcon)
            <div class="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                <i class="{{ $trailingIcon }} text-gray-400 dark:text-gray-600"></i>
            </div>
        @endif
    </div>
    
    @if($error)
        <p class="mt-1 text-xs text-red-600 dark:text-red-400">{{ $error }}</p>
    @elseif($helpText)
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ $helpText }}</p>
    @endif
</div> 