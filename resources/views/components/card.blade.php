@props([
'title' => 'Default Title',
'icons' => 'wms wms-scan_ean',
'image_path' => null,
'count' => 'Default Subtitle',
'update' => 'Default content here...',
'link1' => '#',
'link2' => '#',
'link1Text' => 'Card Link',
'link2Text' => 'Another Link',
'font' => null
])
<div class="grid grid-cols-2 gap-1">
    <div class="w-full">
        <h1 class="mb-1 text-black dark:text-gray-200">
            <a href="{{ $link1 }}">
                @if($image_path)
                    <img src="{{ $image_path }}" alt="{{ $title }}" class="w-full h-auto object-contain" style="max-height: 120px;">
                @else
                    <i class="{{ $icons }} text-9xl" aria-hidden="true"></i>
                @endif
            </a>
        </h1>
    </div>
    <div class="content-center">
        <h2 class="mb-1 text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold leading-none gant-modern-bold tracking-tighter">
        <a href="{{ $link1 }}">{{ $title }}</a>
        </h2>
        <p class="mb-1 text-sm font-medium leading-tight text-black dark:text-neutral-300">
            <span class="font-normal opacity-50">icons: </span><span class="gant-modern-bold">{{ $count }}</span>
        </p>
        <p class="mb-2 text-sm font-medium leading-tight text-blackdark:text-neutral-300">
        <i class="design design-commit opacity-50"></i> {{ $update }}</i>
        </p>
        <div class="flex gap-3">
                            <a class="text-base font-normal text-primary pointer-events-auto inline-block text-sm cursor-pointer transition duration-150 ease-in-out hover:text-primary-600 focus:text-primary-600 focus:outline-none focus:ring-0 active:text-primary-700 dark:text-primary-400"
                                href="javascript:void(0);" onclick="copyToFile('{{ $font->hash }}')"><i
                                    class="design design-scss opacity-50"></i> .scss</a>
                            <a class="text-base font-normal text-primary pointer-events-auto inline-block text-sm cursor-pointer transition duration-150 ease-in-out hover:text-primary-600 focus:text-primary-600 focus:outline-none focus:ring-0 active:text-primary-700 dark:text-primary-400"
                                href="{{ route('download_font', ['filename' => $font->hash]) }}" target="_blank"><i
                                    class="design design-woff opacity-50"></i> .woff2</a>
                            <input type="hidden" name="content_{{ $font->hash }}" id="content_{{ $font->hash }}"
                                value="{{ file_get_contents(resource_path('sass/'.$font->path)) }}">
                        </div>
    </div>
    @push('scripts')
                    <script>
                        function copyToFile(hash) {
                            var copyText = document.getElementById("content_" + hash);
                            if (copyText) {
                                if (document.queryCommandSupported('copy')) {
                                    copyText.select();
                                    document.execCommand('copy');
                                    alert('Obsah súboru skopírovaný!');
                                } else {
                                    console.error('Copy command is not supported');
                                }
                            } else {
                                console.error('Element not found or not selectable');
                            }
                        }
                        </script>
                        @endpush

</div>

