@props([
    'user',
    'size' => 'md',
    'ring' => true
])

@php
$sizes = [
    'sm' => 'w-10 h-10',
    'md' => 'w-20 h-20',
    'lg' => 'w-32 h-32'
];
@endphp

<div class="flex-shrink-0 {{ $sizes[$size] }} bg-gray-950 dark:bg-slate-900 rounded-full flex items-center justify-center overflow-hidden {{ $ring ? 'ring-2 ring-gray-200 dark:ring-gray-700' : '' }}">
    <img 
        src="{{ $user->avatar_url }}"
        alt="{{ $user->name }}'s avatar"
        class="w-full h-full object-cover"
        onerror="this.src='/img/default-avatar.png'"
    >
</div> 