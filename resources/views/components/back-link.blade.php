@php
    $referer = request()->header('Referer');
    $previousUrl = $referer ? parse_url($referer, PHP_URL_PATH) : null;
    
    // Map routes to readable names with translations
    $routeNames = [
        '/' => __('messages.dashboard'),
        '/iconsearch' => __('messages.icon_search'),
        '/projectassets' => __('messages.assets'),
        '/typeface' => __('messages.font_family'),
        '/classes' => __('messages.classes'),
        '/playground' => __('messages.playground'),
        '/admin' => __('messages.administration'),
    ];
    
    $pageTitle = $previousUrl ? ($routeNames[$previousUrl] ?? __('messages.previous_page')) : __('messages.back');
@endphp

@if($previousUrl)
    <a href="#" 
        onclick="if(history.length > 1) { window.history.back(); } return false;" 
        class="inline-flex items-center px-3 py-3 rounded-lg text-sm text-gray-900 bg-gray-50 border border-gray-300 hover:bg-gray-50 dark:bg-gray-900 dark:border-gray-600 dark:text-white dark:hover:bg-gray-900 transition-all duration-300 ease-out gant-modern-regular group w-fit"
    >
        <i class="design design-arrow-left text-xl transition-transform duration-300 group-hover:-translate-x-0.5"></i>
        <span class="ms-0 max-w-0 overflow-hidden whitespace-nowrap group-hover:max-w-[200px] group-hover:ms-2 transition-all duration-300 ease-out">
            <span class="font-medium text-gray-500 dark:text-gray-400">{{ __('messages.back_to') }} </span>
            <span class="gant-modern-bold text-gray-900 dark:text-gray-100">{{ $pageTitle }}</span>
        </span>
    </a>
@else
    <span class="inline-flex items-center px-3 py-3 rounded-lg text-sm text-gray-400 bg-gray-50 border border-gray-300 dark:bg-gray-950 dark:border-gray-600 dark:text-gray-600 cursor-not-allowed opacity-75 gant-modern-regular">
        <i class="design design-arrow-left text-xl"></i>
        <span class="ms-2 opacity-50">
            <span class="font-medium text-gray-500 dark:text-gray-400"> </span>
            <span class="gant-modern-bold text-gray-900 dark:text-gray-100">{{ $pageTitle }}</span>
        </span>
    </span>
@endif
