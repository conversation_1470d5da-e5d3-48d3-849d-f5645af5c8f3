<ul>
    <li class="w-full">
        <a class="nav-link {{ request()->routeIs('iconsearch') ? 'nav-link-active' : '' }}" href="{{ route('iconsearch') }}">
            <i class="design design-search-fill me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.search_icons') }}</span>
        </a>
    </li>
    <li class="w-full">
        <a class="nav-link {{ request()->routeIs('projectassets') ? 'nav-link-active' : '' }}" href="{{ route('projectassets') }}">
            <i class="design design-figma me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.assets') }}</span>
        </a>
    </li>
    <li class="w-full">
        <a class="nav-link {{ request()->routeIs('code') ? 'nav-link-active' : '' }}" href="{{ route('code') }}">
            <i class="design design-bracket2 me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.code') }}</span> 
        </a>
    </li>
    <li class="w-full">
        <a class="nav-link {{ request()->routeIs('animations') ? 'nav-link-active' : '' }}" href="{{ route('animations') }}">
            <i class="design design-transparency me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.animations') }}</span>
        </a>
    </li>
    <li class="w-full">
        <a class="nav-link {{ request()->routeIs('tools') || request()->routeIs('tools.*') ? 'nav-link-active' : '' }}" href="{{ route('tools') }}">
            <i class="design design-measuring me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.tools') }}</span>
        </a>
    </li>
    <li class="w-full">
        <a class="nav-link" href="https://vermont.mareknovy.sk/" target="_blank">
            <i class="design design-clock me-3 text-lg opacity-80"></i>
            <span>Old website</span>
        </a>
    </li>

    <li class="w-full mt-8 border-t dark:border-gray-800 pt-6">
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <a href="#" onclick="event.preventDefault(); this.closest('form').submit();" class="nav-link">
                <i class="design design-logout me-3 text-lg opacity-80"></i>
                <span>{{ __('messages.logout') }}</span>
            </a>
        </form>
    </li>
    
    @if(auth()->check() && in_array(auth()->user()->login, config('vermont.admin_user_logins', [])))
    <li class="w-full">
        <a href="{{ route('admin.index') }}" class="nav-link {{ request()->routeIs('admin.index') ? 'nav-link-active' : '' }}">
            <i class="design design-customization me-3 text-lg opacity-80"></i>
            <span>{{ __('messages.administration') }}</span>
            <span class="ml-auto inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full min-w-[20px] h-5">
                1
            </span>
        </a>
    </li>
    <li class="w-full">
        <img src="https://img.shields.io/endpoint?url=https%3A%2F%2Fforge.laravel.com%2Fsite-badges%2F5aaafdad-4865-4ab7-8b47-e21744d57f10%3Fdate%3D1&style=flat-square" alt="Forge Deploy" class="inline-block">
    </li>
    @endif

    <li class="w-full mt-4">
        <div class="nav-link !p-2">
            <language-switcher></language-switcher>
        </div>
    </li>
    
    <li class="w-full mt-4">
        <div class="w-full align-center">
            <dark-mode-toggle></dark-mode-toggle>
        </div>
    </li>
</ul> 