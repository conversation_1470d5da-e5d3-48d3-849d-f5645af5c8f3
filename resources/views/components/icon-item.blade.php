@props([
'title' => 'Default Title',
'icons' => 'wms wms-scan_ean',
'subtitle' => 'Default Subtitle',
'content' => 'Default content here...',
'link1' => '#',
'link2' => '#',
'link1Text' => 'Card Link',
'link2Text' => 'Another Link'
])
<div
    class=" rounded-lg text-left text-surface shadow-secondary-1 dark:bg-surface-dark dark:text-white">
    <div class="p-6">
        <h1 class="mb-1 text-7xl text-gray-500 dark:text-gray-600" >
            <a href="{{ $link1 }}"><i class="{{ $icons }}" aria-hidden="true" ></i></a>
        </h1>
        <h2 class="mb-1 text-2xl font-bold leading-tight ">
            {{ $title }}
        </h2>
        <p class="mb-2 text-base font-medium leading-tight text-surface/75 dark:text-neutral-300">
            {{ $subtitle }}
        </p>
        <p class="mb-4 text-base leading-normal">
            {{ $content }}
        </p>
        <a type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 border border-blue-700 rounded mr-6" href="{{ $link1 }}"
            class="pointer-events-auto me-5 inline-block cursor-pointer rounded text-base font-normal leading-normal text-primary transition duration-150 ease-in-out hover:text-primary-600 focus:text-primary-600 focus:outline-none focus:ring-0 active:text-primary-700 dark:text-primary-400">
            {{ $link1Text }}
        </a>
        <a type="button" href="{{ $link2 }}" target="_blank"
            class="pointer-events-auto inline-block cursor-pointer rounded text-base font-normal leading-normal text-primary transition duration-150 ease-in-out hover:text-primary-600 focus:text-primary-600 focus:outline-none focus:ring-0 active:text-primary-700 dark:text-primary-400 opacity-25">
           {{ $link2Text }}
        </a>
    </div>
</div>