@props([
    'pretitle' => 'VERMONT DEV PROJECTS',
    'title' => 'Default Title',
    'lastUpdate' => 'xxx',
    'iconsCount' => 'xxx',
    'downloadScssLink' => '#1',
    'indexLink' => '#3',
    'webfontLink' => '#4',
    'hash' => null
])

@php($font = \App\Models\Font::where('hash', $hash)->first())

<icon-font-header
    :title="'{{ $title }}'"
    :last-update="'{{ $font ? ($font->updated_at ? \Carbon\Carbon::parse($font->updated_at)->format('d.m.Y H:i:s') : 'No update date available') : 'Font data not available' }}'"
    :icons-count="{{ $font ? $font->icons_count : 0 }}"
    :download-font-url="'{{ route('download_font', ['filename' => $font->hash]) }}'"
    :index-link="'{{ $indexLink }}'"
    :webfont-link="'{{ $webfontLink }}'"
    :scss-content='@json($font ? file_get_contents(resource_path('sass/'.$font->path)) : "")'
></icon-font-header>