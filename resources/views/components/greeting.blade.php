@props(['user'])

@php
    $greetings = [
        'default' => [
            __('messages.hello'),
            __('messages.welcome_back'),
            __('messages.nice_to_see_you'),
            __('messages.good_to_have_you'),
            __('messages.hey_there')
        ],
        'u1111' => [
            __('messages.special_hello_u1111'),
            __('messages.welcome_back_boss'),
            __('messages.chief_in_the_house')
        ],
        '4431' => [
            __('messages.hello_designer'),
            __('messages.welcome_design_master'),
            __('messages.design_wizard')
        ]
    ];

    $timeOfDay = now()->hour;
    $timeBasedGreeting = '';
    
    if ($timeOfDay >= 5 && $timeOfDay < 12) {
        $timeBasedGreeting = __('messages.good_morning');
    } elseif ($timeOfDay >= 12 && $timeOfDay < 17) {
        $timeBasedGreeting = __('messages.good_afternoon');
    } elseif ($timeOfDay >= 17 && $timeOfDay < 22) {
        $timeBasedGreeting = __('messages.good_evening');
    } else {
        $timeBasedGreeting = __('messages.good_night');
    }

    // Get user login safely
    $userLogin = $user->login ?? 'default';
    
    // First check if user has custom greetings set in the admin
    if (isset($user->custom_greetings) && !empty($user->custom_greetings)) {
        $userGreetings = $user->custom_greetings;
    } else {
        // Fall back to predefined greetings if no custom ones are set
        $userGreetings = $greetings[$userLogin] ?? $greetings['default'];
    }
    
    // Make sure $userGreetings is an array
    if (!is_array($userGreetings)) {
        $userGreetings = $greetings['default'];
    }
    
    // Add time-based greeting
    $userGreetings[] = $timeBasedGreeting;

    // Make sure we have a valid username to display
    $userName = $user->name ?? 'Guest';
@endphp

<div class="w-full max-w-screen-2xl mx-auto px-4">
    <div class="greeting-container relative flex justify-center items-center">
        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-[5vw] 2xl:text-[6vw] gant-modern-bold leading-none tracking-tighter text-gray-900 dark:text-gray-200 whitespace-nowrap">
            <!-- Debug info (only visible in development) -->
            @if(config('app.env') === 'local')
            <span class="hidden">Debug: {{ json_encode($userGreetings) }} - User: {{ $userName }}</span>
            @endif
            
            <!-- Using v-bind with raw JSON string for better compatibility -->
            <typed-greeting 
                :custom-greetings='@json($userGreetings)'
                name="{{ $userName }}"
            ></typed-greeting>
        </h1>
    </div>
</div>
