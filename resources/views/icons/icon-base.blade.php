@extends('layouts.application')

@section('title', $title ?? 'Icon Fonts')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-4">
            <a href="{{ route('icons.search') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest hover:bg-gray-700 dark:hover:bg-gray-300 active:bg-gray-900 dark:active:bg-gray-400 focus:outline-none focus:border-gray-900 dark:focus:border-gray-500 focus:ring ring-gray-300 dark:ring-gray-700 disabled:opacity-25 transition ease-in-out duration-150">
                <i class="design design-search-fill mr-2"></i>
                All Icons
            </a>
        </div>

        <div id="icon-app" data-font="{{ $fontClass ?? 'default' }}" data-font-id="{{ $fontId ?? 0 }}">
            @yield('icon-content')
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const appElement = document.getElementById('icon-app');
        if (appElement) {
            const fontClass = appElement.dataset.font;
            const fontId = parseInt(appElement.dataset.fontId, 10) || null;
            
            // This will be initialized by the specific icon page
            if (window.initializeIconGrid) {
                window.initializeIconGrid(fontClass, fontId);
            }
        }
    });
</script>
@endpush
