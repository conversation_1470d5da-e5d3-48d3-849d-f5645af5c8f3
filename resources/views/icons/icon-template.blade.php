@extends('layouts.application')

@section('title', $title ?? 'Font Icons')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-4">
            <a href="{{ route('icons.search') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest hover:bg-gray-700 dark:hover:bg-gray-300 active:bg-gray-900 dark:active:bg-gray-400 focus:outline-none focus:border-gray-900 dark:focus:border-gray-500 focus:ring ring-gray-300 dark:ring-gray-700 disabled:opacity-25 transition ease-in-out duration-150">
                <i class="design design-search-fill mr-2"></i>
                All Icons
            </a>
        </div>
        
        <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-gray-100">{{ $title ?? 'Font Icons' }}</h1>
        
        <div id="icon-grid-container">
            <icon-grid-detail
                v-if="loaded"
                :initial-font="fontClass"
                :initial-icons="icons" 
                :font-id="fontId"
            />
            <div v-else class="flex items-center justify-center h-64">
                <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm rounded-md text-white bg-gray-800 dark:bg-gray-200 dark:text-gray-800 shadow transition ease-in-out duration-150">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white dark:text-gray-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading icons...
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    window.initializeIconGrid = function(fontClass, fontId) {
        const app = Vue.createApp({
            data() {
                return {
                    fontClass: fontClass,
                    fontId: fontId,
                    icons: [],
                    loaded: false
                };
            },
            mounted() {
                this.fetchIcons();
            },
            methods: {
                async fetchIcons() {
                    try {
                        const response = await fetch(`/api/fonts/${fontId}/icons`);
                        const data = await response.json();
                        
                        this.icons = data.map(icon => ({
                            id: icon.id || `${fontClass}-${icon.css_class}`,
                            cssClass: icon.css_class,
                            cssContent: icon.css_content,
                            tags: Array.isArray(icon.tags) ? icon.tags : []
                        }));
                        
                        this.loaded = true;
                    } catch (error) {
                        console.error('Error loading icons:', error);
                    }
                }
            }
        });

        // Register global components
        app.component('IconGridDetail', IconGridDetail);
        app.component('IconDetailModal', IconDetailModal);
        
        // Mount the app
        app.mount('#icon-grid-container');
    };
</script>
@endpush 