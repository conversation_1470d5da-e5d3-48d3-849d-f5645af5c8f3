@extends('layouts.aplication')

@section('title', '{{ Font Name }} Icons')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-4">
        <a href="{{ route('icons.search') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest hover:bg-gray-700 dark:hover:bg-gray-300 active:bg-gray-900 dark:active:bg-gray-400 focus:outline-none focus:border-gray-900 dark:focus:border-gray-500 focus:ring ring-gray-300 dark:ring-gray-700 disabled:opacity-25 transition ease-in-out duration-150">
            <i class="design design-search-fill mr-2"></i>
            All Icons
        </a>
    </div>

    <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-gray-100">{{ Font Name }} Icons</h1>

    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        @foreach($icons as $icon)
        <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow text-center">
            <div class="text-4xl mb-2">
                <i class="{{ fontClass }} {{ $icon->css_class }}"></i>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300 truncate">
                {{ $icon->css_class }}
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection 