@extends('layouts.application')

@section('title', 'Order Group Icons')

@section('content')
    <div class="py-12">
        <div class="mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-slate-950 overflow-hidden">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    @php($font = \App\Models\Font::where('hash', 'og')->first())
                    @php($icons = collect($font->icons_data ?? []))
                    
                    <x-iconfont-header 
                        :hash="$font->hash"
                        :title="'Order Group'"
                        :lastUpdate="$font ? ($font->updated_at ? \Carbon\Carbon::parse($font->updated_at)->format('d.m.Y') : 'No update date available') : 'Font data not available'"
                        :downloadScssLink="'/download/scss'"
                        :indexLink="url('/icons/search')"
                        :webfontLink="'#'"
                    />
                    
                    <icon-grid-detail 
                        :initial-font="'{{ $font->css_class }}'"
                        :initial-icons="{{ json_encode($icons->map(function($icon) {
                            return [
                                'id' => $icon['id'] ?? null,
                                'cssClass' => $icon['css_class'] ?? null,
                                'cssContent' => $icon['css_content'] ?? null,
                                'tags' => $icon['tags'] ?? [],
                            ];
                        })) }}"
                        :font-id="{{ $font->id }}" 
                    ></icon-grid-detail>
                </div>
            </div>
        </div>
    </div>
@endsection