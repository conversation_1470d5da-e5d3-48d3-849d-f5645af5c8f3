@extends('layouts.application')

@section('title', 'Order Group Icons')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-4">
        <a href="{{ url('/icons/search') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 dark:bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-white dark:text-gray-800 uppercase tracking-widest hover:bg-gray-700 dark:hover:bg-gray-300 active:bg-gray-900 dark:active:bg-gray-400 focus:outline-none focus:border-gray-900 dark:focus:border-gray-500 focus:ring ring-gray-300 dark:ring-gray-700 disabled:opacity-25 transition ease-in-out duration-150">
            <i class="design design-search-fill mr-2"></i>
            All Icons
        </a>
    </div>

    <h1 class="text-3xl font-bold mb-6 text-gray-900 dark:text-gray-100">Order Group Icons</h1>

    <div id="icon-grid-container">
        <!-- This will be populated by Vue -->
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Correct the font information based on your database
        const fontClass = 'og'; // Using the hash value from your database
        const fontId = {{ \App\Models\Font::where('hash', 'og')->first()->id ?? 'null' }}; // Get the actual ID from database
        
        // Check if we got a valid font ID
        if (!fontId) {
            document.getElementById('icon-grid-container').innerHTML = `
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded" role="alert">
                    <p class="font-bold">Error</p>
                    <p>Font data not found in database.</p>
                </div>
            `;
            return;
        }
        
        const app = Vue.createApp({
            data() {
                return {
                    fontClass: fontClass,
                    fontId: fontId,
                    icons: [],
                    loaded: false
                };
            },
            mounted() {
                this.fetchIcons();
            },
            methods: {
                async fetchIcons() {
                    try {
                        const response = await fetch(`/api/fonts/${fontId}/icons`);
                        const data = await response.json();
                        
                        this.icons = data.map(icon => ({
                            id: icon.id || `${fontClass}-${icon.css_class}`,
                            cssClass: icon.css_class,
                            cssContent: icon.css_content,
                            tags: Array.isArray(icon.tags) ? icon.tags : []
                        }));
                        
                        this.loaded = true;
                    } catch (error) {
                        console.error('Error loading icons:', error);
                    }
                }
            }
        });

        // Register global components
        app.component('IconGridDetail', IconGridDetail);
        app.component('IconDetailModal', IconDetailModal);
        
        // Mount the app
        app.mount('#icon-grid-container');
    });
</script>
@endpush 