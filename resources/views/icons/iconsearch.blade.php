@extends('layouts.application')

@section('title', 'Icon Search')

@section('content')
<div class="px-4 py-8">    
    <div id="icon-search-app">
        <icon-grid></icon-grid>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const app = Vue.createApp({});
        
        // Register components
        app.component('IconGrid', IconGrid);
        app.component('IconDetailModal', IconDetailModal);
        
        // Mount the app
        app.mount('#icon-search-app');
    });
</script>
@endpush