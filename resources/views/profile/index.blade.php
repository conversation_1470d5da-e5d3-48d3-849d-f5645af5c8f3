@extends('layouts.application')

@section('title', 'Profile')

@section('content')
<!-- Pattern SVG Background -->
<div class="fixed inset-0 pointer-events-none">
    <svg class="absolute inset-0 w-full h-full text-gray-900/[0.07] dark:text-gray-100/[0.08]" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-3)" />
    </svg>
</div>

<!-- Content Container -->
<div class="min-h-screen flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8 relative">
    <div class="w-full max-w-4xl">
        <div class="bg-white dark:bg-slate-950 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
            <!-- Profile Header -->
            <section class="border-b border-gray-200 dark:border-gray-700/50 p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                    <!-- User Identity - Left Side -->
                    <div class="flex items-center gap-6">
                    <x-avatar :user="$user" size="md" :ring="true" />
                        <div>
                            <h2 class="text-2xl gant-modern-bold text-gray-900 dark:text-white">{{ $user->login }}</h2>
                            <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-lg">
                                {{ $user->name }} {{ $user->surname ?? '' }}
                            </p>
                            @if($user->email)
                                <p class="text-gray-500 dark:text-gray-400 gant-modern-regular flex items-center mt-1">
                                    <i class="claims claims-mailrecords-outline mr-1.5 opacity-70"></i>{{ $user->email }}
                                </p>
                            @endif
                        </div>
                    </div>
                    
                    <!-- User Status - Right Side -->
                    <div class="flex flex-wrap items-center gap-3">
                        <!-- Active Status Badge -->
                        @if($user->active)
                            <div class="flex items-center gap-2 px-3 py-1.5 text-sm gant-modern-medium text-green-800 dark:text-green-300 bg-green-50 dark:bg-green-900/10 rounded-full border border-green-200 dark:border-green-800/30">
                                <div class="w-2.5 h-2.5 rounded-full bg-green-500 animate-pulse"></div>
                                <span>Aktívny používateľ</span>
                            </div>
                        @else
                            <div class="flex items-center gap-2 px-3 py-1.5 text-sm gant-modern-medium text-red-800 dark:text-red-300 bg-red-50 dark:bg-red-900/10 rounded-full border border-red-200 dark:border-red-800/30">
                                <div class="w-2.5 h-2.5 rounded-full bg-red-500"></div>
                                <span>Neaktívny používateľ</span>
                            </div>
                        @endif
                        
                        <!-- Last Login Indicator -->
                        @if($user->last_login_at)
                            <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                <i class="claims claims-clock-outline mr-1.5"></i>
                                {{ \Carbon\Carbon::parse($user->last_login_at)->diffForHumans() }}
                            </span>
                        @endif
                        <form method="POST" action="{{ route('logout') }}" class="inline-block">
                        @csrf
                        <button type="submit" class="inline-flex items-center gap-2 px-4 py-2 text-base gant-modern-medium text-slate-100 dark:text-slate-900 bg-slate-900 dark:bg-gray-200 rounded-md border border-slate-200 dark:border-slate-800 hover:bg-slate-700 dark:hover:bg-slate-200">
                            <i class="design design-logout text-slate-200 dark:text-slate-800"></i>
                            <span>Odhlásiť sa</span>
                        </button>
                    </form>
                    </div>
                </div>
            </section>

            <!-- Profile Content -->
            <div class="p-6 space-y-6">
                <!-- Row 1: Login and Registration Date -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Login -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Login</div>
                        <div class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $user->login }}</div>
                    </div>
                    
                    <!-- Column 2: Registration Date -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Dátum registrácie</div>
                        <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                            @if($user->created_at)
                                <div class="flex items-center gant-modern-bold">
                                    <i class="claims claims-calendar-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                    {{ $user->created_at->format('d.m.Y') }}
                                    <span class="mx-2 text-gray-300 dark:text-gray-600 gant-modern-regular">|</span>
                                    <i class="claims claims-clock-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                    {{ $user->created_at->format('H:i') }}
                                </div>
                            @else
                                <span class="text-gray-400 dark:text-gray-600 gant-modern-bold">-</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Row 2: Name and Surname -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Name -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Meno</div>
                        <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">{{ $user->name }}</div>
                    </div>
                    
                    <!-- Column 2: Surname -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Priezvisko</div>
                        <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">{{ $user->surname ?? '-' }}</div>
                    </div>
                </div>

                <!-- Row 3: Email and Notifications -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Email -->
                    <div class="group">
                        <profile-edit 
                            :user-data='@json($user, JSON_HEX_APOS|JSON_HEX_QUOT)' 
                            :csrf-token="'{{ csrf_token() }}'"
                            update-url="{{ route('profile.update') }}"
                        ></profile-edit>
                    </div>
                    <div class="group hidden sm:block">
                        <!-- Intentionally left empty for layout balance -->
                    </div>
                    <!-- Column 2: Email Notifications -->
                   
                </div>

                <!-- Row 4: Projects and Default Page -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
   
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Povoliť notifikácie emailom</div>
                        <div class="text-gray-900 dark:text-white gant-modern-regular text-lg">
                          <div class="relative inline-flex items-center cursor-not-allowed opacity-50">
                              <div class="w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full">
                                  <div class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full"></div>
                              </div>
                              <span class="ml-3 text-sm text-gray-500 dark:text-gray-400">Vypnuté (Čoskoro, v príprave)</span>
                          </div>
                        </div>
                    </div>
                    <!-- Column 1: Projects -->

                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Predvolená stránka (Čoskoro, v príprave)</div>
                        <div class="text-gray-900 dark:text-white gant-modern-regular">
                            <default-page-selector
                                :initial-page="'{{ $user->default_page ?? 'projectassets' }}'"
                                :update-url="'{{ route('profile.update') }}'"
                                :csrf-token="'{{ csrf_token() }}'"
                            ></default-page-selector>
                        </div>
                    </div>
                    
                    
                    <!-- Column 2: Default Page -->

                </div>

                <!-- Row 5: Shared Projects -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Empty (or could add something else here) -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Projekty</div>
                        <div class="text-gray-900 dark:text-white gant-modern-medium text-lg">
                          @if($user->projects->count() > 0)
                            <div class="flex flex-col gap-1">
                                @foreach($user->projects as $project)
                                    <a href="{{ route('projects.show', $project) }}" 
                                       class="inline-flex items-center p-3 min-h-14 rounded-md text-lg font-medium bg-white dark:bg-slate-950 text-gray-800 dark:text-gray-200 border border-solid border-gray-200 dark:border-slate-800 hover:bg-gray-50 dark:hover:bg-slate-900 hover:border-gray-300 dark:hover:border-slate-700 transition-colors">
                                        <i class="design design-folder-open mr-2 opacity-70"></i>
                                        {{ $project->name }}
                                    </a>
                                @endforeach
                            </div>
                          @else
                            <span class="text-gray-400 dark:text-gray-600 italic">Žiadne priradené projekty</span>
                          @endif
                        </div>
                    </div>

                    
                    <!-- Column 2: Shared Projects -->
                    <div class="group sm:col-start-2">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Spoločné projekty</div>
                        <div class="text-gray-900 dark:text-white">
                            @php
                                $coworkers = $user->projects->flatMap->users
                                    ->where('id', '!=', $user->id)
                                    ->unique('id')
                                    ->map(function($coworker) use ($user) {
                                        return [
                                            'user' => $coworker,
                                            'projects' => $user->projects->filter(function($project) use ($coworker) {
                                                return $project->users->contains('id', $coworker->id);
                                            })->pluck('name')
                                        ];
                                    });
                            @endphp
                            
                            @if($coworkers->count() > 0)
                                <div class="flex flex-col gap-1">
                                    @foreach($coworkers as $coworkerData)
                                        <div class="flex items-center gap-2 px-3 py-1 min-h-14 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 rounded-lg">
                                            <x-avatar :user="$coworkerData['user']" size="sm" :ring="true" />
                                            <div class="flex flex-col">
                                                <span class="text-base gant-modern-medium">{{ $coworkerData['user']->name }} {{ $coworkerData['user']->surname }}</span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 gant-modern-regular">
                                                    {{ $coworkerData['projects']->join(', ') }}
                                                </span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <span class="text-gray-400 dark:text-gray-600 italic text-sm">Si nato sám šefko</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Assets Section -->
                <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                    <div class="flex items-center gap-3 mb-4">
                        <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Moje assety</h3>
                    </div>
                    
                    @if($user->fonts->count() > 0)
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($user->fonts as $font)
                            <a href="{{ $font->hash === 'og' ? route('og_icons') : route(str_replace(' ', '_', strtolower($font->hash)) . '_icons') }}" 
                               class="relative flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-900 rounded-lg p-4 hover:border-gray-300 dark:hover:border-slate-700 transition-colors duration-200">
                                @if($font->new_icons_count > 0)
                                    <span class="absolute -top-3 -right-3 flex items-center justify-center pr-1 w-10 h-10 bg-blue-200 dark:bg-blue-600 text-blue-500 dark:text-blue-100 rounded-full text-xs gant-modern-bold">
                                       +{{ $font->new_icons_count }}
                                    </span>
                                @endif
                                <i class="{{ $font->css_class }} {{ $font->preview_icon ?? $font->css_class.'-thumbnail' }} text-3xl text-slate-500 dark:text-slate-600 mb-2"></i>
                                <span class="text-md mb-1 gant-modern-bold text-slate-900 dark:text-slate-200 text-center leading-none">{{ $font->name }}</span>
                                <span class="text-xs gant-modern-regular text-slate-600 dark:text-slate-400">{{ $font->icons_count }} ikon</span>
                            </a>
                        @endforeach
                        </div>
                    @else
                        <div class="py-3 text-center">
                            <p class="text-sm gant-modern-italic text-gray-500 dark:text-gray-400 italic">
                                Nemáte priradené žiadne fonty
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 