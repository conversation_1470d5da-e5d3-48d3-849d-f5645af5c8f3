<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="is-admin" content="{{ json_encode(in_array(auth()->user()->login ?? '', config('vermont.admin_user_logins'))) }}">
        <link rel="icon" href="/img/icons/favicon.svg">
        <link rel="apple-touch-icon" sizes="180x180" href="/img/icons/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/img/icons/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/img/icons/favicon-16x16.png">
        <link rel="mask-icon" href="/img/icons/safari-pinned-tab.svg" color="#5bbad5">
        
        <!-- Make route helper available to JavaScript -->
        <script>
            window.route = function(name) {
                return @json(collect(\Route::getRoutes()->getRoutesByName())->mapWithKeys(function($route, $name) {
                    return [$name => url($route->uri())];
                })->toArray())[name];
            }
        </script>
        
        @php
            $currentRoute = request()->segment(1);
            $pageTitle = 'Vermont Design System';
            
            switch($currentRoute) {
                case '':
                    $pageTitle = 'Home - Vermont Design System';
                    break;
                case 'hello':
                    $pageTitle = 'Welcome - Vermont Design System';
                    break;
                case 'animations':
                    $pageTitle = 'Animations - Vermont Design System';
                    break;
                case 'projectassets':
                    $pageTitle = 'Project Assets - Vermont Design System';
                    break;
                case 'typeface':
                    $pageTitle = 'Typeface - Vermont Design System';
                    break;
                case 'iconsearch':
                    $pageTitle = 'Icon Search - Vermont Design System';
                    break;
                case (preg_match('/^.*_icons$/', $currentRoute) ? true : false):
                    $iconType = str_replace('_icons', '', $currentRoute);
                    $pageTitle = ucfirst($iconType) . ' Icons - Vermont Design System';
                    break;
                default:
                    if ($currentRoute) {
                        $pageTitle = ucfirst($currentRoute) . ' - Vermont Design System';
                    }
            }
        @endphp
        <title>{{ $pageTitle }}</title>

        <!-- Fonts -->
        <!-- Scripts -->
        @vite([
            'resources/css/app.css', 
            'resources/css/main.css', 
            'resources/css/plugins.css', 
            'resources/css/modalboxes.css', 
            'resources/js/app.js',
            'resources/sass/template.scss'
        ])
    </head>
    <div id="preloader">
		<div class="loader_line"></div>
	</div>
    <body>
    <main id="app" class="designdev_tm_all_wrap w-full h-auto clear-both relative dark:bg-slate-950">
            <!-- Sidebar container with its own ID for separate mounting when needed -->
            <div id="mainContent" class="designdev_tm_mainpart w-full transition-all duration-300">
                {{ $slot }}
            </div>
            
            <footer class="bg-white dark:bg-gray-900 shadow">
<!--[if lt IE 10]> <script type="text/javascript" src="assets/js/ie8.js"></script> <![endif]-->	
            </footer>
        </main>
        @stack('scripts')
    </body>
</html>
