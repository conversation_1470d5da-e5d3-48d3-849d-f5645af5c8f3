<!--/MOMOBILE MENU -->
		<div class="designdev_tm_mobile_menu block md:hidden w-full h-auto fixed top-0 left-0 z-[10] transition-all duration-300">
			<div class="mobile_menu_inner w-full h-auto clear-both float-left bg-white dark:bg-slate-950 pt-[10px] px-[20px] pb-[10px]">
				<div class="mobile_in w-full h-auto clear-both flex items-center justify-between">
					<div class="logo w-14 h-14 p-3">
                    <a href="{{ route('hello') }}">
					<img src="/img/devMobileLight.svg" class="dark:hidden" alt="Vermont Dev Logo">
					<img src="/img/devMobileDark.svg" class="hidden dark:block" alt="Vermont Dev Logo">
                    </a>
				</div>
					<div class="trigger leading-[0]">
						<button type="button" class="hamburger hamburger--slider" aria-label="Menu">
							<div class="hamburger-box">
								<div class="hamburger-inner before:bg-gray-900 after:bg-gray-900 bg-gray-900 dark:bg-white dark:before:bg-white dark:after:bg-white transition-colors duration-200"></div>
							</div>
						</button>
					</div>
				</div>
			</div>
			<div class="dropdown w-full h-auto clear-both float-left bg-white dark:bg-slate-950 hidden">
				<div class="dropdown_inner w-full h-auto clear-both float-left p-[20px]">
					<nav>
						<x-mobile-menu-items />
					</nav>
				</div>
			</div>
		</div>
		<!-- /MOBILE MENU -->
	
		<!-- SIDEBAR MENU -->
		<div class="designdev_tm_leftpart">
			<div class="designdev_tm_sidebar_menu py-[20px] lx:py-[40px] left-0 top-0 bottom-0 fixed bg-white dark:bg-slate-950 hidden md:flex justify-center transition-all duration-300">
				<!-- Vue Sidebar Menu Component with optimized props -->
				<sidebar-menu
					:current-route="'{{ Route::currentRouteName() }}'"
					:csrf-token="'{{ csrf_token() }}'"
					:messages="{{ json_encode([
						'search_icons' => __('messages.search_icons'),
						'assets' => __('messages.assets'),
						'code' => __('messages.code'),
						'animations' => __('messages.animations'),
						'tools' => __('messages.tools'),
						'logout' => __('messages.logout'),
						'administration' => __('messages.administration'),
						'userLogin' => auth()->user()->name ?? auth()->user()->login ?? '',
						'changelog' => __('messages.changelog'),
						'todolist' => 'To do List'
					]) }}"
					:routes="{{ json_encode([
						'hello' => route('hello'),
						'iconsearch' => route('iconsearch'),
						'projectassets' => route('projectassets'),
						'code' => route('code'),
						'animations' => route('animations'),
						'tools' => route('tools'),
						'whiteboard' => route('whiteboard'),
						'logout' => route('logout'),
						'admin' => auth()->check() && in_array(auth()->user()->login ?? '', config('vermont.admin_user_logins', [])) ? route('admin.index') : null
					]) }}"
					:user="{{ auth()->check() ? json_encode(auth()->user()) : 'null' }}"
					:admin-user-logins="{{ json_encode(config('vermont.admin_user_logins', [])) }}"
					asset-prefix="{{ asset('') }}"
					previous-url="{{ request()->header('Referer') }}"
				></sidebar-menu>
			</div>
		</div>
		<!-- /SIDEBAR MENU -->
