<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <link rel="icon" href="/img/icons/favicon.svg">
        <link rel="apple-touch-icon" sizes="180x180" href="/img/icons/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/img/icons/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/img/icons/favicon-16x16.png">
        <link rel="mask-icon" href="/img/icons/safari-pinned-tab.svg" color="#5bbad5">
        <title>{{ config('app.name', 'Design Vermont Dev') }}</title>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/css/main.css', 'resources/css/plugins.css', 'resources/css/modalboxes.css', 'resources/css/gradient-svg.css', 'resources/js/app.js', 'resources/sass/template.scss'])
        

    </head>
    <body id="app" class="font-sans antialiased">
        <div class="container relative h-screen flex-col items-center justify-center md:grid md:grid-cols-2 lg:grid-cols-12 xl:grid-cols-12 lg:max-w-none lg:px-0 transition-all duration-300 ease-in-out">
            <div class="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r md:flex md:col-span-1 lg:col-span-8 xl:col-span-9 overflow-hidden transition-all duration-300 ease-in-out">
                <svg class="gradient-svg dark:fill-white absolute inset-0" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice">
                    <defs>
                        <radialGradient id="Gradient1" cx="50%" cy="50%" fx="10%" fy="50%" r=".5">
                            <animate attributeName="fx" dur="120s" values="0%;3%;0%" repeatCount="indefinite" />
                            <stop offset="0%" stop-color="#ff0" />
                            <stop offset="100%" stop-color="#ff00" />
                        </radialGradient>
                        <radialGradient id="Gradient2" cx="50%" cy="50%" fx="10%" fy="50%" r=".5">
                            <animate attributeName="fx" dur="90s" values="0%;3%;0%" repeatCount="indefinite" />
                            <stop offset="0%" stop-color="#0ff" />
                            <stop offset="100%" stop-color="#0ff0" />
                        </radialGradient>
                        <radialGradient id="Gradient3" cx="50%" cy="50%" fx="50%" fy="50%" r=".5">
                            <animate attributeName="fx" dur="80s" values="0%;3%;0%" repeatCount="indefinite" />
                            <stop offset="0%" stop-color="#f0f" />
                            <stop offset="100%" stop-color="#f0f0" />
                        </radialGradient>
                    </defs>
                    <rect x="0" y="0" width="100%" height="100%" fill="url(#Gradient1)">
                        <animate attributeName="x" dur="60s" values="25%;0%;25%" repeatCount="indefinite" />
                        <animate attributeName="y" dur="63s" values="0%;25%;0%" repeatCount="indefinite" />
                        <animateTransform attributeName="transform" type="rotate" from="0 50 50" to="360 50 50" dur="51s" repeatCount="indefinite" />
                    </rect>
                    <rect x="0" y="0" width="100%" height="100%" fill="url(#Gradient2)">
                        <animate attributeName="x" dur="69s" values="-25%;0%;-25%" repeatCount="indefinite" />
                        <animate attributeName="y" dur="72s" values="0%;50%;0%" repeatCount="indefinite" />
                        <animateTransform attributeName="transform" type="rotate" from="0 50 50" to="360 50 50" dur="54s" repeatCount="indefinite" />
                    </rect>
                    <rect x="0" y="0" width="100%" height="100%" fill="url(#Gradient3)">
                        <animate attributeName="x" dur="75s" values="0%;25%;0%" repeatCount="indefinite" />
                        <animate attributeName="y" dur="78s" values="0%;25%;0%" repeatCount="indefinite" />
                        <animateTransform attributeName="transform" type="rotate" from="360 50 50" to="0 50 50" dur="57s" repeatCount="indefinite" />
                    </rect>
                </svg>
                <div class="relative z-20 flex items-center text-lg font-medium">
                    <img src="/img/Logotype.svg" alt="{{ config('app.name', 'Design Vermont Dev') }}" class="h-12 block dark:hidden">
                    <img src="/img/LogotypeDark.svg" alt="{{ config('app.name', 'Design Vermont Dev') }}" class="h-12 hidden dark:block">
                </div>
                <div class="relative z-20 flex items-center justify-center flex-1">
                    @php
                        $quotes = [
                            ['text' => "Absolute\nabsolute\nrelative\nabsolute", 'author' => 'Chrome DevTools'],
                            ['text' => "Text-500...\nOpacity-50...", 'author' => 'Marek Nový'],
                            ['text' => "Bazinga", 'author' => 'Sheldon Cooper'],
                        ];
                        $randomQuote = $quotes[array_rand($quotes)];
                    @endphp
                    <blockquote class="space-y-4 md:space-y-6 mix-blend-exclusion text-center max-w-[95%] xl:max-w-[85%] mx-auto">
                        <p class="text-[clamp(2rem,5vw,5rem)] lg:text-[clamp(3rem,7vw,7rem)] gradient-overlay-text gant-modern-bold bg-blend-difference leading-[1.2] md:leading-[1.1] whitespace-pre-line">
                            "{{ $randomQuote['text'] }}"
                        </p>
                        <footer class="text-[clamp(1rem,2vw,2rem)] gradient-overlay-text gant-modern-regular bg-blend-difference">
                            — {{ $randomQuote['author'] }}
                        </footer>
                    </blockquote>
                </div>
            </div>
            <div class="h-full flex items-center justify-center bg-white dark:bg-slate-950 md:col-span-1 lg:col-span-4 xl:col-span-3 transition-all duration-300 ease-in-out">
                <div class="mx-auto flex w-full flex-col justify-center space-y-6 px-8 sm:w-[350px] lg:w-[400px]">
                    <div class="flex flex-col items-center gap-6">
                        <img src="/img/devLight.svg" class="h-20 sm:h-24 lg:h-36 xl:h-40 dark:hidden " alt="Vermont Dev Logo">
                        <img src="/img/devDark.svg" class="h-20 sm:h-24 lg:h-36 xl:h-40 hidden dark:block" alt="Vermont Dev Logo">
                        {{ $slot }}
                    </div>
                    <div class="flex justify-center">
                        <dark-mode-toggle></dark-mode-toggle>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
