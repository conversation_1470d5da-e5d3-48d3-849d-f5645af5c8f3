@extends('layouts.application')

@section('title', 'Changelog')

@section('content')
<!-- Pattern SVG Background -->
<div class="fixed inset-0 pointer-events-none">
    <svg class="absolute inset-0 w-full h-full text-gray-900/[0.07] dark:text-gray-100/[0.08]" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-3)" />
    </svg>
</div>

<div class="min-h-screen bg-gray-50/50 dark:bg-gray-950/50 relative flex flex-col">
    <!-- Empty top half with ParticleOrb -->
    <div class="h-[50vh] relative">
        <!-- ParticleOrb centered relative to the headline -->
        <div class="absolute inset-x-0 bottom-0 h-[50vh] flex items-center justify-center pointer-events-none" style="transform: translateY(50%);">
            <div class="relative w-[75vh] h-[75vh]">
                <particle-orb />
            </div>
        </div>
    </div>
    
    <!-- Content Section (Bottom Half) -->
    <div class="min-h-[50vh] w-full">
        <!-- Scrollable Content -->
        <div class="relative w-full h-full overflow-y-auto">
            <div class="pt-8 pb-16">
                <!-- Header -->
                <div class="text-center mb-16 relative">
                    <h1 class="leading-none tracking-tighter text-black dark:text-gray-200 text-5xl lg:text-6xl xl:text-8xl gant-modern-bold">
                        Changelog
                    </h1>
                    <p class="mt-6 text-gray-600 dark:text-gray-400">
                        Track our latest updates and improvements
                    </p>
                </div>

                <!-- Timeline Section -->
                <div class="container mx-auto px-4">
                    <div class="max-w-3xl mx-auto">
                        <!-- Timeline -->
                        <div class="relative flex justify-center">
                            <!-- Vertical Line -->
                            <div class="absolute inset-y-0 w-0.5 bg-gradient-to-b from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 rounded-full"></div>

                            <!-- Timeline Content -->
                            <div class="relative w-full max-w-xl">
                                <div class="space-y-16">
                                    @foreach($entries as $entry)
                                        <!-- Entry -->
                                        <div class="relative pt-8">
                                            <!-- Timeline Dot with Ring Effect -->
                                            <div class="absolute -top-[11px] left-1/2 -translate-x-1/2">
                                                <div class="w-[22px] h-[22px] rounded-full bg-white dark:bg-gray-900 flex items-center justify-center ring-[6px] ring-gray-100 dark:ring-gray-800 shadow-md">
                                                    <div class="w-2.5 h-2.5 rounded-full bg-gradient-to-br from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-400"></div>
                                                </div>
                                            </div>

                                            <!-- Content -->
                                            <div class="bg-white dark:bg-gray-950 rounded-xl border border-gray-200/80 dark:border-gray-700/80 shadow-sm hover:shadow-md transition-all duration-200">
                                                <!-- Header -->
                                                <div class="p-6">
                                                    <div class="flex items-center gap-3 mb-3">
                                                        <span @class([
                                                            'px-3 py-1 rounded-full text-xs gant-modern-medium shadow-sm',
                                                            'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 ring-1 ring-red-200 dark:ring-red-800/30' => $entry->is_major,
                                                            'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 ring-1 ring-blue-200 dark:ring-blue-800/30' => !$entry->is_major,
                                                        ])>
                                                            v{{ $entry->version }}
                                                        </span>
                                                        <time class="text-sm text-gray-500 dark:text-gray-400">
                                                            {{ $entry->release_date->format('d.m.Y') }}
                                                        </time>
                                                    </div>

                                                    <h2 class="text-lg gant-modern-bold text-gray-900 dark:text-white mb-2">
                                                        {{ $entry->title }}
                                                    </h2>

                                                    @if($entry->description)
                                                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                                                            {{ $entry->description }}
                                                        </p>
                                                    @endif

                                                    <!-- Changes List -->
                                                    @if($entry->changes && count($entry->changes))
                                                        <ul class="mt-4 space-y-2.5">
                                                            @foreach($entry->changes as $change)
                                                                <li class="flex items-start gap-2.5 text-sm">
                                                                    <i class="design design-arr text-gray-600 dark:text-gray-500"></i>
                                                                    <span class="text-gray-900 dark:text-gray-300">
                                                                        {{ $change }}
                                                                    </span>
                                                                </li>
                                                            @endforeach
                                                        </ul>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 