@extends('layouts.application')

@section('content')
<div class="px-6 lg:px-8 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ route('profile') }}" class="inline-flex items-center px-3 py-2 text-sm gant-modern-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-950 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-900 transition-colors">
            <i class="design design-arrow-left mr-2"></i>
            Späť na profil
        </a>
    </div>

    <!-- Project Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div class="flex items-center gap-6">
            <div class="h-16 w-16 flex items-center justify-center rounded-full text-2xl 
                {{ $project->status === 'Development' ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400' : 
                   ($project->status === 'Stage' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 
                   ($project->status === 'Production' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' : 
                   ($project->status === 'Refactor' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' : 
                   ($project->status === 'Nechytat' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400' : 
                   ($project->status === 'Netrap sa' ? 'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400' : 
                   ($project->status === 'Urgent' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' : 
                   'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400')))))) }}
            ">
                <i class="{{ $project->status === 'Development' ? 'design design-code' : 
                           ($project->status === 'Stage' ? 'design design-lab' : 
                           ($project->status === 'Production' ? 'design design-monitor-check' : 
                           ($project->status === 'Refactor' ? 'design design-swap' : 
                           ($project->status === 'Nechytat' ? 'design design-hand-block' : 
                           ($project->status === 'Netrap sa' ? 'design design-emotion-sad' : 
                           ($project->status === 'Urgent' ? 'wms wms-alert' : 
                           'design design-flag')))))) }}"></i>
            </div>
            <div>
                <h1 class="text-2xl md:text-3xl gant-modern-bold text-gray-900 dark:text-white">{{ $project->name }}</h1>
                <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-lg">
                    {{ Str::limit($project->description, 60) }}
                </p>
                <div class="flex items-center gap-2 mt-2">
                    <span class="px-2 py-1 text-xs rounded-full 
                        {{ $project->status === 'Development' ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400' : 
                           ($project->status === 'Stage' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400' : 
                           ($project->status === 'Production' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : 
                           ($project->status === 'Refactor' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400' : 
                           ($project->status === 'Nechytat' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' : 
                           ($project->status === 'Netrap sa' ? 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-400' : 
                           ($project->status === 'Urgent' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 
                           'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-400')))))) }}
                    ">
                        <i class="{{ $project->status === 'Development' ? 'design design-code mr-1 opacity-70' : 
                                   ($project->status === 'Stage' ? 'design design-lab mr-1 opacity-70' : 
                                   ($project->status === 'Production' ? 'design design-monitor-check mr-1 opacity-70' : 
                                   ($project->status === 'Refactor' ? 'design design-swap mr-1 opacity-70' : 
                                   ($project->status === 'Nechytat' ? 'design design-hand-block mr-1 opacity-70' : 
                                   ($project->status === 'Netrap sa' ? 'design design-emotion-sad mr-1 opacity-70' : 
                                   ($project->status === 'Urgent' ? 'wms wms-alert mr-1 opacity-70' : 
                                   'design design-flag mr-1 opacity-70')))))) }}"></i>
                        {{ $project->status }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Details Content -->
    <div class="bg-white dark:bg-slate-950 shadow-sm rounded-xl border border-gray-200 dark:border-gray-800">
        <div class="p-6 space-y-6">
            <!-- Row 1: Project Info -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                <!-- Column 1: Backend -->
                <div class="group">
                    <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Backend</div>
                    <div class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $project->backend ?: '-' }}</div>
                </div>
                
                <!-- Column 2: Frontend -->
                <div class="group">
                    <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Frontend</div>
                    <div class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $project->frontend ?: '-' }}</div>
                </div>
            </div>

            <!-- Row 2: Dates -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                <!-- Column 1: Deployed Date -->
                <div class="group">
                    <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Deployed</div>
                    <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                        @if($project->start_date)
                            <div class="flex items-center gant-modern-bold">
                                <i class="claims claims-calendar-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                {{ $project->start_date->format('d.m.Y') }}
                            </div>
                        @else
                            <span class="text-gray-400 dark:text-gray-600 gant-modern-bold">-</span>
                        @endif
                    </div>
                </div>
                
                <!-- Column 2: Upgrade Date -->
                <div class="group">
                    <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Upgrade</div>
                    <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                        @if($project->end_date)
                            <div class="flex items-center gant-modern-bold">
                                <i class="claims claims-calendar-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                {{ $project->end_date->format('d.m.Y') }}
                            </div>
                        @else
                            <span class="text-gray-400 dark:text-gray-600 gant-modern-bold">-</span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Project Description -->
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700/50">
                <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Popis projektu</div>
                <div class="bg-gray-50 dark:bg-slate-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="text-gray-800 dark:text-gray-200 gant-modern-regular">
                        {{ $project->description ?: 'Bez popisu' }}
                    </div>
                </div>
            </div>

            <!-- Project Users -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400">Používatelia projektu</h3>
                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">{{ $project->users->count() }}</span>
                </div>
                
                @if($project->users->count() > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        @foreach($project->users as $user)
                            <div class="flex items-center gap-3 p-3 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg">
                                <x-avatar :user="$user" size="sm" :ring="true" />
                                <div class="flex flex-col">
                                    <span class="text-base gant-modern-medium text-gray-900 dark:text-white">{{ $user->name }} {{ $user->surname }}</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ $user->email }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400 italic py-2">Žiadni používatelia nie sú priradení k tomuto projektu</p>
                @endif
            </div>

            <!-- Project Fonts -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400">Fonty</h3>
                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">{{ $project->fonts->count() }}</span>
                </div>
                
                @if($project->fonts->count() > 0)
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($project->fonts as $font)
                            <div class="flex flex-col items-center justify-center p-4 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg">
                                @php
                                    $iconClass = '';
                                    
                                    // Special cases
                                    if ($font->name === 'Order Group') {
                                        $iconClass = 'ordergroup ordergroup-thumbnail';
                                    } elseif ($font->name === 'Category') {
                                        $iconClass = 'vermont-category vermont-category-thumbnail';
                                    } elseif ($font->name === 'CDB' || $font->name === 'Central Database') {
                                        $iconClass = 'cdb cdb-thumbnail';
                                    } elseif ($font->name === 'Eshop') {
                                        $iconClass = 'vermont-icon vermont-icon-thumbnail';
                                    } else {
                                        $iconClass = strtolower($font->name) . ' ' . strtolower($font->name) . '-thumbnail';
                                    }
                                @endphp
                                
                                <i class="{{ $iconClass }} text-3xl text-gray-700 dark:text-gray-400 mb-2"></i>
                                <span class="text-sm text-center gant-modern-medium text-gray-900 dark:text-white">{{ $font->name }}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400 italic py-2">Žiadne fonty nie sú priradené k tomuto projektu</p>
                @endif
            </div>

            <!-- URL if available -->
            @if($project->url)
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">URL projektu</div>
                <div class="bg-gray-50 dark:bg-slate-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <a href="{{ $project->url }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline flex items-center">
                        {{ $project->url }}
                        <i class="design design-external-link ml-2 text-sm"></i>
                    </a>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection 