@extends('layouts.application')

@section('content')
<div class="px-6 lg:px-8 py-8">
    <!-- Handle error message if present -->
    @if(isset($error))
        <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
            <h3 class="text-lg font-bold">Error Loading Changelog</h3>
            <p>{{ $error }}</p>
            <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
        </div>
    @endif
    
    <!-- Set default values for variables that might be missing -->
    @php
        $needsUpdateCount = $needsUpdateCount ?? 0;
        $newUsersCount = $newUsersCount ?? 0;
        $entries = $entries ?? collect([]);
    @endphp
    
    <!-- Admin Navigation Tabs -->
    <admin-navigation 
        current-route="admin.changelog"
        :needs-update-count="{{ $needsUpdateCount }}"
        :new-users-count="{{ $newUsersCount }}"
        class="mb-8"
    ></admin-navigation>

    <!-- Alert Messages -->
    @if(session('success') || session('error'))
        <div class="mb-8">
            @if(session('success'))
                <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                    <i class="design design-circle-checked mr-2 text-lg"></i>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                    <i class="design design-circle-remove mr-2 text-lg"></i>
                    <span>{{ session('error') }}</span>
                </div>
            @endif
        </div>
    @endif

    <!-- Container with Padding -->
    <div class="">
        <div class="max-w-7xl w-full mx-auto">
            <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl">
                <!-- Pattern SVG -->
                <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                            <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#pattern-3)" />
                </svg>

                <!-- Header Section -->
                <div class="relative z-10 pt-[50vh]">
                    <div class="bg-white dark:bg-gray-800/20 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                            <!-- Icon and Title/Description -->
                            <div class="flex items-center gap-6">
                                <div class="flex-shrink-0 w-20 h-20 bg-gray-800 dark:bg-gray-800/30 rounded-full flex items-center justify-center ring-2 ring-gray-200 dark:ring-gray-700">
                                    <i class="wms wms-browse-history-outline text-white dark:text-gray-400 text-4xl"></i>
                                </div>
                                <div class="flex flex-col items-center">
                                    <h2 class="text-2xl gant-modern-bold text-gray-900 dark:text-white text-center ">Changelog Management</h2>
                                    <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-lg">
                                        Manage and track application updates and changes
                                    </p>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div>
                                <button 
                                    type="button" 
                                    onclick="openCreateModal()"
                                    class="inline-flex items-center px-4 py-2 text-sm gant-modern-medium rounded-xl bg-gray-800 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
                                >
                                    <i class="design design-plus mr-2"></i>
                                    Add New Entry
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Changelog Entries List (Separate Container) -->
    <div class="max-w-full">
        <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl">
            <!-- Pattern SVG -->
            <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                            <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern-3)" />
            </svg>
            <div class="relative z-10">
                <div class="max-w-7xl mx-auto bg-white dark:bg-gray-800/20 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
                    <div class="p-6">
                        <div class="space-y-6">
                            @forelse($entries as $entry)
                                <div class="bg-white dark:bg-gray-800/30 rounded-xl border border-gray-200 dark:border-gray-700 p-6 transition-all duration-200 hover:shadow-md">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-3">
                                                @if(isset($entry['is_major']) && $entry['is_major'])
                                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-purple-800 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/10 rounded-full border border-purple-200 dark:border-purple-800/30">
                                                        Major Update
                                                    </span>
                                                @endif
                                                <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                                    v{{ $entry['version'] }}
                                                </span>
                                                <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                                    <i class="claims claims-clock-outline mr-1.5"></i>
                                                    {{ \Carbon\Carbon::parse($entry['release_date'])->format('d.m.Y') }}
                                                </span>
                                            </div>
                                            <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white mb-2">
                                                {{ $entry['title'] }}
                                            </h3>
                                            @if(isset($entry['description']) && $entry['description'])
                                                <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                                    {{ $entry['description'] }}
                                                </p>
                                            @endif
                                            @if(isset($entry['changes']) && count($entry['changes']) > 0)
                                                <ul class="space-y-2">
                                                    @foreach($entry['changes'] as $change)
                                                        <li class="flex items-start gap-2 text-gray-600 dark:text-gray-400">
                                                            <i class="design design-circle-checked mt-1 text-green-500 dark:text-green-400"></i>
                                                            <span>{{ $change }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            @endif
                                            @if(isset($entry['image_path']) && $entry['image_path'])
                                                <div class="mt-4">
                                                    <img src="{{ Storage::url($entry['image_path']) }}" alt="Update preview" class="rounded-lg max-h-64 object-cover">
                                                </div>
                                            @endif
                                        </div>
                                        <div class="flex gap-2">
                                            <button 
                                                onclick="editEntry('{{ $entry['id'] }}')"
                                                class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-200"
                                            >
                                                <i class="design design-edit text-lg"></i>
                                            </button>
                                            <button 
                                                onclick="deleteEntry('{{ $entry['id'] }}')"
                                                class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200"
                                            >
                                                <i class="design design-trash text-lg"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-12">
                                    <div class="flex justify-center mb-4">
                                        <i class="wms wms-browse-history-outline text-6xl text-gray-400 dark:text-gray-600"></i>
                                    </div>
                                    <h3 class="text-lg gant-modern-medium text-gray-900 dark:text-white mb-2">No Changelog Entries</h3>
                                    <p class="text-gray-500 dark:text-gray-400">Start by adding your first changelog entry.</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="changelogModal" class="fixed inset-0 z-50 hidden">
    <div class="absolute inset-0 bg-gray-900/50 dark:bg-gray-900/80 backdrop-blur-sm"></div>
    <div class="relative min-h-screen flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white" id="modalTitle">
                    Add New Changelog Entry
                </h3>
            </div>
            <form id="changelogForm" class="p-6 space-y-6">
                @csrf
                <input type="hidden" name="id" id="entryId">
                <input type="hidden" name="order" id="order" value="0">
                
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Version</label>
                        <input type="text" name="version" id="version" class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
                    </div>
                    <div>
                        <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Release Date</label>
                        <input type="date" name="release_date" id="release_date" class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
                    </div>
                </div>

                <div>
                    <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Title</label>
                    <input type="text" name="title" id="title" class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
                </div>

                <div>
                    <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea name="description" id="description" rows="3" class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"></textarea>
                </div>

                <div>
                    <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Changes</label>
                    <div id="changesContainer" class="space-y-3">
                        <div class="flex gap-2">
                            <input type="text" name="changes[]" class="flex-1 rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
                            <button type="button" onclick="addChangeField()" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                <i class="design design-plus text-lg"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Image</label>
                    <input type="file" name="image" id="image" accept="image/*" class="w-full">
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="is_major" id="is_major" class="rounded border-gray-300 dark:border-gray-700 text-gray-600 focus:ring-gray-500">
                    <label for="is_major" class="ml-2 text-sm gant-modern-medium text-gray-700 dark:text-gray-300">
                        Mark as major update
                    </label>
                </div>
            </form>
            <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
                <button 
                    type="button"
                    onclick="closeModal()"
                    class="px-4 py-2 text-sm gant-modern-medium rounded-xl border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                    Cancel
                </button>
                <button 
                    type="button"
                    onclick="saveEntry()"
                    class="px-4 py-2 text-sm gant-modern-medium rounded-xl bg-gray-800 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600"
                >
                    Save Entry
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add New Changelog Entry';
    document.getElementById('changelogForm').reset();
    document.getElementById('entryId').value = '';
    document.getElementById('changelogModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('changelogModal').classList.add('hidden');
}

function addChangeField() {
    const container = document.getElementById('changesContainer');
    const div = document.createElement('div');
    div.className = 'flex gap-2';
    div.innerHTML = `
        <input type="text" name="changes[]" class="flex-1 rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
        <button type="button" onclick="removeChangeField(this)" class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
            <i class="design design-minus text-lg"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeChangeField(button) {
    button.parentElement.remove();
}

async function editEntry(id) {
    try {
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const response = await fetch(`/admin/changelog/${id}`, {
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        
        document.getElementById('modalTitle').textContent = 'Edit Changelog Entry';
        document.getElementById('entryId').value = data.id;
        document.getElementById('version').value = data.version;
        document.getElementById('title').value = data.title;
        document.getElementById('description').value = data.description || '';
        document.getElementById('release_date').value = data.release_date;
        document.getElementById('is_major').checked = data.is_major;

        // Clear existing changes fields
        const container = document.getElementById('changesContainer');
        container.innerHTML = '';

        // Add fields for existing changes
        if (data.changes && data.changes.length > 0) {
            data.changes.forEach(change => {
                const div = document.createElement('div');
                div.className = 'flex gap-2';
                div.innerHTML = `
                    <input type="text" name="changes[]" value="${change}" class="flex-1 rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400">
                    <button type="button" onclick="removeChangeField(this)" class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300">
                        <i class="design design-minus text-lg"></i>
                    </button>
                `;
                container.appendChild(div);
            });
        }

        document.getElementById('changelogModal').classList.remove('hidden');
    } catch (error) {
        console.error('Error fetching entry:', error);
        alert('Failed to load changelog entry. Please try again.');
    }
}

async function deleteEntry(id) {
    if (confirm('Are you sure you want to delete this entry?')) {
        try {
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const response = await fetch(`/admin/changelog/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Server returned non-JSON response');
            }

            if (response.ok) {
                const data = await response.json();
                window.location.reload();
            } else {
                const data = await response.json();
                if (data.message) {
                    alert(data.message);
                } else {
                    throw new Error('Failed to delete entry');
                }
            }
        } catch (error) {
            console.error('Error deleting entry:', error);
            alert('Failed to delete changelog entry. Please try again.');
        }
    }
}

async function saveEntry() {
    const form = document.getElementById('changelogForm');
    const formData = new FormData(form);
    const entryId = formData.get('id');
    
    // Always use POST method with FormData
    const url = entryId ? `/admin/changelog/${entryId}` : '/admin/changelog';
    if (entryId) {
        formData.append('_method', 'PUT');
    }

    // Get CSRF token from meta tag
    const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    try {
        const response = await fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            const data = await response.json();
            if (data.errors) {
                const errorMessages = Object.values(data.errors).flat().join('\n');
                alert(errorMessages);
            } else if (data.message) {
                alert(data.message);
            } else {
                throw new Error('Failed to save entry');
            }
            return;
        }

        const data = await response.json();
        window.location.reload();
    } catch (error) {
        console.error('Error saving entry:', error);
        alert('Failed to save changelog entry. Please try again.');
    }
}
</script>
@endpush
@endsection 