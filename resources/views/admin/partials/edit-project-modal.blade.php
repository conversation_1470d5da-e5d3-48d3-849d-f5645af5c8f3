<!-- Edit Project Modal -->
<div id="editProjectModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Upraviť projekt</h3>
                <button type="button" onclick="document.getElementById('editProjectModal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                    <i class="design design-close text-xl"></i>
                </button>
            </div>
            
            <form id="editProjectForm" action="" method="POST">
                @csrf
                @method('PUT')
                <div class="space-y-4">
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Názov projektu</label>
                        <input type="text" name="name" id="edit_name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                    </div>
                    
                    <div>
                        <label for="edit_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Popis</label>
                        <textarea name="description" id="edit_description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white"></textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_backend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Backend</label>
                            <input type="text" name="backend" id="edit_backend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                        </div>
                        
                        <div>
                            <label for="edit_frontend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Frontend</label>
                            <input type="text" name="frontend" id="edit_frontend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Deployed</label>
                            <input type="date" name="start_date" id="edit_start_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                        </div>
                        
                        <div>
                            <label for="edit_end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upgrade</label>
                            <input type="date" name="end_date" id="edit_end_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                        </div>
                    </div>
                    
                    <div>
                        <label for="edit_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stav</label>
                        <select name="status" id="edit_status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                            @foreach($statuses as $status)
                                <option value="{{ $status->slug }}">{{ $status->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label for="edit_font_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradené fonty</label>
                        <select name="font_ids[]" id="edit_font_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                            @foreach($fonts as $font)
                                <option value="{{ $font->id }}">{{ $font->name }}</option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých fontov</p>
                    </div>
                    
                    <div>
                        <label for="edit_user_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradení používatelia</label>
                        <select name="user_ids[]" id="edit_user_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}">{{ $user->name }} {{ $user->surname }} ({{ $user->login }})</option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých používateľov</p>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <button type="button" onclick="document.getElementById('editProjectModal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-500 focus:outline-none">
                        Zrušiť
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Uložiť zmeny
                    </button>
                </div>
            </form>
        </div>
    </div>
</div> 