{{-- 
    Management Tab
    Purpose: Provides administrative controls for user management (role changes, user deletion)
    Dependencies: 
    - Requires $user variable to be passed from parent
    - Uses JavaScript functions from management-tab.js
    - Requires admin.users.update-role route for role changes
    - Requires admin.users.delete route for user deletion
    - Uses role and delete confirmation modals
--}}

<!-- Management Tab Content -->
<div id="management-content" class="tab-content hidden">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Change Role -->
        <div class="p-6 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800/20">
            <h4 class="text-base gant-modern-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <i class="design design-user-switch mr-2 text-gray-500 dark:text-gray-400"></i>
                Zmena role
            </h4>
            <form id="roleForm" action="{{ route('admin.users.update-role', $user) }}" method="POST" class="space-y-4">
                @csrf
                @method('PATCH')
                <div>
                    <label for="roleSelect" class="block text-sm gant-modern-medium text-gray-600 dark:text-gray-400 mb-1">Vyberte rolu</label>
                    <div class="flex items-center space-x-2">
                        <select name="role" id="roleSelect" class="w-full text-sm gant-modern-regular rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 focus:border-gray-500 dark:focus:border-gray-600 focus:ring-gray-500 dark:focus:ring-gray-600 shadow-sm">
                            <option value="user" {{ !in_array($user->login, array_merge(
                                config('vermont.admin_user_logins'), 
                                config('vermont.developer_user_logins'),
                                config('vermont.dev_user_logins'),
                                config('vermont.tibi_user_logins'),
                                config('vermont.pm_user_logins')
                            )) ? 'selected' : '' }}>Používateľ</option>
                            
                            <option value="admin" {{ in_array($user->login, config('vermont.admin_user_logins')) ? 'selected' : '' }}>Admin</option>
                            
                            <option value="developer" {{ in_array($user->login, config('vermont.developer_user_logins')) ? 'selected' : '' }}>Developer</option>
                            
                            <option value="dev" {{ in_array($user->login, config('vermont.dev_user_logins')) ? 'selected' : '' }}>Dev</option>
                            
                            <option value="tibi" {{ in_array($user->login, config('vermont.tibi_user_logins')) ? 'selected' : '' }}>Tibi</option>
                            
                            <option value="pm" {{ in_array($user->login, config('vermont.pm_user_logins')) ? 'selected' : '' }}>PM</option>
                        </select>
                        <button type="button" id="confirm-role-btn" class="px-4 py-2 text-sm gant-modern-medium rounded-md bg-gray-700 text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center justify-center sm:w-auto w-full">
                            <i class="claims claims-save mr-1.5"></i> Uložiť
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Delete User -->
        <div class="p-6 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800/20">
            <h4 class="text-base gant-modern-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <i class="design design-trash mr-2 text-gray-500 dark:text-gray-400"></i>
                Vymazanie používateľa
            </h4>
            <p class="text-sm gant-modern-regular text-gray-500 dark:text-gray-400 mb-4">
                Táto akcia natrvalo odstráni používateľa z databázy a nemôže byť vrátená späť.
            </p>

            <form id="deleteForm" action="{{ route('admin.users.delete', $user) }}" method="POST">
                @csrf
                @method('DELETE')
                <button 
                    type="button" 
                    id="confirm-delete-btn"
                    onclick="document.getElementById('deleteModal').classList.remove('hidden')"
                    class="inline-flex items-center justify-center px-4 py-2 text-sm gant-modern-medium rounded-md bg-red-600 hover:bg-red-700 text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 w-full sm:w-auto"
                >
                    <i class="design design-trash mr-1.5"></i> Vymazať používateľa
                </button>
            </form>
        </div>
    </div>
</div>
