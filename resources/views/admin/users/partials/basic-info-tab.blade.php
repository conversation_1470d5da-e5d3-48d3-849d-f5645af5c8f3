{{-- 
    Basic Info Tab
    Purpose: Displays the user's basic information including login, email, name, etc.
    Dependencies: 
    - Requires $user variable to be passed from parent
    - Uses toggleEmailEdit() JavaScript function from basic-info-tab.js
    - Requires admin.users.update-email route for email updates
--}}

<!-- Basic Info Tab Content -->
<div id="basic-info-content" class="tab-content">
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
        <dl class="space-y-6">
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Login</dt>
                <dd class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $user->login }}</dd>
            </div>
            
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Email</dt>
                <dd class="text-gray-900 dark:text-white gant-modern-bold text-2xl flex items-center gap-3">
                    <div id="email-display" class="{{ $user->email ? '' : 'hidden' }}">
                        <span id="email-value">{{ $user->email ?? '' }}</span>
                        <button 
                            type="button" 
                            onclick="toggleEmailEdit(true)"
                            class="ml-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                        >
                            <i class="claims claims-edit-text-outline text-base"></i>
                        </button>
                    </div>
                    <form id="email-form" action="{{ route('admin.users.update-email', $user) }}" method="POST" class="{{ $user->email ? 'hidden' : '' }} w-full flex items-center">
                        @csrf
                        @method('PATCH')
                        <div class="flex-grow relative">
                            <input 
                                type="email" 
                                id="email-input" 
                                name="email" 
                                class="!bg-white dark:!bg-gray-800 flex h-10 rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-300 dark:focus:ring-gray-600 dark:focus:ring-offset-gray-800 gant-modern-regular w-full" 
                                placeholder="Zadajte email"
                                value="{{ $user->email ?? '' }}"
                            >
                        </div>
                        <div class="flex items-center ml-2 gap-2">
                            <button 
                                type="submit" 
                                class="py-1.5 px-3 text-white bg-gray-800 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 flex items-center"
                            >
                                <i class="claims claims-save text-base mr-2"></i>
                                <span class="gant-modern-regular text-sm">Uložiť</span>
                            </button>
                            <button 
                                type="button"
                                onclick="toggleEmailEdit(false)" 
                                class="py-1.5 px-3 text-gray-800 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 flex items-center"
                            >
                                <i class="claims claims-close text-base mr-2"></i>
                                <span class="gant-modern-regular text-sm">Zrušiť</span>
                            </button>
                        </div>
                    </form>
                </dd>
            </div>
            
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Dátum registrácie</dt>
                <dd class="text-gray-900 dark:text-white text-2xl gant-modern-bold">
                    @if($user->created_at)
                        <div class="flex items-center gant-modern-bold">
                            <i class="claims claims-calendar-outline mr-2 text-gray-500 dark:text-gray-400"></i>
                            {{ $user->created_at->format('d.m.Y') }}
                            <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                            <i class="claims claims-clock-outline mr-2 text-gray-500 dark:text-gray-400"></i>
                            {{ $user->created_at->format('H:i') }}
                        </div>
                    @else
                        <span class="text-gray-400 dark:text-gray-600 gant-modern-regular">-</span>
                    @endif
                </dd>
            </div>
        </dl>
        
        <dl class="space-y-6">
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Meno</dt>
                <dd class="text-gray-900 dark:text-white  text-2xl gant-modern-bold">{{ $user->name }}</dd>
            </div>
            
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Priezvisko</dt>
                <dd class="text-gray-900 dark:text-white r text-2xl gant-modern-bold">{{ $user->surname ?? '-' }}</dd>
            </div>
            
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Dátum narodenia</dt>
                <dd class="text-gray-900 dark:text-white text-2xl gant-modern-bold">
                    <div id="birthday-display" class="{{ $user->birthday ? '' : 'hidden' }}">
                        <div class="flex items-center gant-modern-bold">
                            <i class="claims claims-calendar-outline mr-2 text-gray-500 dark:text-gray-400"></i>
                            <span id="birthday-value">{{ $user->birthday ? $user->birthday->format('d.m.Y') : '' }}</span>
                            <button 
                                type="button" 
                                onclick="toggleBirthdayEdit(true)"
                                class="ml-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                            >
                                <i class="claims claims-edit-text-outline text-base"></i>
                            </button>
                        </div>
                    </div>
                    <div id="birthday-empty" class="{{ $user->birthday ? 'hidden' : '' }}">
                        <span class="text-gray-400 dark:text-gray-600 gant-modern-regular flex items-center">
                            - 
                            <button 
                                type="button" 
                                onclick="toggleBirthdayEdit(true)"
                                class="ml-2 text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
                            >
                                <i class="claims claims-edit-text-outline text-base"></i>
                            </button>
                        </span>
                    </div>
                    <form id="birthday-form" action="{{ route('admin.users.update-birthday', $user) }}" method="POST" class="hidden w-full flex items-center">
                        @csrf
                        @method('PATCH')
                        <div class="flex-grow relative">
                            <input 
                                type="date" 
                                id="birthday-input" 
                                name="birthday" 
                                class="!bg-white dark:!bg-gray-800 flex h-10 rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-300 dark:focus:ring-gray-600 dark:focus:ring-offset-gray-800 gant-modern-regular w-full" 
                                placeholder="Zadajte dátum narodenia"
                                value="{{ $user->birthday ? $user->birthday->format('Y-m-d') : '' }}"
                            >
                        </div>
                        <div class="flex items-center ml-2 gap-2">
                            <button 
                                type="submit" 
                                class="py-1.5 px-3 text-white bg-gray-800 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 flex items-center"
                            >
                                <i class="claims claims-save text-base mr-2"></i>
                                <span class="gant-modern-regular text-sm">Uložiť</span>
                            </button>
                            <button 
                                type="button"
                                onclick="toggleBirthdayEdit(false)" 
                                class="py-1.5 px-3 text-gray-800 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 flex items-center"
                            >
                                <i class="claims claims-close text-base mr-2"></i>
                                <span class="gant-modern-regular text-sm">Zrušiť</span>
                            </button>
                        </div>
                    </form>
                </dd>
            </div>
            
            <div class="group">
                <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Posledné prihlásenie</dt>
                <dd class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                    @if($user->last_login_at)
                        <div class="flex items-center gant-modern-bold">
                            <i class="claims claims-calendar-outline mr-2 text-gray-500 dark:text-gray-400"></i>
                            {{ \Carbon\Carbon::parse($user->last_login_at)->format('d.m.Y') }}
                            <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                            <i class="claims claims-clock-outline mr-2 text-gray-500 dark:text-gray-400"></i>
                            {{ \Carbon\Carbon::parse($user->last_login_at)->format('H:i') }}
                        </div>
                    @else
                        <span class="text-gray-400 dark:text-gray-600 gant-modern-regular">-</span>
                    @endif
                </dd>
            </div>
        </dl>
    </div>
</div>

@push('scripts')
<!-- Include the JavaScript file that contains the functions -->
<script src="{{ asset('js/admin/users/basic-info-tab.js') }}"></script>
@endpush 