{{-- 
    Greetings Tab
    Purpose: Allows management of custom user greetings that display on login
    Dependencies: 
    - Requires $user variable to be passed from parent
    - Uses JavaScript functions from greetings-tab.js
    - Requires admin.users.update-greetings route for saving greetings
    - Uses Vue component <typed-greeting> if available
--}}

<!-- Greetings Tab Content -->
<div id="greetings-content" class="tab-content hidden">
    <div class="rounded-lg overflow-hidden">
        <!-- Introduction Banner -->
        <div class="px-6 py-5 bg-gray-100 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700/50">
            <div class="flex items-start sm:items-center gap-4">
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-800 dark:bg-gray-700 flex items-center justify-center">
                    <i class="design design-hello-collegue text-white dark:text-gray-300 text-lg"></i>
                </div>
                <div>
                    <h3 class="text-base gant-modern-medium text-gray-900 dark:text-white mb-1">V<PERSON><PERSON><PERSON> pozdravy</h3>
                    <p class="text-sm gant-modern-regular text-gray-600 dark:text-gray-400">
                        Vlastné pozdravy sa zobrazia pri prihlásení používateľa a budú vybraté náhodne. Ak používateľ nemá vlastné pozdravy, použijú sa všeobecné.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <!-- Add New Greeting Form -->
            <div class="flex items-center gap-3 mb-4">
                <i class="hrms hrms-user-edit text-gray-800 dark:text-gray-200 text-2xl"></i>
                <h3 class="text-base gant-modern-medium text-gray-900 dark:text-white">Pridať nový pozdrav</h3>
            </div>
            
            <div class="flex flex-col sm:flex-row sm:items-center gap-3 mb-8">
                <div class="flex-grow relative">
                    <input 
                        type="text" 
                        id="newGreetingInput" 
                        class="!bg-white  dark:!bg-gray-800 flex h-12 w-full rounded-xl  border border-gray-300 dark:border-gray-700  px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-300 dark:focus:ring-gray-600 dark:focus:ring-offset-gray-800 pl-10 pr-4 gant-modern-regular" 
                        placeholder="Napíšte pozdrav (bez mena)"
                    >
                    <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        <i class="claims claims-edit-text-outline text-gray-400 dark:text-gray-600"></i>
                    </div>
                </div>
                <button 
                    type="button" 
                    onclick="addNewGreeting()" 
                    class="px-4 py-2 h-12 text-sm gant-modern-medium rounded-md bg-gray-800 text-white hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center justify-center sm:w-auto w-full gap-1.5"
                >
                    <span id="addButtonText">Pridať</span>
                    <i class="claims claims-status-okay ml-1.5"></i>
                </button>
            </div>
        
            <!-- Current Greetings Section -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <i class="design design-user-outline text-gray-800 dark:text-gray-200 text-2xl"></i>
                        <h4 class="text-base gant-modern-medium text-gray-900 dark:text-white">Aktuálne pozdravy</h4>
                    </div>
                    <button 
                        id="deleteSelectedBtn" 
                        type="button" 
                        class="hidden px-3 py-1.5 text-xs gant-modern-medium rounded-xl bg-red-600 text-white focus:outline-none transition-colors duration-200 flex items-center gap-1.5"
                        onclick="deleteSelectedGreetings()"
                    >
                        <i class="claims claims-trash"></i>
                        <span>Vymazať vybrané (<span id="selectedCount">0</span>)</span>
                    </button>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div id="greetingsList" class="divide-y divide-gray-200 dark:divide-gray-700 max-h-60 overflow-y-auto">
                        <!-- Greetings will be rendered here by JavaScript -->
                    </div>
                    
                    <div class="p-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 text-center" id="emptyGreetingsMessage" style="display: none;">
                        <p class="text-sm gant-modern-italic text-gray-500 dark:text-gray-400 italic">
                            Žiadne vlastné pozdravy. Použijú sa štandardné pozdravy.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Preview Section -->
            <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <i class="design design-visible text-gray-800 dark:text-gray-200 text-2xl"></i>
                            <h3 class="text-sm gant-modern-medium text-gray-900 dark:text-white">Ukážka pozdravu</h3>
                        </div>
                        <button 
                            type="button" 
                            onclick="refreshPreview()" 
                            class="px-3 py-1.5 text-xs gant-modern-medium rounded-xl bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 focus:outline-none transition-colors duration-200 flex items-center gap-1.5"
                        >
                            <i class="design design-refresh"></i>
                            <span>Obnoviť náhodný</span>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="greeting-preview" class="min-h-12 text-gray-900 dark:text-white gant-modern-regular text-4xl flex items-center justify-center gant-modern-bold">
                        <!-- Preview content will be rendered here -->
                    </div>
                </div>
            </div>
            
            <!-- Save Changes -->
            <div class="mt-8 flex justify-end">
                <form id="greetingsForm" action="{{ route('admin.users.update-greetings', $user) }}" method="POST">
                    @csrf
                    @method('PATCH')
                    <input type="hidden" id="greetingsData" name="greetings" value="{{ json_encode($user->custom_greetings ?? []) }}">
                    <button 
                        type="button" 
                        onclick="saveAllGreetings()" 
                        class="px-5 py-2.5 text-sm gant-modern-medium rounded-md bg-gray-800 text-white hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center gap-2"
                    >
                        <i class="claims claims-save"></i>
                        <span>Uložiť všetky zmeny</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div> 