{{-- 
    Role Change Confirmation Modal
    Purpose: Displays a confirmation dialog before changing a user's role
    Dependencies: 
    - Requires $user variable to be passed from parent
    - Used by management-tab.blade.php
    - Referenced by JavaScript functions in role-management.js
--}}
<div id="roleModal" class="fixed inset-0 bg-gray-900/50 dark:bg-gray-900/80 z-50 flex items-center justify-center hidden backdrop-blur-sm">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4 overflow-hidden transform transition-all border border-gray-200 dark:border-gray-700 shadow-xl">
        <div class="p-6">
            <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-white mb-3 flex items-center">
                <i class="design design-info-circle mr-2 text-gray-500 dark:text-gray-400"></i>
                Potvrdiť zmenu role
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-5 gant-modern-regular">
                <PERSON><PERSON><PERSON> chcete zmeniť rolu používateľa <span id="roleUserLogin" class="gant-modern-bold">{{ $user->login }}</span> na <span id="roleNewValue" class="gant-modern-bold"></span>?
            </p>
            <div class="flex justify-end space-x-3">
                <button type="button" id="close-role-btn" class="px-4 py-2 text-sm gant-modern-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 flex items-center">
                    <i class="design design-close mr-1.5"></i> Zrušiť
                </button>
                <button type="button" id="submit-role-btn" class="px-4 py-2 text-sm gant-modern-medium text-white bg-gray-700 rounded-md hover:bg-gray-800 transition-colors duration-200 flex items-center">
                    <i class="design design-save mr-1.5"></i> Potvrdiť
                </button>
            </div>
        </div>
    </div>
</div> 