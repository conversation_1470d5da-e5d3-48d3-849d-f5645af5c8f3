{{-- 
    Projects & Fonts Tab
    Purpose: Allows management of assigned projects and fonts for the user
    Dependencies: 
    - Requires $user, $availableProjects, and $availableFonts variables to be passed from parent
    - Uses JavaScript functions from projects-tab.js
    - Requires admin.users.projects.add, admin.users.projects.remove, admin.users.fonts.add, admin.users.fonts.remove routes
--}}

<div id="projects-content" class="tab-content hidden">
    <div class="rounded-lg overflow-hidden">
        <!-- Introduction Banner -->
        <div class="px-6 py-5 bg-gray-100 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700/50">
            <div class="flex items-start sm:items-center gap-4">
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-800 dark:bg-gray-700 flex items-center justify-center">
                    <i class="design design-service text-white dark:text-gray-300 text-lg"></i>
                </div>
                <div>
                    <h3 class="text-base gant-modern-medium text-gray-900 dark:text-white mb-1">Projekty a fonty používateľa</h3>
                    <p class="text-sm gant-modern-regular text-gray-600 dark:text-gray-400">
                        Spravujte priradené projekty a vlastné fonty používateľa. Priradené fonty a projekty sa používajú na riadenie prístupu k zdrojom.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="p-6 space-y-8">
            <!-- Projects Assignment Section -->
            <div>
                <!-- Section Header -->
                <div class="flex items-center gap-3 mb-4">
                    <i class="design design-folder-outline text-gray-800 dark:text-gray-200 text-2xl"></i>
                    <h3 class="text-base gant-modern-medium text-gray-900 dark:text-white">Priradené projekty</h3>
                </div>
                
                <!-- Current Projects -->
                <div class="mb-6">
                    <h4 class="text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                        <i class="design design-format-list-checks mr-2 opacity-70"></i>
                        Aktuálne projekty
                    </h4>
                    
                    @if($user->projects && $user->projects->count() > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($user->projects as $project)
                                <div class="bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg p-4 flex justify-between items-start">
                                    <div>
                                        <h6 class="gant-modern-bold text-gray-900 dark:text-white">{{ $project->name }}</h6>
                                        <p class="text-sm gant-modern-regular text-gray-500 dark:text-gray-400 mt-1">
                                            @if($project->status === 'Development')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400 inline-flex items-center">
                                                    <i class="design design-code mr-1 opacity-70 text-xs"></i>Development
                                                </span>
                                            @elseif($project->status === 'Stage')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 inline-flex items-center">
                                                    <i class="design design-lab mr-1 opacity-70 text-xs"></i>Stage
                                                </span>
                                            @elseif($project->status === 'Produkcia')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 inline-flex items-center">
                                                    <i class="design design-monitor-check mr-1 opacity-70 text-xs"></i>Produkcia
                                                </span>
                                            @elseif($project->status === 'Refactor')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400 inline-flex items-center">
                                                    <i class="design design-swap mr-1 opacity-70 text-xs"></i>Refactor
                                                </span>
                                            @elseif($project->status === 'Nechytat')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 inline-flex items-center">
                                                    <i class="design design-hand-block mr-1 opacity-70 text-xs"></i>Nechytat
                                                </span>
                                            @elseif($project->status === 'Netrap sa')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-white dark:bg-gray-900/30 text-gray-800 dark:text-gray-400 inline-flex items-center">
                                                    <i class="design design-emotion-sad mr-1 opacity-70 text-xs"></i>Netrap sa
                                                </span>
                                            @elseif($project->status === 'Urgent')
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 inline-flex items-center">
                                                    <i class="design design-alert mr-1 opacity-70 text-xs"></i>Urgent
                                                </span>
                                            @else
                                                <span class="px-2 py-0.5 text-xs gant-modern-medium rounded-full bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-400 inline-flex items-center">
                                                    <i class="design design-flag mr-1 opacity-70 text-xs"></i>{{ $project->status }}
                                                </span>
                                            @endif
                                        </p>
                                        @if($project->start_date)
                                        <p class="text-xs gant-modern-regular text-gray-500 dark:text-gray-400 mt-2">
                                            <span>Začiatok: {{ $project->start_date->format('d.m.Y') }}</span>
                                            @if($project->end_date)
                                                <span class="mx-1">•</span>
                                                <span>Koniec: {{ $project->end_date->format('d.m.Y') }}</span>
                                            @endif
                                        </p>
                                        @endif
                                    </div>
                                    <form action="{{ route('admin.users.projects.remove', ['user' => $user->id, 'project' => $project->id]) }}" method="POST" class="ml-2">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-500 hover:text-red-700 dark:hover:text-red-400 transition-colors rounded-full p-1 hover:bg-red-50 dark:hover:bg-red-900/20 gant-modern-medium" title="Odstrániť projekt">
                                            <i class="design design-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="py-3 text-center">
                            <p class="text-sm gant-modern-italic text-gray-500 dark:text-gray-400 italic">
                                Používateľ nemá priradené žiadne projekty
                            </p>
                        </div>
                    @endif
                </div>
                
                <!-- Add Project Form -->
                <div>
                    <h4 class="text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                        <i class="design design-add-folder mr-2 opacity-70"></i>
                        Pridať projekt používateľovi
                    </h4>
                    
                    <form action="{{ route('admin.users.projects.add', $user->id) }}" method="POST" class="py-2">
                        @csrf
                        <div class="flex flex-col sm:flex-row sm:items-end gap-3">
                            <div class="flex-grow">
                                <label for="project_id" class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1">Vyberte projekt</label>
                                <select name="project_id" id="project_id" class="block w-full rounded-xl border border-gray-300 dark:border-gray-700 dark:bg-gray-800/80 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white text-sm gant-modern-regular" required>
                                    <option value="" class="gant-modern-regular">-- Vyberte projekt --</option>
                                    @foreach($availableProjects as $project)
                                        <option value="{{ $project->id }}" class="gant-modern-regular">{{ $project->name }} ({{ $project->status }})</option>
                                    @endforeach
                                </select>
                            </div>
                            <button type="submit" class="px-4 py-2.5 h-10 text-sm gant-modern-medium rounded-md bg-gray-800 text-white hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center justify-center sm:w-auto w-full gap-1.5">
                                <i class="design design-add mr-1.5"></i>
                                <span>Pridať projekt</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Fonts Assignment Section -->
            <div>
                <!-- Section Header -->
                <div class="flex items-center gap-3 mb-4">
                    <i class="design design-symbol text-gray-800 dark:text-gray-200 text-2xl"></i>
                    <h3 class="text-base gant-modern-medium text-gray-900 dark:text-white">Priradené fonty</h3>
                </div>
                
                <!-- Current Fonts -->
                <div class="mb-6">
                    <h4 class="text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                        <i class="design design-format-list-checks mr-2 opacity-70"></i>
                        Aktuálne fonty
                    </h4>
                    
                    @if($user->fonts && $user->fonts->count() > 0)
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            @foreach($user->fonts as $font)
                                <div class="flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg p-4 relative">
                                    <i class="{{ $font->css_class }} {{ $font->preview_icon ?? $font->css_class.'-thumbnail' }} text-4xl text-gray-900 dark:text-white"></i>
                                    <span class="mt-2 text-xs gant-modern-medium text-gray-700 dark:text-gray-300 text-center">{{ $font->name }}</span>
                                    <span class="mt-1 text-xs gant-modern-regular text-gray-500 dark:text-gray-400">{{ $font->icons_count }} ikon</span>
                                    
                                    <form action="{{ route('admin.users.fonts.remove', ['user' => $user->id, 'font' => $font->id]) }}" method="POST" class="absolute top-1 right-1">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-500 hover:text-red-700 dark:hover:text-red-400 transition-colors rounded-full p-1 hover:bg-red-50 dark:hover:bg-red-900/20 gant-modern-medium" title="Odstrániť font">
                                            <i class="design design-trash text-sm"></i>
                                        </button>
                                    </form>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="py-3 text-center">
                            <p class="text-sm gant-modern-italic text-gray-500 dark:text-gray-400 italic">
                                Používateľ nemá priradené žiadne fonty
                            </p>
                        </div>
                    @endif
                </div>

                <!-- Add Font Form -->
                <div>
                    <h4 class="text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                        <i class="design design-add-symbol mr-2 opacity-70"></i>
                        Pridať font používateľovi
                    </h4>
                    
                    <form action="{{ route('admin.users.fonts.add', $user->id) }}" method="POST" class="py-2">
                        @csrf
                        <div class="flex flex-col sm:flex-row sm:items-end gap-3">
                            <div class="flex-grow">
                                <label for="font_id" class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1">Vyberte font</label>
                                <select name="font_id" id="font_id" class="block w-full rounded-xl border border-gray-300 dark:border-gray-700 dark:bg-gray-800/80 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white text-sm gant-modern-regular" required>
                                    <option value="" class="gant-modern-regular">-- Vyberte font --</option>
                                    @foreach($availableFonts as $font)
                                        <option value="{{ $font->id }}" class="gant-modern-regular">{{ $font->name }} ({{ $font->icons_count }} ikon)</option>
                                    @endforeach
                                </select>
                            </div>
                            <button type="submit" class="px-4 py-2.5 h-10 text-sm gant-modern-medium rounded-md bg-gray-800 text-white hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 flex items-center justify-center sm:w-auto w-full gap-1.5">
                                <i class="design design-add mr-1.5"></i>
                                <span>Pridať font</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 