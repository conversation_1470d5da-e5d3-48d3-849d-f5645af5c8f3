<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Datatable Component Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- TOS Icons Font -->
    <style>
        body { padding: 2rem; }
        
        .code-highlight {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        
        /* Responsive styling for table cells */
        @media screen and (max-width: 768px) {
            .datatable-component.responsive-table .table td::before {
                content: attr(data-label);
                position: absolute;
                left: 0;
                width: 45%;
                padding-left: 1rem;
                font-weight: bold;
                text-align: left;
                white-space: normal;
            }
            
            .datatable-component.responsive-table .table td {
                padding-left: 50%;
            }
        }
        
        /* TOS Icons Font */
        @font-face {
            font-family: 'tos';
            src: url('{{ asset('resources/fonts-icons/tos.woff2') }}') format('woff2'),
                 url('{{ asset('resources/fonts-icons/tos.woff') }}') format('woff'),
                 url('{{ asset('resources/fonts-icons/tos.ttf') }}') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: block;
        }

        [class^="tos-"], [class*=" tos-"] {
            /* use !important to prevent issues with browser extensions that change fonts */
            font-family: 'tos' !important;
            speak: never;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 1;
            vertical-align: -.125em;
            text-rendering: auto;
            /* Better Font Rendering =========== */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Include the necessary TOS icons for this demo */
        .tos-cells-setup-outline:before {
            content: "\eb1e";  /* gear-setup */
        }
        
        .tos-hidden:before {
            content: "\eb2b";
        }
        
        .tos-download:before {
            content: "\eaf4";  /* download-export */
        }
        
        .tos-thumbnail:before {
            content: "\ebd1";
        }

        /* Add specific icons for download buttons */
        .tos-down-loading:before {
            content: "\eaf3";
        }
        
        .tos-download-export:before {
            content: "\eaf4";
        }

        /* Add column header icons */
        .tos-good-id:before {
            content: "\eb28";
        }
        
        .tos-article_name:before {
            content: "\ea4e";
        }
        
        .tos-key:before {
            content: "\ea4a"; /* Using article_code as key */
        }
        
        .tos-code:before {
            content: "\ea4a"; /* Using article_code for code */
        }
        
        .tos-sort-by:before {
            content: "\ebb1";
        }
        
        .tos-document-search:before {
            content: "\ead0";
        }
        
        .tos-eye:before {
            content: "\eb0e";
        }
        
        .tos-edit:before {
            content: "\eafe";
        }
        
        .tos-filter:before {
            content: "\eb11";
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-2">Datatable Component Preview</h1>
        
        <!-- Live Demo First -->
        <div id="app" class="">
<div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="mb-0">Live Demo</p>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" v-model="isResponsive" id="responsiveSwitch">
                        <label class="form-check-label" for="responsiveSwitch">Responsive Mode @{{ isResponsive ? 'On' : 'Off' }}</label>
                    </div>
                </div>
                <datatable-component 
                    :columns="columns" 
                    :data="data.data"
                    :pagination="data"
                    @page-change="handlePageChange"
                    :sticky-header="true"
                    table-classes="table-hover"
                    default-column-align="start"
                    :responsive="isResponsive"
                >
                    <template #header-actions>
                        <h5 class="mb-0 text-primary">Font Icons <small class="text-muted">with alignment examples</small></h5>
                    </template>
                    
                    <template #table-tools>
                        <button class="btn btn-sm btn-outline-secondary d-flex align-items-center">
                            <i class="tos tos-download-export me-2 opacity-50"></i> Export
                        </button>
                        <button class="btn btn-sm btn-outline-secondary d-flex align-items-center">
                            <i class="tos tos-filter me-2 opacity-50"></i> Filter
                        </button>
                    </template>
                    
                    <template #cell(id)="{ value }">
                        <span class="badge bg-light text-dark border">@{{ value }}</span>
                    </template>
                    
                    <template #cell(css_class)="{ value }">
                        <code class="bg-light px-2 py-1 rounded small">@{{ value }}</code>
                    </template>
                    
                    <template #cell(order_nr)="{ value }">
                        <div class="d-flex justify-content-center">
                            <span class="badge bg-primary">@{{ value }}</span>
                        </div>
                    </template>
                    
                    <template #actions="{ item }">
                        <div class="d-flex justify-content-end gap-1">
                            <a href="#" @click.prevent="$parent.alert('View ' + item.name)" class="btn btn-sm btn-info">
                                <i class="tos tos-eye"></i>
                            </a>
                            <a href="#" @click.prevent="$parent.alert('Edit ' + item.name)" class="btn btn-sm btn-primary">
                                <i class="tos tos-edit"></i>
                            </a>
                        </div>
                    </template>
                    
                    <template #empty-state>
                        <div class="py-4 text-center text-muted">
                            <i class="tos tos-document-search fs-2 d-block mb-2"></i>
                            No data found. Please try a different search.
                        </div>
                    </template>
                </datatable-component>

                <!-- Alignment Legend -->


        </div>
        
        <!-- Download Options -->
        <div class="row mb-4 mt-5">
            <h3 class="mb-4">Documentation</h3>
            <div class="col-12 col-md-6 mb-2">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">Download Files</h3>
                    </div>
                    <div class="card-body">
                        <p>Files to implement this component:</p>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="{{ route('datatable.download.component') }}" class="btn btn-light border w-100 text-start py-2">
                                    <i class="tos tos-down-loading me-2 opacity-50"></i> 
                                    <span class="fw-bold">DatatableComponent.vue</span>
                                    <small class="d-block text-muted ms-4 mt-1">Vue component</small>
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="{{ route('datatable.download.blade') }}" class="btn btn-light border w-100 text-start py-2">
                                    <i class="tos tos-down-loading me-2 opacity-50"></i> 
                                    <span class="fw-bold">datatable-preview.blade.php</span>
                                    <small class="d-block text-muted ms-4 mt-1">Example Blade template</small>
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="{{ route('datatable.download.controller') }}" class="btn btn-light border w-100 text-start py-2">
                                    <i class="tos tos-down-loading me-2 opacity-50"></i> 
                                    <span class="fw-bold">DatatableController.php</span>
                                    <small class="d-block text-muted ms-4 mt-1">Example controller file</small>
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="{{ route('datatable.download.package') }}" class="btn btn-light border w-100 text-start py-2">
                                    <i class="tos tos-download-export me-2 opacity-50"></i> 
                                    <span class="fw-bold">Complete Package</span>
                                    <small class="d-block text-muted ms-4 mt-1">ZIP with all files including fonts</small>
                                </a>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted fst-italic">The complete package includes all necessary files plus TOS font icons.</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-6 mb-2">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title">Usage Example</h3>
                    </div>
                    <div class="card-body">
                        <p>This is a reusable datatable component built with Vue and Bootstrap 5.1.</p>
                        <div class="code-highlight">
                            <pre><code>&lt;datatable-component 
    :columns="columns" 
    :data="data.data"
    :pagination="data"
    @page-change="handlePageChange"
    :sticky-header="true"
    table-classes="table-hover"
    default-column-align="start"
    :responsive="true"
&gt;
    &lt;template #header-actions&gt;
        &lt;h5 class="mb-0"&gt;Table Title&lt;/h5&gt;
    &lt;/template&gt;
    
    &lt;template #table-tools&gt;
        &lt;button class="btn btn-sm btn-outline-secondary"&gt;
            &lt;i class="icon-export me-2"&gt;&lt;/i&gt; Export
        &lt;/button&gt;
    &lt;/template&gt;
    
    &lt;template #cell(id)="{ value }"&gt;
        &lt;span class="badge bg-light"&gt;@{{ value }}&lt;/span&gt;
    &lt;/template&gt;
    
    &lt;template #actions="{ item }"&gt;
        &lt;div class="d-flex justify-content-end gap-1"&gt;
            &lt;a :href="'/items/' + item.id" class="btn btn-sm btn-info"&gt;
                &lt;i class="icon-view"&gt;&lt;/i&gt;
            &lt;/a&gt;
        &lt;/div&gt;
    &lt;/template&gt;
&lt;/datatable-component&gt;</code></pre>

                        </div>
                        <div class="mt-3 p-3 border rounded bg-light">
                    <h6 class="mb-2">Cell Alignment Options</h6>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body p-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-secondary border me-2">start</span>
                                        <span>Left-aligned default</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body p-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-secondary border me-2">center</span>
                                        <span>Center-aligned content</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body p-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-light text-secondary border me-2">end</span>
                                        <span>Right-aligned content</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="small text-muted mb-0">Each column can have its own alignment setting. Look for the alignment badge in the column header.</p>
                </div>
                    </div>
                </div>
            </div>

        </div>
        </div>

    </div>

    <script src="https://unpkg.com/vue@3.4.27/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        const { createApp, ref, computed, watch, getCurrentInstance } = Vue;

        const DatatableComponent = {
            name: 'DatatableComponent',
            props: { columns: { type: Array, required: true }, data: { type: Array, required: true }, pagination: { type: Object, default: null }, showColumnHiding: { type: Boolean, default: true }, rowClass: { type: Function, default: null }, responsive: { type: Boolean, default: true }, stickyHeader: { type: Boolean, default: false }, tableClasses: { type: String, default: '' }, defaultColumnAlign: { type: String, default: 'start' } },
            emits: ['page-change', 'columns-updated'],
            template: `
                <div class="datatable-component" :class="{'responsive-table': responsive}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div><slot name="header-actions"></slot></div>
                        <div class="dropdown" v-if="showColumnHiding">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" :id="'table_settings_' + uid" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="tos tos-cells-setup-outline me-2"></i> Hide Columns
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" :aria-labelledby="'table_settings_' + uid">
                                <li v-for="col in localColumns" :key="col.key" class="px-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" v-model="col.visible"
                                            :id="'col_vis_' + col.key + uid">
                                        <label class="form-check-label ms-2" :for="'col_vis_' + col.key + uid">@{{ col.label }}</label>
                                    </div>
                                </li>
                                <li class="text-center mt-2">
                                    <button @click.prevent="resetColumns" class="btn btn-outline-secondary btn-sm w-75">Reset</button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm table-bordered">
                            <thead :class="['bg-light', responsive ? 'd-none d-md-table-header-group' : '']">
                                <tr class="text-nowrap">
                                    <th v-for="col in visibleColumns" :key="col.key" scope="col">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>@{{ col.label }}</span>
                                            <a v-if="showColumnHiding" href="#" @click.prevent="hideColumn(col.key)"
                                                class="link-secondary text-decoration-none" title="Hide">
                                                <i class="tos tos-hidden"></i>
                                            </a>
                                        </div>
                                    </th>
                                    <th v-if="$slots.actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, itemIndex) in data" :key="item.id || itemIndex" 
                                    :class="[rowClass ? rowClass(item) : '', responsive ? 'd-block d-md-table-row mb-3 border rounded' : '']">
                                    <td v-for="(col, colIndex) in visibleColumns" :key="col.key" :data-label="col.label"
                                        :class="[responsive ? 'd-block d-md-table-cell text-end position-relative border-top' : '', responsive && colIndex === 0 ? 'border-top-0' : '']">
                                        <slot :name="'cell(' + col.key + ')'" :item="item" :value="getValue(item, col.key)">@{{ getValue(item, col.key) }}</slot>
                                    </td>
                                    <td v-if="$slots.actions" data-label="Actions"
                                        :class="[responsive ? 'd-block d-md-table-cell text-end position-relative border-top' : '', responsive && visibleColumns.length === 0 ? 'border-top-0' : '']">
                                        <slot name="actions" :item="item"></slot>
                                    </td>
                                </tr>
                                <tr v-if="!data || data.length === 0">
                                    <td :colspan="visibleColumns.length + ($slots.actions ? 1 : 0)"
                                        class="text-center p-5 bg-light">
                                        No data available.
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <nav v-if="pagination && pagination.links && pagination.links.length > 1">
                        <ul class="pagination">
                            <li v-for="(link, index) in pagination.links" :key="index"
                                :class="['page-item', { 'active': link.active, 'disabled': !link.url }]">
                                <a class="page-link" href="#" @click.prevent="onPageChange(link.url)" v-html="link.label"></a>
                            </li>
                        </ul>
                    </nav>
                </div>`,
            setup(props, { emit }) {
                const uid = getCurrentInstance().uid;
                const localColumns = ref(JSON.parse(JSON.stringify(props.columns)));
                watch(() => props.columns, (newColumns) => { localColumns.value = JSON.parse(JSON.stringify(newColumns)); }, { deep: true, immediate: true });
                const visibleColumns = computed(() => localColumns.value.filter(c => c.visible));
                const hideColumn = (key) => { const col = localColumns.value.find(c => c.key === key); if (col) col.visible = false; };
                const resetColumns = () => { localColumns.value = JSON.parse(JSON.stringify(props.columns)); };
                const getValue = (item, key) => key.split('.').reduce((acc, part) => acc && acc[part], item);
                const onPageChange = (url) => { if (url) emit('page-change', url); };
                watch(localColumns, (newCols) => { emit('columns-updated', JSON.parse(JSON.stringify(newCols))); }, { deep: true });
                return { uid, localColumns, visibleColumns, hideColumn, resetColumns, getValue, onPageChange };
            }
        };

        const app = createApp({
            setup() {
                const columns = ref(@json($columns));
                const data = ref(@json($fonts));
                const isResponsive = ref(true); // Default to responsive mode
                const handlePageChange = (url) => {
                    window.location.href = url;
                };
                const alert = (msg) => window.alert(msg);
                return { columns, data, isResponsive, handlePageChange, alert };
            }
        });

        app.component('datatable-component', DatatableComponent);
        app.mount('#app');
    </script>
</body>
</html> 