@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Dashboard</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Success</h3>
                <p>{{ session('success') }}</p>
            </div>
        @endif
        
        @if(session('error'))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error</h3>
                <p>{{ session('error') }}</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $adminActionCount = $adminActionCount ?? 0;
            $iconStats = $iconStats ?? ['totalIcons' => 0, 'totalIconsPerFont' => []];
            $projectsCount = $projectsCount ?? 0;
            $usersCount = $usersCount ?? 0;
            $activeUsers = $activeUsers ?? collect([]);
            $fonts = $fonts ?? collect([]);
            $totalTagsCount = 0;
            
            // Calculate tag count directly in the view
            $calculatedTagsCount = 0;
            
            if (isset($fonts) && is_countable($fonts)) {
                foreach($fonts as $font) {
                    if (isset($font->icons) && is_countable($font->icons)) {
                        foreach($font->icons as $icon) {
                            if (isset($icon->tags) && is_array($icon->tags)) {
                                $calculatedTagsCount += count($icon->tags);
                            } else if (isset($icon->tags) && is_string($icon->tags)) {
                                $tagsArray = json_decode($icon->tags, true);
                                if (is_array($tagsArray)) {
                                    $calculatedTagsCount += count($tagsArray);
                                }
                            }
                        }
                    }
                }
            }
        @endphp
        
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.index"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
        ></admin-navigation>

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
                <h2 class="text-4xl lg:text-5xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.dashboard') }}</h2>
                <div class="text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular">
                    <i class="design design-presentation opacity-50 me-1"></i>{{ __('admin.system_stats') }}
                </div>
            </div>
            <div class="flex gap-3">
                <button onclick="refreshDashboardData()" class="group inline-flex items-center px-4 py-3 rounded-lg gant-modern-bold text-sm text-gray-900 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-800 transition-all duration-200">
                    <i class="design design-refresh mr-2 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 group-hover:rotate-180 transition-all duration-500"></i>
                    {{ __('admin.refresh_data') }}
                </button>
            </div>
        </div>

        <!-- Dashboard Grid Layout - Single Container with Pattern -->
        <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl">
            <!-- Pattern SVG - Applied to entire container -->
            <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                        <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern-3)" />
            </svg>

            <!-- Content Container - On top of pattern -->
            <div class="relative z-10 space-y-6">
                <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-50 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="design design-pizza text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.quick_actions') }}</h3>
                            </div>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        <div class="flex flex-wrap gap-4">
                            <!-- Create Change Log Widget -->
                            <a href="{{ route('admin.changelog.create') }}" class="block group">
                                <div class="p-5 rounded-lg border border-solid border-gray-50 dark:border-slate-900 bg-white dark:bg-slate-950 hover:bg-white dark:hover:bg-slate-950 transition-all duration-200 flex flex-col items-center justify-center text-center h-full">
                                    <div class="w-14 h-14 bg-gray-100 dark:bg-gray-900/20 rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-all duration-300">
                                        <i class="design design-changes text-2xl text-gray-900 dark:text-gray-400"></i>
                                    </div>
                                    <h4 class="text-gray-900 dark:text-white gant-modern-bold mb-1">{{ __('admin.new_changelog') }}</h4>
                                </div>
                            </a>

                            <!-- Create Project Widget -->
                            <a href="{{ route('admin.projects.create') }}" class="block group">
                                <div class="p-5 rounded-lg border border-solid border-gray-50 dark:border-slate-900 bg-white dark:bg-slate-950 hover:bg-white dark:hover:bg-slate-950 transition-all duration-200 flex flex-col items-center justify-center text-center h-full">
                                    <div class="w-14 h-14 bg-gray-100 dark:bg-gray-900/20 rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-all duration-300">
                                        <i class="design design-service text-2xl text-gray-900 dark:text-gray-400"></i>
                                    </div>
                                    <h4 class="text-gray-900 dark:text-white gant-modern-bold mb-1">{{ __('admin.new_project') }}</h4>
                                </div>
                            </a>

                            <!-- Tag Manager Widget -->
                            <a href="{{ route('admin.tags') }}" class="block group">
                                <div class="p-5 rounded-lg border border-solid border-gray-50 dark:border-slate-900 bg-white dark:bg-slate-950 hover:bg-white dark:hover:bg-slate-950 transition-all duration-200 flex flex-col items-center justify-center text-center h-full">
                                    <div class="w-14 h-14 bg-gray-100 dark:bg-gray-900/20 rounded-full flex items-center justify-center mb-3 group-hover:scale-110 transition-all duration-300">
                                        <i class="design design-tag-outline text-2xl text-gray-900 dark:text-gray-400"></i>
                                    </div>
                                    <h4 class="text-gray-900 dark:text-white gant-modern-bold mb-1">{{ __('admin.tag_manager') }}</h4>
                                </div>
                            </a>
                        </div>
                    </div>
                <!-- First Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6">
                    
                    <!-- Widget 1: Users Overview -->
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="design design-user-outline text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <div class="flex items-center">
                                    <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.users') }}</h3>
                                    @if(isset($newUsersCount) && $newUsersCount > 0)
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs gant-modern-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            <i class="design design-circle-plus mr-1 opacity-70"></i> {{ __('admin.new_users') }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <button onclick="window.location.href='{{ route('admin.users') }}'" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                {{ __('admin.view_all_users') }}
                                <i class="design design-arrow-right ml-1"></i>
                            </button>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        <div class="flex justify-between items-end">
                            <div>
                                @php
                                    // Get the total user count that will match users.blade.php
                                    $userCount = App\Models\User::count();
                                @endphp
                                <p class="text-4xl gant-modern-bold text-gray-900 dark:text-white"><i class="vermont-icon vermont-icon-vermont-clen text-3xl opacity-50"></i>
                                    {{ $userCount }}
                                </p>
                                <p class="text-sm text-gray-500 gant-modern-regular dark:text-gray-400">{{ __('admin.total_users') }}</p>
                                
                            </div>
                            <div class="flex flex-col items-end">
                                @php
                                    $newUsersCount = $newUsersCount ?? 0;
                                    
                                    // Calculate users logged in today
                                    $todayLoginsCount = 0;
                                    if (isset($users) && is_countable($users)) {
                                        $today = \Carbon\Carbon::today();
                                        foreach($users as $user) {
                                            if (isset($user->last_login_at) && 
                                                !is_null($user->last_login_at) && 
                                                \Carbon\Carbon::parse($user->last_login_at)->isToday()) {
                                                $todayLoginsCount++;
                                            }
                                        }
                                    } else {
                                        // Get users logged in today directly from the database
                                        $todayLoginsCount = App\Models\User::whereNotNull('last_login_at')
                                            ->whereDate('last_login_at', \Carbon\Carbon::today())
                                            ->count();
                                    }
                                @endphp
                                <div class="flex items-center text-gray-600 dark:text-gray-400">
                                    <i class="design design-arrow-up-right mr-1"></i>
                                    <span class="text-sm gant-modern-bold">
                                        @if($newUsersCount > 0)
                                            +{{ $newUsersCount }} {{ $newUsersCount == 1 ? __('admin.new_user_singular') : __('admin.new_user_plural') }}
                                        @else
                                            {{ __('admin.no_new_colleagues') }} :/
                                        @endif
                                    </span>
                                </div>
                                <div class="flex items-center text-gray-600 dark:text-gray-400 mt-1">
                                    <i class="design design-clock mr-1"></i>
                                    <span class="text-sm gant-modern-regular">
                                        {{ $todayLoginsCount }} {{ $todayLoginsCount == 1 ? __('admin.logged_in_today_singular') : __('admin.logged_in_today_plural') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <!-- Widget 2: Icons Overview -->
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="vermont-icon vermont-icon-solo-vermontdays-outline text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.vermont_icons') }}</h3>
                            </div>
                            <button onclick="window.location.href='{{ route('admin.icons') }}'" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                {{ __('admin.manage_icons') }}
                                <i class="design design-arrow-right ml-1"></i>
                            </button>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        <div class="flex justify-between items-end">
                            <div>
                                <p class="text-4xl gant-modern-bold text-gray-900 dark:text-white"><i class="design design-component text-3xl opacity-50"></i>
                                    @php
                                        $iconsCount = 0;
                                        $fontsCount = isset($fonts) && is_countable($fonts) ? count($fonts) : 11; 
                                        
                                        // Calculate icon count
                                        if (isset($fonts) && is_countable($fonts)) {
                                            foreach($fonts as $font) {
                                                $iconsCount += $font->icons_count ?? 0;
                                            }
                                        }
                                        
                                        // Display the actual count without any formatting
                                        echo $iconsCount;
                                    @endphp
                                    
                                </p>
                                <p class="text-sm text-gray-500 gant-modern-regular dark:text-gray-400">{{ __('admin.total_icons') }}</p>
                            </div>
                            <div>
                                <p class="text-4xl gant-modern-bold text-gray-900 dark:text-white"><i class="design design-tag-outline text-3xl opacity-50"></i>
                                    @php
                                        // Initialize tag counter
                                        $totalTagsCount = 0;
                                        
                                        // Count tags from icons_data instead of a relationship
                                        if (isset($fonts) && is_countable($fonts)) {
                                            foreach($fonts as $font) {
                                                // Access icons_data property instead of icons relationship
                                                if (isset($font->icons_data) && is_array($font->icons_data)) {
                                                    foreach($font->icons_data as $icon) {
                                                        // Count tags from each icon
                                                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                                                            $totalTagsCount += count($icon['tags']);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        
                                        echo $totalTagsCount;
                                    @endphp
                                    
                                </p>
                                <p class="text-sm text-gray-500 gant-modern-regular dark:text-gray-400">{{ __('admin.total_search_tags') }}</p>
                            </div>
                            <div class="flex flex-col items-end">
                                <div class="flex items-center text-gray-600 dark:text-gray-400">
                                    <i class="design design-folder-fill mr-1"></i>
                                    <span class="text-sm gant-modern-bold">{{ $fontsCount }} {{ __('admin.fonts') }}</span>
                                </div>
                                
                                
                                <div class="flex items-center {{ $needsUpdateCount > 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400' }} mt-1">
                                    <i class="design {{ $needsUpdateCount > 0 ? 'design-circle-remove' : 'design-circle-checked' }} mr-1"></i>
                                    <span class="text-sm gant-modern-bold">
                                        @if($needsUpdateCount > 0)
                                            {{ $needsUpdateCount }} {{ $needsUpdateCount == 1 ? __('admin.font_needs_update_singular') : __('admin.font_needs_update_plural') }}
                                        @else
                                            {{ __('admin.all_current') }}
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
                    <!-- Widget 4: Recent Activity -->
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="wms wms-browse-history-outline text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.recent_activity') }}</h3>
                            </div>
                            <button onclick="window.location.href='{{ route('admin.activities') }}'" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                {{ __('admin.view_all_activity') }}
                                <i class="design design-arrow-right ml-1"></i>
                            </button>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        <div class="space-y-4">
                            @if(isset($fonts) && is_countable($fonts) && count($fonts) > 0 && isset($fonts[0]->icons_max_updated_at))
                                <div class="flex items-start gap-3">
                                    <div class="p-2 bg-gray-900 dark:bg-gray-200 rounded-full flex-shrink-0 w-10 h-10 flex items-center justify-center">
                                        <i class="design design-changes text-lg text-gray-100 dark:text-gray-900"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-gray-900 dark:text-white gant-modern-bold">{{ __('admin.icon_update') }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-sm gant-modern-regular">{{ __('admin.last_icon_update') }}</p>
                                        <p class="text-gray-400 dark:text-gray-500 text-xs mt-1 gant-modern-regular">
                                            {{ \Carbon\Carbon::parse($fonts[0]->icons_max_updated_at)->format('d.m.Y H:i') }}
                                        </p>
                                    </div>
                                </div>
                            @endif
                            
                            @if(isset($newUsersCount) && $newUsersCount > 0)
                                <div class="flex items-start gap-3">
                                    <div class="p-2 bg-gray-900 dark:bg-gray-200 rounded-full flex-shrink-0 w-10 h-10 flex items-center justify-center">
                                        <i class="design design-user-outline text-lg text-gray-100 dark:text-gray-900"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-gray-900 dark:text-white gant-modern-bold">{{ __('admin.new_users_activity') }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-sm gant-modern-regular">{{ $newUsersCount }} {{ $newUsersCount == 1 ? __('admin.new_user_singular') : __('admin.new_user_plural') }}</p>
                                        <p class="text-gray-400 dark:text-gray-500 text-xs mt-1 gant-modern-regular">{{ __('admin.today') }}</p>
                                    </div>
                                </div>
                            @endif
                            
                            @if(isset($needsUpdateCount) && $needsUpdateCount > 0)
                                <div class="flex items-start gap-3">
                                    <div class="p-2 bg-red-600 rounded-full flex-shrink-0 w-10 h-10 flex items-center justify-center">
                                        <i class="wms wms-alert text-lg text-gray-100"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-gray-900 dark:text-white gant-modern-bold">{{ __('admin.fonts_need_update') }}</p>
                                        <p class="text-gray-500 dark:text-gray-400 text-sm gant-modern-regular">{{ $needsUpdateCount }} {{ $needsUpdateCount == 1 ? __('admin.font_needs_update_singular') : __('admin.font_needs_update_plural') }}</p>
                                        <p class="text-gray-400 dark:text-gray-500 text-xs mt-1 gant-modern-regular">{{ __('admin.check_icons_section') }} <a href="{{ route('admin.icons') }}" class="text-blue-500 dark:text-blue-400">{{ __('admin.manage_icons') }}</a></p>
                                    </div>
                                </div>
                            @endif
                            
                            @if((!isset($fonts) || !is_countable($fonts) || count($fonts) == 0) && 
                                (!isset($newUsersCount) || $newUsersCount == 0) && 
                                (!isset($needsUpdateCount) || $needsUpdateCount == 0))
                                <div class="flex items-center justify-center text-gray-500 dark:text-gray-400 py-8">
                                    <p class="gant-modern-regular">{{ __('admin.no_recent_activity') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
    
                    <!-- Widget 5: Emails -->
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="design design-flow-chart text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.emails') }}</h3>
                            </div>
                            <button onclick="window.location.href='{{ route('admin.activities') }}'" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                {{ __('admin.view_all_activity') }}
                                <i class="design design-arrow-right ml-1"></i>
                            </button>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        <div class="space-y-4">
                        </div>
                    </div>
                </div>

                <!-- Widget 6: Database Manager -->
                <div class="mt-6">
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-start">
                                <i class="design design-database text-2xl text-gray-900 dark:text-gray-400 mr-2 mt-0.5 opacity-50"></i>
                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">{{ __('admin.database_manager') }}</h3>
                            </div>
                        </div>
                        <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 mb-4"></div>
                        
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1 p-5 rounded-lg border border-solid border-gray-50 dark:border-slate-900 bg-gray-50 dark:bg-slate-900 hover:bg-white dark:hover:bg-slate-950 transition-all duration-200">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-3">
                                        <i class="design design-download text-2xl text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <h4 class="text-gray-900 dark:text-white gant-modern-bold mb-2">{{ __('admin.export_database') }}</h4>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">{{ __('admin.download_backup_desc') }}</p>
                                    <a href="{{ route('admin.database.export') }}" class="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors text-center inline-block">
                                        {{ __('admin.export_now') }}
                                    </a>
                                </div>
                            </div>
                            
                            <div class="flex-1 p-5 rounded-lg border border-solid border-gray-50 dark:border-slate-900 bg-gray-50 dark:bg-slate-900 hover:bg-white dark:hover:bg-slate-950 transition-all duration-200">
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-14 h-14 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-3">
                                        <i class="design design-upload text-2xl text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <h4 class="text-gray-900 dark:text-white gant-modern-bold mb-2">{{ __('admin.import_database') }}</h4>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-4">{{ __('admin.replace_database_desc') }}</p>
                                    <form action="{{ route('admin.database.import') }}" method="POST" enctype="multipart/form-data" class="w-full">
                                        @csrf
                                        <label class="w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors cursor-pointer flex items-center justify-center">
                                            <input type="file" name="database" accept=".sqlite,.db,.sqlite3,.db3" class="hidden" onchange="this.form.submit()">
                                            {{ __('admin.import_now') }}
                                        </label>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function refreshDashboardData() {
        // Add loading indicator to the button
        const button = event.currentTarget;
        const originalContent = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <i class="design design-refresh mr-2 text-gray-400 dark:text-gray-500 animate-spin"></i>
            {{ __('admin.loading') }}...
        `;

        // Reload the page with a cache-busting parameter
        setTimeout(() => {
            window.location.href = window.location.pathname + '?refresh=' + new Date().getTime();
        }, 500);
    }
</script>
@endpush
