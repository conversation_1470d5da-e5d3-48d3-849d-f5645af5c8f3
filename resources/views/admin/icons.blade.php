@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-solid border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Icons</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $fonts = $fonts ?? collect([]);
            $totalIcons = $totalIcons ?? 0;
        @endphp
        
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.icons"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Header Section - Responsive Layout -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8">
            <div>
                <div class="flex items-center gap-3">
                    <h2 class="text-4xl lg:text-5xl gant-modern-bold text-gray-900 dark:text-white">Fonty</h2>
                    <a href="{{ route('admin.tags') }}" 
                       class="inline-flex items-center px-2 py-1.5 bg-slate-900 dark:bg-slate-200 text-white dark:text-slate-900 border rounded-md gant-modern-regular text-sm hover:bg-slate-700 dark:hover:bg-slate-300 transition duration-150 ease-in-out">
                        <i class="design design-lab mr-2 opacity-50"></i>
                        Tag Manager
                    </a>
                    <a href="{{ route('admin.initialize_font_tags') }}" 
                       class="inline-flex items-center px-2 py-1.5 bg-slate-900 dark:bg-slate-200 text-white dark:text-slate-900 border rounded-md gant-modern-regular text-sm hover:bg-slate-700 dark:hover:bg-slate-300 transition duration-150 ease-in-out">
                        <i class="design design-swap mr-2 opacity-50"></i>
                        Tags Repair
                    </a>
                </div>
                <div class="text-gray-500 dark:text-gray-400 mt-2">
                    <i class="design design-component opacity-50 me-1"></i>Počet fontov: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ count($fonts) }}</span>
                </div>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.run_font_seeder') }}" 
                   class="inline-flex items-center px-4 py-2 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 rounded-md gant-modern-regular text-sm text-slate-600 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                    <i class="wms wms-play-button mr-1 opacity-50"></i>
                    <span class="gant-modern-medium me-1">Run Font Seeder </span> <i class="design design-alert mx-1 opacity-50"></i> WARMING 
                </a>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success') || session('error') || session('updateFontIconsCommandOutput') || session('initializeFontTagsOutput'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
                
                @if(session('updateFontIconsCommandOutput'))
                    <div class="p-4 mb-8 text-sm text-green-700 dark:text-green-400 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800" role="alert">
                        <div class="flex items-center mb-2">
                            <i class="design design-circle-checked mr-2 text-lg"></i>
                            <span class="gant-modern-medium">Aktualizácia ikon bola úspešná</span>
                        </div>
                        <div class="mt-2 bg-green-100 dark:bg-green-900/30 p-3 rounded-lg overflow-auto max-h-60">
                            <pre class="whitespace-pre-wrap">{{ session('updateFontIconsCommandOutput') }}</pre>
                        </div>
                    </div>
                @endif

                @if(session('initializeFontTagsOutput'))
                    <div class="p-4 mb-8 text-sm text-green-700 dark:text-green-400 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800" role="alert">
                        <div class="flex items-center mb-2">
                            <i class="design design-circle-checked mr-2 text-lg"></i>
                            <span class="gant-modern-medium">Font tags initialized successfully</span>
                        </div>
                        <div class="mt-2 bg-green-100 dark:bg-green-900/30 p-3 rounded-lg overflow-auto max-h-60">
                            <pre class="whitespace-pre-wrap">{{ session('initializeFontTagsOutput') }}</pre>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <!-- Fonts Grid -->
        <div class="relative p-4 sm:p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl mb-6">
            <!-- Pattern SVG - Applied to entire container -->
            <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                        <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern-3)" />
            </svg>

            <!-- Content Container - On top of pattern -->
            <div class="relative z-10 grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
                @forelse($fonts as $font)
                    @php
                        $needsUpdate = file_exists(resource_path('sass/'.$font->path)) && 
                            (is_null($font->icons_max_updated_at) || 
                            (filemtime(resource_path('sass/'.$font->path)) > \Carbon\Carbon::parse($font->icons_max_updated_at)->timestamp));
                            
                        // Handle special cases with different naming conventions
                        $specialCases = [
                            'central database' => 'central-database',
                            'order group' => 'order-group'
                        ];
                        
                        // Determine which project icon to use based on the font name
                        $iconName = strtolower($font->name);
                        
                        if (array_key_exists($iconName, $specialCases)) {
                            $iconName = $specialCases[$iconName];
                        } else {
                            // General rule: replace spaces with dashes for other font names
                            $iconName = str_replace(' ', '-', $iconName);
                        }
                        
                        $iconPath = "img/projects/{$iconName}_small.png";
                        $hasCustomIcon = file_exists(public_path($iconPath));
                    @endphp
                    <div class="{{ $needsUpdate ? 'bg-white dark:bg-slate-950 border border-solid border-gray-200 outline outline-offset-1 outline-solid outline-red-500/50' : 'bg-white dark:bg-slate-950' }} rounded-lg border {{ $needsUpdate ? 'border-red-600 dark:border-red-800/30 hover:border-red-300 dark:hover:border-red-700/50' : 'border-solid border-gray-200 dark:border-gray-700/50 hover:border-gray-300 dark:hover:border-gray-600' }} p-4 sm:p-5 transition-all duration-200">
                        <div class="flex flex-col sm:flex-row justify-between items-start gap-4 mb-3">
                            <div class="flex items-center gap-3 w-full sm:w-auto">
                                <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                                    <i class="design {{ $needsUpdate ? 'design-circle-remove text-red-500 dark:text-red-400' : 'design-circle-checked text-green-500 dark:text-green-400' }} text-xl"
                                       title="{{ $needsUpdate ? 'Needs update' : 'Up to date' }}"></i>
                                </div>
                                
                                @if($hasCustomIcon)
                                    <img src="{{ asset($iconPath) }}" alt="{{ $font->name }}" class="w-10 h-10 rounded" />
                                @else
                                    <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded-full flex-shrink-0 w-10 h-10 flex items-center justify-center">
                                        <i class="design design-component text-base text-gray-500 dark:text-gray-400"></i>
                                    </div>
                                @endif
                                
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white leading-tight truncate">{{ $font->name }}</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 font-mono truncate max-w-[140px]">{{ $font->hash }}</p>
                                </div>
                            </div>
                            
                            @if($needsUpdate)
                            <div class="flex-shrink-0 w-full sm:w-auto">
                                <a href="{{ route('admin.update_font_icons', [$font]) }}" 
                                   class="inline-flex w-full sm:w-auto justify-center items-center px-3 py-1.5 rounded-lg text-sm gant-modern-medium transition-all duration-200 bg-red-500 text-white hover:bg-red-600 dark:bg-red-600/90 dark:hover:bg-red-600">
                                    <i class="retail retail-refresh mr-1 transition-transform duration-500 group-hover:rotate-180"></i>
                                    Aktualizovať
                                </a>
                            </div>
                            @endif
                        </div>
                        
                        <div class="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-3">
                            <!-- Last update -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="wms wms-bin-history mr-1 w-3 text-center"></i>Last update:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-sm">
                                    {{ (is_null($font->icons_max_updated_at)) ? '-' : \Carbon\Carbon::parse($font->icons_max_updated_at)->format('d.m - H:i') }}
                                </div>
                            </div>

                            <!-- Icon count -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="design design-component mr-1 w-3 text-center"></i>Počet:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-lg">
                                    {{ $font->icons_count }}
                                </div>
                            </div>

                            <!-- Tags count -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="design design-tag-outline mr-1 w-3 text-center"></i>Tags:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-lg">
                                    @php
                                        $tagsCount = 0;
                                        if (isset($font->icons_data) && is_array($font->icons_data)) {
                                            foreach ($font->icons_data as $icon) {
                                                if (isset($icon['tags']) && is_array($icon['tags'])) {
                                                    $tagsCount += count($icon['tags']);
                                                }
                                            }
                                        }
                                    @endphp
                                    {{ $tagsCount }}
                                </div>
                            </div>

                            <!-- CSS class -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="design design-css mr-1 w-3 text-center"></i>CSS:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-sm truncate font-mono">
                                    {{ $font->css_class }}
                                </div>
                            </div>

                            <!-- Route -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="design design-customization mr-1 w-3 text-center"></i>Route:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-sm truncate">
                                    {{ $font->route_name }}
                                </div>
                            </div>

                            <!-- File path -->
                            <div class="flex flex-col border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                                <div class="text-gray-500 dark:text-gray-400 flex items-center gant-modern text-xs">
                                    <i class="design design-woff mr-1 w-3 text-center"></i>Súbor:
                                </div>
                                <div class="text-gray-900 dark:text-white gant-modern-medium text-sm truncate font-mono">
                                    {{ $font->path }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-center">
                            <a href="{{ route('admin.fonts.manage', [$font]) }}" 
                               class="inline-flex items-center px-3 py-2 rounded-lg text-sm  text-gray-900 bg-slate-100 hover:bg-slate-600 dark:bg-slate-900 dark:text-slate-300 dark:hover:bg-gray-800 border border-solid border-gray-200 dark:border-gray-700 transition-all duration-200 gant-modern-medium">
                                <i class="design design-edit mr-2 opacity-70"></i>
                                 {{ $font->name }} Tags
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-gray-500 dark:text-gray-400">Neboli nájdené žiadne fonty</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
@endsection 