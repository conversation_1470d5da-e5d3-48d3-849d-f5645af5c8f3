@extends('layouts.application')

@section('title', 'Create Project')

@section('content')
<div class="w-full px-4 py-8">
    <!-- Set default values for variables that might be missing -->
    @php
        $needsUpdateCount = $needsUpdateCount ?? 0;
        $newUsersCount = $newUsersCount ?? 0;
    @endphp
    
    <!-- Admin Navigation -->
    <admin-navigation
        current-route="admin.projects"
        :needs-update-count="{{ $needsUpdateCount }}"
        :new-users-count="{{ $newUsersCount }}"
    ></admin-navigation>

    <!-- Project Creation Form -->
    <div class="max-w-5xl mx-auto">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl gant-modern-bold text-gray-900 dark:text-white">Vytvoriť nový projekt</h1>
                <p class="text-gray-500 dark:text-gray-400 mt-1">Vytvorte nový projekt a priradenie tímu</p>
            </div>
            <a href="{{ route('admin.projects') }}" class="inline-flex items-center px-4 py-2 rounded-lg gant-modern text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                <i class="design design-arrow-left mr-2"></i>
                Späť na zoznam
            </a>
        </div>

        <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 shadow-sm">
            <form action="{{ route('admin.projects.store') }}" method="POST" class="space-y-8">
                @csrf
                
                <!-- Project Name -->
                <div>
                    <label for="name" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Názov projektu</label>
                    <input type="text" name="name" id="name" placeholder="Názov projektu" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Názov projektu, ktorý bude zobrazený na karte</p>
                </div>
                
                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Popis</label>
                    <textarea name="description" id="description" rows="3" placeholder="Detailný popis projektu a jeho cieľov" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Stručný popis projektu, jeho funkcionality alebo cieľov</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Backend -->
                    <div>
                        <label for="backend" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Backend</label>
                        <input type="text" name="backend" id="backend" placeholder="Laravel, Node.js, Django..." class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Použité backend technológie</p>
                    </div>
                    
                    <!-- Frontend -->
                    <div>
                        <label for="frontend" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Frontend</label>
                        <input type="text" name="frontend" id="frontend" placeholder="Vue.js, React, Angular..." class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Použité frontend technológie</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Dátum začiatku</label>
                        <input type="date" name="start_date" id="start_date" value="{{ date('Y-m-d') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Plánovaný dátum začiatku projektu</p>
                    </div>
                    
                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Dátum ukončenia</label>
                        <input type="date" name="end_date" id="end_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Predpokladaný dátum ukončenia (voliteľné)</p>
                    </div>
                </div>
                
                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Status</label>
                    <select name="status" id="status" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        <option value="">Vyberte status</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status->slug }}">{{ $status->name }}</option>
                        @endforeach
                    </select>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Aktuálny stav projektu</p>
                </div>
                
                <!-- Team Members -->
                <div>
                    <label for="user_ids" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Členovia tímu</label>
                    <select name="user_ids[]" id="user_ids" multiple class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                        @endforeach
                    </select>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Podržte Ctrl/Cmd pre výber viacerých používateľov</p>
                </div>
                
                <!-- Fonts -->
                <div>
                    <label for="font_ids" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Ikony</label>
                    <select name="font_ids[]" id="font_ids" multiple class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        @foreach($fonts as $font)
                            <option value="{{ $font->id }}">{{ $font->name }}</option>
                        @endforeach
                    </select>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Podržte Ctrl/Cmd pre výber viacerých fontov</p>
                </div>
                
                <!-- Submit Button -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-5">
                    <div class="flex justify-end">
                        <button type="button" onclick="window.location.href='{{ route('admin.projects') }}'" class="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm gant-modern text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Zrušiť
                        </button>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm gant-modern-bold text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                            <i class="design design-save mr-2"></i>
                            Uložiť projekt
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection 