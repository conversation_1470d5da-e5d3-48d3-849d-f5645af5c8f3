@extends('layouts.application')

@section('title', 'Create Changelog Entry')

@section('content')
<div class="w-full px-4 py-8">
    <!-- Set default values for variables that might be missing -->
    @php
        $needsUpdateCount = $needsUpdateCount ?? 0;
        $newUsersCount = $newUsersCount ?? 0;
    @endphp
    
    <!-- Admin Navigation -->
    <admin-navigation
        current-route="admin.changelog"
        :needs-update-count="{{ $needsUpdateCount }}"
        :new-users-count="{{ $newUsersCount }}"
    ></admin-navigation>

    <!-- Changelog Creation Form -->
    <div class="max-w-5xl mx-auto">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl gant-modern-bold text-gray-900 dark:text-white">Vytvoriť nový changelog</h1>
                <p class="text-gray-500 dark:text-gray-400 mt-1">Zaznamenajte novú verziu a jej zmeny</p>
            </div>
            <a href="{{ route('admin.changelog') }}" class="inline-flex items-center px-4 py-2 rounded-lg gant-modern text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                <i class="design design-arrow-left mr-2"></i>
                Späť na zoznam
            </a>
        </div>

        <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 shadow-sm">
            <form action="{{ route('admin.changelog.store') }}" method="POST" class="space-y-8">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Verzia</label>
                        <input type="text" name="version" id="version" placeholder="napr. 1.0.0" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Verzia vydania (napr. 1.0.0, 2.1.3)</p>
                    </div>
                    
                    <!-- Release Date -->
                    <div>
                        <label for="release_date" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Dátum vydania</label>
                        <input type="date" name="release_date" id="release_date" value="{{ date('Y-m-d') }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Kedy bola táto verzia vydaná</p>
                    </div>
                </div>
                
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Názov</label>
                    <input type="text" name="title" id="title" placeholder="Názov novej verzie" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Stručný názov alebo titulok tejto verzie</p>
                </div>
                
                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Popis</label>
                    <textarea name="description" id="description" rows="3" placeholder="Detailný popis verzie a jej zmien" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Dlhší popis cieľov tejto verzie (voliteľné)</p>
                </div>
                
                <!-- Changes -->
                <div>
                    <label class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Zmeny</label>
                    <p class="mb-2 text-xs text-gray-500 dark:text-gray-400">Zoznam jednotlivých zmien v tejto verzii</p>
                    
                    <div id="changes-container" class="space-y-2 mb-3">
                        <div class="flex items-center gap-2">
                            <input type="text" name="changes[]" placeholder="Opravená chyba v..." class="flex-1 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <button type="button" onclick="removeChange(this)" class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors">
                                <i class="design design-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <button type="button" onclick="addChange()" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm gant-modern text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                        <i class="design design-plus mr-1"></i>
                        Pridať zmenu
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Order -->
                    <div>
                        <label for="order" class="block text-sm gant-modern-bold text-gray-700 dark:text-gray-300 mb-1">Poradie</label>
                        <input type="number" name="order" id="order" value="0" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Určuje poradie zobrazenia, vyššie hodnoty sa zobrazujú nižšie</p>
                    </div>
                    
                    <!-- Major Release -->
                    <div class="flex items-start pt-6">
                        <div class="flex items-center h-5">
                            <input type="checkbox" name="is_major" id="is_major" value="1" class="rounded border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="is_major" class="font-medium text-gray-700 dark:text-gray-300">Hlavná verzia</label>
                            <p class="text-gray-500 dark:text-gray-400">Označenie významných verzií s väčšími zmenami</p>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-5">
                    <div class="flex justify-end">
                        <button type="button" onclick="window.location.href='{{ route('admin.changelog') }}'" class="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm gant-modern text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Zrušiť
                        </button>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm gant-modern-bold text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                            <i class="design design-save mr-2"></i>
                            Uložiť changelog
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function addChange() {
        const container = document.getElementById('changes-container');
        const newChangeDiv = document.createElement('div');
        newChangeDiv.className = 'flex items-center gap-2';
        newChangeDiv.innerHTML = `
            <input type="text" name="changes[]" placeholder="Pridaná funkcionalita..." class="flex-1 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <button type="button" onclick="removeChange(this)" class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors">
                <i class="design design-trash"></i>
            </button>
        `;
        container.appendChild(newChangeDiv);
    }

    function removeChange(button) {
        const changeDiv = button.parentElement;
        changeDiv.remove();
    }
</script>
@endpush
@endsection 