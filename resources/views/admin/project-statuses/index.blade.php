@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Project Statuses</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $statuses = $statuses ?? collect([]);
        @endphp

        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.project-statuses"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8">
            <div>
                <h2 class="text-3xl lg:text-4xl gant-modern-bold text-gray-900 dark:text-white">Stavy projektov</h2>
                <div class="text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular">
                    <i class="design design-flag opacity-50 me-1"></i>Počet stavov: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ count($statuses) }}</span>
                </div>
            </div>
            <button 
                type="button" 
                onclick="document.getElementById('createStatusModal').classList.remove('hidden')"
                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            >
                <i class="design design-add mr-2"></i>
                Nový stav projektu
            </button>
        </div>

        @if($errors->any())
            <div class="p-4 mb-6 text-sm gant-modern-regular text-red-700 dark:text-red-400 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800" role="alert">
                <div class="flex items-center mb-2">
                    <i class="design design-warning mr-2 text-lg"></i>
                    <span class="font-semibold">Chyba pri spracovaní formulára</span>
                </div>
                <ul class="list-disc list-inside pl-2">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Project Statuses Table -->
        <div class="bg-white dark:bg-slate-950 rounded-lg shadow-sm overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-white">Všetky stavy projektov</h3>
                <p class="mt-1 text-sm gant-modern-regular text-gray-500 dark:text-gray-400">
                    Zoznam všetkých stavov, ktoré môžu mať projekty. Stavy je možné upravovať a mazať.
                </p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs uppercase bg-gray-50 dark:bg-gray-900/50 text-gray-700 dark:text-gray-400 gant-modern-bold">
                        <tr>
                            <th scope="col" class="px-6 py-3">Názov</th>
                            <th scope="col" class="px-6 py-3">Slug</th>
                            <th scope="col" class="px-6 py-3">Farba</th>
                            <th scope="col" class="px-6 py-3">Poradie</th>
                            <th scope="col" class="px-6 py-3">Počet projektov</th>
                            <th scope="col" class="px-6 py-3">Akcie</th>
                        </tr>
                    </thead>
                    <tbody id="statusTableBody">
                        @forelse($statuses as $status)
                            <tr class="bg-white dark:bg-slate-950 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900/50" data-status-id="{{ $status->id }}">
                                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white flex items-center gant-modern-medium">
                                    @if($status->icon)
                                        <i class="{{ $status->icon }} mr-2"></i>
                                    @endif
                                    {{ $status->name }}
                                </td>
                                <td class="px-6 py-4 font-mono text-xs">
                                    {{ $status->slug }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center gap-2">
                                        <div class="w-5 h-5 rounded" style="background-color: {{ $status->color }}"></div>
                                        <span class="text-xs font-mono">{{ $status->color }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    {{ $status->sort_order }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ $projectCounts[$status->id] ?? 0 }}
                                </td>
                                <td class="px-6 py-4 text-right">
                                    <div class="flex space-x-2">
                                        <button 
                                            type="button" 
                                            onclick="openEditModal({{ $status->id }})"
                                            class="text-blue-600 dark:text-blue-500 hover:text-blue-900 dark:hover:text-blue-400"
                                        >
                                            <i class="design design-edit text-lg"></i>
                                        </button>
                                        <form action="{{ route('admin.project-statuses.destroy', $status->id) }}" method="POST" onsubmit="return confirm('Ste si istý, že chcete odstrániť tento stav? Táto akcia sa nedá vrátiť.')">
                                            @csrf
                                            @method('DELETE')
                                            <button 
                                                type="submit" 
                                                class="text-red-600 dark:text-red-500 hover:text-red-900 dark:hover:text-red-400"
                                                {{ $projectCounts[$status->id] > 0 ? 'disabled' : '' }}
                                                title="{{ $projectCounts[$status->id] > 0 ? 'Nemožno vymazať stav, pretože je používaný v projektoch' : 'Vymazať stav' }}"
                                            >
                                                <i class="design design-trash text-lg {{ $projectCounts[$status->id] > 0 ? 'opacity-50' : '' }}"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr class="bg-white dark:bg-slate-950 border-b dark:border-gray-700">
                                <td colspan="7" class="px-6 py-4 text-center">
                                    <div class="flex flex-col items-center justify-center py-8">
                                        <i class="design design-flag text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                                        <p class="text-lg gant-modern-regular text-gray-500 dark:text-gray-400">Neboli nájdené žiadne stavy projektov</p>
                                        <p class="text-sm text-gray-400 dark:text-gray-500">Vytvorte nový stav tlačidlom vyššie</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Create Status Modal -->
        <div id="createStatusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Vytvoriť nový stav projektu</h3>
                        <button type="button" onclick="document.getElementById('createStatusModal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <i class="design design-close text-xl"></i>
                        </button>
                    </div>
                    
                    <form action="{{ route('admin.project-statuses.store') }}" method="POST">
                        @csrf
                        <div class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Názov stavu</label>
                                <input type="text" name="name" id="name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Slug sa vygeneruje automaticky z názvu.</p>
                            </div>
                            
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ikona (CSS trieda)</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                                        <i class="design design-code"></i>
                                    </span>
                                    <input type="text" name="icon" id="icon" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:text-white" placeholder="design-code">
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Zadajte CSS triedu ikony, napr. "design-code".</p>
                            </div>
                            
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Farba</label>
                                <div class="mt-1 flex items-center gap-3">
                                    <input type="color" name="color" id="color" class="h-10 w-10 border-gray-300 dark:border-gray-700 rounded shadow-sm" value="#6B7280">
                                    <input type="text" name="color_code" id="color_code" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:text-white" value="#6B7280" readonly>
                                </div>
                            </div>
                            
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Poradie</label>
                                <input type="number" name="sort_order" id="sort_order" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" value="0" min="0">
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="button" onclick="document.getElementById('createStatusModal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-500 focus:outline-none">
                                Zrušiť
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Vytvoriť
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Status Modal -->
        <div id="editStatusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Upraviť stav projektu</h3>
                        <button type="button" onclick="document.getElementById('editStatusModal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <i class="design design-close text-xl"></i>
                        </button>
                    </div>
                    
                    <form id="editStatusForm" action="" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="space-y-4">
                            <div>
                                <label for="edit_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Názov stavu</label>
                                <input type="text" name="name" id="edit_name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Pozor: zmena názvu zmení aj slug, čo môže ovplyvniť existujúce projekty.</p>
                            </div>
                            
                            <div>
                                <label for="edit_icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ikona (CSS trieda)</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
                                        <i id="edit_icon_preview" class="design design-code"></i>
                                    </span>
                                    <input type="text" name="icon" id="edit_icon" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:text-white" placeholder="design-code">
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Zadajte CSS triedu ikony, napr. "design-code".</p>
                            </div>
                            
                            <div>
                                <label for="edit_color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Farba</label>
                                <div class="mt-1 flex items-center gap-3">
                                    <input type="color" name="color" id="edit_color" class="h-10 w-10 border-gray-300 dark:border-gray-700 rounded shadow-sm">
                                    <input type="text" name="color_code" id="edit_color_code" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:text-white" readonly>
                                </div>
                            </div>
                            
                            <div>
                                <label for="edit_sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Poradie</label>
                                <input type="number" name="sort_order" id="edit_sort_order" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" min="0">
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="button" onclick="document.getElementById('editStatusModal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-500 focus:outline-none">
                                Zrušiť
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Uložiť zmeny
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Color picker sync
    document.getElementById('color').addEventListener('input', function() {
        document.getElementById('color_code').value = this.value;
    });
    
    document.getElementById('edit_color').addEventListener('input', function() {
        document.getElementById('edit_color_code').value = this.value;
    });
    
    // Icon preview
    document.getElementById('edit_icon').addEventListener('input', function() {
        const iconPreview = document.getElementById('edit_icon_preview');
        iconPreview.className = this.value;
    });
    
    // Edit modal functions
    function openEditModal(statusId) {
        // Find the status data in the table
        const row = document.querySelector(`tr[data-status-id="${statusId}"]`);
        if (!row) return;
        
        // Extract data from the row
        const name = row.querySelector('td:nth-child(1)').innerText.trim();
        const slug = row.querySelector('td:nth-child(2)').innerText.trim();
        const colorEl = row.querySelector('td:nth-child(3) div div');
        const color = window.getComputedStyle(colorEl).backgroundColor;
        const colorHex = row.querySelector('td:nth-child(3) span').innerText.trim();
        const sortOrder = row.querySelector('td:nth-child(4)').innerText.trim();
        
        // Get icon class if it exists
        let icon = '';
        const iconEl = row.querySelector('td:nth-child(1) i');
        if (iconEl) {
            // Get all classes except first one (which is the base 'design' class)
            const classes = Array.from(iconEl.classList);
            if (classes.length > 0) {
                icon = classes.join(' ');
            }
        }
        
        // Set form action
        document.getElementById('editStatusForm').action = `/admin/project-statuses/${statusId}`;
        
        // Fill in form data
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_icon').value = icon;
        document.getElementById('edit_icon_preview').className = icon;
        document.getElementById('edit_color').value = colorHex;
        document.getElementById('edit_color_code').value = colorHex;
        document.getElementById('edit_sort_order').value = sortOrder;
        
        // Show the modal
        document.getElementById('editStatusModal').classList.remove('hidden');
    }
</script>
@endpush 