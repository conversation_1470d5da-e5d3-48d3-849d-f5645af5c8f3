@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.tags"
            :needs-update-count="{{ $needsUpdateCount ?? 0 }}"
            :new-users-count="{{ $newUsersCount ?? 0 }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <h2 class="text-4xl lg:text-5xl gant-modern-bold text-slate-900 dark:text-white">Tag Management</h2>
            <a href="{{ route('admin.icons') }}" 
               class="inline-flex items-center px-4 py-3 rounded-lg gant-modern-bold text-sm text-slate-900 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-300 dark:hover:bg-slate-800 transition-all duration-200">
                <i class="design design-arrow-left mr-2"></i>
                Back to Icons
            </a>
        </div>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif


        <!-- Tag Manager Vue Component -->
        <div id="tag-manager-app" class="mb-8">
            <tag-manager></tag-manager>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Import Vue from the globally registered components in app.js
        // No need to use Vue.createApp since we're using the Vue instance from app.js
        const app = window.app || createApp({});
        
        // The TagManager component is already registered globally in app.js
        // so we just need to mount it
        if (!window.app) {
            app.mount('#tag-manager-app');
        }
    });
</script>
@endpush 