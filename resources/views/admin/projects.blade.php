@extends('layouts.application')

@section('content')
    @php
        // Helper function to adjust color opacity
        function colorOpacity($hex, $opacity = 1) {
            // Remove the "#" if it exists
            $hex = str_replace('#', '', $hex);
            
            // Convert hex to RGB
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
            
            // Return RGBA
            return "rgba($r, $g, $b, $opacity)";
        }
    @endphp
    
    <div class="px-6 lg:px-8 py-8" data-admin-panel="true">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Projects</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $projects = $projects ?? collect([]);
            $statuses = $statuses ?? collect([]);
        @endphp
        
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.projects"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif
        
        <!-- Form Validation Errors -->
        @if($errors->any())
            <div class="p-4 mb-6 text-sm gant-modern-regular text-red-700 dark:text-red-400 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800" role="alert">
                <div class="flex items-center mb-2">
                    <i class="design design-warning mr-2 text-lg"></i>
                    <span class="gant-modern-regular">Chyba pri spracovaní formulára</span>
                </div>
                <ul class="list-disc list-inside pl-2">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Header Section - Responsive Layout -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8">
            <div>
                <div class="flex items-center gap-3">
                <h2 class="text-3xl lg:text-4xl gant-modern-bold text-gray-900 dark:text-white">Projekty</h2>
                <button 
                    type="button" 
                    onclick="document.getElementById('createProjectModal').classList.remove('hidden')"
                    class="inline-flex items-center px-2 py-1.5 bg-slate-900 dark:bg-slate-200 text-white dark:text-slate-900 border rounded-md gant-modern-regular text-sm text-slate-900 hover:text-white hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                >
                    <i class="design design-add mr-2"></i>
                    Nový
                </button>
                </div>
                <div class="text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular">
                    <i class="design design-folder opacity-50 me-1"></i>Počet projektov: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ count($projects) }}</span>
                </div>
                
            </div>
            <div class="flex gap-3">
                <button
                    type="button"
                    onclick="window.location.href='{{ route('admin.project-statuses.index') }}'"
                    class="inline-flex items-center px-4 py-2 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 rounded-md gant-modern-regular text-sm text-slate-600 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
                >
                    <i class="design design-customization mr-2"></i>
                    Nastavenia
                </button>

            </div>
        </div>

        <!-- Projects Table - Vue Component -->
        <projects-table :projects="{{ json_encode($projects, JSON_PARTIAL_OUTPUT_ON_ERROR) }}"></projects-table>

        <!-- Mobile Card View -->
        <div class="lg:hidden space-y-4">
            @forelse($projects as $project)
                <div class="bg-white dark:bg-gray-900/30 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-white">
                            <a href="{{ route('admin.projects.show', $project) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                {{ $project->name }}
                            </a>
                        </h3>
                        <div class="flex items-center space-x-2">
                            <button 
                                type="button" 
                                onclick="openEditModal({{ json_encode($project) }}, {{ json_encode($project->fonts->pluck('id')) }}, {{ json_encode($project->users->pluck('id')) }})"
                                class="text-blue-600 dark:text-blue-500 hover:text-blue-900 dark:hover:text-blue-400"
                            >
                                <i class="design design-edit text-lg"></i>
                            </button>
                            <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" onsubmit="return confirm('Ste si istý, že chcete odstrániť tento projekt?');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 dark:text-red-500 hover:text-red-900 dark:hover:text-red-400">
                                    <i class="design design-trash text-lg"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="text-gray-500 dark:text-gray-400">Popis:</span>
                            <p class="text-gray-900 dark:text-gray-300">{{ Str::limit($project->description, 100) ?: '-' }}</p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Backend:</span>
                                <p class="text-gray-900 dark:text-gray-300">{{ $project->backend !== null ? $project->backend : '-' }}</p>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Frontend:</span>
                                <p class="text-gray-900 dark:text-gray-300">{{ $project->frontend !== null ? $project->frontend : '-' }}</p>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Deployed:</span>
                                <p class="text-gray-900 dark:text-gray-300">{{ $project->start_date ? $project->start_date->format('d.m.Y') : '-' }}</p>
                            </div>
                            <div>
                                <span class="text-gray-500 dark:text-gray-400">Upgrade:</span>
                                <p class="text-gray-900 dark:text-gray-300">{{ $project->end_date ? $project->end_date->format('d.m.Y') : '-' }}</p>
                            </div>
                        </div>
                        
                        <div>
                            <span class="text-gray-500 dark:text-gray-400">Stav:</span>
                            @if($project->projectStatus)
                                <span class="px-3 py-1 text-xs rounded-full inline-flex items-center w-auto" 
                                      style="background-color: {{ $project->projectStatus->color }}; color: white;">
                                    @if($project->projectStatus->icon)
                                        <i class="{{ $project->projectStatus->icon }} mr-1.5 opacity-80"></i>
                                    @endif
                                    {{ $project->projectStatus->name }}
                                </span>
                            @else
                                <span class="px-3 py-1 text-xs rounded-full bg-gray-600 text-white inline-flex items-center w-auto">
                                    <i class="design design-flag mr-1.5 opacity-80"></i>
                                    {{ $project->status }}
                                </span>
                            @endif
                        </div>
                        
                        <div>
                            <span class="text-gray-500 dark:text-gray-400">Fonty:</span>
                            <div class="flex flex-wrap gap-1 mt-1">
                                @forelse($project->fonts as $font)
                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">
                                        {{ $font->name }}
                                    </span>
                                @empty
                                    <span class="text-gray-500 dark:text-gray-400">-</span>
                                @endforelse
                            </div>
                        </div>
                        
                        <div>
                            <span class="text-gray-500 dark:text-gray-400">Používatelia:</span>
                            <div class="flex flex-wrap gap-1 mt-1">
                                @forelse($project->users as $user)
                                    <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">
                                        {{ $user->name }} {{ $user->surname }}
                                    </span>
                                @empty
                                    <span class="text-gray-500 dark:text-gray-400">-</span>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="bg-white dark:bg-gray-900/30 rounded-lg border border-gray-200 dark:border-gray-700 p-4 text-center">
                    <p class="text-gray-500 dark:text-gray-400">Nenašli sa žiadne projekty</p>
                </div>
            @endforelse
        </div>

        <!-- Create Project Modal -->
        <div id="createProjectModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Vytvoriť nový projekt</h3>
                        <button type="button" onclick="document.getElementById('createProjectModal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <i class="design design-close text-xl"></i>
                        </button>
                    </div>
                    
                    <form action="{{ route('admin.projects.store') }}" method="POST">
                        @csrf
                        <div class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Názov projektu</label>
                                <input type="text" name="name" id="name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                            </div>
                            
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Popis</label>
                                <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white"></textarea>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="backend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Backend</label>
                                    <input type="text" name="backend" id="backend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                                
                                <div>
                                    <label for="frontend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Frontend</label>
                                    <input type="text" name="frontend" id="frontend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Deployed</label>
                                    <input type="date" name="start_date" id="start_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                                
                                <div>
                                    <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upgrade</label>
                                    <input type="date" name="end_date" id="end_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                            </div>
                            
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stav</label>
                                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status->slug }}">{{ $status->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div>
                                <label for="font_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradené fonty</label>
                                <select name="font_ids[]" id="font_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                                    @foreach($fonts as $font)
                                        <option value="{{ $font->id }}">{{ $font->name }}</option>
                                    @endforeach
                                </select>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých fontov</p>
                            </div>
                            
                            <div>
                                <label for="user_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradení používatelia</label>
                                <select name="user_ids[]" id="user_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} {{ $user->surname }} ({{ $user->login }})</option>
                                    @endforeach
                                </select>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých používateľov</p>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="button" onclick="document.getElementById('createProjectModal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-500 focus:outline-none">
                                Zrušiť
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Vytvoriť
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Edit Project Modal -->
        <div id="editProjectModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Upraviť projekt</h3>
                        <button type="button" onclick="document.getElementById('editProjectModal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                            <i class="design design-close text-xl"></i>
                        </button>
                    </div>
                    
                    <form id="editProjectForm" action="" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="space-y-4">
                            <div>
                                <label for="edit_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Názov projektu</label>
                                <input type="text" name="name" id="edit_name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                            </div>
                            
                            <div>
                                <label for="edit_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Popis</label>
                                <textarea name="description" id="edit_description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white"></textarea>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="edit_backend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Backend</label>
                                    <input type="text" name="backend" id="edit_backend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                                
                                <div>
                                    <label for="edit_frontend" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Frontend</label>
                                    <input type="text" name="frontend" id="edit_frontend" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="edit_start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Deployed</label>
                                    <input type="date" name="start_date" id="edit_start_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                                
                                <div>
                                    <label for="edit_end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Upgrade</label>
                                    <input type="date" name="end_date" id="edit_end_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white">
                                </div>
                            </div>
                            
                            <div>
                                <label for="edit_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stav</label>
                                <select name="status" id="edit_status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" required>
                                    @foreach($statuses as $status)
                                        <option value="{{ $status->slug }}">{{ $status->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div>
                                <label for="edit_font_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradené fonty</label>
                                <select name="font_ids[]" id="edit_font_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                                    @foreach($fonts as $font)
                                        <option value="{{ $font->id }}">{{ $font->name }}</option>
                                    @endforeach
                                </select>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých fontov</p>
                            </div>
                            
                            <div>
                                <label for="edit_user_ids" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priradení používatelia</label>
                                <select name="user_ids[]" id="edit_user_ids" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:text-white" multiple>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }} {{ $user->surname }} ({{ $user->login }})</option>
                                    @endforeach
                                </select>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Podržte Ctrl (alebo Cmd na Mac) pre výber viacerých používateľov</p>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="button" onclick="document.getElementById('editProjectModal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-500 focus:outline-none">
                                Zrušiť
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md font-medium text-sm text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Uložiť zmeny
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // Make function globally available for the Vue component to use
    window.openEditModal = function(project, fontIds, userIds) {

        
        // Set form action
        document.getElementById('editProjectForm').action = `/admin/projects/${project.id}`;
        
        // Fill in form data
        document.getElementById('edit_name').value = project.name || '';
        document.getElementById('edit_description').value = project.description || '';
        document.getElementById('edit_start_date').value = formatDate(project.start_date);
        document.getElementById('edit_end_date').value = formatDate(project.end_date);
        document.getElementById('edit_status').value = project.status || '';
        
        // Explicitly handle backend and frontend values with strict null checks
        document.getElementById('edit_backend').value = 
            (project.backend === null || project.backend === undefined) ? '' : project.backend;
        document.getElementById('edit_frontend').value = 
            (project.frontend === null || project.frontend === undefined) ? '' : project.frontend;
        
        // Set selected fonts
        const fontSelect = document.getElementById('edit_font_ids');
        for (let i = 0; i < fontSelect.options.length; i++) {
            fontSelect.options[i].selected = fontIds.includes(parseInt(fontSelect.options[i].value));
        }
        
        // Set selected users
        const userSelect = document.getElementById('edit_user_ids');
        for (let i = 0; i < userSelect.options.length; i++) {
            userSelect.options[i].selected = userIds.includes(parseInt(userSelect.options[i].value));
        }
        
        // Show the modal
        document.getElementById('editProjectModal').classList.remove('hidden');
    }
</script>
@endpush 