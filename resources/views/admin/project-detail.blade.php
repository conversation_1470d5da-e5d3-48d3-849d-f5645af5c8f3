@extends('layouts.application')

@section('title', 'Project Detail')

@section('content')
@php
    // Helper function to adjust color opacity
    function colorOpacity($hex, $opacity = 1) {
        // Remove the "#" if it exists
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Return RGBA
        return "rgba($r, $g, $b, $opacity)";
    }
@endphp

<!-- Pattern SVG Background -->
<div class="fixed inset-0 pointer-events-none">
    <svg class="absolute inset-0 w-full h-full text-gray-900/[0.07] dark:text-gray-100/[0.08]" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-3)" />
    </svg>
</div>

<!-- Content Container -->
<div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8 relative">
    <div class="w-full max-w-5xl mx-auto">
        <!-- Back to Projects Button -->
        <div class="mb-6">
            <a href="{{ route('admin.projects') }}" class="inline-flex items-center px-3 py-2 text-sm gant-modern-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-950 rounded-md border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-900 transition-colors">
                <i class="design design-arrow-left mr-2"></i>
                Späť na projekty
            </a>
        </div>

        <div class="bg-white dark:bg-slate-950 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
            <!-- Project Header -->
            <section class="border-b border-gray-200 dark:border-gray-700/50 p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                    <!-- Project Identity - Left Side -->
                    <div class="flex items-center gap-6">
                        <div class="h-16 w-16 flex items-center justify-center rounded-full text-2xl"
                             @if($project->projectStatus)
                                style="background-color: {{ $project->projectStatus->color }}; color: white;"
                             @else
                                class="bg-gray-600 text-white"
                             @endif
                        >
                            @if($project->projectStatus && $project->projectStatus->icon)
                                <i class="{{ $project->projectStatus->icon }}"></i>
                            @else
                                <i class="design design-flag"></i>
                            @endif
                        </div>
                        <div>
                            <h2 class="text-2xl gant-modern-bold text-gray-900 dark:text-white">{{ $project->name }}</h2>
                            <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-lg">
                                {{ Str::limit($project->description, 60) }}
                            </p>
                            <div class="mt-2 flex items-center gap-2">
                                <span class="px-3 py-1 text-xs rounded-full inline-flex items-center w-auto"
                                     @if($project->projectStatus)
                                        style="background-color: {{ $project->projectStatus->color }}; color: white;"
                                     @else
                                        class="bg-gray-600 text-white"
                                     @endif
                                >
                                    @if($project->projectStatus && $project->projectStatus->icon)
                                        <i class="{{ $project->projectStatus->icon }} mr-1.5 opacity-80"></i>
                                        {{ $project->projectStatus->name }}
                                    @else
                                        <i class="design design-flag mr-1.5 opacity-80"></i>
                                        {{ $project->status }}
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Project Actions - Right Side -->
                    <div class="flex flex-wrap items-center gap-3">
                        <button 
                            type="button" 
                            onclick="openEditModal({{ json_encode($project) }}, {{ json_encode($project->fonts->pluck('id')) }}, {{ json_encode($project->users->pluck('id')) }})"
                            class="inline-flex items-center gap-2 px-4 py-2 text-base gant-modern-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/10 rounded-md border border-blue-200 dark:border-blue-800/30 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                        >
                            <i class="design design-edit text-blue-500 dark:text-blue-400"></i>
                            <span>Upraviť projekt</span>
                        </button>
                        
                        <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" onsubmit="return confirm('Ste si istý, že chcete odstrániť tento projekt?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="inline-flex items-center gap-2 px-4 py-2 text-base gant-modern-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/10 rounded-md border border-red-200 dark:border-red-800/30 hover:bg-red-100 dark:hover:bg-red-900/20">
                                <i class="design design-trash text-red-500 dark:text-red-400"></i>
                                <span>Odstrániť</span>
                            </button>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Project Content -->
            <div class="p-6 space-y-6">
                <!-- Row 1: Project Info -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Backend -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Backend</div>
                        <div class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $project->backend ?: '-' }}</div>
                    </div>
                    
                    <!-- Column 2: Frontend -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Frontend</div>
                        <div class="text-gray-900 dark:text-white font-mono gant-modern-bold text-2xl">{{ $project->frontend ?: '-' }}</div>
                    </div>
                </div>

                <!-- Row 2: Dates -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                    <!-- Column 1: Deployed Date -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Deployed</div>
                        <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                            @if($project->start_date)
                                <div class="flex items-center gant-modern-bold">
                                    <i class="claims claims-calendar-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                    {{ $project->start_date->format('d.m.Y') }}
                                </div>
                            @else
                                <span class="text-gray-400 dark:text-gray-600 gant-modern-bold">-</span>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Column 2: Upgrade Date -->
                    <div class="group">
                        <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Upgrade</div>
                        <div class="text-gray-900 dark:text-white gant-modern-bold text-2xl">
                            @if($project->end_date)
                                <div class="flex items-center gant-modern-bold">
                                    <i class="claims claims-calendar-outline mr-1.5 text-gray-500 dark:text-gray-700"></i>
                                    {{ $project->end_date->format('d.m.Y') }}
                                </div>
                            @else
                                <span class="text-gray-400 dark:text-gray-600 gant-modern-bold">-</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Row 3: Description -->
                <div class="pt-4 border-t border-gray-200 dark:border-gray-700/50">
                    <div class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-3">Popis projektu</div>
                    <div class="bg-gray-50 dark:bg-slate-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="text-gray-800 dark:text-gray-200 gant-modern-regular">
                            {{ $project->description ?: 'Bez popisu' }}
                        </div>
                    </div>
                </div>

                <!-- Row 4: Users -->
                <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400">Používatelia projektu</h3>
                        <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">{{ $project->users->count() }}</span>
                    </div>
                    
                    @if($project->users->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                            @foreach($project->users as $user)
                                <div class="flex items-center gap-3 p-3 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg">
                                    <x-avatar :user="$user" size="sm" :ring="true" />
                                    <div class="flex flex-col">
                                        <span class="text-base gant-modern-medium text-gray-900 dark:text-white">{{ $user->name }} {{ $user->surname }}</span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">{{ $user->login }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 dark:text-gray-400 italic py-2">Žiadni používatelia nie sú priradení k tomuto projektu</p>
                    @endif
                </div>

                <!-- Row 5: Fonts -->
                <div class="pt-6 border-t border-gray-200 dark:border-gray-700/50">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400">Fonty</h3>
                        <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">{{ $project->fonts->count() }}</span>
                    </div>
                    
                    @if($project->fonts->count() > 0)
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            @foreach($project->fonts as $font)
                                <div class="flex flex-col items-center justify-center p-4 bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-gray-700 rounded-lg">
                                    @php
                                        $iconClass = '';
                                        
                                        // Special cases
                                        if ($font->name === 'Order Group') {
                                            $iconClass = 'ordergroup ordergroup-thumbnail';
                                        } elseif ($font->name === 'Category') {
                                            $iconClass = 'vermont-category vermont-category-thumbnail';
                                        } elseif ($font->name === 'CDB' || $font->name === 'Central Database') {
                                            $iconClass = 'cdb cdb-thumbnail';
                                        } elseif ($font->name === 'Eshop') {
                                            $iconClass = 'vermont-icon vermont-icon-thumbnail';
                                        } else {
                                            $iconClass = strtolower($font->name) . ' ' . strtolower($font->name) . '-thumbnail';
                                        }
                                    @endphp
                                    
                                    <i class="{{ $iconClass }} text-3xl text-gray-700 dark:text-gray-400 mb-2"></i>
                                    <span class="text-sm text-center gant-modern-medium text-gray-900 dark:text-white">{{ $font->name }}</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 dark:text-gray-400 italic py-2">Žiadne fonty nie sú priradené k tomuto projektu</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Project Modal -->
@include('admin.partials.edit-project-modal', ['project' => $project, 'fonts' => $fonts, 'users' => $users, 'statuses' => $statuses])
@endsection

@push('scripts')
<script>
    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // Make function globally available for the Vue component to use
    window.openEditModal = function(project, fontIds, userIds) {
        
        // Set form action
        document.getElementById('editProjectForm').action = `/admin/projects/${project.id}`;
        
        // Fill in form data
        document.getElementById('edit_name').value = project.name || '';
        document.getElementById('edit_description').value = project.description || '';
        document.getElementById('edit_start_date').value = formatDate(project.start_date);
        document.getElementById('edit_end_date').value = formatDate(project.end_date);
        document.getElementById('edit_status').value = project.status || '';
        
        // Explicitly handle backend and frontend values with strict null checks
        document.getElementById('edit_backend').value = 
            (project.backend === null || project.backend === undefined) ? '' : project.backend;
        document.getElementById('edit_frontend').value = 
            (project.frontend === null || project.frontend === undefined) ? '' : project.frontend;
        
        // Set selected fonts
        const fontSelect = document.getElementById('edit_font_ids');
        for (let i = 0; i < fontSelect.options.length; i++) {
            fontSelect.options[i].selected = fontIds.includes(parseInt(fontSelect.options[i].value));
        }
        
        // Set selected users
        const userSelect = document.getElementById('edit_user_ids');
        for (let i = 0; i < userSelect.options.length; i++) {
            userSelect.options[i].selected = userIds.includes(parseInt(userSelect.options[i].value));
        }
        
        // Show the modal
        document.getElementById('editProjectModal').classList.remove('hidden');
    }
</script>
@endpush 