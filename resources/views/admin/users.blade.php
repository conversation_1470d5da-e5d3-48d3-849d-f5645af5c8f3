@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8" data-admin-panel="true">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Users</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $users = $users ?? collect([]);
        @endphp
        
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.users"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Header Section - Responsive Layout -->
        <div class="flex flex-col gap-4 mb-6 lg:mb-8">
            <div>
                <h2 class="text-3xl lg:text-4xl gant-modern-bold text-gray-900 dark:text-white">Používatelia</h2>
                <div class="text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular">
                    <i class="design design-user opacity-50 me-1"></i>Počet používateľov: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ count($users) }}</span>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif

        <!-- Users Table - Vue Component -->
        <users-table 
            :users="{{ $users->toJson(JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
            :admin-user-logins="{{ json_encode(config('vermont.admin_user_logins'), JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
            :developer-user-logins="{{ json_encode(config('vermont.developer_user_logins'), JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
            :dev-user-logins="{{ json_encode(config('vermont.dev_user_logins'), JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
            :pm-user-logins="{{ json_encode(config('vermont.pm_user_logins'), JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
            :tibi-user-logins="{{ json_encode(config('vermont.tibi_user_logins'), JSON_PARTIAL_OUTPUT_ON_ERROR) }}"
        ></users-table>

        <!-- Mobile Card View -->
        <div class="lg:hidden space-y-4">
            @forelse($users as $user)
                <div 
                    onclick="window.location='{{ route('admin.users.detail', $user) }}'"
                    class="bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50 shadow p-4 space-y-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/70 transition-colors duration-150"
                >
                    <!-- User Header -->
                    <div class="flex items-center gap-3">
                        <!-- Add Avatar component -->
                        <avatar :user="{{ json_encode($user) }}" size="sm" :ring="false"></avatar>
                        <div class="flex-grow">
                            <span class="relative font-mono gant-modern-bold text-gray-900 dark:text-white">
                                {{ $user->login }}
                                @if($user->created_at && $user->created_at->diffInDays(now()) < 7)
                                    <span class="absolute -top-1 -right-2 inline-flex h-2 w-2 rounded-full bg-indigo-500" title="Nový používateľ"></span>
                                @endif
                            </span>
                        </div>
                        <!-- Role Badge -->
                        <div>
                            @if(in_array($user->login, config('vermont.admin_user_logins')))
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                                    <i class="design design-vermont-intranet-pass mr-1 opacity-70"></i> Admin
                                </span>
                            @elseif(in_array($user->login, config('vermont.developer_user_logins')))
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                    <i class="design design-bracket3 mr-1 opacity-70"></i> Developer
                                </span>
                            @elseif(in_array($user->login, config('vermont.dev_user_logins')))
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                    <i class="design design-bracket3 mr-1 opacity-70"></i> Dev
                                </span>
                            @elseif(in_array($user->login, config('vermont.tibi_user_logins')))
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                    <i class="design design-coffee mr-1 opacity-70"></i> Tibi
                                </span>
                            @elseif(in_array($user->login, config('vermont.pm_user_logins')))
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
                                    <i class="design design-race mr-1 opacity-70"></i> PM
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                    <i class="design design-vermont-ucko mr-1 opacity-70"></i> Používateľ
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- User Info -->
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="design design-user mr-2 opacity-70"></i>
                            {{ $user->name }} {{ $user->surname }}
                        </div>
                        @if($user->birthday)
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="claims claims-calendar-outline mr-2 opacity-70"></i>
                            {{ $user->birthday->format('d.m.Y') }}
                        </div>
                        @endif
                        @if($user->email)
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="design design-email mr-2 opacity-70"></i>
                            {{ $user->email }}
                        </div>
                        @endif
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="design design-service mr-2 opacity-70"></i>
                            <span class="{{ count($user->projects ?? []) > 0 ? 'text-gray-600 dark:text-gray-400' : 'text-gray-400 dark:text-gray-600' }}">
                                Projekty: {{ count($user->projects ?? []) > 0 ? count($user->projects) . ' ' . (count($user->projects) == 1 ? 'projekt' : (count($user->projects) >= 2 && count($user->projects) <= 4 ? 'projekty' : 'projektov')) : '-' }}
                            </span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="design design-package mr-2 opacity-70"></i>
                            <span class="{{ count($user->fonts ?? []) > 0 ? 'text-gray-600 dark:text-gray-400' : 'text-gray-400 dark:text-gray-600' }}">
                                Fonty: {{ count($user->fonts ?? []) > 0 ? count($user->fonts) . ' ' . (count($user->fonts) == 1 ? 'font' : (count($user->fonts) >= 2 && count($user->fonts) <= 4 ? 'fonty' : 'fontov')) : '-' }}
                            </span>
                        </div>
                    </div>

                    <!-- Status and Dates -->
                    <div class="pt-2 border-t border-gray-100 dark:border-gray-700 space-y-2">
                        <!-- Status -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">Status:</span>
                            @if($user->active)
                                <div class="flex items-center gap-2">
                                    <div class="w-2 h-2 rounded-full bg-green-500"></div>
                                    <span class="text-xs gant-modern-medium text-gray-600 dark:text-gray-400">Aktívny</span>
                                </div>
                            @else
                                <div class="flex items-center gap-2">
                                    <div class="w-2 h-2 rounded-full bg-red-500"></div>
                                    <span class="text-xs gant-modern-medium text-gray-600 dark:text-gray-400">Neaktívny</span>
                                </div>
                            @endif
                        </div>

                        <!-- Last Login -->
                        <div class="flex items-center text-gray-600 dark:text-gray-400">
                            <i class="claims claims-clock-outline mr-1.5 opacity-70"></i>
                            {{ \Carbon\Carbon::parse($user->last_login_at)->format('d.m.Y H:i') }}
                        </div>
                    </div>
                </div>
            @empty
                <div class="bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50 shadow p-8 text-center">
                    <div class="flex flex-col items-center justify-center">
                        <i class="design design-user text-4xl mb-3 opacity-30"></i>
                        <p class="text-lg gant-modern-medium text-gray-900 dark:text-white">Neboli nájdení žiadni používatelia</p>
                        <p class="text-sm mt-1 gant-modern-regular text-gray-500 dark:text-gray-400">Používatelia sa zobrazia po prihlásení do systému</p>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
@endsection

@push('scripts')
<!-- You can remove the old search script as it's handled by the Vue component now -->
@endpush
