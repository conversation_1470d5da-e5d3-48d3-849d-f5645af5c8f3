@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading Activities</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $totalActivities = $totalActivities ?? 0;
            $fonts = $fonts ?? collect([]);
        @endphp
        
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.activities"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
        ></admin-navigation>

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
                <h2 class="text-4xl lg:text-5xl gant-modern-bold text-gray-900 dark:text-white">Nedávna aktivita</h2>
                <div class="text-gray-500 dark:text-gray-400">
                    <i class="wms wms-browse-history-outline opacity-50 me-1"></i>Celkový počet aktivít: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ $totalActivities }}</span>
                </div>
            </div>
        </div>
        
        <!-- Activities Container -->
        <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl mb-6 min-h-[70vh]">
            <!-- Pattern SVG - Applied to entire container -->
            <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                        <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#pattern-3)" />
            </svg>

            <!-- Activities List -->
            <div class="relative z-10 space-y-4">
                <!-- Icons Update Activity -->
                @php
                    $hasIconsUpdateActivity = false;
                    $latestIconUpdate = null;
                    
                    if (isset($fonts) && is_countable($fonts) && count($fonts) > 0) {
                        foreach ($fonts as $font) {
                            if (isset($font->updated_at)) {
                                if (!$latestIconUpdate || $font->updated_at > $latestIconUpdate) {
                                    $latestIconUpdate = $font->updated_at;
                                    $hasIconsUpdateActivity = true;
                                }
                            }
                        }
                    }
                @endphp
                
                @if($hasIconsUpdateActivity && $latestIconUpdate)
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 max-w-2xl mx-auto">
                        <div class="flex items-start gap-4">
                            <div class="p-3 bg-gray-900 dark:bg-gray-200 rounded-full flex-shrink-0 w-12 h-12 flex items-center justify-center">
                                <i class="design design-changes text-lg text-gray-100 dark:text-gray-900"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Aktualizácia ikon</h3>
                                    <a href="{{ route('admin.icons') }}" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                        Spravovať ikony
                                        <i class="design design-arrow-right ml-1"></i>
                                    </a>
                                </div>
                                <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 my-3"></div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                        <span class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">Posledná aktualizácia</span>
                                        <div class="flex items-center mt-1">
                                            <i class="wms wms-calendar-clock text-gray-400 dark:text-gray-500 mr-1.5"></i>
                                            <p class="text-gray-900 dark:text-white gant-modern-medium">
                                                {{ \Carbon\Carbon::parse($latestIconUpdate)->format('d.m.Y H:i') }}
                                            </p>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <i class="wms wms-clock-outline opacity-75 mr-1"></i>
                                            {{ \Carbon\Carbon::parse($latestIconUpdate)->diffForHumans() }}
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <span class="text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400">Počet fontov</span>
                                        <div class="flex items-center mt-1">
                                            <i class="design design-palette text-gray-400 dark:text-gray-500 mr-1.5"></i>
                                            <p class="text-gray-900 dark:text-white gant-modern-medium">
                                                {{ isset($totalIcons) ? $totalIcons : (isset($fonts) && is_countable($fonts) ? count($fonts) : 0) }}
                                            </p>
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <span class="px-2 py-0.5 rounded-md bg-green-50 text-green-600 dark:bg-green-900/30 dark:text-green-300">
                                                <i class="design design-circle-checked opacity-75 mr-1"></i>
                                                Zdrojové súbory: {{ isset($fonts) && is_countable($fonts) ? count($fonts) : 0 }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                
                <!-- Fonts Needing Update Activity -->
                @if(isset($needsUpdateCount) && $needsUpdateCount > 0)
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-red-200 dark:border-red-700/30 p-6 transition-all duration-200 max-w-2xl mx-auto">
                        <div class="flex items-start gap-4">
                            <div class="p-3 bg-red-600 rounded-full flex-shrink-0 w-12 h-12 flex items-center justify-center">
                                <i class="wms wms-alert text-lg text-gray-100"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Fonty potrebujú aktualizáciu</h3>
                                    <a href="{{ route('admin.icons') }}" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                        Vyriešiť
                                        <i class="design design-arrow-right ml-1"></i>
                                    </a>
                                </div>
                                <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 my-3"></div>
                                
                                <div class="mt-2">
                                    <p class="text-gray-900 dark:text-white">{{ $needsUpdateCount }} {{ $needsUpdateCount == 1 ? 'font potrebuje' : 'fonty potrebujú' }} aktualizáciu</p>
                                    
                                    <div class="mt-3 space-y-3">
                                        @php
                                            $fontsNeedingUpdate = [];
                                            if (isset($fonts) && is_countable($fonts)) {
                                                foreach ($fonts as $font) {
                                                    if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                                                        $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                                                        
                                                        if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                                                            $fontsNeedingUpdate[] = $font;
                                                        }
                                                    }
                                                }
                                            }
                                        @endphp
                                        
                                        @foreach($fontsNeedingUpdate as $font)
                                            <div class="bg-red-50 dark:bg-red-900/10 rounded-lg p-3 border border-red-100 dark:border-red-900/20">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <i class="{{ $font->css_class }} {{ $font->css_class }}-arrow-down-left text-lg text-red-600 dark:text-red-400 mr-2"></i>
                                                        <span class="text-gray-900 dark:text-white gant-modern-bold">{{ $font->name }}</span>
                                                    </div>
                                                    <a href="{{ route('admin.update_font_icons', $font) }}" class="inline-flex items-center px-2 py-1 rounded-md text-xs gant-modern-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 transition-all duration-200">
                                                        Aktualizovať
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    
                                    <div class="text-gray-500 dark:text-gray-400 mt-4 text-sm">
                                        Aktualizácie fontov sú potrebné, keď sa CSS súbory zmenia. Kliknutím na "Aktualizovať" sa aktualizujú ikony.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                
                <!-- New Users Activity -->
                @if(isset($newUsersCount) && $newUsersCount > 0)
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-6 transition-all duration-200 max-w-2xl mx-auto">
                        <div class="flex items-start gap-4">
                            <div class="p-3 bg-blue-600 rounded-full flex-shrink-0 w-12 h-12 flex items-center justify-center">
                                <i class="design design-user-outline text-lg text-gray-100"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white">Noví používatelia</h3>
                                    <a href="{{ route('admin.users') }}" class="inline-flex items-center px-3 py-1.5 rounded-lg gant-modern-medium text-sm text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800/50 dark:text-gray-300 dark:hover:bg-gray-700 transition-all duration-200">
                                        Zobraziť používateľov
                                        <i class="design design-arrow-right ml-1"></i>
                                    </a>
                                </div>
                                <div class="w-full h-px bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 my-3"></div>
                                
                                <div class="mt-2">
                                    <p class="text-gray-900 dark:text-white">{{ $newUsersCount }} {{ $newUsersCount == 1 ? 'nový používateľ' : 'noví používatelia' }} za posledných 7 dní</p>
                                    
                                    <div class="mt-4">
                                        @php
                                            $recentUsers = [];
                                            try {
                                                $recentUsers = \App\Models\User::where('created_at', '>=', now()->subDays(7))
                                                    ->orderBy('created_at', 'desc')
                                                    ->limit(5)
                                                    ->get();
                                            } catch (\Exception $e) {
                                                \Log::error('Error fetching recent users: ' . $e->getMessage());
                                            }
                                        @endphp
                                        
                                        @if(count($recentUsers) > 0)
                                            <div class="space-y-3">
                                                @foreach($recentUsers as $user)
                                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/30 rounded-lg">
                                                        <div class="flex items-center">
                                                            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mr-3">
                                                                <span class="text-gray-500 dark:text-gray-300 text-sm">{{ strtoupper(substr($user->name ?? 'U', 0, 1)) }}</span>
                                                            </div>
                                                            <div>
                                                                <div class="text-gray-900 dark:text-white gant-modern-medium">{{ $user->name }}</div>
                                                                <div class="text-gray-500 dark:text-gray-400 text-xs">{{ $user->login }}</div>
                                                            </div>
                                                        </div>
                                                        <div class="text-gray-500 dark:text-gray-400 text-xs">
                                                            {{ $user->created_at ? $user->created_at->format('d.m.Y') : 'N/A' }}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @else
                                            <div class="p-4 bg-gray-50 dark:bg-gray-900/30 rounded-lg text-center text-gray-500 dark:text-gray-400">
                                                Žiadni nedávno pridaní používatelia
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                
                <!-- No Activities -->
                @if((!isset($fonts) || count($fonts) == 0) && 
                   (!isset($newUsersCount) || $newUsersCount == 0) && 
                   (!isset($needsUpdateCount) || $needsUpdateCount == 0))
                    <div class="bg-white dark:bg-slate-950 rounded-lg border border-gray-200 dark:border-gray-700/50 p-8 max-w-2xl mx-auto text-center">
                        <i class="wms wms-browse-history-outline text-6xl text-gray-300 dark:text-gray-700 mb-4"></i>
                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-white mb-2">Žiadna nedávna aktivita</h3>
                        <p class="text-gray-500 dark:text-gray-400">Momentálne nie sú žiadne aktivity na zobrazenie</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection 