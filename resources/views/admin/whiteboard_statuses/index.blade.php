@extends('layouts.application')

@section('title', 'Manage Whiteboard Statuses')

@section('content')
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Manage Whiteboard Card Statuses
        </h1>
        
        <button type="button" onclick="document.getElementById('addStatusModal').classList.remove('hidden')" class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-slate-800 dark:bg-slate-700 border border-transparent rounded-md font-medium text-sm text-white hover:bg-slate-700 dark:hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add New Status
        </button>
    </div>
    
    <!-- Flash Messages -->
    @if (session('success'))
        <div class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded" role="alert">
            <p class="font-medium">{{ session('success') }}</p>
        </div>
    @endif
    
    @if (session('error'))
        <div class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded" role="alert">
            <p class="font-medium">{{ session('error') }}</p>
        </div>
    @endif
    
    <!-- Statuses Table -->
    <div class="bg-white dark:bg-slate-800 shadow overflow-hidden rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex flex-col mt-2">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="shadow overflow-hidden border-b border-gray-200 dark:border-slate-700 rounded-lg">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-700" id="sortable-statuses">
                                <thead class="bg-gray-50 dark:bg-slate-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Order
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Name
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Slug
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Color
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Settings
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
                                    @foreach ($statuses as $status)
                                        <tr data-id="{{ $status->id }}" class="hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <span class="flex-shrink-0 h-6 w-6 flex items-center justify-center rounded-full bg-gray-200 dark:bg-slate-700 text-gray-500 dark:text-gray-400 cursor-move">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                                        </svg>
                                                    </span>
                                                    <span class="ml-2 text-sm text-gray-900 dark:text-gray-300">{{ $status->order }}</span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <span class="h-3 w-3 rounded-full mr-2" style="background-color: {{ $status->color }}"></span>
                                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $status->name }}</span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="text-sm text-gray-500 dark:text-gray-400 font-mono">{{ $status->slug }}</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <span class="text-sm text-gray-500 dark:text-gray-400 font-mono">{{ $status->color }}</span>
                                                    <div class="ml-2 w-6 h-6 rounded border border-gray-300 dark:border-gray-600" style="background-color: {{ $status->color }}"></div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center space-x-4">
                                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ $status->is_default ? 'Default' : '' }}
                                                    </span>
                                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ $status->is_system ? 'System' : '' }}
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button type="button" 
                                                    onclick="editStatus('{{ $status->id }}', '{{ $status->name }}', '{{ $status->color }}', {{ $status->is_default ? 'true' : 'false' }})" 
                                                    class="text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white mr-3">
                                                    Edit
                                                </button>
                                                
                                                @if (!$status->is_system)
                                                    <form action="{{ route('admin.whiteboard-statuses.destroy', $status->id) }}" method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this status?')">
                                                            Delete
                                                        </button>
                                                    </form>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Status Modal -->
    <div id="addStatusModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-slate-900 opacity-75"></div>
            </div>
            
            <div class="inline-block align-bottom bg-white dark:bg-slate-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form action="{{ route('admin.whiteboard-statuses.store') }}" method="POST">
                    @csrf
                    <div class="bg-white dark:bg-slate-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start mb-4">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    Add New Status
                                </h3>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status Name</label>
                            <input type="text" name="name" id="name" class="mt-1 focus:ring-slate-500 focus:border-slate-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-slate-700 dark:bg-slate-900 dark:text-white rounded-md" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Color</label>
                            <div class="mt-1 flex items-center">
                                <input type="color" name="color" id="color" value="#6B7280" class="h-10 border-gray-300 dark:border-slate-700 rounded-md focus:ring-slate-500 focus:border-slate-500">
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400" id="color-hex">#6B7280</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_default" id="is_default" class="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 dark:border-slate-700 rounded">
                                <label for="is_default" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Make Default Status (replaces current default)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-slate-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-slate-800 dark:bg-slate-600 text-base font-medium text-white hover:bg-slate-700 dark:hover:bg-slate-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Create Status
                        </button>
                        <button type="button" onclick="document.getElementById('addStatusModal').classList.add('hidden')" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-slate-600 shadow-sm px-4 py-2 bg-white dark:bg-slate-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit Status Modal -->
    <div id="editStatusModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-slate-900 opacity-75"></div>
            </div>
            
            <div class="inline-block align-bottom bg-white dark:bg-slate-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form id="editStatusForm" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="bg-white dark:bg-slate-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start mb-4">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    Edit Status
                                </h3>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="edit_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status Name</label>
                            <input type="text" name="name" id="edit_name" class="mt-1 focus:ring-slate-500 focus:border-slate-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-slate-700 dark:bg-slate-900 dark:text-white rounded-md" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="edit_color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Color</label>
                            <div class="mt-1 flex items-center">
                                <input type="color" name="color" id="edit_color" class="h-10 border-gray-300 dark:border-slate-700 rounded-md focus:ring-slate-500 focus:border-slate-500">
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400" id="edit_color_hex">#000000</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_default" id="edit_is_default" class="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 dark:border-slate-700 rounded">
                                <label for="edit_is_default" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Make Default Status (replaces current default)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-slate-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-slate-800 dark:bg-slate-600 text-base font-medium text-white hover:bg-slate-700 dark:hover:bg-slate-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Update Status
                        </button>
                        <button type="button" onclick="document.getElementById('editStatusModal').classList.add('hidden')" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-slate-600 shadow-sm px-4 py-2 bg-white dark:bg-slate-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    // Color input handlers
    document.getElementById('color').addEventListener('input', function() {
        document.getElementById('color-hex').textContent = this.value;
    });
    
    document.getElementById('edit_color').addEventListener('input', function() {
        document.getElementById('edit_color_hex').textContent = this.value;
    });
    
    // Status editing
    function editStatus(id, name, color, isDefault) {
        const form = document.getElementById('editStatusForm');
        form.action = "{{ route('admin.whiteboard-statuses.update', '') }}/" + id;
        
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_color').value = color;
        document.getElementById('edit_color_hex').textContent = color;
        document.getElementById('edit_is_default').checked = isDefault;
        
        document.getElementById('editStatusModal').classList.remove('hidden');
    }
    
    // Sortable table
    document.addEventListener('DOMContentLoaded', function() {
        const sortable = Sortable.create(document.querySelector('#sortable-statuses tbody'), {
            handle: '.cursor-move',
            animation: 150,
            onEnd: function() {
                // Get the new order
                const rows = document.querySelectorAll('#sortable-statuses tbody tr');
                const order = Array.from(rows).map(row => row.dataset.id);
                
                // Send AJAX request to update order
                fetch("{{ route('admin.whiteboard-statuses.order') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ order })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Status order updated successfully');
                        // Refresh the page to show updated order numbers
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error updating status order:', error);
                });
            }
        });
    });
</script>
@endpush
@endsection 