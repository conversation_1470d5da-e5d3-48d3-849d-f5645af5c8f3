@extends('layouts.app') {{-- Assuming you have a layout file --}}

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                        <h6 class="text-white text-capitalize ps-3">Datatable Component Example</h6>
                    </div>
                </div>
                <div class="card-body px-0 pb-2">
                    <div id="app">
                        <datatable-component 
                            :columns="{{ json_encode($columns) }}" 
                            :data="{{ json_encode($users->items()) }}"
                            :pagination="{{ json_encode($users) }}"
                            :row-class="rowClass"
                            @page-change="handlePageChange"
                        >
                            <template #cell(avatar)="{ item }">
                                <img :src="item.avatar" alt="avatar" class="avatar avatar-sm me-3 border-radius-lg">
                            </template>

                            <template #cell(status)="{ value }">
                                <span :class="['badge', value === 'active' ? 'bg-success' : 'bg-secondary']">
                                    {{ value }}
                                </span>
                            </template>

                             <template #actions="{ item }">
                                <a href="#" class="btn btn-sm btn-info me-1">View</a>
                                <a href="#" class="btn btn-sm btn-primary me-1">Edit</a>
                                <a href="#" class="btn btn-sm btn-danger">Delete</a>
                            </template>
                        </datatable-component>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // This would be inside your main app.js or a specific script file
    const app = new Vue({
        el: '#app',
        components: {
            'datatable-component': DatatableComponent // Assuming it's registered globally or locally
        },
        data: {
            users: @json($users),
            columns: @json($columns),
        },
        methods: {
            handlePageChange(url) {
                window.location.href = url;
            },
            rowClass(item) {
                if (item.status === 'inactive') {
                    return 'opacity-50';
                }
                return '';
            }
        },
    });
</script>
@endpush 