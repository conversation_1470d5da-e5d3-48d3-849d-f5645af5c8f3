@extends('layouts.application')

@section('content')
<div class="px-6 lg:px-8 py-8">
        <!-- Handle error message if present -->
        @if(isset($error))
            <div class="bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 px-4 py-3 rounded-md mb-6">
                <h3 class="text-lg font-bold">Error Loading User Details</h3>
                <p>{{ $error }}</p>
                <p class="mt-2">Try refreshing the page or contact the system administrator.</p>
            </div>
        @endif
        
        <!-- Set default values for variables that might be missing -->
        @php
            $needsUpdateCount = $needsUpdateCount ?? 0;
            $newUsersCount = $newUsersCount ?? 0;
            $user = $user ?? null;
            $availableProjects = $availableProjects ?? collect([]);
            $availableFonts = $availableFonts ?? collect([]);
        @endphp
        
        <!-- Back to Users Button -->


        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.users"
            :needs-update-count="{{ $needsUpdateCount }}"
            :new-users-count="{{ $newUsersCount }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif
 <div class="max-w-full min-h-screen">
    <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl h-full">
        <!-- Pattern SVG - Applied to container -->
        <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                    <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#pattern-3)" />
        </svg>

        <!-- Content with relative positioning -->
        <div class="relative z-10 min-h-[70vh]">
            <div class="max-w-7xl mx-auto bg-white dark:bg-slate-950/100 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden h-full">
                <!-- User Content Container - Centered and Narrow -->
                <div class="mt-6 mx-6">
                    <a href="{{ route('admin.users') }}" class="inline-flex items-center px-4 py-2 text-sm gant-modern-medium rounded-md border border-solid border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-slate-950 dark:hover:bg-slate-100 hover:text-white dark:hover:text-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200">
                        <i class="design design-arrow-left mr-2"></i> Späť na používateľov
                    </a>
                </div>
                @if($user)
                    <!-- User Profile Header - Restructured to have identity and status on same row -->
                    <section class="border-b border-gray-200 dark:border-gray-700/50 p-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                            <!-- User Identity - Left Side -->
                            <div class="flex items-center gap-6">
                                <!-- Replace Vue avatar component with direct Blade implementation -->
                                <div class="relative">
                                    <img 
                                        src="{{ $user->avatar_url ?? '#' }}" 
                                        alt="{{ $user->name ?? 'User' }}'s avatar" 
                                        class="w-20 h-20 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                                    >
                                    @if($user->active ?? false)
                                        <div class="absolute bottom-0 right-0 w-3.5 h-3.5 rounded-full bg-green-500 border-2 border-white dark:border-gray-800"></div>
                                    @endif
                                </div>
                                <div>
                                    <h2 class="text-2xl gant-modern-bold text-gray-900 dark:text-white">{{ $user->login ?? 'Unknown' }}</h2>
                                    <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-lg">
                                        {{ $user->name ?? '' }} {{ $user->surname ?? '' }}
                                    </p>
                                    @if($user->email ?? false)
                                        <p class="text-gray-500 dark:text-gray-400 gant-modern-regular flex items-center mt-1">
                                            <i class="claims claims-mailrecords-outline mr-1.5 opacity-70"></i>{{ $user->email }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- User Status - Right Side -->
                            <div class="flex flex-wrap items-center gap-3">
                                <!-- Active Status Badge -->
                                @if($user->active ?? false)
                                    <div class="flex items-center gap-2 px-3 py-1.5 text-sm gant-modern-medium text-green-800 dark:text-green-300 bg-green-50 dark:bg-green-900/10 rounded-full border border-green-200 dark:border-green-800/30">
                                        <div class="w-2.5 h-2.5 rounded-full bg-green-500 animate-pulse"></div>
                                        <span>Aktívny používateľ</span>
                                    </div>
                                @else
                                    <div class="flex items-center gap-2 px-3 py-1.5 text-sm gant-modern-medium text-red-800 dark:text-red-300 bg-red-50 dark:bg-red-900/10 rounded-full border border-red-200 dark:border-red-800/30">
                                        <div class="w-2.5 h-2.5 rounded-full bg-red-500"></div>
                                        <span>Neaktívny používateľ</span>
                                    </div>
                                @endif
                                
                                <!-- Role Badge -->
                                @if(in_array($user->login ?? '', config('vermont.admin_user_logins', [])))
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-vermont-intranet-pass mr-1.5"></i> Admin
                                    </span>
                                @elseif(in_array($user->login ?? '', config('vermont.developer_user_logins', [])))
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-bracket3 mr-1.5"></i> Developer
                                    </span>
                                @elseif(in_array($user->login ?? '', config('vermont.dev_user_logins', [])))
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-bracket3 mr-1.5"></i> Dev
                                    </span>
                                @elseif(in_array($user->login ?? '', config('vermont.tibi_user_logins', [])))
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-coffee mr-1.5"></i> Tibi
                                    </span>
                                @elseif(in_array($user->login ?? '', config('vermont.pm_user_logins', [])))
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-race mr-1.5"></i> PM
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="design design-vermont-ucko mr-1.5"></i> Používateľ
                                    </span>
                                @endif
                                
                                <!-- Last Login Indicator -->
                                @if($user->last_login_at ?? false)
                                    <span class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                                        <i class="claims claims-clock-outline mr-1.5"></i>
                                        {{ \Carbon\Carbon::parse($user->last_login_at)->diffForHumans() }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    </section>

                    <!-- Improved Tabbed Interface with Modern Design - Bigger Icons without Circles -->
                    <div class="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/30">
                        <div class="px-6">
                            <ul class="flex flex-wrap -mb-px text-base gant-modern-medium" id="userDetailTabs" role="tablist">
                                <li class="mr-1" role="presentation">
                                    <button 
                                        class="inline-flex items-center py-4 px-5 border-b-2 border-gray-800 dark:border-gray-400 text-gray-800 dark:text-gray-200 rounded-t-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/30 transition-all duration-200" 
                                        id="basic-info-tab" 
                                        data-tab="basic-info" 
                                        type="button" 
                                        role="tab"
                                        aria-selected="true"
                                        aria-controls="basic-info-content"
                                        onclick="switchTab('basic-info')"
                                    >
                                        <div class="flex items-center gap-3">
                                            <i class="design design-user-outline text-gray-800 dark:text-gray-200 text-2xl"></i>
                                            <span>Základné informácie</span>
                                        </div>
                                    </button>
                                </li>
                                <li class="mr-1" role="presentation">
                                    <button 
                                        class="inline-flex items-center py-4 px-5 border-b-2 border-transparent text-gray-500 dark:text-gray-400 rounded-t-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/30 hover:text-gray-700 dark:hover:text-gray-300 transition-all duration-200" 
                                        id="projects-tab" 
                                        data-tab="projects" 
                                        type="button" 
                                        role="tab"
                                        aria-selected="false"
                                        aria-controls="projects-content"
                                        onclick="switchTab('projects')"
                                    >
                                        <div class="flex items-center gap-3">
                                            <i class="design design-service text-gray-500 dark:text-gray-400 text-2xl"></i>
                                            <span>Projekty a ikony</span>
                                        </div>
                                    </button>
                                </li>
                                <li class="mr-1" role="presentation">
                                    <button 
                                        class="inline-flex items-center py-4 px-5 border-b-2 border-transparent text-gray-500 dark:text-gray-400 rounded-t-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/30 hover:text-gray-700 dark:hover:text-gray-300 transition-all duration-200" 
                                        id="greetings-tab" 
                                        data-tab="greetings" 
                                        type="button" 
                                        role="tab"
                                        aria-selected="false"
                                        aria-controls="greetings-content"
                                        onclick="switchTab('greetings')"
                                    >
                                        <div class="flex items-center gap-3">
                                            <i class="design design-hello-collegue text-gray-500 dark:text-gray-400 text-2xl"></i>
                                            <span>Vlastné pozdravy</span>
                                        </div>
                                    </button>
                                </li>
                                <li class="mr-1" role="presentation">
                                    <button 
                                        class="inline-flex items-center py-4 px-5 border-b-2 border-transparent text-gray-500 dark:text-gray-400 rounded-t-lg hover:bg-gray-100/50 dark:hover:bg-gray-700/30 hover:text-gray-700 dark:hover:text-gray-300 transition-all duration-200" 
                                        id="management-tab" 
                                        data-tab="management" 
                                        type="button" 
                                        role="tab"
                                        aria-selected="false"
                                        aria-controls="management-content"
                                        onclick="switchTab('management')"
                                    >
                                        <div class="flex items-center gap-3">
                                            <i class="design design-vermont-intranet-pass text-gray-500 dark:text-gray-400 text-2xl"></i>
                                            <span>Správa používateľa</span>
                                        </div>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Tab Content Sections -->
                    <div class="p-6">
                        <!-- Include the tab content partials -->
                        @include('admin.users.partials.basic-info-tab')
                        @include('admin.users.partials.projects-tab')
                        @include('admin.users.partials.greetings-tab')
                        @include('admin.users.partials.management-tab')
                    </div>
                @else
                    <div class="p-6">
                        <div class="bg-yellow-100 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300 px-4 py-3 rounded-md mb-6">
                            <h3 class="text-lg font-bold">User Not Found</h3>
                            <p>The requested user could not be loaded. Please return to the users list and try again.</p>
                        </div>
                        <div class="mt-4">
                            <a href="{{ route('admin.users') }}" class="inline-flex items-center px-4 py-2 text-sm gant-modern-medium rounded-md border border-solid border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-slate-950 dark:hover:bg-slate-100 hover:text-white dark:hover:text-slate-950 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200">
                                <i class="design design-arrow-left mr-2"></i> Back to Users
                            </a>
                        </div>
                    </div>
                @endif
                </div>
            </div>
        </div>
    </div>
    </div>
    <!-- Include modals -->
    @include('admin.users.partials.modals.role-confirm-modal')
    @include('admin.users.partials.modals.delete-confirm-modal')

    @push('scripts')
    <!-- Include modularized JavaScript files -->
    <script src="{{ asset('js/admin/users/tab-switcher.js') }}"></script>
    <script src="{{ asset('js/admin/users/basic-info-tab.js') }}"></script>
    <script src="{{ asset('js/admin/users/projects-tab.js') }}"></script>
    <script src="{{ asset('js/admin/users/greetings-tab.js') }}"></script>
    <script src="{{ asset('js/admin/users/management-tab.js') }}"></script>
    
    <!-- Add username meta tag for JavaScript use -->
    <meta name="username" content="{{ $user->name ?? 'Unknown' }}">
    
    <script>
        // Execute code when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs if they exist
            // Make sure toggleBirthdayEdit is available
            if (typeof window.toggleBirthdayEdit !== 'function') {
                console.error('toggleBirthdayEdit function not found, defining fallback');
                window.toggleBirthdayEdit = function(show) {
                    const birthdayDisplay = document.getElementById('birthday-display');
                    const birthdayEmpty = document.getElementById('birthday-empty');
                    const birthdayForm = document.getElementById('birthday-form');
                    
                    if (!birthdayDisplay || !birthdayForm) {
                        console.error('Birthday elements not found in the DOM');
                        return;
                    }
                    
                    if (show) {
                        if (birthdayDisplay) birthdayDisplay.classList.add('hidden');
                        if (birthdayEmpty) birthdayEmpty.classList.add('hidden');
                        birthdayForm.classList.remove('hidden');
                        const birthdayInput = document.getElementById('birthday-input');
                        if (birthdayInput) birthdayInput.focus();
                    } else {
                        const birthdayValue = document.getElementById('birthday-value');
                        const hasBirthday = birthdayValue && birthdayValue.textContent.trim() !== '';
                        
                        if (hasBirthday) {
                            birthdayDisplay.classList.remove('hidden');
                            if (birthdayEmpty) birthdayEmpty.classList.add('hidden');
                        } else {
                            if (birthdayDisplay) birthdayDisplay.classList.add('hidden');
                            if (birthdayEmpty) birthdayEmpty.classList.remove('hidden');
                        }
                        
                        birthdayForm.classList.add('hidden');
                    }
                };
            }
        });
    </script>
    @endpush
@endsection 