@extends('layouts.application')

@section('content')
    <div class="px-6 lg:px-8 py-8">
        <!-- Admin Navigation Tabs -->
        <admin-navigation 
            current-route="admin.icons"
            :needs-update-count="{{ $needsUpdateCount ?? 0 }}"
            :new-users-count="{{ $newUsersCount ?? 0 }}"
            class="mb-8"
        ></admin-navigation>

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
                <h2 class="text-4xl lg:text-5xl gant-modern-bold text-slate-900 dark:text-white">{{ $font->name }} – Tag Management</h2>
                <div class="text-slate-500 dark:text-slate-400">
                    <i class="design design-component opacity-50 me-1"></i>Počet ikon: <span class="gant-modern-bold text-slate-900 dark:text-white">{{ $font->icons_count }}</span>
                </div>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.icons') }}" 
                   class="inline-flex items-center px-4 py-3 rounded-lg gant-modern-bold text-sm text-slate-900 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800/50 dark:text-slate-300 dark:hover:bg-slate-800 transition-all duration-200">
                    <i class="design design-arrow-left mr-2"></i>
                    Back to Fonts
                </a>
                @if($needsUpdate ?? false)
                <a href="{{ route('admin.update_font_icons', [$font]) }}" 
                   class="inline-flex items-center px-4 py-3 rounded-lg gant-modern-bold text-sm text-red-50 bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:text-red-50 dark:hover:bg-red-700 transition-all duration-200">
                    <i class="retail retail-refresh mr-2"></i>
                    Update Icons
                </a>
                @endif
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success') || session('error'))
            <div class="mb-8">
                @if(session('success'))
                    <div class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
                        <i class="design design-circle-checked mr-2 text-lg"></i>
                        <span>{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
                        <i class="design design-circle-remove mr-2 text-lg"></i>
                        <span>{{ session('error') }}</span>
                    </div>
                @endif
            </div>
        @endif

        <!-- Font Icon Manager Vue Component -->
        <div class="bg-white dark:bg-slate-950 rounded-xl mb-8">
            <div id="font-icon-manager-app">
                <font-icon-manager
                    :font-id="{{ $font->id }}"
                    font-name="{{ $font->name }}"
                    font-class="{{ $font->css_class }}"
                    :initial-icons="{{ json_encode($icons, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) }}"
                ></font-icon-manager>
            </div>
        </div>
    </div>
@endsection 