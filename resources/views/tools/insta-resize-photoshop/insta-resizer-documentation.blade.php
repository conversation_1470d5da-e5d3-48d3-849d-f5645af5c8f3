@extends('layouts.application')

@section('content')
    <div data-page="tool-documentation" class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }}">
        <div class="min-h-screen bg-white dark:bg-slate-950">
            <!-- Main container -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header Section -->
                <header class="pt-24 pb-0 sm:pb-16">
                    <p class="text-sm sm:text-base md:text-lg lg:text-xl text-gray-500 dark:text-gray-400 mb-4 gant-modern-light">
                        –––––– Photoshop automation script for batch processing and resizing images for social media.
                    </p>
                    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl gant-modern-bold text-gray-900 dark:text-gray-100 tracking-tighter">
                        Insta Resizer <span class="gant-modern-light">v0.1</span>
                    </h1>
                </header>

                <!-- Content Section with overflow container -->
                <div class="relative pb-0 sm:pb-16">
                    <!-- Mobile Table of Contents (shown only on mobile) -->
                    <div class="lg:hidden mb-12">
                        <div class=" p-0">
                            <ul class="space-y-1 text-base">
                                <li>
                                    <a href="#" 
                                       data-nav-link="dimensions"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        1. Image Dimensions
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="crop"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        2. Processing Method
                                    </a>
                                    <!-- Processing Method Subsections -->
                                    <ul class="pl-6 mt-1 space-y-1">
                                        <li>
                                            <a href="#" 
                                               data-nav-link="normal-fill"
                                               class="block py-1 px-0 md:px-3 rounded-lg transition-colors duration-200 text-sm gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                • Normal Fill
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" 
                                               data-nav-link="crop-to-fit"
                                               class="block py-1 px-0 md:px-3 rounded-lg transition-colors duration-200 text-sm gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                • Crop to Fit
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" 
                                               data-nav-link="generative-expand"
                                               class="block py-1 px-0 md:px-3 rounded-lg transition-colors duration-200 text-sm gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                • Generative Expand
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="background"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        3. Background Color
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="overlay"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        4. Overlay & Badges
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="quality"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        5. JPEG Quality
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="folders"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        6. Folder Selection
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="process"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        7. Process Images
                                    </a>
                                </li>
                                <li>
                                    <a href="#" 
                                       data-nav-link="troubleshooting"
                                       class="block py-2 px-0 md:px-3 rounded-lg transition-colors duration-200 gant-modern-regular hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100">
                                        8. Troubleshooting
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex flex-col lg:flex-row lg:gap-16">
                        <!-- Main Content -->
                        <main class="flex-1 overflow-hidden">
                            <div class="prose prose-lg max-w-none dark:prose-invert gant-modern-regular">
                                <!-- Documentation content -->
                                <div class="mb-8">
                                    <!-- Image section -->
                                    <div class="flex flex-col items-center">
                                        <img src="{{ asset('img/tools/insta-resize/hero.png') }}" 
                                             alt="Base Image Example"
                                             class="w-full">
                                        <p class="text-xs sm:text-sm md:text-base text-gray-500 dark:text-gray-400 mt-4 text-center italic gant-modern-light">
                                            Multiple images in the folder will be resized to the same dimensions and background color.  
                                        </p>
                                    </div>
                                </div>
                                <!-- Usage section -->
                                <section class="mb-8">
                                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 dark:text-gray-200 gant-modern-bold flex items-center gap-3">
                                        <i class="design design-play text-3xl text-blue-500 dark:text-blue-400"></i>
                                        How to start script in Photoshop
                                    </h2>

                                    <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800">
                                        <p class="text-gray-600 dark:text-gray-300 gant-modern-light mb-3">
                                        <img src="{{ asset('img/enLight.svg') }}" alt="CZ" class="inline-block h-4 w-6 me-1"> Photoshop: <span class="gant-modern-bold me-2">File <i class="design design-arrow-right"></i> Scripts <i class="design design-arrow-right"></i> Browse </span> (find and open insta-image-resize.jsx)
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-300 gant-modern-light mb-3">
                                        <img src="{{ asset('img/czLight.svg') }}" alt="CZ" class="inline-block h-4 w-6 me-1"> Photoshop:<span class="gant-modern-bold me-2"> Soubor  <i class="design design-arrow-right"></i> Skripty <i class="design design-arrow-right"></i> Procházet</span> (najdi a otevri insta-image-resize.jsx)
                                        </p>
                                    </div>
                                </section>
                                <!-- Features section -->
                                <section class="mb-24">
                                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 dark:text-gray-200 gant-modern-bold flex items-center gap-3">
                                        <i class="design design-sparkles text-3xl text-blue-500 dark:text-blue-400"></i>
                                        Key Features
                                    </h2>

                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        <!-- Batch Processing -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="wms wms-performance-statistics text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Batch Processing</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Process multiple images simultaneously for your social media campaigns
                                            </p>
                                        </div>

                                        <!-- Smart Resizing -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="design design-zoom-out-1 text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Square Format</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Automatically resize to 1080x1080px with customizable dimensions for perfect Instagram posts
                                            </p>
                                        </div>

                                        <!-- Intelligent Fill -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="wms wms-magic_wand text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Intelligent Fill</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Choose between Normal Fill, Crop to Fit, or AI-powered Generative Expand
                                            </p>
                                        </div>

                                        <!-- Color Control -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="tos tos-sizes text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Color Control</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Customize background colors using HEX or RGB values for brand consistency
                                            </p>
                                        </div>

                                        <!-- Badge Overlay -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="wms wms-layers text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Badge Overlay</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Add brand badges or overlay elements with customizable size, position, and opacity
                                            </p>
                                        </div>

                                        <!-- Quality Settings -->
                                        <div class="p-6 rounded-xl bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 group hover:border-blue-500 dark:hover:border-blue-500/50 transition-all duration-300">
                                            <div class="flex items-center gap-3 mb-4">
                                                <i class="wms wms-setup text-2xl text-blue-500 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300"></i>
                                                <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100">Quality Settings</h3>
                                            </div>
                                            <p class="text-gray-600 dark:text-gray-300 gant-modern-regular">
                                                Fine-tune JPEG compression from 1-12 for optimal file size and quality balance
                                            </p>
                                        </div>
                                    </div>
                                </section>

                                <!-- User Manual section -->
                                <div class="mb-24">
                                    <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-8 dark:text-gray-200 gant-modern-bold">📖 User Manual</h2>
                                    <ul class="space-y-24 text-sm sm:text-base md:text-lg text-gray-900 dark:text-gray-300 gant-modern-regular">
                                        <li class="flex items-start" data-section="dimensions">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">1.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Set Image Dimensions</p>
                                                <img src="{{ asset('img/tools/insta-resize/setimagedimensions.png') }}" alt="Image Dimensions Interface" class="w-full mb-4">
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Configure the target dimensions for your social media images using precise pixel values:</p>
                                                <ul class="ml-4 mt-2 space-y-4">
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Default Settings:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Width: 1080 pixels (optimized for Instagram and Facebook posts)</li>
                                                                <li>- Height: 1080 pixels (perfect square format for social media feeds)</li>
                                                                <li>- Automatically applied when script initializes</li>
                                                                <li>- Pre-configured for most popular social platforms</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Custom Dimensions:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Enter custom values for other social media formats (story, reel, etc.)</li>
                                                                <li>- Both width and height parameters must be specified</li>
                                                                <li>- Input accepts only numerical values</li>
                                                                <li>- Common social media formats: 1080×1080px (feed), 1080×1920px (story)</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Social Media Optimization:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Square format ensures your content displays properly in social feeds</li>
                                                                <li>- Maintains optimal resolution for high-quality display on mobile devices</li>
                                                                <li>- Ensures consistency across your social media content</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="crop">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">2.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Choose Processing Method</p>
                                                <img src="{{ asset('img/tools/insta-resize/choosecropoption.png') }}" alt="Processing Method Options Interface" class="w-full mb-4">
                                                <p class="mb-6 text-gray-900 dark:text-gray-300">Select one of the three available processing methods based on your specific image requirements and desired output:</p>
                                                
                                                <!-- Method cards in grid layout -->
                                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                    <!-- Normal Fill Method -->
                                                    <div class="p-3" data-section="normal-fill">
                                                    <h2 class="text-gray-900 dark:text-gray-100 text-6xl ms-5">
                                                            <i class="retail retail-vermont-def"></i>
                                                        </h2>
                                                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                                                            <i class="design design-paint-bucket text-blue-500 dark:text-blue-400 mr-2"></i>
                                                            Normal Fill
                                                        </h3>
                                                        <ul class="space-y-2 text-base text-gray-900 dark:text-gray-300">
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Maintains original image proportions and aspect ratio</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Creates a canvas extension with solid color background</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Preserves entire original image without clipping</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Utilizes the background color defined in step 3</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Implements non-destructive layer-based workflow</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Centers subject in frame for optimal visual balance</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Ideal for product photography requiring complete visibility</span>
                                                            </li>
                                                        </ul>
                                                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                                            <p class="text-sm text-blue-600 dark:text-blue-400 italic">
                                                                Recommended for e-commerce product images, particularly those requiring isolated subjects on clean backgrounds
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <!-- Crop to Fit Method -->
                                                    <div class="p-3" data-section="crop-to-fit">
                                                    <h2 class="text-gray-900 dark:text-gray-100 text-6xl ms-5">
                                                            <i class="retail retail-gant-icon-def"></i>
                                                        </h2>
                                                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                                                            <i class="design design-crop text-blue-500 dark:text-blue-400 mr-2"></i>
                                                            Crop to Fit
                                                        </h3>
                                                        <ul class="space-y-2 text-base text-gray-900 dark:text-gray-300">
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Utilizes content-aware scaling to fill target dimensions</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Employs intelligent cropping from image periphery</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Maintains aspect ratio while performing transform operations</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Implements center-weighted content preservation algorithm</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Eliminates need for background color selection</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Optimizes image composition for visual impact</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Ideal for lifestyle, environmental, or contextual imagery</span>
                                                            </li>
                                                        </ul>
                                                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                                            <p class="text-sm text-blue-600 dark:text-blue-400 italic">
                                                                Recommended for GANT-specific marketing requirements and editorial imagery where aspect ratio consistency is critical
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <!-- Generative Expand Method -->
                                                    <div class="p-3" data-section="generative-expand">
                                                        <h2 class="text-gray-900 dark:text-gray-100 text-6xl ms-5">
                                                            <i class="retail retail-karl1"></i>
                                                        </h2>
                                                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                                                            <i class="wms wms-magic_wand text-blue-500 dark:text-blue-400 mr-2"></i>
                                                            Generative Expand
                                                        </h3>
                                                        <ul class="space-y-2 text-base text-gray-900 dark:text-gray-300">
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Leverages Adobe Firefly AI to extend image boundaries</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Analyzes existing image patterns and textures</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Intelligently synthesizes new content at image borders</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Preserves original image proportions and content</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Creates seamless transitions between original and generated areas</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Produces photorealistic background extensions</span>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <span>Requires Photoshop with Generative AI capabilities (23.3+)</span>
                                                            </li>
                                                        </ul>
                                                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                                            <p class="text-sm text-blue-600 dark:text-blue-400 italic">
                                                                Optimal for images with complex backgrounds, textures, or patterns that need seamless extension while maintaining visual coherence
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mt-6">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>For professional e-commerce product photography on white backgrounds, "Normal Fill" with white background (RGB: 255,255,255) is the industry standard. This ensures no product details are lost and maintains maximum compatibility with marketplace requirements like Amazon, eBay, and specialized e-commerce platforms.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Technical Considerations:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span><span class="gant-modern-bold">Normal Fill:</span> Most efficient in terms of processing time and resource usage</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span><span class="gant-modern-bold">Crop to Fit:</span> Moderate processing requirements, excellent for batch operations</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span><span class="gant-modern-bold">Generative Expand:</span> Computationally intensive, may require significant processing time for high-resolution images or large batches</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="background">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">3.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Set Background Color <span class="gant-modern-light">(for Normal Fill Only)</span></p>
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Specify the precise color to be used for canvas extension when using the Normal Fill method. Color selection offers multiple input options for maximum flexibility:</p>
                                                <img src="{{ asset('img/tools/insta-resize/setbackgroundcolor.png') }}" 
                                             alt="Background Color Selection Interface"
                                             class="w-full mb-4">
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6 mb-6">
                                                    <div>
                                                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Color Input Methods</h3>
                                                        <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">HEX format:</span>
                                                                    <ul class="ml-6 mt-2 space-y-1">
                                                                        <li>- Standard 6-digit hexadecimal notation (e.g., #FFFFFF for white)</li>
                                                                        <li>- Optional # prefix (both "FFFFFF" and "#FFFFFF" accepted)</li>
                                                                        <li>- Case-insensitive input (both "ffffff" and "FFFFFF" valid)</li>
                                                                    </ul>
                                                                </div>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">RGB values:</span>
                                                                    <ul class="ml-6 mt-2 space-y-1">
                                                                        <li>- Individual channel values from 0-255</li>
                                                                        <li>- Comma-separated format (e.g., "255,255,255" for white)</li>
                                                                        <li>- Spaces between values are optional</li>
                                                                    </ul>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    
                                                    <div>
                                                        <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Common E-Commerce Colors</h3>
                                                        <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">Pure White:</span> #FFFFFF or 255,255,255
                                                                    <p class="mt-1">Standard for most product photography</p>
                                                                </div>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">Studio White:</span> #F8F8F8 or 248,248,248
                                                                    <p class="mt-1">Slightly off-white for reduced highlights</p>
                                                                </div>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">Light Gray:</span> #EEEEEE or 238,238,238
                                                                    <p class="mt-1">For products with white elements</p>
                                                                </div>
                                                            </li>
                                                            <li class="flex items-start">
                                                                <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                                <div>
                                                                    <span class="gant-modern-bold">Transparent:</span> Special case, achieved via PNG format
                                                                    <p class="mt-1">See JPEG Quality section for format options</p>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                
                                                <div class="mt-6 mb-4">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>When processing images for a specific marketplace or e-commerce platform, refer to their technical specifications for background color requirements. Some platforms like Amazon enforce strict white background standards, while others may require specific RGB values to match their site aesthetics.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Technical Note:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Colors are processed in the sRGB color space for maximum web compatibility. If your workflow involves different color profiles, consider converting to sRGB before processing for predictable results.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="overlay">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">4.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Overlay & Badges</p>
                                                <img src="{{ asset('img/tools/insta-resize/watermark.png') }}" alt="Badge Overlay Interface" class="w-full mb-4">
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Add professional branding or copyright protection to your images with a custom overlay:</p>
                                                <ul class="ml-4 mt-2 space-y-4">
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Enabling Watermarks:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Check the "Enable Watermark" checkbox to activate this feature</li>
                                                                <li>- Click "Select Watermark File" to browse for and choose your watermark PNG or JPG</li>
                                                                <li>- Transparent PNGs work best for professional looking overlays</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Sizing and Positioning:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Scale slider: Adjust watermark size from 10% to 100% of its original dimensions</li>
                                                                <li>- Opacity slider: Control watermark transparency from 10% to 100%</li>
                                                                <li>- Placement dropdown: Position your watermark in 5 key locations (center, top-left, top-right, bottom-left, bottom-right)</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Blend Mode:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Select from 13 different blend modes to control how your watermark interacts with the underlying image</li>
                                                                <li>- Normal: Standard overlay with no blending effects</li>
                                                                <li>- Multiply: Darkens image where watermark is present (good for light watermarks)</li>
                                                                <li>- Screen: Lightens image where watermark is present (good for dark watermarks)</li>
                                                                <li>- Overlay/Soft Light: Creates natural integration with image luminosity</li>
                                                                <li>- Difference/Exclusion: Creates high-contrast effects with underlying colors</li>
                                                                <li>- Additional modes: Dissolve, Hard Light, Linear Light, Pin Light, Darken, and Lighten for specialized effects</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="flex items-start">
                                                        <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                        <div>
                                                            <span class="gant-modern-bold">Best Practices:</span>
                                                            <ul class="ml-6 mt-2 space-y-1">
                                                                <li>- Use subtle watermarks with 20-40% opacity for non-intrusive branding</li>
                                                                <li>- For copyright protection, use larger watermarks with higher opacity</li>
                                                                <li>- Experiment with blend modes like Overlay and Soft Light for more natural integration</li>
                                                                <li>- Position watermarks in corners for minimal impact on main subject</li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="quality">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">5.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Set JPEG Quality</p>
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Configure output compression to balance image quality and file size based on your specific requirements:</p>
                                                <img src="{{ asset('img/tools/insta-resize/setquality.png') }}" 
                                             alt="JPEG Quality Setting Interface"
                                             class="w-full mb-6">
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Quality Range</h3>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Adjustable from 1-12 on Photoshop's scale</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Photoshop quality 12 = Highest quality (minimal compression)</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Photoshop quality 1 = Lowest quality (maximum compression)</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Photoshop quality 8-10 = Standard web recommendation</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Technical Details</h3>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Higher quality = Larger file size</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Uses DCT-based compression algorithm</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Lossy compression may impact fine details</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Chroma subsampling optimizes file size</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Baseline encoding for maximum compatibility</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Recommendations</h3>
                                                    <ul class="space-y-3 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">E-commerce product images:</span>
                                                                <p class="mt-1">Quality 10-12 (higher quality preserves product details)</p>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Lifestyle/marketing images:</span>
                                                                <p class="mt-1">Quality 8-10 (balanced quality/size ratio)</p>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Thumbnail/preview images:</span>
                                                                <p class="mt-1">Quality 6-8 (optimized for faster loading)</p>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="mt-4">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>When transparency is required, the script automatically switches to PNG format with lossless compression, regardless of the JPEG quality setting. This ensures that transparent areas are properly preserved while maintaining maximum image quality.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Important:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>JPEG compression is most visible in areas with sharp color transitions, gradients, and fine details. For images with text, intricate patterns, or sharp edges, consider using higher quality settings (10-12) to avoid compression artifacts.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                

                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="folders">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">6.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Choose Folders</p>
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Configure source and destination directories for batch image processing workflow:</p>
                                                <img src="{{ asset('img/tools/insta-resize/choosefolders.png') }}" 
                                             alt="Folder Selection Interface"
                                             class="w-full mb-6">
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="design design-folder-open text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Input Folder
                                                    </h3>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Source directory containing original image files</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Supports multiple image formats (.jpg, .jpeg, .png, .tif, .tiff, .psd)</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Processes all compatible images within the selected directory</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Images in subfolders are excluded from processing</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Directory browsing uses native OS file selection dialog</span>
                                                        </li>
                                                    </ul>
                                                    <p class="text-blue-600 dark:text-blue-400 italic mt-3 ml-4">
                                                        Best Practice: Organize source images in a dedicated folder separate from your final outputs to avoid confusion
                                                    </p>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="design design-folder-upload text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Output Location Options
                                                    </h3>
                                                    <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Option 1: Use input folder</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Automatically creates a 'processed' subfolder</li>
                                                                    <li>- Preserves original file organization</li>
                                                                    <li>- Convenient for quick workflow implementation</li>
                                                                    <li>- Prevents accidental overwriting of source files</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Option 2: Custom output folder</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Select any accessible directory on your system</li>
                                                                    <li>- Allows for more complex file organization strategies</li>
                                                                    <li>- Ideal for multi-stage processing workflows</li>
                                                                    <li>- Supports network locations for collaborative environments</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                    <p class="text-blue-600 dark:text-blue-400 italic mt-3 ml-4">
                                                        The script automatically creates output directories if they don't already exist
                                                    </p>
                                                </div>
                                                <div class="mb-8">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>For e-commerce production workflows, consider implementing a consistent naming convention and folder structure that separates raw source files, intermediate processing stages, and final delivery assets. Example:</span>
                                                        </li>
                                                    </ul>
                                                    <ul class="ml-10 mt-2 space-y-1 text-gray-900 dark:text-gray-300 text-sm">
                                                        <li>• /ProductID_Raw/ - Original camera files</li>
                                                        <li>• /ProductID_Edit/ - Retouched master files</li>
                                                        <li>• /ProductID_Final/ - Processed output files for specific platforms</li>
                                                    </ul>
                                                </div>
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Important:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Ensure you have sufficient disk space in your output location. Processed images may consume significant storage depending on quality settings and batch size. The script does not automatically check for available disk space.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                

                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="process">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">7.</span>
                                            <div>
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Process Images</p>
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">Execute the batch processing operation and monitor progress through Photoshop's script interface:</p>
                                                <img src="{{ asset('img/tools/insta-resize/processimages.png') }}" 
                                             alt="Process Images Interface"
                                             class="w-full mb-6">
                                             
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="wms wms-play-and-pause-button text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Execution Controls
                                                    </h3>
                                                    <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Run Button:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Initiates the batch processing operation</li>
                                                                    <li>- Validates all input parameters before execution</li>
                                                                    <li>- Creates required output directories if needed</li>
                                                                    <li>- Locks interface during processing to prevent conflicts</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Abort Button:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Safely terminates active processing</li>
                                                                    <li>- Preserves already completed images</li>
                                                                    <li>- Closes any open documents without saving</li>
                                                                    <li>- Returns interface to interactive state</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="wms wms-clock text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Progress Indicators
                                                    </h3>
                                                    <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Progress Bar:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Visual representation of batch completion percentage</li>
                                                                    <li>- Updates in real-time as each image processes</li>
                                                                    <li>- Provides at-a-glance status of overall job</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Status Messages:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Detailed text feedback on current operations</li>
                                                                    <li>- File counts and current processing stage</li>
                                                                    <li>- Error notifications and troubleshooting information</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="wms wms-priority text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Processing Workflow
                                                    </h3>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">1.</span>
                                                            <span>Source image inventory and validation</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">2.</span>
                                                            <span>Sequential file opening and processing</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">3.</span>
                                                            <span>Application of selected processing method</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">4.</span>
                                                            <span>Dimensions and quality adjustments</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">5.</span>
                                                            <span>Output file generation and optimization</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">6.</span>
                                                            <span>Cleanup of temporary files and layers</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>The script uses non-destructive editing techniques and never modifies your original files. If you encounter any issues with the processed images, your source files remain intact and can be reprocessed with different settings.</span>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Performance Considerations:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>During processing, Photoshop temporarily uses more system resources. For optimal performance:</span>
                                                        </li>
                                                    </ul>
                                                    <ul class="ml-10 mt-2 space-y-1 text-gray-900 dark:text-gray-300 text-sm">
                                                        <li>• Close other resource-intensive applications</li>
                                                        <li>• Process large batches in smaller groups if experiencing slowdowns</li>
                                                        <li>• Ensure sufficient free disk space on both source and destination drives</li>
                                                        <li>• Temporarily increase Photoshop's memory allocation in Preferences → Performance</li>
                                                    </ul>
                                                </div>
                                                

                                                
                                                <div class="mt-4">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Error Handling</h3>
                                                    <p class="text-gray-600 dark:text-gray-300 mb-3 ml-4">The script includes robust error handling to manage common issues:</p>
                                                    <ul class="space-y-1 text-gray-900 dark:text-gray-300 ml-8">
                                                        <li>• <span class="gant-modern-bold">Invalid file formats:</span> Non-image files are automatically skipped</li>
                                                        <li>• <span class="gant-modern-bold">Corrupted images:</span> Damaged files are bypassed with error notifications</li>
                                                        <li>• <span class="gant-modern-bold">Write permission errors:</span> Issues with output locations are reported</li>
                                                        <li>• <span class="gant-modern-bold">Memory limitations:</span> Graceful handling of resource constraints</li>
                                                        <li>• <span class="gant-modern-bold">Generative AI issues:</span> Automatic fallback to standard methods if AI fails</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>

                                        <li class="flex items-start" data-section="troubleshooting">
                                            <span class="mr-2 text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200">8.</span>
                                            <div class="mb-48">
                                                <p class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Troubleshooting</p>
                                                <p class="mb-4 text-gray-900 dark:text-gray-300">If you encounter issues while using the Eshop Resizer script, consider these common solutions:</p>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="wms wms-alert text-red-500 dark:text-blue-400 mr-2"></i>
                                                        Common Issues
                                                    </h3>
                                                    <ul class="space-y-4 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-red-500 dark:text-red-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Permission Errors:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Ensure Photoshop has necessary permissions to read from input and write to output folders</li>
                                                                    <li>- Run Photoshop as administrator if on Windows</li>
                                                                    <li>- Check folder permissions in system settings</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-red-500 dark:text-red-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Image Corruption:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Verify source images can be opened manually in Photoshop</li>
                                                                    <li>- Check for incomplete downloads or transfer errors</li>
                                                                    <li>- Try processing images individually to identify problematic files</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-red-500 dark:text-red-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Storage Issues:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Ensure sufficient disk space is available for output files</li>
                                                                    <li>- Check for write-protection on destination drives</li>
                                                                    <li>- Verify network drives are properly connected if using shared storage</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-red-500 dark:text-red-400 mr-2">•</span>
                                                            <div>
                                                                <span class="gant-modern-bold">Compatibility:</span>
                                                                <ul class="ml-6 mt-2 space-y-1">
                                                                    <li>- Confirm you're using Photoshop version 23.0 or higher</li>
                                                                    <li>- For Generative Expand features, Photoshop 23.3+ is required</li>
                                                                    <li>- Update Photoshop to the latest version if possible</li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mb-8">
                                                    <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                                                        <i class="design design-refresh text-blue-500 dark:text-blue-400 mr-2"></i>
                                                        Reset Procedures
                                                    </h3>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Restart Photoshop to clear any memory issues or script conflicts</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Clear Photoshop's cache: Edit → Purge → All</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Reset Photoshop preferences if script consistently fails</span>
                                                        </li>
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>Download a fresh copy of the script if corruption is suspected</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mt-4">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Tips:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>For complex troubleshooting, try running the script with a single test image in a simple folder path with no special characters or spaces.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg">
                                                    <h4 class="text-md gant-modern-bold text-gray-900 dark:text-gray-100 mb-2">Important Note:</h4>
                                                    <ul class="space-y-2 text-gray-900 dark:text-gray-300 text-sm ml-4">
                                                        <li class="flex items-start">
                                                            <span class="text-blue-500 dark:text-blue-400 mr-2">•</span>
                                                            <span>The script includes built-in error handling that will attempt to continue processing the batch even if individual images fail. Check the script dialog for specific error messages that can help identify the exact cause of any issues.</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </main>

                        <!-- Desktop Sidebar (hidden on mobile) -->
                        <div class="hidden lg:block w-56 relative">
                            <div class="lg:fixed lg:w-56">
                                <div class="space-y-4">
                                    <!-- Photoshop Logo -->
                                    <div class="flex flex-col items-center mb-2">
                                        <img src="{{ asset('img/tools/insta-resize/scriptfile.svg') }}" 
                                             alt="Adobe Photoshop" 
                                             class="w-64 h-48">
                                        <span class="text-xs text-gray-500 dark:text-gray-400 mt-2 italic gant-modern-light">
                                            * 2023 edition only
                                        </span>
                                    </div>

                                    <!-- Download Button -->
                                    <div class="space-y-4">
                                        <!-- Open Modal Button with simpler modal opening approach -->
                                        <button 
                                           class="group w-full inline-flex items-center justify-center px-4 py-3 rounded-lg text-sm gant-modern-bold bg-gray-900 hover:bg-gray-800 dark:bg-gray-100 dark:hover:bg-gray-200 text-gray-100 dark:text-gray-900 transition-colors"
                                           onclick="document.getElementById('download-modal').style.display = 'flex';">
                                            <i class="tos tos-down-loading mr-2 opacity-70"></i>
                                            <span>Download .JSX script</span>
                                        </button>

                                        <!-- Simple Modal without Vue dependencies -->
                                        <div id="download-modal"
                                             class="fixed inset-0 z-[9999] hidden items-center justify-center">
                                            
                                            <!-- Modal Backdrop with blur -->
                                            <div class="fixed inset-0 bg-gray-900/60 dark:bg-gray-950/70 backdrop-blur-sm" 
                                                 onclick="document.getElementById('download-modal').style.display = 'none';"></div>
                                            
                                            <!-- Modal Content -->
                                            <div class="relative bg-white dark:bg-slate-900 rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden z-10">
                                                <!-- Modal Header -->
                                                <div class="px-8 py-8 border-b border-gray-200 dark:border-gray-700">
                                                    <div class="flex items-center justify-between">
                                                        <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-gray-100">
                                                            Download Insta Resizer Script
                                                        </h3>
                                                        <button type="button" 
                                                                class="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" 
                                                                onclick="document.getElementById('download-modal').style.display = 'none';">
                                                            <span class="sr-only">Close</span>
                                                            <i class="tos tos-close text-xl"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- Modal Body -->
                                                <div class="px-8 py-5">
                                                    <!-- Logo section -->
                                                    <div class="flex items-center justify-center mb-5">
                                                        <img src="{{ asset('img/tools/insta-resize/scriptfile.svg') }}" 
                                                             alt="Adobe Photoshop" 
                                                             class="w-64 h-48">
                                                    </div>
                                                    
                                                    <!-- Version info -->
                                                    <div class="mb-6">
                                                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center mb-2 gant-modern-regular">
                                                            Version: <span class="gant-modern-bold text-gray-900 dark:text-gray-100">0.1</span> | Last updated: <span class="gant-modern-bold text-gray-900 dark:text-gray-100"> {{ date('d. m. Y.', file_exists($filePath = resource_path('tools/insta-resize-photoshop/insta-image-resize.jsx')) ? filemtime($filePath) : time()) }}</span>
                                                        </p>
                                                    </div>
                                                    
                                                    <!-- Download button - centered -->
                                                    <div class="flex justify-center mb-2">
                                                        <a href="{{ route('tools.download', ['filename' => 'insta-resizer',]) }}" 
                                                           class="group inline-flex items-center justify-center px-4 py-3 rounded-lg text-sm gant-modern-bold bg-gray-900 hover:bg-gray-800 dark:bg-gray-100 dark:hover:bg-gray-200 text-gray-100 dark:text-gray-900 transition-colors   ">
                                                            <i class="tos tos-down-loading mr-2 opacity-80"></i>
                                                            <span>Download Latest Version</span>
                                                        </a>
                                                    </div>
                                                   
                                                </div>
                                                
                                                <!-- Modal Footer -->
                                                <div class="px-8 pb-6 justify-center">
                                                <p class="text-gray-500 text-xs text-center gant-modern-regular">
                                                            * Tento plugin bol vytvorený ako voľnočasová pomoc pre kolegu. 
                                                            <span class="gant-modern-bold underline">DEV nenesie zodpovednosť</span> 
                                                            za jeho funkčnosť ani za akékoľvek problémy s ním spojené! 
                                                           
                                                        </p>
                                                        <p class="text-gray-500 text-xs text-center gant-modern-regular">
                                                            <span class="block mt-4 gant-modern-bold">¯\_(ツ)_/¯</span>
                                                        </p> 
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Last Modified Info -->
                                        <p class="text-xs text-center text-gray-500 dark:text-gray-400 mt-2 gant-modern-medium">
                                            <i class="wms wms-browse-history-outline opacity-60 mr-1"></i>
                                            Last update: <span class="gant-modern-bold">{{ date('d. m. Y.', file_exists($filePath = resource_path('tools/insta-resize-photoshop/insta-image-resize.jsx')) ? filemtime($filePath) : time()) }}</span>
                                            <span class="opacity-75 ml-1">{{ date('H:i', file_exists($filePath) ? filemtime($filePath) : time()) }}</span>
                                        </p>

                                        <!-- Table of Contents -->
                                        <div class="pt-8 flex flex-col items-center">
                                            <div class="w-4/5">
                                                <h3 class="text-xl gant-modern-bold text-gray-900 dark:text-gray-100 mb-2 text-left">User Manual</h3>
                                                <ul class="space-y-1 text-base text-left">
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="dimensions"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            1. Image Dimensions
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="crop"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            2. Processing Method
                                                        </a>
                                                        <!-- Processing Method Subsections -->
                                                        <ul class="pl-4 mt-1 space-y-1">
                                                            <li>
                                                                <a href="#" 
                                                                   data-nav-link="normal-fill"
                                                                   class="block py-1 transition-colors duration-200 text-sm gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                                    • Normal Fill
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#" 
                                                                   data-nav-link="crop-to-fit"
                                                                   class="block py-1 transition-colors duration-200 text-sm gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                                    • Crop to Fit
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#" 
                                                                   data-nav-link="generative-expand"
                                                                   class="block py-1 transition-colors duration-200 text-sm gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100 text-gray-600 dark:text-gray-400">
                                                                    • Generative Expand
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="background"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            3. Background Color
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="overlay"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            4. Overlay & Badges
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="quality"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            5. JPEG Quality
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="folders"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            6. Folder Selection
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="process"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            7. Process Images
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" 
                                                           data-nav-link="troubleshooting"
                                                           class="block py-1 transition-colors duration-200 gant-modern-regular hover:text-gray-900 dark:hover:text-gray-100">
                                                            8. Troubleshooting
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
<script>
    // Escape key handler for modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            var modal = document.getElementById('download-modal');
            if (modal && modal.style.display === 'flex') {
                modal.style.display = 'none';
            }
        }
    });

    // No need for click handler since we have inline onclick on the backdrop
    // This keeps the JS cleaner and more efficient
</script>
@endpush
