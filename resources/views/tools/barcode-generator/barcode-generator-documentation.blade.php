@extends('layouts.application')

@section('title', 'Barcode Generator Documentation')

@section('content')
<div data-page="barcode-generator-product" class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }} min-h-screen bg-white dark:bg-slate-950">
    <!-- Main snap scrolling container with Tailwind classes -->
    <div class="snap-container h-screen overflow-y-scroll snap-y snap-mandatory">
        <!-- Fixed Navigation Dots - Modernized UI -->
        <div class="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden sm:block">
            <div class="flex flex-col space-y-4">
                <a href="#hero" data-section="hero" class="nav-dot group relative flex items-center">
                    <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                    <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Home</span>
                </a>
                <a href="#features" data-section="features" class="nav-dot group relative flex items-center">
                    <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                    <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Features</span>
                </a>
                <a href="#how-it-works" data-section="how-it-works" class="nav-dot group relative flex items-center">
                    <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                    <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">How It Works</span>
                </a>
                <a href="#get-started" data-section="get-started" class="nav-dot group relative flex items-center">
                    <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                    <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Get Started</span>
                </a>
            </div>
        </div>
        
        <!-- Mobile Navigation Dots -->
        <div class="fixed bottom-6 left-0 right-0 z-50 sm:hidden">
            <div class="flex justify-center space-x-3">
                <a href="#hero" data-section="hero" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                <a href="#features" data-section="features" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                <a href="#how-it-works" data-section="how-it-works" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                <a href="#get-started" data-section="get-started" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
            </div>
        </div>

        <!-- Section 1: Hero Section -->
        <section id="hero" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 relative">
            <div class="w-full max-w-[95vw] md:max-w-[90vw] lg:max-w-[80vw] mx-auto flex flex-col items-center justify-center py-6 md:py-8 lg:py-12">
                <!-- Header Text -->
                <header class="w-full text-center mb-3 sm:mb-4 md:mb-6 lg:mb-8">
                    <p class="text-sm sm:text-base md:text-xl lg:text-2xl xl:text-3xl text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 md:mb-3 gant-modern-light">
                        Valid Barcodes Instantly
                    </p>
                    <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl gant-modern-bold text-black dark:text-white tracking-tighter leading-tight">
                        BarcodeGenerator <span class="gant-modern-light text-gray-500 dark:text-gray-400">v1.0</span>
                    </h1>
                </header>
                
                <!-- Hero Image with Hover CTA -->
                <div class="relative w-full mx-auto mb-4 sm:mb-6 md:mb-8 lg:mb-10 group">
                    <div class="flex items-center justify-center">
                        <div class="relative">
                            <!-- Background highlight effect -->
                            <div class="absolute inset-0 bg-gray-500/0 rounded-2xl filter blur-xl opacity-0 group-hover:opacity-30 transition-all duration-700 -z-10"></div>
                            
                            <!-- Images with hover effect -->
                            <img 
                                src="{{ asset('img/tools/barcode/heroLight.svg') }}" 
                                alt="Barcode Generator Interface"
                                class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto block dark:hidden transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(0,0,0,0.1)]"
                            >
                            <img 
                                src="{{ asset('img/tools/barcode/heroDark.svg') }}" 
                                alt="Barcode Generator Interface"
                                class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto hidden dark:block transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.1)]"
                            >
                        </div>
                    </div>
                    
                    <!-- CTA Buttons overlay - Positioned absolutely within the group container -->
                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                        <div class="flex justify-center space-x-4 ">
                            <a href="{{ route('tools.barcode-generator') }}" 
                               class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black font-medium py-4 px-5 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-xl z-20">
                                <i class="design design-barcode text-xl sm:text-2xl"></i>
                                <span class="text-base sm:text-lg gant-modern-bold">Try it now</span>
                            </a>
                            <a href="#features" 
                               class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-white dark:bg-slate-950 text-slate-900 dark:text-white border border-slate-300 dark:border-slate-700 font-medium py-4 px-5 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg z-20">
                               <i class="design design-book text-xl sm:text-2xl"></i>
                               <span class="text-base sm:text-lg gant-modern-bold">Learn more</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Scroll Down Indicator -->
                <div class="absolute bottom-8 sm:bottom-10 md:bottom-12 lg:bottom-16 left-0 right-0 mx-auto w-max flex flex-col items-center animate-bounce">
                    <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 gant-modern-regular">Scroll Down</span>
                    <i class="design design-pizza text-lg sm:text-xl md:text-2xl text-gray-800 dark:text-gray-200"></i>
                </div>
            </div>
        </section>

        <!-- Section 2: Features Section -->
        <section id="features" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 bg-slate-50/80 dark:bg-slate-900/80 backdrop-blur-sm transition-colors">
            <div class="w-full max-w-7xl mx-auto py-8 sm:py-10 md:py-12">
                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl gant-modern-bold tracking-tighter mb-6 text-black dark:text-white flex items-center gap-2 sm:gap-3">
                    <i class="design design-mixer text-2xl sm:text-3xl text-black dark:text-white"></i>
                    Features
                </h2>
                <p class="text-base sm:text-lg md:text-xl max-w-3xl text-gray-600 dark:text-gray-400 mb-10">
                    Everything you need to create professional barcodes for your projects
                </p>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Feature 1 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-barcode text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Multiple Formats</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            Support for EAN-13, EAN-8, and Code128 barcode standards, with more formats coming soon.
                        </p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-wrench text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Export Options</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            Download your barcodes as vector SVG files for perfect scaling or high-resolution PNG images.
                        </p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-measuring text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Customization</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            Adjust barcode width, height, font size, and toggle display options to suit your needs.
                        </p>
                    </div>

                    <!-- Feature 4 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-visible text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Live Preview</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            See your barcodes update in real-time as you make changes, before downloading.
                        </p>
                    </div>

                    <!-- Feature 5 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-listview text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Batch Processing</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            Generate multiple barcodes at once by entering comma-separated values for efficient workflows.
                        </p>
                    </div>

                    <!-- Feature 6 -->
                    <div class="p-6 rounded-xl bg-white dark:bg-slate-800/90 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="bg-slate-100 dark:bg-slate-700 p-2 rounded-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-coffee text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-white gant-modern-bold">Client-Side Processing</h3>
                        </div>
                        <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                            All operations run directly in your browser with no server requests, for speed and privacy.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: How It Works Section -->
        <section id="how-it-works" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 transition-colors">
            <div class="w-full max-w-7xl mx-auto py-8 sm:py-10 md:py-12">
                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl gant-modern-bold tracking-tighter mb-6 text-black dark:text-white flex items-center gap-2 sm:gap-3">
                    <i class="design design-idea text-2xl sm:text-3xl text-black dark:text-white"></i>
                    How It Works
                </h2>
                <p class="text-base sm:text-lg md:text-xl max-w-3xl text-gray-600 dark:text-gray-400 mb-10">
                    Creating barcodes has never been easier
                </p>

                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="order-2 lg:order-1">
                        <ol class="relative border-l border-slate-300 dark:border-slate-700 ml-3 space-y-8">
                            <li class="ml-6">
                                <span class="absolute flex items-center justify-center w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-full -left-4 ring-4 ring-white dark:ring-slate-900 text-slate-700 dark:text-slate-300 group-hover:scale-110 transition-transform duration-300">1</span>
                                <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Select Barcode Type</h3>
                                <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                    Choose from EAN-13, EAN-8, or Code128 formats depending on your requirements.
                                </p>
                            </li>
                            <li class="ml-6">
                                <span class="absolute flex items-center justify-center w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-full -left-4 ring-4 ring-white dark:ring-slate-900 text-slate-700 dark:text-slate-300 group-hover:scale-110 transition-transform duration-300">2</span>
                                <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Enter Your Data</h3>
                                <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                    Input the numeric values for your barcode. For multiple barcodes, separate values with commas.
                                </p>
                            </li>
                            <li class="ml-6">
                                <span class="absolute flex items-center justify-center w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-full -left-4 ring-4 ring-white dark:ring-slate-900 text-slate-700 dark:text-slate-300 group-hover:scale-110 transition-transform duration-300">3</span>
                                <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Customize Appearance</h3>
                                <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                    Adjust dimensions, font size, and toggle text visibility to match your design needs.
                                </p>
                            </li>
                            <li class="ml-6">
                                <span class="absolute flex items-center justify-center w-8 h-8 bg-slate-100 dark:bg-slate-800 rounded-full -left-4 ring-4 ring-white dark:ring-slate-900 text-slate-700 dark:text-slate-300 group-hover:scale-110 transition-transform duration-300">4</span>
                                <h3 class="text-xl font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Download Your Barcode</h3>
                                <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                    Choose SVG for vector quality or PNG for raster images, then download with a single click.
                                </p>
                            </li>
                        </ol>
                    </div>
                    <div class="order-1 lg:order-2">
                        <div class="bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <img src="{{ asset('img/tools/barcode/barcode-example.svg') }}" 
                                 alt="Barcode Generator Interface" 
                                 class="w-full rounded-lg transform transition-transform duration-500 group-hover:scale-105" 
                                 onerror="this.src='{{ asset('images/barcode-example-fallback.png') }}';this.onerror=null;" />
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Get Started Section -->
        <section id="get-started" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 bg-slate-50/80 dark:bg-slate-900/80 backdrop-blur-sm transition-colors">
            <div class="w-full max-w-7xl mx-auto py-8 sm:py-10 md:py-12">
                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl gant-modern-bold tracking-tighter mb-6 text-black dark:text-white flex items-center gap-2 sm:gap-3">
                    <i class="design design-ascend text-2xl sm:text-3xl text-black dark:text-white"></i>
                    Get Started
                </h2>
                <p class="text-base sm:text-lg md:text-xl max-w-3xl text-gray-600 dark:text-gray-400 mb-10">
                    Start creating professional barcodes in seconds
                </p>

                <div class="max-w-4xl mx-auto">
                    <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden border-2 border-gray-200 dark:border-gray-800 hover:border-black dark:hover:border-white transition-all duration-300">
                        <div class="md:flex">
                            <div class="md:flex-shrink-0">
                                <div class="h-48 w-full md:w-48 bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                    <i class="design design-barcode text-6xl text-slate-500 dark:text-slate-400"></i>
                                </div>
                            </div>
                            <div class="p-8">
                                <div class="text-xl font-semibold text-slate-900 dark:text-white mb-2 gant-modern-bold">Ready to create your first barcode?</div>
                                <p class="text-slate-600 dark:text-slate-400 mb-6 gant-modern-regular">
                                    Our intuitive interface makes barcode generation simple. No account required, no downloads, just instant results.
                                </p>
                                <div class="flex space-x-4">
                                    <a href="{{ route('tools.barcode-generator') }}" class="px-6 py-2 rounded-lg bg-slate-900 dark:bg-slate-700 text-white font-medium hover:bg-slate-800 dark:hover:bg-slate-600 transition-colors hover:scale-105 transform duration-300">
                                        <span class="gant-modern-bold">Launch tool</span>
                                    </a>
                                    <a href="#" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;" class="px-6 py-2 rounded-lg border border-slate-300 dark:border-slate-700 text-slate-700 dark:text-slate-300 font-medium hover:bg-slate-100 dark:hover:bg-slate-800/50 transition-colors hover:scale-105 transform duration-300">
                                        <span class="gant-modern-bold">Back to top</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-16 grid md:grid-cols-3 gap-8">
                        <div class="bg-white dark:bg-slate-800/90 backdrop-blur-sm p-6 rounded-xl shadow-sm border-2 border-gray-200 dark:border-gray-700 text-center group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-700 mb-4 group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-monitor-check text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-lg font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Quick Start</h3>
                            <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                No installation required. Open the tool and start creating barcodes instantly.
                            </p>
                        </div>

                        <div class="bg-white dark:bg-slate-800/90 backdrop-blur-sm p-6 rounded-xl shadow-sm border-2 border-gray-200 dark:border-gray-700 text-center group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-700 mb-4 group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-code text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-lg font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Learn More</h3>
                            <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                Check out other tools in our suite for all your design and development needs.
                            </p>
                        </div>

                        <div class="bg-white dark:bg-slate-800/90 backdrop-blur-sm p-6 rounded-xl shadow-sm border-2 border-gray-200 dark:border-gray-700 text-center group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-700 mb-4 group-hover:scale-110 transition-transform duration-300">
                                <i class="design design-circle-checked text-2xl text-slate-700 dark:text-slate-300"></i>
                            </div>
                            <h3 class="text-lg font-bold text-slate-900 dark:text-white mb-2 gant-modern-bold">Need Help?</h3>
                            <p class="text-slate-600 dark:text-slate-400 gant-modern-regular">
                                Visit our documentation for detailed information about barcode standards and usage.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sections = document.querySelectorAll('section[id]');
        const navDots = document.querySelectorAll('.nav-dot');
        
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.7
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.getAttribute('id');
                    
                    // Update nav dots
                    navDots.forEach(dot => {
                        dot.classList.remove('active');
                        if (dot.getAttribute('data-section') === id) {
                            dot.classList.add('active');
                        }
                    });
                }
            });
        }, observerOptions);
        
        sections.forEach(section => {
            observer.observe(section);
        });
    });
</script>
@endpush 