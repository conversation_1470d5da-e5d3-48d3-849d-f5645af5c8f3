@extends('layouts.application')

@section('title', 'Barcode Generator')

@section('content')
<!-- Pattern SVG Background with lowest z-index and pointer-events-none -->
<div class="fixed inset-0 pointer-events-none z-0 bg-white dark:bg-slate-950">
    <svg class="absolute inset-0 w-full h-full text-gray-900/[0.07] dark:text-gray-100/[0.08]" xmlns="http://www.w3.org/2000/svg" style="background-color: transparent;">
        <defs>
            <pattern id="pattern-barcode-generator" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
            </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-barcode-generator)" style="fill: url(#pattern-barcode-generator); background-color: transparent;" />
    </svg>
</div>

<!-- Main content with higher z-index -->
<div class="relative z-10 px-6 sm:px-12 md:px-12 lg:px-16 mt-16 bg-transparent dark:bg-transparent">
    <!-- Header Section -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8 bg-transparent dark:bg-transparent">
        <div>
            <div class="flex items-center gap-3">
                <h2 class="text-3xl md:text-5xl lg:text-6xl xl:text-8xl gant-modern-bold leading-tight md:leading-none tracking-tighter mb-4 dark:text-gray-200">Barcode Generator <span class="text-sm md:text-lg lg:text-xl gant-modern-regular opacity-60 leading-none tracking-tighter">v1.0</span></h2>
            </div>
            <p class="text-sm md:text-base text-gray-500 dark:text-gray-400 max-w-3xl">
                Generate EAN-13, EAN-8, and Code128 barcodes. Customize size and appearance, then download as SVG or PNG.
            </p>
        </div>
        <a href="{{ route('tools.barcode-generator.documentation') }}" class="px-3 py-1.5 rounded-lg border border-solid bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors text-base gant-modern-bold ">
            <i class="design design-book mr-2 opacity-60"></i>
            <span>Documentation</span>
        </a>
    </div>

    <!-- Barcode Generator Component Container -->
    <div class="w-full bg-transparent dark:bg-transparent">
        <div id="barcode-generator-app" class="bg-transparent dark:bg-transparent">
            <barcode-generator></barcode-generator>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // Force dark mode application immediately if needed
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        if (isDarkMode) {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark');
            
            // Remove any white backgrounds
            document.querySelectorAll('.bg-white').forEach(el => {
                if (el.classList.contains('dark:bg-slate-950') || 
                    el.classList.contains('dark:bg-transparent')) {
                    el.classList.remove('bg-white');
                    el.classList.add('bg-transparent');
                }
            });
        }
    });
</script>
@endpush 