@extends('master')

@section('title')
    | {{ $seasonOrder->season->name }} - {{__('samples.name')}}: {{ __('samples.receiving') }}
@endsection

@section('content')
    <header class="row">
        <div class="col-12 py-2">
            @include('partials.breadcrumbs', $breadCrumbs)
            <div class="d-flex justify-content-between">
                <div>
                    <div class="d-flex align-items-center">
                        <a href="{{route('season-orders.samples.index', $seasonOrder)}}"
                           class="btn btn-outline-secondary me-1">
                            <font-awesome-icon :icon="['far','left-long']"></font-awesome-icon>
                        </a>
                        <h1>
                            {{ __('samples.receiving') }}
                        </h1>
                    </div>
                </div>
                <div class="d-inline-flex">
                    <button class="btn btn-primary me-2 d-flex align-items-center" type="button"
                            data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true"
                            aria-controls="filterCollapse">
                        @if($filtering ?? null)
                            <i class="tos tos-checked me-2"></i>
                        @else
                            <i class="tos tos-empty me-2"></i>
                        @endif
                        <i class="tos tos-filter-filled me-2"></i>
                        <span>Filter Articles</span>
                    </button>

                    <div class="d-flex align-items-center">
                        <a href="{{route('season-orders.samples.receiving.actions.import-delivery-notes.create', $seasonOrder)}}"
                           class="btn btn-outline-secondary">
                            {{ __('frontend.user_tasks.import_delivery_note') }}
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </header>

    {{--    Filter  --}}
    <div class="accordion" id="accordionParentWrapper">
        <div>
            <universal-filter
                    :filter="{{ json_encode($filter ?? []) }}"
                    :filter-Inputs="{{ json_encode($filterInputs) }}"
                    reset-filter-route="{{route('reset-filter', [$filterName])}}"
                    reset-filter-attribute-route="{{route('reset-filter-attribute', [$filterName])}}"
                    :filter-tags="{{json_encode($filterTags)}}"
                    :disable-margin-bottom="{{json_encode(true)}}"
                    v-cloak
            >
                <!-- Pagination -->
                <div class="col-6 col-md-4 col-lg-2">
                    <label><i class="tos tos-display_items opacity-25 me-1"></i>Display Items</label>
                    <select name="filter[custom][pagination]" class="form-select">

                        <option value="25" @selected(($filter['custom']['pagination'] ?? null) == 25)>25
                        </option>
                        <option value="50" @selected(($filter['custom']['pagination'] ?? null) == 50)>50
                        </option>
                        <option value="100" @selected(($filter['custom']['pagination'] ?? null) == 100)>100
                        </option>
                        <option value="150" @selected(($filter['custom']['pagination'] ?? null) == 150)>150
                        </option>
                        <option value="200" @selected(($filter['custom']['pagination'] ?? null) == 200)>200
                        </option>
                    </select>
                </div>
            </universal-filter>
        </div>

    </div>

    {{--    FORM    --}}
    <div class="row bg-light justify-content-center">
        <div class="col-xl-6 col-lg-8 col-11 border border-1 rounded-5 bg-white my-xs-1 my-3">
            <form action="{{route('season-orders.samples.receiving.store', $seasonOrder)}}" method="POST"
                  class="d-flex align-items-center justify-content-center my-2">
                @csrf
                <i class="tos tos-article-scanned-outline fs-4 me-1 opacity-50"></i>
                <span class="d-xs-none fw-bold fs-4 me-2 text-nowrap"> {{__('articles.article-scan-item')}}</span>
                <input type="text" id="ean" name="ean" class="form-control me-2"
                       placeholder="{{__('articles.article_ean_code')}}" autofocus required inputmode="none">
                {{--                <button type="submit" class="btn btn-primary text-nowrap">{{__('actions.receive')}} <i--}}
                {{--                            class="tos tos-checked-outline"></i></button>--}}
            </form>
        </div>
    </div>

    {{--    LAST SCANNED    --}}
    <div class="row bg-light justify-content-around">
        <div class="col-12 text-center text-muted"><i
                    class="tos tos-deadline-outline"></i> {{__('actions.last_item_scanned')}}:
        </div>
        @if(!is_null($scannedSample))
            <div @class([
                    "col-xl-9 col-lg-10 col-11 fs-4 border border-1 rounded-5 mb-2 d-flex justify-content-between align-content-center alert alert-dismissible p-0",
                    "bg-danger" => $scannedSample->originalArticleColour->is_global_out || $scannedSample->originalArticleColour->is_removed,
                    "bg-white" => !$scannedSample->originalArticleColour->is_global_out && !$scannedSample->originalArticleColour->is_removed,
                    ])>
                <div class="my-2 w-100 text-center">
                    <i class="tos tos-article_code opacity-50"></i> {{$scannedSample->originalArticle->code}} - <i
                            class="tos tos-swatch"
                            style="color: {{$scannedSample->originalArticleColour->colour->hex_code}};"
                    >
                    </i>
                    {{$scannedSample->originalArticleColour->colour->colour_name}}
                    ({{$scannedSample->originalArticleColour->colour->colour_number}}) -
                    <span
                            class="fw-bold"><i
                                class="ordergroup ordergroup-{{strtolower(str_replace(' ', '',$scannedSample->originalArticle->orderGroup?->name))}}  opacity-50"></i>
                        {{$scannedSample->originalArticle->orderGroup?->name}}
                    </span>
                    -
                    @forelse($scannedSample->originalArticleColour->articleFlags as $articleFlag)
                        @if($articleFlag->flag_id === 2)

                            @foreach($articleFlag->centres as $centre)
                                {!!createBadge(getInitials($articleFlag->flag->name).': '.$centre->code, $articleFlag->flag->hex_color_code)!!}

                            @endforeach
                        @else
                            {!! createBadge(getInitials($articleFlag->flag->name), $articleFlag->flag->hex_color_code) !!}

                        @endif
                    @empty
                        0 {{__('flags.name')}}
                    @endforelse
                    @if(!is_null($scannedSample->note))
                        - <span class="fw-bold">{{$scannedSample->note}}</span>
                    @endif
                    @if($notFoundDiffPiecesSample == true)
                        <div class="col-12 text-center mt-2">
                    <span class="alert alert-danger p-1 rounded">{{__('samples.sample_outside_expected_number_error')}}. <span
                                class="fw-bold">{{__('articles.expected_pcs')}}:</span> {{$scannedSample->expected_pcs}} / <span
                                class="fw-bold">{{__('articles.received_pcs')}}:</span> {{$scannedSample->received_pcs}}</span>
                        </div>
                    @endif
                </div>

                <button type="button" class="btn opacity-50" data-bs-dismiss="alert"
                        aria-label="Close"><i class="tos tos-close"></i></button>

            </div>
        @endif

    </div>



    {{--TABLE--}}
    <div class="row bg-light">
        <div class="col-12 mt-2">
            <div class="table-responsive">
                <table class="table table-hover table-sm table border">
                    <thead class="bg-gray-600 text-light border">
                    <tr class="text-nowrap">
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                {{--  Hide columns selector  --}}
                                <form method="get">
                                    <div class="dropdown me-1">
                                        <button @class(["btn btn-sm btn-outline-secondary dropdown-toggle",
                                                    "text-light"=> !$isSomeColumnHidden,
                                                    "text-info" => $isSomeColumnHidden
                                                    ])
                                                type="button"
                                                id="form_settings" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="tos tos-cells-setup-outline me-2">&nbsp;</i>
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="form_settings">
                                            <li class="ps-2">{{__('actions.hide_columns')}}</li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][sample_season]"
                                                       id="sample_season" @checked(isset($filter['custom']['hide_attributes']['sample_season']) && $filter['custom']['hide_attributes']['sample_season'] ==1 )>
                                                <label class="form-check-label ms-2" for="sample_season">
                                                    {{__('articles.sample_season')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][article_code]"
                                                       id="article_code" @checked(isset($filter['custom']['hide_attributes']['article_code']) && $filter['custom']['hide_attributes']['article_code'] ==1 )>
                                                <label class="form-check-label ms-2" for="article_code">
                                                    {{__('articles.code')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][article_name]"
                                                       id="article_name" @checked(isset($filter['custom']['hide_attributes']['article_name']) && $filter['custom']['hide_attributes']['article_name'] ==1 )>
                                                <label class="form-check-label ms-2" for="article_name">
                                                    {{__('frontend.article_size_range_update.table.article_name')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][colour]"
                                                       id="colour" @checked(isset($filter['custom']['hide_attributes']['colour']) && $filter['custom']['hide_attributes']['colour'] ==1 )>
                                                <label class="form-check-label ms-2" for="colour">
                                                    {{__('articles.colour')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][gender]"
                                                       id="gender" @checked(isset($filter['custom']['hide_attributes']['gender']) && $filter['custom']['hide_attributes']['gender'] ==1 )>
                                                <label class="form-check-label ms-2" for="gender">
                                                    {{__('articles.gender')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][brand]"
                                                       id="brand" @checked(isset($filter['custom']['hide_attributes']['brand']) && $filter['custom']['hide_attributes']['brand'] ==1 )>
                                                <label class="form-check-label ms-2" for="brand">
                                                    {{__('articles.brand')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][order_group]"
                                                       id="order_group" @checked(isset($filter['custom']['hide_attributes']['order_group']) && $filter['custom']['hide_attributes']['order_group'] ==1 )>
                                                <label class="form-check-label ms-2" for="order_group">
                                                    {{__('articles.order-group')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][delivery_note]"
                                                       id="delivery_note" @checked(isset($filter['custom']['hide_attributes']['delivery_note']) && $filter['custom']['hide_attributes']['delivery_note'] ==1 )>
                                                <label class="form-check-label ms-2" for="delivery_note">
                                                    {{__('articles.delivery_note')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][expected_pcs]"
                                                       id="expected_pcs" @checked(isset($filter['custom']['hide_attributes']['expected_pcs']) && $filter['custom']['hide_attributes']['expected_pcs'] ==1 )>
                                                <label class="form-check-label ms-2" for="expected_pcs">
                                                    {{__('articles.expected_pcs')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][received_pcs]"
                                                       id="received_pcs" @checked(isset($filter['custom']['hide_attributes']['received_pcs']) && $filter['custom']['hide_attributes']['received_pcs'] ==1 )>
                                                <label class="form-check-label ms-2" for="received_pcs">
                                                    {{__('articles.received_pcs')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][flags]"
                                                       id="flags" @checked(isset($filter['custom']['hide_attributes']['flags']) && $filter['custom']['hide_attributes']['flags'] ==1 )>
                                                <label class="form-check-label ms-2" for="flags">
                                                    {{__('flags.name')}}
                                                </label>
                                            </li>
                                            <li class="ms-2">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                       name="filter[custom][hide_attributes][note]"
                                                       id="note" @checked(isset($filter['custom']['hide_attributes']['note']) && $filter['custom']['hide_attributes']['note'] ==1 )>
                                                <label class="form-check-label ms-2" for="note">
                                                    {{__('priceList.user-note')}}
                                                </label>
                                            </li>
                                            <li class="text-center">
                                                <a href="?hide=reset"
                                                   class="btn btn-outline-secondary btn-sm w-90">Reset</a>
                                            </li>
                                            <li class="text-center">
                                                <input type="submit" class="btn btn-success btn-sm mt-1 w-90"
                                                       value="{{__('actions.save')}}">
                                            </li>
                                            <input type="hidden" name="hide_columns_selector" value="1">
                                        </ul>
                                    </div>
                                </form>

                                @if(!isset($filter['custom']['hide_attributes']['sample_season']) || $filter['custom']['hide_attributes']['sample_season'] == 0)
                                    {{ __('articles.sample_season') }}
                                    <a href="?hide=sample_season-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=sample_season-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' => __('articles.sample_season')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif

                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['article_code']) || $filter['custom']['hide_attributes']['article_code'] == 0)
                                    {{ __('articles.code') }}
                                    <a href="?hide=article_code-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=article_code-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' => __('articles.code')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['article_name']) || $filter['custom']['hide_attributes']['article_name'] == 0)
                                    {{__('frontend.of_gallery_modal.article_name')}}
                                    <a href="?hide=article_name-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=article_name-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' => __('frontend.of_gallery_modal.article_name')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['colour']) || $filter['custom']['hide_attributes']['colour'] == 0)
                                    {{ __('frontend.article.attributes.colour') }}
                                    <a href="?hide=colour-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=colour-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('frontend.article.attributes.colour')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th class="d-print-none" scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['gender']) || $filter['custom']['hide_attributes']['gender'] == 0)
                                    {{ __('articles.gender') }}
                                    <a href="?hide=gender-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=gender-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('articles.gender')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th class="d-print-none" scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['brand']) || $filter['custom']['hide_attributes']['brand'] == 0)
                                    {{ __('articles.brand') }}
                                    <a href="?hide=brand-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=brand-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('articles.brand')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th class="d-print-none" scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['order_group']) || $filter['custom']['hide_attributes']['order_group'] == 0)
                                    {{ __('orderGroups.name') }}
                                    <a href="?hide=order_group-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=order_group-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('orderGroups.name')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['delivery_note']) || $filter['custom']['hide_attributes']['delivery_note'] == 0)
                                    {{ __('articles.delivery_note') }}
                                    <a href="?hide=delivery_note-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=delivery_note-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('articles.delivery_note')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['received_pcs']) || $filter['custom']['hide_attributes']['received_pcs'] == 0)
                                    {{ __('articles.received_pcs') }}
                                    <a href="?hide=received_pcs-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=received_pcs-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('articles.received_pcs')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['expected_pcs']) || $filter['custom']['hide_attributes']['expected_pcs'] == 0)
                                    {{ __('articles.expected_pcs') }}
                                    <a href="?hide=expected_pcs-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=expected_pcs-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('articles.expected_pcs')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>

                        <th class="d-print-none" scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['flags']) || $filter['custom']['hide_attributes']['flags'] == 0)
                                    {{ __('flags.name') }}
                                    <a href="?hide=flags-1" class="text-light opacity-25"
                                       title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=flags-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('flags.name')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th class="d-print-none justify-content-between" scope="col">
                            <div class="d-flex justify-content-between align-items-center">
                                @if(!isset($filter['custom']['hide_attributes']['note']) || $filter['custom']['hide_attributes']['note'] == 0)
                                    {{ __('priceList.user-note') }}
                                    <a href="?hide=note-1" class="text-light opacity-25" title="{{__('actions.hide')}}"><i
                                                class="tos tos-hidden ms-2">&nbsp;</i></a>
                                @else
                                    <a href="?hide=note-0" class="text-light opacity-25"
                                       title="{{__('actions.show', ['attributeName' =>  __('priceList.user-note')])}}"><i
                                                class="tos tos-visible ms-2">&nbsp;</i></a>
                                @endif
                            </div>
                        </th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody class="text-nowrap border">
                    @forelse($receivedSamples as $receivedSample)
                        <tr @class(['border' => true, 'bg-danger-light opacity-25' => $receivedSample->originalArticleColour->is_global_out == 1 || $receivedSample->originalArticleColour->is_removed == 1, 'opacity-75' => $receivedSample->originalArticleColour->is_global_out == 1 || $receivedSample->originalArticleColour->is_removed == 1,])>
                            <td>
                                <div class="d-flex justify-content-between align-items-center">
                                    @if($receivedSample->received_pcs == $receivedSample->expected_pcs && !$receivedSample->originalArticleColour->is_global_out && !$receivedSample->originalArticleColour->is_removed)
                                        <i class="tos tos-checked-outline text-success ms-2 opacity-50"></i>
                                    @elseif($receivedSample->received_pcs == $receivedSample->expected_pcs && ($receivedSample->originalArticleColour->is_global_out || $receivedSample->originalArticleColour->is_removed))
                                        <i class="tos tos-global_out-okay-outline text-danger ms-2 opacity-50"></i>
                                    @elseif($receivedSample->originalArticleColour->is_global_out || $receivedSample->originalArticleColour->is_removed)
                                        <i class="tos tos-global_out-outline text-danger ms-2 opacity-50"></i>
                                    @else
                                        <i class="tos tos-empty-slim ms-2 opacity-50"></i>
                                    @endif
                                    @if(!isset($filter['custom']['hide_attributes']['sample_season']) || $filter['custom']['hide_attributes']['sample_season'] == 0)
                                        {{$receivedSample->sampleSeasonOrder?->name ??__('actions.not_defined')}}
                                    @endif
                                    <div></div>
                                </div>

                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['article_code']) || $filter['custom']['hide_attributes']['article_code'] == 0)
                                    {{$receivedSample->articleColour->article->code}}
                                @endif</td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['article_name']) || $filter['custom']['hide_attributes']['article_name'] == 0)
                                    {{$receivedSample->originalArticle->name}}
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['colour']) || $filter['custom']['hide_attributes']['colour'] == 0)
                                    <i class="tos tos-swatch"
                                       style="color: {{$receivedSample->originalArticleColour->colour->hex_code}};"
                                    >
                                    </i>
                                    {{$receivedSample->originalArticleColour->colour->colour_name}}
                                    ({{$receivedSample->originalArticleColour->colour->colour_number}})
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['gender']) || $filter['custom']['hide_attributes']['gender'] == 0)
                                    {{ $receivedSample->originalArticleColour->gender->name ?? $receivedSample->originalArticleColour->article->gender->name ?? null }}
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['brand']) || $filter['custom']['hide_attributes']['brand'] == 0)
                                    {{ $receivedSample->originalArticle->brand }}
                                @endif
                            </td>
                            <td class="d-print-none">
                                @if(!isset($filter['custom']['hide_attributes']['order_group']) || $filter['custom']['hide_attributes']['order_group'] == 0)
                                    @if( $receivedSample->originalArticle->orderGroup != null)
                                        <i
                                                class="ordergroup ordergroup-{{strtolower(str_replace(' ', '',$receivedSample->originalArticle->orderGroup?->name))}} opacity-50"></i>
                                        {{ $receivedSample->originalArticle->orderGroup->name ?? null}}
                                        ({{ $receivedSample->originalArticle->orderGroup->name_sk_hu ?? null}})
                                    @else
                                        -
                                    @endif
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['delivery_note']) || $filter['custom']['hide_attributes']['delivery_note'] == 0)
                                    {{ $receivedSample->delivery_note_number ?? __('actions.not_defined')}}
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['received_pcs']) || $filter['custom']['hide_attributes']['received_pcs'] == 0)
                                    {!! $receivedSample->received_pcs == 0 ? '<i class="tos tos-empty-count2 opacity-50"></i>' :  ($receivedSample->received_pcs == $receivedSample->expected_pcs ? '<i class="tos tos-checked opacity-50"></i> '.$receivedSample->received_pcs : $receivedSample->received_pcs)!!}
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['expected_pcs']) || $filter['custom']['hide_attributes']['expected_pcs'] == 0)
                                    {{ $receivedSample->expected_pcs}}
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['flags']) || $filter['custom']['hide_attributes']['flags'] == 0)
                                    @forelse($receivedSample->originalArticleColour->articleFlags as $articleFlag)
                                        @if($articleFlag->flag_id === 2)

                                            @foreach($articleFlag->centres as $centre)
                                                {{createBadge(getInitials($articleFlag->flag->name).': '.$centre->code, $articleFlag->flag->hex_color_code)}}
                                            @endforeach

                                        @else
                                            {!! createBadge(getInitials($articleFlag->flag->name), $articleFlag->flag->hex_color_code)!!}

                                        @endif
                                    @empty
                                    @endforelse
                                @endif
                            </td>
                            <td>
                                @if(!isset($filter['custom']['hide_attributes']['note']) || $filter['custom']['hide_attributes']['note'] == 0)
                                    {{$receivedSample->note}}
                                @endif
                            </td>
                            <td class="text-center">
                                @if($receivedSample->received_pcs > 0)
                                    <form action="{{route('season-orders.samples.receiving.destroy', [$seasonOrder,$receivedSample])}}"
                                          method="post">
                                        @method('DELETE')
                                        @csrf
                                        <button type="submit" class="btn btn-outline-danger">
                                            <font-awesome-icon class="d-block"
                                                               :icon="['far','trash']"></font-awesome-icon>
                                        </button>
                                    </form>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr class="text-center bg-light">
                            <td class="p-5" colspan="100">{{__('actions.empty')}}</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>

        </div>
        {{$receivedSamples->links()}}
    </div>
@endsection

@section('script')
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const input = document.querySelector("#ean");

            if (input) {
                input.setAttribute("readonly", true);
                input.focus();
                setTimeout(() => {
                    input.removeAttribute("readonly");
                }, 100);
            }
        });
    </script>
@endsection