<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sell Through</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    @vite('resources/css/tos.css')
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        body {
            display: flex;
            flex-direction: column;
        }
        main {
            flex-grow: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .table-container-wrapper {
            flex-grow: 1;
            overflow-y: hidden;
            display: flex;
            flex-direction: column;
        }
        .table-container {
            flex-grow: 1;
            overflow: auto;
        }

        .table th, .table td {
            white-space: nowrap;
            padding: .35rem .4rem;
            background-color: rgba(255, 255, 255, 1.0) !important;
        }

        .table thead th,
        .table tfoot th {
            position: sticky;
            z-index: 2;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table thead th {
            top: 0;
            font-weight: 600;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .table tfoot th {
            bottom: 0;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .sticky-col,
        .sticky-col-right {
            position: sticky;
            z-index: 1;
        }

        .table thead .sticky-col, .table thead .sticky-col-right,
        .table tfoot .sticky-col, .table tfoot .sticky-col-right {
            z-index: 3; /* Corners on top */
            background-color: rgba(255, 255, 255, 1.0) !important;
        }
        /* Corrected selector for dark sticky columns when .table-dark is on thead/tfoot */
        .table thead.table-dark .sticky-col, .table thead.table-dark .sticky-col-right,
        .table tfoot.table-dark .sticky-col, .table tfoot.table-dark .sticky-col-right {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .sticky-col, .sticky-col-right {
            background-color: rgba(255, 255, 255, 1.0) !important;
        }

        .table-dark .sticky-col,
        .table-dark .sticky-col-right {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .sticky-col.table-dark,
        .sticky-col-right.table-dark {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }

        .table-dark th, .table-dark td, .table.table-dark {
            border-color: #495057;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table-info .sticky-col,
        .table-info .sticky-col-right {
            background-color: rgba(209, 236, 241, 1.0) !important;
        }

        .table-striped tbody tr:nth-child(odd) .sticky-col,
        .table-striped tbody tr:nth-child(odd) .sticky-col-right {
            background-color: rgba(248, 249, 250, 1.0) !important;
        }

        .table-hover tbody tr:hover .sticky-col,
        .table-hover tbody tr:hover .sticky-col-right {
            background-color: rgba(233, 236, 239, 1.0) !important;
        }

        .group-total-row td {
            position: sticky;
            z-index: 1;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .group-total-row .sticky-col,
        .group-total-row .sticky-col-right {
            z-index: 2;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .sticky-col-1 { width: 150px; left: 0px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-2 { width: 80px;  left: 150px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-3 { width: 200px; left: 230px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-4 { width: 80px;  left: 430px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-5 { width: 80px;  left: 510px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-6 { width: 80px;  left: 590px; background-color: rgba(255, 255, 255, 1.0) !important; }

        .sticky-col-right-1 { width: 300px; right: 0px; background-color: rgba(255, 255, 255, 1.0) !important; }

    </style>
</head>
<body onload="fnBrowserDetect()" data-new-gr-c-s-check-loaded="14.1242.0" data-gr-ext-installed="">
<div id="browserError" style="background-color: red; display: none; padding: 8px; color: white"></div>
<header class="container-fluid d-print-none bg-gray-100" style="background-color: #f8f9fa;">
    <nav class="navbar navbar-expand-lg navbar-light border-bottom pb-0">
    <a class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none" href="#">
        <img src="{{ asset('img/tos-logo.png') }}" height="25">
            </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse justify-content-between" id="navbarNav">
        <ul class="navbar-nav">

        </ul>
        <ul class="nav nav-tabs border-bottom-0">
                                        <li class="nav-item">
                    <a class="nav-link text-secondary" href="#/season-orders">
                        <i class="tos tos-products-multiple opacity-25 me-1"></i>
                        Season Orders
                    </a>
                </li>
                                                    <li class="nav-item">
                    <a class="nav-link text-secondary" href="#/gallery">
                        <i class="tos tos-images opacity-25 me-1"></i>
                        Gallery
                    </a>
                </li>
                                                        <li class="nav-item">
                    <a class="nav-link text-secondary" href="#/centres">
                        <i class="tos tos-center opacity-25 me-1"></i>
                        Centres
                    </a>
                </li>
                                                        <li class="nav-item">
                    <a class="nav-link text-secondary" href="#/global-settings">
                        <i class="tos tos-gear-setup opacity-25 me-1"></i>
                        Global Settings
                    </a>
                </li>
                                        <li class="nav-item">
                    <a class="nav-link text-secondary text-black active" href="#/statistics">
                        <i class="tos tos-status opacity-25 me-1"></i>
                        Statistics
                    </a>
                </li>
                                        <li class="nav-item">
                    <a class="nav-link text-secondary" href="#/financial-reports">
                        <i class="tos tos-coins opacity-25 me-1"></i>
                        Financial reports
                    </a>
                </li>
                                    <li class="nav-item dropdown">
                <a class="btn btn-light border dropdown-toggle fw-bold ms-1 mb-1" href="#" id="mainNavbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="tos tos-vermont-ucko opacity-50 me-1"></i>
                    Marek Nový
                </a>
                <ul class="dropdown-menu" aria-labelledby="mainNavbarDropdown">
                    <li><a href="#/users/logout" class="dropdown-item">Logout</a></li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
</header>

<main class="container-fluid" >
    <header class="row align-items-center">
        <div class="col-12 py-2">
            <nav aria-label="breadcrumb" class="d-xs-none">
                <ol class="breadcrumb m-0 d-print-none">
                    <li class="breadcrumb-item"><a href="#"><i
                                class="tos tos-house-fill text-muted"></i></a></li>
                    <li class="breadcrumb-item"><a class="link-dark" href="#/statistics">
                            Statistics </a></li>
                    <li class="breadcrumb-item"> Sell Through </li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between">
                <div class="d-flex align-items-center">
                    <h1 class="text-nowrap"> Sell Through </h1>
                </div>
                <div class="d-flex align-items-center d-print-none gap-2 flex-wrap justify-content-end">
                    <a class="btn btn-outline-secondary border " href="#/statistics">
                        <i class="tos tos-sales opacity-75 me-1"></i> Basic 
                    </a>
                    <a class="btn btn-outline-secondary border active" href="#/statistics">
                        <i class="tos tos-sales opacity-75 me-1"></i> Sell Through 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/presentation">
                        <i class="tos tos-play-button opacity-75 me-1"></i> Presentation 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/jeans-length">
                        <i class="tos tos-article_sizes_range opacity-75 me-1"></i> Jeans - Length 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/jeans-fit">
                        <i class="tos tos-categorization opacity-75 me-1"></i> Jeans - Fit 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/evaluate-order">
                        <i class="tos tos-difference opacity-75 me-1"></i> Evaluate of orders 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/source-data">
                        <i class="tos tos-good-id opacity-75 me-1"></i> Source Data by Goods ID 
                    </a>
                    <a class="btn btn-outline-secondary border" href="#/statistics/logs">
                        <i class="tos tos-info-outline opacity-75 me-1"></i> Logs 
                    </a>
                    <form action="#/statistics" method="POST" onsubmit="return confirm('Are you sure?');">
                        <input type="hidden" name="_method" value="put">
                        <input type="hidden" name="_token" value="qeoIk7kntrUeXD829HUQpeIzkvGedQohQviSThki"> 
                        <input type="submit" class="btn btn-outline-success border" value="Update Sales">
                    </form>
                </div>
            </div>
        </div>
    </header>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h5 class="mb-0"><i class="tos tos-filter me-2"></i>Filter</h5>
            <div class="d-flex align-items-center gap-2">
                <a href="{{ route('tools.sell-through.download.blade') }}" class="btn btn-danger">
                    <i class="tos tos-download me-2"></i>Download Blade Template
                </a>
                <a href="{{ route('tools.sell-through.download.vue') }}" class="btn btn-danger">
                    <i class="tos tos-download me-2"></i>Download Vue Component
                </a>
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                    <i class="tos tos-filter me-2"></i>Search Filter
                </button>
            </div>
        </div>

        <div class="collapse show" id="filterCollapse">
            <div class="mb-4">
                <form action="/tools/sell-through" method="GET">
                    @csrf
                    <div class="row g-3 align-items-center">
                        <div class="col-md-4 col-lg-2">
                            <label for="centre-filter" class="form-label"><i class="tos tos-center_name opacity-50 me-1"></i>Centres</label>
                            <select id="centre-filter" class="form-select" name="filter[centreFilter][equal][id]">
                                <option selected>All</option>
                                <option value="1">Center 1</option>
                                <option value="2">Center 2</option>
                            </select>
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="city-filter" class="form-label"><i class="tos tos-city opacity-50 me-1"></i>City</label>
                            <input type="text" id="city-filter" class="form-control" name="filter[centreFilter][like][city]" placeholder="e.g. Prague">
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="country-filter" class="form-label"><i class="tos tos-country opacity-50 me-1"></i>Country</label>
                            <select id="country-filter" class="form-select" name="filter[centreFilter][equal][country_id]">
                                <option selected>All</option>
                                <option value="1">Slovakia</option>
                                <option value="2">Czech Republic</option>
                            </select>
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="season-order-filter" class="form-label"><i class="tos tos-so_category opacity-50 me-1"></i>Season Order</label>
                            <select id="season-order-filter" class="form-select" name="filter[equal][article.season_order_id]">
                                <option selected>All</option>
                                <option value="1">SS24</option>
                                <option value="2">FW24</option>
                            </select>
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="order-group-filter" class="form-label"><i class="tos tos-order_group opacity-50 me-1"></i>Order Group</label>
                            <select id="order-group-filter" class="form-select" name="filter[equal][article.order_group_id]">
                                <option selected>All</option>
                                <option value="1">01 blazers</option>
                                <option value="2">01 coats</option>
                            </select>
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="brand-filter" class="form-label"><i class="tos tos-brand opacity-50 me-1"></i>Brand</label>
                            <select id="brand-filter" class="form-select" name="filter[equal][article.brand]">
                                <option selected>All</option>
                                <option value="GA">GANT</option>
                                <option value="LM">La Martina</option>
                            </select>
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="goods-id-filter" class="form-label"><i class="tos tos-good-id opacity-50 me-1"></i>Goods ID</label>
                            <input type="text" id="goods-id-filter" class="form-control" name="filter[statistics][like][articleDetail.goods_id]" placeholder="e.g. 123456">
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="article-code-filter" class="form-label"><i class="tos tos-article_code opacity-50 me-1"></i>Article Code</label>
                            <input type="text" id="article-code-filter" class="form-control" name="filter[like][article.code]" placeholder="e.g. 7706259">
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="article-name-filter" class="form-label"><i class="tos tos-article_name opacity-50 me-1"></i>Article Name</label>
                            <input type="text" id="article-name-filter" class="form-control" name="filter[like][article.name]" placeholder="e.g. SLIM JERSEY BLAZER">
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="tax-date-from" class="form-label"><i class="tos tos-date-from opacity-50 me-1"></i>Tax Date From</label>
                            <input type="date" id="tax-date-from" class="form-control" name="filter[statistics][greaterThanOrEqual][tax_date]">
                        </div>
                        <div class="col-md-4 col-lg-2">
                            <label for="tax-date-to" class="form-label"><i class="tos tos-date-to opacity-50 me-1"></i>Tax Date To</label>
                            <input type="date" id="tax-date-to" class="form-control" name="filter[statistics][lessThanOrEqual][tax_date]">
                        </div>
                    </div>

                    <hr class="my-3">

                    <div class="row">
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-secondary"><i class="tos tos-share me-2"></i>Share</button>
                        </div>
                        <div class="col-md-4 d-flex justify-content-center">
                            <a href="/tools/sell-through" class="btn btn-light border me-2"><i class="tos tos-reset me-2"></i>Reset</a>
                            <button class="btn btn-primary" type="submit"><i class="tos tos-search me-2"></i>Search</button>
                        </div>
                        <div class="col-md-4">
                            
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- New Outlet Table -->
    <div id="app" data-v-app="" class="row table-container-wrapper position-relative">
        <loader :visible="showLoader"></loader>
        <div class="col-12 h-100">
            <div class="table-container h-100">
                <table class="table table-sm table-bordered table-hover mb-0 text-center align-middle">
                    <thead class="table-dark text-white fw-normal small">
                        <tr>
                            <th class="sticky-col sticky-col-1 fw-normal">Product</th>
                            @foreach (array_slice($header, 6, -4) as $th)
                                <th><i class="tos tos-center me-1 opacity-50"></i>{{ $th }}</th>
                            @endforeach
                            <th class="sticky-col-right sticky-col-right-1">Sell-Through</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($groups as $groupName => $group)
                            @foreach ($group['rows'] as $row)
                                <tr>
                                    <td class="sticky-col sticky-col-1 text-start p-0">
                                        <div style="padding: 0.35rem 0.4rem; min-width: 340px; max-width: 420px;">
                                        <div class="fw-bold text-truncate" style="max-width: 320px; margin: 0.1rem 0;">  {{ $row['Article'] }}</div>
                                            <div class="fw-bold text-muted">{{ $row['Product Name'] }} &mdash; {{ $row['Order Group'] }} </div>               
                                            <div class="d-flex justify-content-between text-muted">
                                                <small class="text-muted">Colour:<span class="text-dark fw-bold ms-1">{{ $row['Colour'] }}</span> &bull; First delivery:<span class="text-dark fw-bold ms-1">{{ $row['First Delivery'] }}</span></small>
                                                <span class="fw-bold"><span class="bg-secondary text-white rounded-1 px-1">{{ $row['Price'] }}</span></span>
                                            </div>
                                        </div>
                                    </td>
                                    @foreach (array_slice($row, 6, -4) as $value)
                                        <td>{{ $value }}</td>
                                    @endforeach
                                    <td class="sticky-col-right sticky-col-right-1 p-0">
                                        @php
                                            $sales = (int) $row['Sales'];
                                            $order = (int) $row['Order'];
                                            $stock = (int) $row['Stock'];
                                        @endphp
                                        <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}"></sell-through-progress>
                                    </td>
                                </tr>
                            @endforeach
                            @if (!empty($group['total']))
                                <tr class="group-total-row">
                                    <td class="sticky-col sticky-col-1 text-start table-dark fw-bold">{{ $group['total']['Order Group'] }}</td>
                                    <td class="sticky-col sticky-col-2 table-dark"></td>
                                    <td class="sticky-col sticky-col-3 table-dark"></td>
                                    <td class="sticky-col sticky-col-4 table-dark"></td>
                                    <td class="sticky-col sticky-col-5 table-dark"></td>
                                    <td class="sticky-col sticky-col-6 table-dark"></td>
                                    @foreach (array_slice($group['total'], 6, -4) as $value)
                                        <td class="table-dark fw-bold">{{ $value }}</td>
                                    @endforeach
                                    <td class="sticky-col-right sticky-col-right-1 table-dark p-0">
                                        @php
                                            $sales = (int) $group['total']['Sales'];
                                            $order = (int) $group['total']['Order'];
                                            $stock = (int) $group['total']['Stock'];
                                        @endphp
                                        <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}" :dark="true"></sell-through-progress>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                    <tfoot class="table-dark mb-5">
                        <tr>
                            <th class="sticky-col sticky-col-1 text-start" colspan="6">{{ $grandTotal['Order Group'] }}</th>
                            @foreach (array_slice($grandTotal, 6, -4) as $value)
                                <th>{{ $value }}</th>
                            @endforeach
                            <th class="sticky-col-right sticky-col-right-1 p-0">
                                @php
                                    $sales = (int) str_replace(' ', '', $grandTotal['Sales']);
                                    $order = (int) str_replace(' ', '', $grandTotal['Order']);
                                    $stock = (int) str_replace(' ', '', $grandTotal['Stock']);
                                @endphp
                                <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}" :dark="true"></sell-through-progress>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</main>

@vite(['resources/css/tos.css', 'resources/js/sell_through.js'])

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const thead = document.querySelector('.table thead');
        if (thead) {
            const headerHeight = thead.offsetHeight;
            const totalRows = document.querySelectorAll('.group-total-row td');
            totalRows.forEach(td => {
                td.style.top = headerHeight + 'px';
            });
        }

        const filterForm = document.querySelector('form[action="/tools/sell-through"]');
        if(filterForm) {
            filterForm.addEventListener('submit', function(event) {
                const appVm = document.querySelector('#app').__vue_app__;
                if (appVm) {
                    appVm._instance.data.showLoader = true;
                }
            });
        }
    });
</script>

<script>
    function fnBrowserDetect() {
        let userAgent = navigator.userAgent;
        let message;

        if (userAgent.match(/chrome|chromium|crios/i)) {
            message = "";
        } else if (userAgent.match(/firefox|fxios/i)) {
            message = "";
        } else if (userAgent.match(/safari/i)) {
            message = "";
        } else if (userAgent.match(/opr\//i)) {
            message = "";
        } else if (userAgent.match(/edg/i)) {
            message = "";
        } else if (userAgent.match(/trident|MSIE/i)) {
            message = "Your browser is not supported by this application and some features may not work properly." +
                "We recommend using Edge, Chrome, Firefox, Opera, or Safari, for example";
        } else {
            message = "Your browser may not be supported by this application and some features may not work properly." +
                "We recommend using Edge, Chrome, Firefox, Opera, or Safari";
        }
        if (message !== "") {
            document.getElementById("browserError").innerText = message;
            document.getElementById("browserError").style.display = "block";
        }
    }
</script>

</body>
</html>
