@extends('layouts.application')

@section('content')
<div data-page="svg-animation-product" class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }} min-h-screen bg-white dark:bg-slate-950">
        <!-- Main snap scrolling container with Tailwind classes -->
        <div class="snap-container h-screen overflow-y-scroll snap-y snap-mandatory">
            <!-- Fixed Navigation Dots - Modernized UI -->
            <div class="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden sm:block">
                <div class="flex flex-col space-y-4">
                    <a href="#hero" data-section="hero" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Home</span>
                    </a>
                    <a href="#demo" data-section="demo" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Demo</span>
                    </a>
                    <a href="#features" data-section="features" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Features</span>
                    </a>
                    <a href="#roadmap" data-section="roadmap" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Roadmap</span>
                    </a>
                    <a href="#pricing" data-section="pricing" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Get Started</span>
                    </a>
                </div>
            </div>
            
            <!-- Mobile Navigation Dots at the bottom - Only visible on small screens -->
            <div class="fixed bottom-6 left-0 right-0 z-50 sm:hidden">
                <div class="flex justify-center space-x-3">
                    <a href="#hero" data-section="hero" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#demo" data-section="demo" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#features" data-section="features" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#roadmap" data-section="roadmap" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#pricing" data-section="pricing" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                </div>
            </div>

            <!-- Section 1: Hero Section -->
            <section id="hero" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 relative">
                <div class="w-full max-w-[95vw] md:max-w-[90vw] lg:max-w-[80vw] mx-auto flex flex-col items-center justify-center py-6 md:py-8 lg:py-12">
                    <!-- Header Text - Enhanced Responsiveness -->
                    <header class="w-full text-center mb-3 sm:mb-4 md:mb-6 lg:mb-8">
                        <p class="text-sm sm:text-base md:text-xl lg:text-2xl xl:text-3xl text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 md:mb-3 gant-modern-light">
                             Static into Animated 
                        </p>
                        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl gant-modern-bold text-black dark:text-white tracking-tighter leading-tight">
                             Animate.svg <span class="gant-modern-light text-gray-500 dark:text-gray-400">v1.0</span>
                        </h1>
                    </header>
                    
                    <!-- Hero Image with Hover CTA -->
                    <div class="relative w-full mx-auto mb-4 sm:mb-6 md:mb-8 lg:mb-10 group">
                        <div class="flex items-center justify-center">
                            <div class="relative">
                                <!-- Background highlight effect -->
                                <div class="absolute inset-0 bg-gray-500/0 rounded-2xl filter blur-xl opacity-0 group-hover:opacity-30 transition-all duration-700 -z-10"></div>
                                
                                <!-- Images with hover effect -->
                                <img 
                                    src="{{ asset('img/tools/svg-animate/heroLight.svg') }}" 
                                    alt="SVG Animation Tool Hero Light"
                                    class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto block dark:hidden transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(0,0,0,0.1)]"
                                >
                                <img 
                                    src="{{ asset('img/tools/svg-animate/heroDark.svg') }}" 
                                    alt="SVG Animation Tool Hero Dark"
                                    class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto hidden dark:block transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.1)]"
                                >
                            </div>
                        </div>
                        
                        <!-- CTA Buttons overlay - Positioned absolutely within the group container -->
                        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="flex justify-center space-x-4 ">
                                <a href="{{ route('svg.download', ['filename' => 'sample.svg']) }}" 
                                   class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black font-medium py-4 px-5 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-xl z-20">
                                    <i class="design design-download-svg text-xl sm:text-2xl"></i>
                                    <span class="text-base sm:text-lg gant-modern-bold">Download Sample SVG</span>
                                </a>
                                <a href="{{ route('admin.svg-animation') }}" 
                                   class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-white dark:bg-slate-950 text-slate-900 dark:text-white border border-slate-300 dark:border-slate-700 font-medium py-4 px-5 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg z-20">
                                    <i class="design design-animatesvg text-xl sm:text-2xl"></i>
                                    <span class="text-base sm:text-lg gant-modern-bold">Try animate.svg now</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scroll Down Indicator - Improved Centering -->
                    <div class="absolute bottom-8 sm:bottom-10 md:bottom-12 lg:bottom-16 left-0 right-0 mx-auto w-max flex flex-col items-center animate-bounce">
                        <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 gant-modern-regular">Scroll Down</span>
                        <i class="design design-pizza text-lg sm:text-xl md:text-2xl text-gray-800 dark:text-gray-200"></i>
                    </div>
                </div>
            </section>

            <!-- Section 2: Demo Section -->
            <section id="demo" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <div class="relative">
                        <div class="flex flex-col lg:flex-row items-center gap-6 sm:gap-8 md:gap-10 lg:gap-12 py-8 sm:py-10 md:py-12">
                            <div class="flex-1">
                                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl gant-modern-bold text-black dark:text-white mb-3 sm:mb-4 md:mb-6">
                                    One, Two, Three... Bazinga!
                                </h2>
                                <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8">
                                    Our SVG Animation tool lets you easily transform static SVG files into engaging, animated graphics that capture attention and enhance visual storytelling—no coding required.
                                </p>
                            </div>
                            <div class="flex-1 w-full bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 md:p-8 rounded-2xl shadow-lg flex items-center justify-center border border-gray-200 dark:border-gray-800">
                                <!-- Animated SVG Demo - Responsive Container -->
                                <div class="w-full h-48 sm:h-56 md:h-64 flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12">
                                    <img 
                                        src="{{ asset('img/tools/svg-animate/demo.svg') }}" 
                                        alt="SVG Animation Demo"
                                        class="w-full h-full object-contain mb-4 sm:mb-6"
                                    >
                                    <a href="{{ route('svg.download', ['filename' => 'demo.svg']) }}" 
                                    class="inline-flex items-center justify-center gap-1 sm:gap-2 bg-slate-950 dark:bg-white text-white dark:text-black font-medium py-1.5 sm:py-2 px-4 sm:px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-sm sm:text-base">
                                        <i class="design design-download-svg text-base sm:text-lg"></i>
                                        <span>Download Demo SVG</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 3: Key Features Section -->
            <section id="features" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-sparkles text-2xl sm:text-3xl text-black dark:text-white"></i>
                        Key Features
                    </h2>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                        <!-- Path Animation - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-svg-stuff text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Path Animation</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Create stunning drawing animations with path sequencing, timing controls, and easing options
                            </p>
                        </div>

                        <!-- Animation Timing - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-acceleration-outline text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Precision Timing</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Control animation speed, delays between elements, and synchronization for perfect visual storytelling
                            </p>
                        </div>

                        <!-- Remaining feature cards with similar responsive improvements -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-transparency text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Multiple Animation Types</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Choose from Delayed Start, Synchronous, or One-By-One animation patterns for unique visual effects
                            </p>
                        </div>

                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-bracket2 text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Flexible Triggers</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Set animations to start automatically or trigger them manually with custom CSS class names
                            </p>
                        </div>

                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-refresh text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Advanced Loop Control</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Create infinitely looping animations with customizable fade effects, start delays, and end pauses
                            </p>
                        </div>

                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-download-svg text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">One-Click Export</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Download your animated SVGs instantly with optimized code for websites, apps, and marketing materials
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 4: Roadmap -->
            <section id="roadmap" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-roadmap text-2xl sm:text-3xl text-black dark:text-white"></i>
                        Development Roadmap
                    </h2>
                    
                    <!-- Roadmap Timeline -->
                    <div class="relative">
                        <!-- Desktop Timeline Line - Transparent to Black Gradient & Thicker -->
                        <div class="hidden md:block absolute h-1 sm:h-1.5 top-6 left-0 right-0 z-0 bg-gradient-to-r from-transparent via-gray-300 to-black dark:from-transparent dark:via-gray-700 dark:to-white"></div>
                        
                        <!-- Vertical Timeline Line for Mobile - Also Gradient & Thicker -->
                        <div class="md:hidden absolute w-1 sm:w-1.5 bg-gradient-to-b from-transparent to-black dark:from-transparent dark:to-white top-0 bottom-0 left-6 z-0"></div>
                        
                        <!-- Timeline Items - Stack on Mobile, Row on Desktop -->
                        <div class="md:flex md:space-x-0 md:justify-between relative z-10">
                            <!-- Version 1.0 -->
                            <div class="relative flex mb-10 md:mb-0 md:flex-col md:items-center group">
                                <!-- Mobile Layout: Side by Side -->
                                <div class="flex md:block">
                                    <div class="w-12 h-12 rounded-full bg-slate-950 dark:bg-white text-white dark:text-black flex items-center justify-center border-2 border-white dark:border-black shadow-sm z-10">
                                        <span class="text-sm font-bold">0.1</span>
                                    </div>
                                    
                                    <!-- Content for Mobile: Next to Circle -->
                                    <div class="md:hidden ml-6 w-full">
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Dec 15, 2023</div>
                                        <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Initial Release</h3>
                                        <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                            <li>Path animation engine</li>
                                            <li>Animation timing controls</li>
                                            <li>SVG importing and exporting</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Content for Desktop: Below Circle -->
                                <div class="hidden md:block mt-5 px-4 py-4 rounded-lg bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-800 shadow-sm w-full max-w-[250px]">
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-1.5">Dec 15, 2023</div>
                                    <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Initial Release</h3>
                                    <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                        <li>Path animation engine</li>
                                        <li>Animation timing controls</li>
                                        <li>SVG importing and exporting</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Version 1.1 -->
                            <div class="relative flex mb-10 md:mb-0 md:flex-col md:items-center group">
                                <!-- Mobile Layout: Side by Side -->
                                <div class="flex md:block">
                                    <div class="w-12 h-12 rounded-full bg-slate-950 dark:bg-white text-white dark:text-black flex items-center justify-center border-2 border-white dark:border-black shadow-sm z-10">
                                        <span class="text-sm font-bold">0.8</span>
                                    </div>
                                    
                                    <!-- Content for Mobile: Next to Circle -->
                                    <div class="md:hidden ml-6 w-full">
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Feb 2, 2024</div>
                                        <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Enhanced Controls</h3>
                                        <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                            <li>Added loop animation controls</li>
                                            <li>Improved path selection</li>
                                            <li>Fixed Safari compatibility issues</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Content for Desktop: Below Circle -->
                                <div class="hidden md:block mt-5 px-4 py-4 rounded-lg bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-800 shadow-sm w-full max-w-[250px]">
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-1.5">Feb 2, 2024</div>
                                    <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Enhanced Controls</h3>
                                    <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                        <li>Added loop animation controls</li>
                                        <li>Improved path selection</li>
                                        <li>Fixed Safari compatibility issues</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Version 1.2 -->
                            <div class="relative flex mb-10 md:mb-0 md:flex-col md:items-center group">
                                <!-- Mobile Layout: Side by Side -->
                                <div class="flex md:block">
                                    <div class="w-12 h-12 rounded-full bg-slate-950 dark:bg-white text-white dark:text-black flex items-center justify-center border-2 border-white dark:border-black shadow-sm z-10">
                                        <span class="text-sm font-bold">0.9</span>
                                    </div>
                                    
                                    <!-- Content for Mobile: Next to Circle -->
                                    <div class="md:hidden ml-6 w-full">
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Apr 18, 2024</div>
                                        <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Animation Types</h3>
                                        <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                            <li>Added synchronous animation mode</li>
                                            <li>Added one-by-one animation mode</li>
                                            <li>Enhanced easing options</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Content for Desktop: Below Circle -->
                                <div class="hidden md:block mt-5 px-4 py-4 rounded-lg bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-800 shadow-sm w-full max-w-[250px]">
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-1.5">Apr 18, 2024</div>
                                    <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Animation Types</h3>
                                    <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                        <li>Added synchronous animation mode</li>
                                        <li>Added one-by-one animation mode</li>
                                        <li>Enhanced easing options</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Current Version -->
                            <div class="relative flex md:flex-col md:items-center group">
                                <!-- Mobile Layout: Side by Side -->
                                <div class="flex md:block">
                                    <div class="w-12 h-12 rounded-full bg-slate-950 dark:bg-white text-white dark:text-black flex items-center justify-center border-2 border-white dark:border-black shadow-sm z-10 ring-2 ring-black dark:ring-white ring-offset-2">
                                        <span class="text-sm font-bold">1.0</span>
                                    </div>
                                    
                                    <!-- Content for Mobile: Next to Circle -->
                                    <div class="md:hidden ml-6 w-full">
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">Jul 5, 2024</div>
                                        <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Performance Update</h3>
                                        <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                            <li>Optimized SVG rendering</li>
                                            <li>Added manual trigger options</li>
                                            <li>New presets for common animations</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Content for Desktop: Below Circle -->
                                <div class="hidden md:block mt-5 px-4 py-4 rounded-lg bg-white dark:bg-slate-950 border-2 border-black dark:border-white shadow-sm w-full max-w-[250px]">
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-1.5">Jul 5, 2024</div>
                                    <h3 class="text-base gant-modern-bold text-black dark:text-white mb-2">Performance Update</h3>
                                    <ul class="text-xs text-gray-600 dark:text-gray-300 list-disc pl-4 space-y-1">
                                        <li>Optimized SVG rendering</li>
                                        <li>Added manual trigger options</li>
                                        <li>New presets for common animations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Future Updates Teaser - Minimalist Design -->
                    <div class="mt-10 md:mt-16 text-center px-4 py-6 border border-gray-200 dark:border-gray-800 rounded-lg">
                        <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400 gant-modern-light">
                            Stay tuned for upcoming features...
                        </p>
                    </div>
                </div>
            </section>

            <!-- Section 5: Pricing/Get Started -->
            <section id="pricing" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-get-started text-2xl sm:text-3xl text-black dark:text-white"></i>
                        Get Started
                    </h2>
                    
                    <!-- CTA Section - Refined Minimalist Design -->
                    <section class="mb-16 sm:mb-20 md:mb-24 lg:mb-32">
                        <div class="bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 hover:border-black dark:hover:border-white transition-all duration-300 rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-10 lg:p-12 text-center shadow-sm">
                            <i class="design design-magic_wand text-3xl sm:text-4xl md:text-5xl text-black dark:text-white mb-4 sm:mb-6"></i>
                            <h2 class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-black dark:text-white mb-3 sm:mb-4 md:mb-6">Ready to Rock?</h2>
                            <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-2xl mx-auto gant-modern-regular">
                               Have some ideas? Bring your static SVGs to life now with our easy-to-use animation tool.
                            </p>
                            <a href="{{ route('admin.svg-animation') }}" 
                               class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black border-2 border-transparent font-medium py-4 sm:py-5 px-8 sm:px-10 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-base sm:text-lg">
                                <span class="gant-modern-bold">Launch SVG Animator</span>
                                <i class="design design-acceleration-outline text-lg sm:text-xl"></i>
                            </a>
                        </div>
                    </section>
                </div>
            </section>
        </div>
    </div>
@endsection


