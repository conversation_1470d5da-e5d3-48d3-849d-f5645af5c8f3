<x-layouts.blank>
    <div class="font-gant-modern relative h-screen overflow-hidden">
        <div class="relative h-full overflow-hidden">
            <!-- Full-width Drag and Drop Area -->
            <div class="absolute inset-0 w-full h-full p-6">
                <div class="viewer w-full h-full bg-white dark:bg-slate-950 relative overflow-hidden">
                    <!-- Move pattern SVG outside of introbox -->
                    <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.08] dark:text-gray-100/[0.16] z-0" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                                <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#pattern-3)" />
                    </svg>
                    
                    <div class="alert-message hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 absolute top-4 left-4 right-4 z-50" role="alert">
                        <span class="message-text"></span>
                        <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.classList.add('hidden')">
                            <svg class="fill-current h-6 w-6" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </button>
                    </div>             
                    <div class="introbox text-center flex-1 flex flex-col justify-center h-full relative z-10">         
                        <div class="dropzone relative border-2 border-dashed border-gray-300 dark:border-gray-700 p-8 text-center cursor-pointer transition-all duration-200 flex-1 flex flex-col justify-center h-full group">
                            <div class="dropzone-content inline-flex flex-col items-center justify-center gap-4 relative z-1 bg-white dark:bg-slate-950 p-16 rounded-xl shadow-sm mx-auto">
                                <div class="w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                    <i class="hrms hrms-document-upload2-outline text-4xl text-gray-400 dark:text-gray-500 wiggle-animation"></i>
                                </div>
                                <div>
                                    <p class="font-bold text-xl text-gray-900 dark:text-gray-200 gant-modern-bold">Drag and drop your SVG here</p>
                                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 gant-modern-regular">or click to select a file</p>
                                </div>
                            </div>
                            <input type="file" accept="image/svg+xml" class="hidden" id="fileInput">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Floating Options Panel -->
            <div class="sidebar w-full !w-96 backdrop-blur-lg bg-white-100 dark:bg-gray-950/80 rounded-xl shadow-lg p-6 fixed top-4 left-4 z-10 md:absolute md:top-4 md:left-4 md:bottom-auto draggable-panel">
                
                <!-- Panel Header with Drag Handle -->
                <div class="sidebar-head dark:border-gray-700 cursor-move drag-handle flex items-center justify-between pb-0">
                <i class="design design-options opacity-50 text-3xl"></i>
                <h1 class="leading-none tracking-tighter text-black dark:text-gray-200 text-4xl gant-modern-bold">
                Animate.svg
            </h1>
                    <div class="flex items-center gap-2">
                        <button type="button" id="resetPosition" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" title="Reset Position">
                            <i class="design design-change text-lg"></i>
                        </button>
                        <button type="button" id="collapsePanel" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700" title="Collapse Panel">
                            <i class="design design-arrow-up text-lg"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Tab Navigation -->
                <div class="mt-8 mb-3  rounded-xl  border-1 border-gray-400 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 p-1">
                    <div class="flex w-full rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700 bg-gray-100 dark:bg-gray-900">
                        <label class="flex-1 text-center">
                            <input type="radio" name="tab" value="paths" checked class="sr-only peer">
                            <div class="py-2.5 px-3 cursor-pointer text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 peer-checked:bg-white peer-checked:text-gray-900 peer-checked:dark:text-white dark:peer-checked:bg-gray-950/80 transition-colors duration-200 text-sm  gant-modern-bold">
                            <i class="design design-pen-tool opacity-30"></i> Paths
                            </div>
                        </label>
                        <label class="flex-1 text-center border-l border-gray-300 dark:border-gray-700">
                            <input type="radio" name="tab" value="fill" class="sr-only peer">
                            <div class="py-2.5 px-3 cursor-pointer text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 peer-checked:bg-white peer-checked:text-gray-900 peer-checked:dark:text-white dark:peer-checked:bg-gray-950/80 transition-colors duration-200 text-sm  gant-modern-bold">
                              <i class="design design-paint-bucket opacity-30"></i>  Fills
                            </div>
                        </label>
                        <label class="flex-1 text-center border-l border-gray-300 dark:border-gray-700">
                            <input type="radio" name="tab" value="morph" class="sr-only peer">
                            <div class="py-2.5 px-3 cursor-pointer text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 peer-checked:bg-white peer-checked:text-gray-900 peer-checked:dark:text-white dark:peer-checked:bg-gray-950/80 transition-colors duration-200 text-sm gant-modern-bold">
                            <i class="design design-morph-tool opacity-30"></i>  Morph
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Tab Content Containers -->
                <div id="tab-paths" class="block">
                    <!-- SVG Size Control -->
                    <div class="py-3 mb-3 border-b dark:border-gray-700">
                        <label class="block">
                            <div class="flex items-center gap-3">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 gant-modern-medium whitespace-nowrap">Preview size</span>
                                <input type="range" min="20" max="90" name="svgSize" value="50" step="5" class="range-slider flex-1" oninput="viewerCtrl.resizeSvg(this.value)">
                                <span id="svgSizeValue" class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap w-10 text-right">50%</span>
                            </div>
                        </label>
                    </div>
                    
                    <!-- Options Form -->
                    <form class="space-y-3 panel-content">
                        <!-- Animation Start Options -->
                        <div class="space-y-3 border-1 dark:border-gray-700 rounded-xl ">
                            <h3 class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Animation Start</h3>
                            <div class="flex w-full rounded-lg overflow-hidden border-1 border-gray-400 dark:border-gray-700 bg-gray-100 dark:bg-gray-900 p-1">
                                <label class="flex-1 text-center">
                                    <input id="form_start_auto" type="radio" name="start" value="autostart" checked class="sr-only peer" onchange="optionCtrl.updateForm()">
                                    <div class="py-2.5 px-3 cursor-pointer text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-gray-900 peer-checked:bg-white peer-checked:text-gray-900 peer-checked:dark:text-white dark:peer-checked:bg-gray-950/80 transition-colors duration-200 text-sm gant-modern-medium flex items-center justify-center gap-1.5 rounded-md">
                                        <i class="design design-play-outline text-base"></i>
                                        <span>Autostart</span>
                                    </div>
                                </label>
                                <label class="flex-1 text-center border-l border-gray-200 dark:border-gray-700">
                                    <input id="form_start_manuel" type="radio" name="start" value="manual" class="sr-only peer" onchange="optionCtrl.updateForm()">
                                    <div class="py-2.5 px-3 cursor-pointer text-gray-400 dark:text-gray-600 bg-gray-100 dark:bg-gray-900 peer-checked:bg-white peer-checked:text-gray-900 peer-checked:dark:text-white dark:peer-checked:bg-gray-950/80 transition-colors duration-200 text-sm gant-modern-medium flex items-center justify-center gap-1.5 rounded-md">
                                        <i class="design design-hand-pointer text-base"></i>
                                        <span>Manual</span>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- Trigger Class Panel -->
                            <div class="manual-trigger-class-panel hidden">
                                <label class="block">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Trigger class name</span>
                                    <input type="text" name="triggerClass" value="start" class="mt-1.5 block w-full rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular"/>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Click the SVG to toggle animation</p>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Animation Type & Timing -->
                        <div class="space-y-3 border dark:border-gray-700 rounded-xl">
                            <div>
                                <label class="block">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Animation type</span>
                                    <select name="type" onchange="optionCtrl.updateForm()" class="mt-1.5 block w-full rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 dark:text-gray-100 shadow-sm text-sm gant-modern-regular">
                                        <option value="delayed" selected="selected">Delayed start</option>
                                        <option value="async">Synchronous</option>
                                        <option value="oneByOne">One By One</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="delayed-description">Each path starts with a delay after the previous</span>
                                        <span class="async-description hidden">All paths animate at the same time</span>
                                        <span class="oneByOne-description hidden">Paths animate one after another</span>
                                    </p>
                                </label>
                            </div>
                            
                            <div class="delay-panel">
                                <label class="block">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Delay <span class="text-xs text-gray-500">(ms)</span></span>
                                    <input type="number" min="0" name="delay" placeholder="auto" step="20" class="mt-1.5 block w-full rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular"/>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Leave empty for automatic delay</p>
                                </label>
                            </div>
                            
                            <div>
                                <label class="block">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Duration <span class="text-xs text-gray-500">(ms)</span></span>
                                    <div class="flex items-center gap-2">
                                        <input type="range" min="500" max="10000" name="duration_range" value="3000" step="100" class="range-slider flex-1" oninput="document.querySelector('input[name=duration]').value=this.value">
                                        <input type="number" min="0" name="duration" value="3000" step="100" class="w-20 rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular" oninput="document.querySelector('input[name=duration_range]').value=this.value"/>
                                    </div>
                                </label>
                            </div>
                            
                            <div>
                                <label class="block">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Path timing function</span>
                                    <select name="pathTimingFunction" class="mt-1.5 block w-full rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular">
                                        <option value="linear" selected="selected">Linear</option>
                                        <option value="ease">Ease</option>
                                        <option value="ease-in">Ease in</option>
                                        <option value="ease-out">Ease out</option>
                                        <option value="ease-in-out">Ease in out</option>
                                    </select>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Loop Options -->
                        <div class="space-y-3 border dark:border-gray-700 rounded-xl p-3">
                            <div>
                                <label class="flex items-center">
                                    <span class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1 gant-modern-medium me-2">Loop animation</span>
                                    <div class="relative inline-block w-11 mr-2 align-middle select-none">
                                        <input type="checkbox" name="loop" id="loopToggle" class="sr-only peer" onchange="optionCtrl.updateForm()">
                                        <div class="w-11 h-6 bg-gray-200 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-gray-900 dark:peer-checked:bg-gray-200 rounded-full peer dark:bg-gray-700"></div>
                                    </div>
                                    <span id="loopToggleStatus" class="text-sm text-gray-500 dark:text-gray-400">Off</span>
                                </label>
                            </div>
                            
                            <div class="control-loop-panel hidden space-y-3">
                                <div>
                                    <label class="block">
                                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Start delay <span class="text-xs text-gray-500">(ms)</span></span>
                                        <div class="flex items-center gap-2">
                                            <input type="range" min="0" max="5000" name="loopStart_range" value="800" step="100" class="range-slider flex-1" oninput="document.querySelector('input[name=loopStart]').value=this.value">
                                            <input type="number" name="loopStart" value="800" step="100" class="w-20 rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular" oninput="document.querySelector('input[name=loopStart_range]').value=this.value"/>
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Time to wait before fading out</p>
                                    </label>
                                </div>
                                
                                <div>
                                    <label class="block">
                                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">End pause <span class="text-xs text-gray-500">(ms)</span></span>
                                        <div class="flex items-center gap-2">
                                            <input type="range" min="0" max="10000" name="loopEnd_range" value="3000" step="100" class="range-slider flex-1" oninput="document.querySelector('input[name=loopEnd]').value=this.value">
                                            <input type="number" name="loopEnd" value="3000" step="100" class="w-20 rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular" oninput="document.querySelector('input[name=loopEnd_range]').value=this.value"/>
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Time to wait while invisible</p>
                                    </label>
                                </div>
                                
                                <div>
                                    <label class="block">
                                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Fade duration <span class="text-xs text-gray-500">(ms)</span></span>
                                        <div class="flex items-center gap-2">
                                            <input type="range" min="0" max="2000" name="loopTransition_range" value="400" step="50" class="range-slider flex-1" oninput="document.querySelector('input[name=loopTransition]').value=this.value">
                                            <input type="number" name="loopTransition" value="400" step="50" class="w-20 rounded-xl border-gray-300 dark:border-gray-700 dark:bg-slate-900 shadow-sm text-sm gant-modern-regular" oninput="document.querySelector('input[name=loopTransition_range]').value=this.value"/>
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Fade in/out transition time</p>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="space-y-3 pt-2 flex flex-col items-center justify-center">
                            <button type="button" id="drawButton" class="mb-6 rounded-full bg-gray-900 dark:bg-gray-100 hover:bg-gray-700 hover:dark:bg-gray-200 text-gray-900 dark:text-gray-200 hover:text-white hover:dark:text-gray-900 p-3 transition-colors duration-200">
                                <i class="wms wms-magic_wand text-6xl p-3 text-white dark:text-gray-900"></i>
                            </button>
                            <button type="button" id="downloadButton" class="w-full flex items-center justify-center gap-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-900 hover:dark:bg-gray-100 text-gray-900 dark:text-gray-200 hover:text-white hover:dark:text-gray-900 font-medium py-3 px-4 rounded-lg text-sm transition-colors duration-200 gant-modern-medium">
                                <i class="design design-download-svg text-2xl opacity-30 group-hover:opacity-100"></i>
                                <p>Download Animated SVG</p>
                            </button>
                        </div>
                    
                    </form>
                </div>
                
                <!-- Fill Tab (Coming Soon) -->
                <div id="tab-fill" class="hidden">
                    <div class="py-24 text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
                            <i class="design design-paint-bucket text-2xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">Fill Animation</h3>
                        <p class="text-gray-500 dark:text-gray-400">Coming soon! Animate fill colors and opacity.</p>
                    </div>
                </div>
                
                <!-- Morph Tab (Coming Soon) -->
                <div id="tab-morph" class="hidden">
                    <div class="py-24 text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
                            <i class="design design-morph-tool text-2xl text-gray-400 dark:text-gray-500"></i>
                        </div>
                        <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">Morph Animation</h3>
                        <p class="text-gray-500 dark:text-gray-400">Coming soon! Transform your shapes with morphing animations.</p>
                    </div>
                </div>
            </div>
            
            <!-- Toggle Button for Mobile -->
            <button id="toggleOptions" class="md:hidden fixed bottom-4 right-4 bg-gray-900 dark:bg-gray-800 text-white p-3 rounded-full shadow-lg z-20">
                <i class="design design-settings text-xl"></i>
            </button>
        </div>
    </div>
    
    @push('head')
    <style>
        /* Range slider styles */
        .range-slider {
            -webkit-appearance: none;
            appearance: none;
            background: transparent;
            cursor: pointer;
            width: 100%;
        }

        /* Track */
        .range-slider::-webkit-slider-runnable-track {
            height: 2px;
            background: rgb(209 213 219); /* gray-300 */
            border: none;
        }

        .range-slider::-moz-range-track {
            height: 2px;
            background: rgb(209 213 219); /* gray-300 */
            border: none;
        }

        /* Thumb */
        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #000000;
            border: 2px solid #ffffff;
            border-radius: 50%;
            margin-top: -7px;
            transition: all 0.15s ease;
        }

        .range-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #000000;
            border: 2px solid #ffffff;
            border-radius: 50%;
            transition: all 0.15s ease;
        }

        /* Dark mode */
        .dark .range-slider::-webkit-slider-runnable-track {
            background: rgb(55 65 81); /* gray-700 */
        }

        .dark .range-slider::-moz-range-track {
            background: rgb(55 65 81); /* gray-700 */
        }

        .dark .range-slider::-webkit-slider-thumb {
            background: #ffffff;
            border-color: #000000;
        }

        .dark .range-slider::-moz-range-thumb {
            background: #ffffff;
            border-color: #000000;
        }
        
        /* Pattern background for dropzone */
        .pattern-bg {
            position: relative;
        }
        
        /* All pattern-related CSS removed */

        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .svg-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            inset: 0;
            padding: 2rem;
            background: transparent !important;
            z-index: 10;
        }
        
        /* Add styles for the SVG element itself */
        .svg-wrapper svg {
            /* Width and height will be set dynamically via JavaScript */
            object-fit: contain;
            position: absolute !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            fill: none !important;
            background: transparent !important;
        }
        
        /* Ensure proper stacking context */
        .viewer {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            position: relative;
            overflow: hidden;
        }
        
        /* Update introbox z-index */
        .introbox {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10; /* Add z-index to layer above pattern */
        }
        
        /* Skeleton loading animation */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            50%, 100% {
                transform: translateX(100%);
            }
        }
        
        .skeleton-wave {
            position: relative;
            overflow: hidden;
        }

        .skeleton-wave::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.05) 15%,
                rgba(255, 255, 255, 0.1) 30%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.1) 70%,
                rgba(255, 255, 255, 0.05) 85%,
                transparent 100%
            );
            transform: translateX(-100%);
            animation: shimmer 2.5s ease-in-out infinite;
            pointer-events: none;
        }

        /* Dark mode adjustments */
        .dark .skeleton-wave::after {
            background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.02) 15%,
                rgba(255, 255, 255, 0.05) 30%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 70%,
                rgba(255, 255, 255, 0.02) 85%,
                transparent 100%
            );
        }
        
        /* Draggable panel styles */
        .draggable-panel {
            user-select: none;
            transition: box-shadow 0.3s ease, height 0.3s ease;
        }
        
        .draggable-panel.dragging {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            opacity: 0.95;
            z-index: 50 !important;
        }
        
        .draggable-panel.collapsed {
            height: auto;
            width: auto;
        }
        
        .drag-handle {
            cursor: grab;
            position: relative;
        }
        
        .drag-handle::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background-image: radial-gradient(circle, currentColor 1px, transparent 1.5px);
            background-size: 4px 4px;
            background-position: 0 0;
        }

        /* Wiggle animation for the upload icon */
        @keyframes wiggle {
            0%, 100% { 
                transform: rotate(0deg) translate(0, 0); 
            }
            20% { 
                transform: rotate(-3deg) translate(-1px, 1px); 
            }
            40% { 
                transform: rotate(2deg) translate(1px, -1px); 
            }
            60% { 
                transform: rotate(-2deg) translate(-1px, -1px); 
            }
            80% { 
                transform: rotate(3deg) translate(1px, 1px); 
            }
        }

        .wiggle-animation {
            animation: wiggle 2.5s ease-in-out infinite;
            display: inline-block; /* Ensures the animation is contained */
            will-change: transform; /* Optimizes animation performance */
        }

        .dropzone {
            position: absolute;
            inset: 0;
            z-index: 5; /* Below the SVG wrapper but above the pattern */
            pointer-events: none; /* Allow clicking through to the SVG */
        }

        .dropzone-content {
            position: relative;
            z-index: 15; /* Above the SVG wrapper when visible */
            pointer-events: auto; /* Re-enable pointer events for the content */
        }

        body, html {
            overflow: hidden;
            height: 100%;
            margin: 0;
            padding: 0;
        }
        
        #app, .designdev_tm_all_wrap {
            overflow: hidden;
            height: 100vh;
        }
        
        #mainContent {
            overflow: hidden;
        }
    </style>
@push('scripts')
@vite(['resources/js/svg-animation.js'])
@endpush

</x-layouts.blank>