@extends('layouts.application')

@section('content')
    <div class="font-gant-modern dark:bg-gradient-to-b dark:from-slate-950 dark:via-slate-950 dark:to-slate-900">
        <div class="mx-auto max-w-7xl">
            <section class="px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
                <div class="w-full mx-auto">
                    <!-- Back Button -->
                    <div class="mb-6 sm:mb-8">
                        <a href="{{ route('tools') }}" class="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200">
                            <i class="wms wms-arrow-left me-2"></i>
                            <span class="gant-modern-bold text-sm">Back to Tools</span>
                        </a>
                    </div>
                    
                    <!-- Title and Description -->
                    <div class="text-center mb-8 sm:mb-10 md:mb-12">
                        <p class="text-base sm:text-lg md:text-xl gant-modern-regular text-gray-600 dark:text-gray-400 mb-2 sm:mb-3">
                            Documentation
                        </p>
                        <h1 class="text-4xl sm:text-5xl md:text-6xl xl:text-8xl gant-modern-bold leading-tight tracking-tighter dark:text-gray-200">
                            ordergroup Image
                        </h1>
                        <p class="mt-4 sm:mt-6 text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto gant-modern-regular">
                            A powerful tool for combining multiple SVG files into a single SVG output. Easily select, arrange, and export SVGs from your library.
                        </p>
                    </div>
                    
                    <!-- Hero Image -->
                    <div class="relative w-full mx-auto mb-4 sm:mb-6 md:mb-8 lg:mb-10 flex items-center justify-center">
                        <a href="{{ route('tools.ordergroup-image') }}" target="_blank" rel="noopener noreferrer" class="group relative">
                            <img 
                                class="w-full h-auto rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm transition-all duration-300 group-hover:shadow-lg"
                                src="{{ asset('img/tools/ordergroup-image/heroLight.svg') }}"
                                alt="ordergrouproup Image Interface Light"
                            >
                        </a>
                        <a href="{{ route('tools.ordergroup-image') }}" target="_blank" rel="noopener noreferrer" class="group relative">
                            <img 
                                class="w-full h-auto rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm transition-all duration-300 group-hover:shadow-lg"
                                src="{{ asset('img/tools/ordergroup-image/heroDark.svg') }}"
                                alt="ordergrouproup Image Interface Dark"
                            >
                        </a>
                    </div>
                    
                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 mb-12 sm:mb-16">
                        <a href="{{ route('tools.ordergroup-image') }}" 
                           class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black border-2 border-transparent font-medium py-4 sm:py-5 px-8 sm:px-10 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-base sm:text-lg">
                            <span class="gant-modern-bold">Launch ordergroup Image</span>
                            <i class="design design-acceleration-outline text-lg sm:text-xl"></i>
                        </a>
                    </div>
                    
                    <!-- Documentation Sections -->
                    <div class="max-w-5xl mx-auto">
                        <!-- Features Section -->
                        <section class="mb-12 sm:mb-16 md:mb-20">
                            <h2 class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold mb-6 sm:mb-8 dark:text-gray-200">
                                Features
                            </h2>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                <!-- Feature 1 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-image-multiple-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Multiple Folders</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        Browse and select SVGs from different organized folders in your collection.
                                    </p>
                                </div>
                                
                                <!-- Feature 2 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-preview-eye-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Live Preview</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        See how your combined SVG looks before exporting with the real-time preview pane.
                                    </p>
                                </div>
                                
                                <!-- Feature 3 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-download-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Easy Export</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        Export your combined SVG with a single click, ready to use in your projects.
                                    </p>
                                </div>
                                
                                <!-- Feature 4 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-customize-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Customizable Output</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        Set custom dimensions and filename for your combined SVG output.
                                    </p>
                                </div>
                                
                                <!-- Feature 5 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-grid-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Smart Layout</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        The tool automatically arranges your SVGs in an optimized grid layout.
                                    </p>
                                </div>
                                
                                <!-- Feature 6 -->
                                <div class="p-6 border border-gray-100 dark:border-gray-800 rounded-xl bg-white dark:bg-transparent">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                        <i class="design design-bulb-on-outline text-xl text-gray-800 dark:text-gray-200"></i>
                                    </div>
                                    <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Dark Mode Support</h3>
                                    <p class="text-gray-600 dark:text-gray-400 gant-modern-regular">
                                        Work comfortably with full dark mode support for the entire interface.
                                    </p>
                                </div>
                            </div>
                        </section>
                        
                        <!-- How to Use Section -->
                        <section class="mb-12 sm:mb-16 md:mb-20">
                            <h2 class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold mb-6 sm:mb-8 dark:text-gray-200">
                                How to Use
                            </h2>
                            <div class="space-y-8">
                                <!-- Step 1 -->
                                <div class="flex flex-col sm:flex-row gap-6 items-start">
                                    <div class="w-12 h-12 rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0 flex items-center justify-center">
                                        <span class="text-xl gant-modern-bold text-slate-800 dark:text-slate-200">1</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Browse SVG Folders</h3>
                                        <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                            Click on a folder in the accordion menu to expand it and view the SVGs contained within that folder.
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Step 2 -->
                                <div class="flex flex-col sm:flex-row gap-6 items-start">
                                    <div class="w-12 h-12 rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0 flex items-center justify-center">
                                        <span class="text-xl gant-modern-bold text-slate-800 dark:text-slate-200">2</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Select SVGs</h3>
                                        <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                            Click on the SVGs you want to combine. Selected SVGs will be highlighted with a blue border and checkmark.
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Step 3 -->
                                <div class="flex flex-col sm:flex-row gap-6 items-start">
                                    <div class="w-12 h-12 rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0 flex items-center justify-center">
                                        <span class="text-xl gant-modern-bold text-slate-800 dark:text-slate-200">3</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Configure Output Settings</h3>
                                        <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                            Set your desired output dimensions and filename in the settings panel.
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Step 4 -->
                                <div class="flex flex-col sm:flex-row gap-6 items-start">
                                    <div class="w-12 h-12 rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0 flex items-center justify-center">
                                        <span class="text-xl gant-modern-bold text-slate-800 dark:text-slate-200">4</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Combine SVGs</h3>
                                        <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                            Click the "Combine SVGs" button to generate a preview of your combined SVG in the left panel.
                                        </p>
                                    </div>
                                </div>
                                
                                <!-- Step 5 -->
                                <div class="flex flex-col sm:flex-row gap-6 items-start">
                                    <div class="w-12 h-12 rounded-full bg-slate-100 dark:bg-slate-800 flex-shrink-0 flex items-center justify-center">
                                        <span class="text-xl gant-modern-bold text-slate-800 dark:text-slate-200">5</span>
                                    </div>
                                    <div>
                                        <h3 class="text-xl gant-modern-bold mb-2 dark:text-gray-200">Download Combined SVG</h3>
                                        <p class="text-gray-600 dark:text-gray-400 gant-modern-regular mb-4">
                                            Once satisfied with the preview, click the "Download" button to save your combined SVG file.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </section>
                        
                        <!-- Tips Section -->
                        <section class="mb-12 sm:mb-16 md:mb-20">
                            <h2 class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold mb-6 sm:mb-8 dark:text-gray-200">
                                Tips & Best Practices
                            </h2>
                            <div class="bg-gray-50 dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-xl p-6">
                                <ul class="space-y-4 list-disc list-inside text-gray-600 dark:text-gray-400 gant-modern-regular">
                                    <li>For best results, use SVGs with similar aspect ratios when combining</li>
                                    <li>You can select SVGs from multiple different folders at once</li>
                                    <li>The tool automatically arranges SVGs in a grid layout for optimal spacing</li>
                                    <li>Consider the final dimensions carefully to ensure all SVGs are visible</li>
                                    <li>For more control, you can manually edit the SVG after downloading</li>
                                    <li>Use descriptive filenames for your output to easily identify combined SVGs</li>
                                </ul>
                            </div>
                        </section>
                        
                        <!-- Launch Button -->
                        <div class="text-center mb-12 sm:mb-16 md:mb-20">
                            <a href="{{ route('tools.ordergroup-image') }}" 
                               class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black border-2 border-transparent font-medium py-4 sm:py-5 px-8 sm:px-10 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-base sm:text-lg">
                                <span class="gant-modern-bold">Launch ordergroup Image</span>
                                <i class="design design-acceleration-outline text-lg sm:text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
@endsection 