@extends('layouts.application')

@section('content')
    <div class="font-gant-modern relative">
        <div class="relative">
            <div class="w-full px-4 py-8 md:py-16">
                <div class="container mx-auto">
                    <!-- Tool Header -->
                    <div class="mb-8">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4">
                            <h1 class="text-3xl sm:text-4xl gant-modern-bold tracking-tighter dark:text-gray-200 mb-2 sm:mb-0">
                                ordergroup Image <span class="text-sm sm:text-base gant-modern-regular opacity-60">v0.1</span>
                            </h1>
                            <a href="{{ route('tools.ordergroup-image.documentation') }}" class="px-3 py-1.5 rounded-lg border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors text-sm">
                                <i class="design design-information-outline mr-1"></i>
                                <span>Documentation</span>
                            </a>
                        </div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 max-w-3xl">
                            Organize and combine images into ordered groups. Select images from different folders, arrange them in your preferred order, and export them as a combined set.
                        </p>
                    </div>
                    
                    <!-- Pattern Background -->
                    <div class="relative bg-white dark:bg-slate-950 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 pointer-events-none opacity-50">
                            <svg class="absolute inset-0 w-full h-full text-gray-900/[0.02] dark:text-gray-100/[0.05]" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
                                        <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#pattern-3)" />
                            </svg>
                        </div>
                        
                        <!-- ordergroup Image Component Container -->
                        <div class="relative z-10">
                            <ordegroup-image></ordegroup-image>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection 