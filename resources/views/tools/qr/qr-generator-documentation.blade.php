@extends('layouts.application')

@section('content')
<div data-page="qr-generator-product" class="{{ auth()->user()->prefers_dark_mode ? 'dark' : '' }} min-h-screen bg-white dark:bg-slate-950">
        <!-- Main snap scrolling container with Tailwind classes -->
        <div class="snap-container h-screen overflow-y-scroll snap-y snap-mandatory">
            <!-- Fixed Navigation Dots - Modernized UI -->
            <div class="fixed right-8 top-1/2 transform -translate-y-1/2 z-50 hidden sm:block">
                <div class="flex flex-col space-y-4">
                    <a href="#hero" data-section="hero" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Home</span>
                    </a>
                    <a href="#demo" data-section="demo" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Demo</span>
                    </a>
                    <a href="#features" data-section="features" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Features</span>
                    </a>
                    <a href="#how-it-works" data-section="how-it-works" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">How It Works</span>
                    </a>
                    <a href="#get-started" data-section="get-started" class="nav-dot group relative flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white group-hover:scale-125"></span>
                        <span class="absolute right-5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-black dark:bg-white text-white dark:text-black text-xs py-1 px-2 rounded-md whitespace-nowrap">Get Started</span>
                    </a>
                </div>
            </div>
            
            <!-- Mobile Navigation Dots at the bottom - Only visible on small screens -->
            <div class="fixed bottom-6 left-0 right-0 z-50 sm:hidden">
                <div class="flex justify-center space-x-3">
                    <a href="#hero" data-section="hero" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#demo" data-section="demo" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#features" data-section="features" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#how-it-works" data-section="how-it-works" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                    <a href="#get-started" data-section="get-started" class="nav-dot w-2.5 h-2.5 rounded-full bg-gray-300 dark:bg-gray-700 transition-all duration-300 hover:bg-black dark:hover:bg-white"></a>
                </div>
            </div>

            <!-- Section 1: Hero Section -->
            <section id="hero" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 relative">
                <div class="w-full max-w-[95vw] md:max-w-[90vw] lg:max-w-[80vw] mx-auto flex flex-col items-center justify-center py-6 md:py-8 lg:py-12">
                    <!-- Header Text - Enhanced Responsiveness -->
                    <header class="w-full text-center mb-3 sm:mb-4 md:mb-6 lg:mb-8">
                        <p class="text-sm sm:text-base md:text-xl lg:text-2xl xl:text-3xl text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 md:mb-3 gant-modern-light">
                             Generate and Customize 
                        </p>
                        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl gant-modern-bold text-black dark:text-white tracking-tighter leading-tight">
                             QR Code Generator <span class="gant-modern-light text-gray-500 dark:text-gray-400">v1.0</span>
                        </h1>
                    </header>
                    
                    <!-- Hero Image with Hover CTA -->
                    <div class="relative w-full mx-auto mb-4 sm:mb-6 md:mb-8 lg:mb-10 group">
                        <div class="flex items-center justify-center">
                            <div class="relative">
                                <!-- Background highlight effect -->
                                <div class="absolute inset-0 bg-gray-500/0 rounded-2xl filter blur-xl opacity-0 group-hover:opacity-30 transition-all duration-700 -z-10"></div>
                                
                                <!-- Images with hover effect -->
                                <img 
                                    src="{{ asset('img/tools/qr/hero-light.svg') }}" 
                                    alt="QR Code Generator Interface Light"
                                    class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto block dark:hidden transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(0,0,0,0.1)]"
                                >
                                <img 
                                    src="{{ asset('img/tools/qr/hero-dark.svg') }}" 
                                    alt="QR Code Generator Interface Dark"
                                    class="w-auto h-[30vh] sm:h-[35vh] md:h-[40vh] lg:h-[45vh] xl:h-[50vh] max-w-full object-contain mx-auto hidden dark:block transform transition-all duration-700 group-hover:scale-110 group-hover:drop-shadow-[0_0_15px_rgba(255,255,255,0.1)]"
                                >
                            </div>
                        </div>
                        
                        <!-- CTA Buttons overlay - Positioned absolutely within the group container -->
                        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                            <div class="flex justify-center space-x-4 backdrop-blur-sm bg-white/30 dark:bg-slate-900/30 py-2 px-2 rounded-xl shadow-xl">
                                <a href="{{ route('tools.qr-generator') }}" 
                                   class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black font-medium py-4 px-5 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-xl z-20">
                                    <i class="design design-race text-xl sm:text-2xl"></i>
                                    <span class="text-base sm:text-lg gant-modern-bold">Open QR Generator</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scroll Down Indicator - Improved Centering -->
                    <div class="absolute bottom-8 sm:bottom-10 md:bottom-12 lg:bottom-16 left-0 right-0 mx-auto w-max flex flex-col items-center animate-bounce">
                        <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1 sm:mb-2 gant-modern-regular">Scroll Down</span>
                        <i class="design design-pizza text-lg sm:text-xl md:text-2xl text-gray-800 dark:text-gray-200"></i>
                    </div>
                </div>
            </section>

            <!-- Section 2: Demo Section -->
            <section id="demo" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <div class="relative">
                        <div class="flex flex-col lg:flex-row items-center gap-6 sm:gap-8 md:gap-10 lg:gap-12 py-8 sm:py-10 md:py-12">
                            <div class="flex-1">
                                <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl gant-modern-bold text-black dark:text-white mb-3 sm:mb-4 md:mb-6">
                                    Simple Yet Powerful
                                </h2>
                                <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8">
                                    Our QR Code Generator tool allows you to create customized QR codes in seconds. Perfect for business cards, marketing materials, event tickets, and more.
                                </p>
                            </div>
                            <div class="flex-1 w-full bg-gray-100 dark:bg-gray-900 p-4 sm:p-6 md:p-8 rounded-2xl shadow-lg flex items-center justify-center border border-gray-200 dark:border-gray-800">
                                <!-- QR Code Demo - Responsive Container -->
                                <div class="w-full h-48 sm:h-56 md:h-64 flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12">
                                    <img 
                                        src="{{ asset('img/tools/qr/demo-qr.svg') }}" 
                                        alt="QR Code Demo"
                                        class="w-full h-full object-contain mb-4 sm:mb-6"
                                    >
                                    <a href="{{ route('tools.qr-generator') }}" 
                                    class="inline-flex items-center justify-center gap-1 sm:gap-2 bg-slate-950 dark:bg-white text-white dark:text-black font-medium py-1.5 sm:py-2 px-4 sm:px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-sm sm:text-base">
                                        <i class="design design-qr-code-outline text-base sm:text-lg"></i>
                                        <span>Generate Your QR Code</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 3: Key Features Section -->
            <section id="features" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-sparkles text-2xl sm:text-3xl text-black dark:text-white"></i>
                        Key Features
                    </h2>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                        <!-- Feature 1 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-font-design4 text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Custom Content</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Generate QR codes from any URL or text content with perfect encoding
                            </p>
                        </div>

                        <!-- Feature 2 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-zoom-out-1-outline text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Size Customization</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Adjust the size of your QR code from 100px to 1000px for any application
                            </p>
                        </div>

                        <!-- Feature 3 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-paint-bucket text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Color Options</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Customize both foreground and background colors to match your brand
                            </p>
                        </div>

                        <!-- Feature 4 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-download-svg text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Multiple Export Formats</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Download your QR code as SVG or PNG for both digital and print use
                            </p>
                        </div>

                        <!-- Feature 5 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-time text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">Instant Generation</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                See your QR code update in real-time as you make changes to settings
                            </p>
                        </div>

                        <!-- Feature 6 - Responsive Card -->
                        <div class="p-4 sm:p-6 md:p-8 rounded-xl bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 group hover:border-black dark:hover:border-white transition-all duration-300">
                            <div class="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3 md:mb-4">
                                <i class="design design-wrench text-xl sm:text-2xl text-black dark:text-white group-hover:scale-110 transition-transform duration-300"></i>
                                <h3 class="text-base sm:text-lg gant-modern-bold text-black dark:text-white">No Sign-up Required</h3>
                            </div>
                            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-300 gant-modern-regular">
                                Use the tool instantly without creating an account or sharing data
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 4: How It Works -->
            <section id="how-it-works" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-cog text-2xl sm:text-3xl text-black dark:text-white"></i>
                        How It Works
                    </h2>
                    
                    <!-- How It Works Content -->
                    <div class="bg-white dark:bg-slate-950 rounded-2xl p-6 sm:p-8 md:p-10 border-2 border-gray-200 dark:border-gray-800 shadow-sm">
                        <div class="prose dark:prose-invert max-w-none">
                            <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6">
                                The QR Code Generator works by creating a QR code matrix from your input text using a custom algorithm.
                                The matrix is then rendered as either an SVG or PNG image based on your selection.
                            </p>
                            
                            <h3 class="text-lg sm:text-xl md:text-2xl gant-modern-bold text-black dark:text-white mb-4">Technical Details</h3>
                            <p class="text-base text-gray-600 dark:text-gray-400 mb-4">
                                QR codes consist of several components:
                            </p>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6">
                                <div class="flex items-start gap-3">
                                    <i class="design design-images text-xl text-slate-950 dark:text-white mt-1"></i>
                                    <div>
                                        <h4 class="text-base sm:text-lg gant-modern-bold mb-1">Finder Patterns</h4>
                                        <p class="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                            The three large squares in the corners help scanners locate the QR code
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <i class="design design-roadmapview text-xl text-slate-950 dark:text-white mt-1"></i>
                                    <div>
                                        <h4 class="text-base sm:text-lg gant-modern-bold mb-1">Alignment Patterns</h4>
                                        <p class="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                            Additional reference points for the scanner
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <i class="design design-idea text-xl text-slate-950 dark:text-white mt-1"></i>
                                    <div>
                                        <h4 class="text-base sm:text-lg gant-modern-bold mb-1">Timing Patterns</h4>
                                        <p class="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                            Alternating light and dark modules that help determine module coordinates
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-3">
                                    <i class="design design-database text-xl text-slate-950 dark:text-white mt-1"></i>
                                    <div>
                                        <h4 class="text-base sm:text-lg gant-modern-bold mb-1">Data & Error Correction</h4>
                                        <p class="text-sm md:text-base text-gray-600 dark:text-gray-400">
                                            The actual encoded content with error correction capabilities
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <h3 class="text-lg sm:text-xl md:text-2xl gant-modern-bold text-black dark:text-white mb-4">How to Use</h3>
                            <ol class="list-decimal pl-5 space-y-2 mb-6 text-gray-600 dark:text-gray-400">
                                <li>Enter the URL or text content you want to encode</li>
                                <li>Adjust the size using the slider or input field</li>
                                <li>Select foreground and background colors</li>
                                <li>Click "Generate" to create your QR code</li>
                                <li>Use the download buttons to save your QR code in your preferred format</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 5: Get Started -->
            <section id="get-started" class="scroll-section h-screen w-full snap-start flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-950">
                <div class="w-full max-w-7xl mx-auto">
                    <h2 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-8 sm:mb-10 md:mb-12 text-black dark:text-white gant-modern-bold flex items-center gap-2 sm:gap-3">
                        <i class="design design-get-started text-2xl sm:text-3xl text-black dark:text-white"></i>
                        Get Started
                    </h2>
                    
                    <!-- CTA Section - Refined Minimalist Design -->
                    <section class="mb-16 sm:mb-20 md:mb-24 lg:mb-32">
                        <div class="bg-white dark:bg-slate-950 border-2 border-gray-200 dark:border-gray-800 hover:border-black dark:hover:border-white transition-all duration-300 rounded-xl sm:rounded-2xl p-6 sm:p-8 md:p-10 lg:p-12 text-center shadow-sm">
                            <i class="design design-qr-code-outline text-3xl sm:text-4xl md:text-5xl text-black dark:text-white mb-4 sm:mb-6"></i>
                            <h2 class="text-2xl sm:text-3xl md:text-4xl gant-modern-bold text-black dark:text-white mb-3 sm:mb-4 md:mb-6">Ready to Create?</h2>
                            <p class="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-2xl mx-auto gant-modern-regular">
                               Generate your own customized QR codes in seconds with our easy-to-use tool.
                            </p>
                            <a href="{{ route('tools.qr-generator') }}" 
                               class="inline-flex items-center justify-center gap-2 sm:gap-3 bg-slate-950 dark:bg-white text-white dark:text-black border-2 border-transparent font-medium py-4 sm:py-5 px-8 sm:px-10 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md text-base sm:text-lg">
                                <span class="gant-modern-bold">Open QR Generator</span>
                                <i class="design design-arrow-right text-lg sm:text-xl"></i>
                            </a>
                        </div>
                    </section>
                </div>
            </section>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle navigation dots
            const navDots = document.querySelectorAll('.nav-dot');
            const sections = document.querySelectorAll('.scroll-section');
            
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.6
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const id = entry.target.getAttribute('id');
                        navDots.forEach(dot => {
                            if (dot.getAttribute('href') === `#${id}`) {
                                // Check if dot has a span child (desktop) or is the element itself (mobile)
                                const spanElement = dot.querySelector('span:first-child');
                                if (spanElement) {
                                    spanElement.classList.add('bg-black', 'dark:bg-white', 'scale-125');
                                } else {
                                    // This is a mobile dot without child spans
                                    dot.classList.add('bg-black', 'dark:bg-white', 'scale-110');
                                }
                            } else {
                                // Check if dot has a span child (desktop) or is the element itself (mobile)
                                const spanElement = dot.querySelector('span:first-child');
                                if (spanElement) {
                                    spanElement.classList.remove('bg-black', 'dark:bg-white', 'scale-125');
                                } else {
                                    // This is a mobile dot without child spans
                                    dot.classList.remove('bg-black', 'dark:bg-white', 'scale-110');
                                }
                            }
                        });
                    }
                });
            }, observerOptions);
            
            sections.forEach(section => {
                observer.observe(section);
            });
            
            // Smooth scrolling for navigation dots
            navDots.forEach(dot => {
                dot.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
    @endpush
@endsection 