#bg-wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
    overflow: hidden;
    pointer-events: none;
}

#bg-wrap svg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

/* Make content container relative to stack above background */
#app {
    position: relative;
    z-index: 1;
}

/* Adjust content background transparency */
.bg-white {
    background-color: rgba(255, 255, 255, 0.95) !important;
}

.dark .dark\:bg-slate-900 {
    background-color: rgba(15, 23, 42, 0.95) !important;
}

.dark .dark\:bg-gray-800 {
    background-color: rgba(31, 41, 55, 0.95) !important;
}

/* Ensure transparent backgrounds work properly in dark mode */
.dark .dark\:bg-transparent {
    background-color: transparent !important;
}

/* Fix for header area background in dark mode */
.dark .bg-white {
    background-color: transparent !important;
}
