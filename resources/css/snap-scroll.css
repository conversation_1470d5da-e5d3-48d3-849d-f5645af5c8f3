/* For browsers that support CSS scroll behavior */
html {
    scroll-behavior: smooth;
}

/* For touch devices, use proximity-based snapping for better UX */
@media (hover: none) and (pointer: coarse) {
    .snap-container {
        scroll-snap-type: y proximity;
    }
}

/* Navigation dots styling and animations */
.nav-dot {
    position: relative;
    transition: all 0.3s ease;
}

/* Desktop navigation styling */
.nav-dot span:first-child {
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    position: relative;
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
}

/* Hover effect for desktop navigation */
.nav-dot:hover span:first-child {
    transform: scale(1.3);
}

/* Active dot indicator with pulse animation */
.nav-dot span:first-child.scale-125 {
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
}

/* Dark mode active dot indicator */
.dark .nav-dot span:first-child.scale-125 {
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* Label animation for desktop navigation */
.nav-dot span:last-child {
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.nav-dot:hover span:last-child {
    transform: translateX(0);
}

/* Pulse animation for the active dot */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(0, 0, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

/* Dark mode pulse animation */
.dark .nav-dot span:first-child {
    animation: pulse-dark 2s infinite;
}

@keyframes pulse-dark {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
} 