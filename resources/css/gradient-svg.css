:root {
  --logo-text-color: #000;
}

.dark {
  --logo-text-color: #fff;
}

.gradient-svg {
  grid-area: main;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.gradient-overlay-text {
  grid-area: main;
  font: 10vmin/0.8;
  font-weight: 700;
  letter-spacing: -0.05em;
  color: rgba(255, 255, 255, 0.2);
  text-shadow: 
    0 0 20px rgba(255, 255, 255, 0.3),
    0 0 40px rgba(255, 255, 255, 0.2),
    0 0 80px rgba(255, 255, 255, 0.1);
  mix-blend-mode: overlay;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  filter: blur(0.5px);
  transform: translateZ(-10px);
  perspective: 1000px;
}

.dark .gradient-overlay-text {
  color: rgba(0, 0, 0, 0.1);
  text-shadow: 
    0 0 20px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(0, 0, 0, 0.3),
    0 0 80px rgba(0, 0, 0, 0.2);
  mix-blend-mode: screen;
  filter: blur(0.7px);
}

@media (prefers-color-scheme: dark) {
  .gradient-overlay-text {
    color: rgba(255, 255, 255, 0.1);
    text-shadow: 
      0 0 20px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(255, 255, 255, 0.3),
      0 0 80px rgba(255, 255, 255, 0.2);
    mix-blend-mode: screen;
    filter: blur(0.7px);
  }
}

.logo-svg {
  height: 2rem;
  width: auto;
}

.logo-svg path[fill="black"] {
  fill: var(--logo-text-color);
  transition: fill 0.2s ease-in-out;
}
