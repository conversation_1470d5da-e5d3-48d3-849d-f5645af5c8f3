/* tailwindcss */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@layer base {
  :root {
    --background: #ffffff;
    --text: #1a202c;
    --primary: 15 23 42;
    --secondary: 255 255 255;
  }

  [data-theme="dark"] {
    --background: #1a202c;
    --text: #ffffff;
    --primary: 255 255 255;
    --secondary: 15 23 42;
  }

  /* Base transitions */
  * {
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Custom checkbox styling */
  [type='checkbox'] {
    @apply appearance-none w-4 h-4 border border-gray-300 rounded bg-white cursor-pointer relative align-middle;
  }

  .dark [type='checkbox'] {
    @apply border-gray-600 bg-transparent;
  }

  [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='black' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
    @apply bg-cover bg-center bg-no-repeat;
  }

  .dark [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
    @apply bg-cover bg-center bg-no-repeat;
  }

  [v-cloak] {
    display: none;
  }

  /* Dark mode transitions */
  .dark {
    color-scheme: dark;
  }

  /* Chrome autofill override */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus {
    transition-property: background-color, color;
    transition-duration: 5000s;
    transition-timing-function: ease-in-out;
    transition-delay: 0s;
    -webkit-text-fill-color: rgb(17 24 39) !important;
    -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
    background-color: transparent !important;
    background-image: none !important;
    color: rgb(17 24 39) !important;
  }

  .dark input:-webkit-autofill,
  .dark input:-webkit-autofill:hover,
  .dark input:-webkit-autofill:focus {
    -webkit-text-fill-color: white !important;
    color: white !important;
  }
}

@layer utilities {
  .bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(var(--primary) / var(--tw-bg-opacity));
  }

  .bg-secondary {
    --tw-bg-opacity: 1;
    background-color: rgb(var(--secondary) / var(--tw-bg-opacity));
  }

  .text-primary {
    --tw-text-opacity: 1;
    color: rgb(var(--primary) / var(--tw-text-opacity));
  }

  .text-secondary {
    --tw-text-opacity: 1;
    color: rgb(var(--secondary) / var(--tw-text-opacity));
  }
}

@layer components {
  /* Input field styling */
  .input-wrapper {
    @apply relative;
  }

  .input-icon {
    @apply absolute left-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-gray-400 dark:text-gray-500 pointer-events-none;
  }

  .input-field {
    @apply block w-full rounded-lg bg-white dark:bg-transparent border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 focus:border-transparent transition-shadow py-3 px-4;
  }

  .input-field-with-icon {
    padding-left: 2rem !important;
  }

  /* Navigation styles */
  .nav-link {
    @apply inline-flex items-center w-full px-4 py-3 rounded-lg text-gray-900 border border-gray-300 
           hover:bg-gray-50 dark:border-gray-600 dark:text-white dark:hover:bg-gray-800 
           transition-all duration-200;
    font-family: var(--font-family-Gant-Modern-regular);
    font-weight: var(--font-weight-Gant-Modern-regular);
  }

  .nav-link-active {
    @apply bg-gray-100 ms-3 dark:bg-gray-800;
    font-family: var(--font-family-Gant-Modern-bold);
    font-weight: var(--font-weight-Gant-Modern-bold);
  }
}

/* Input autofill overrides */
body {
  @apply bg-white text-gray-900 dark:bg-slate-950 dark:text-white;
}

.dark body {
  @apply bg-slate-950 text-white;
}

.input-wrapper {
  @apply relative;
}

.input-wrapper::before {
  @apply absolute inset-0 rounded-lg transition-colors content-[''] border border-gray-200 dark:border-gray-700 -z-10;
  background-color: white;
}

.dark .input-wrapper::before {
  @apply bg-transparent border-gray-700;
}

.input-wrapper:focus-within::before {
  @apply ring-2 ring-gray-300 dark:ring-gray-600;
}

.input-wrapper:focus-within {
  @apply relative outline outline-2 outline-gray-400 dark:outline-gray-500 outline-offset-2 rounded-lg;
}

.input-icon {
  @apply absolute left-3 top-1/2 -translate-y-1/2 flex items-center justify-center text-gray-400 dark:text-gray-500 pointer-events-none;
}

.input-field-with-icon {
  @apply pl-24;
}

/* Scroll Spy Styles */
html {
    scroll-behavior: smooth;
}

[data-nav-link].active {
    @apply text-gray-900 dark:text-gray-100;
    position: relative;
}

[data-nav-link].active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: rgb(17 24 39); /* bg-gray-900 */
    .dark & {
        background-color: rgb(243 244 246); /* bg-gray-100 */
    }
}
