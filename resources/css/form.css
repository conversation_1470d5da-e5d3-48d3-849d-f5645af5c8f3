.designdev_tm_contact{
	width: 100%;
	padding: 143px 0px 140px 0px;
	background-color: #f5f5f5;
}
.designdev_tm_contact .contact_inner{
	width: 100%;
}
.designdev_tm_contact .form_wrapper{
	width: 100%;
}
.designdev_tm_contact .form_wrapper ul{
	display: flex;
	flex-wrap: wrap;
	margin-left: -10px;
}
.designdev_tm_contact .form_wrapper ul li{
	width: 50%;
	padding-left: 10px;
	margin-bottom: 10px;
}
.designdev_tm_contact .form_wrapper ul li input,
.designdev_tm_contact .form_wrapper ul li select{
	width: 100%;
	height: 50px;
	padding: 5px 20px;
	background-color: #eaeaea;
	font-family: var(--font-syne);
	font-size: 16px;
	color: #868a9b;
}
.designdev_tm_contact .form_wrapper ul li input:focus,
.designdev_tm_contact .form_wrapper ul li select:focus,
.designdev_tm_contact .form_wrapper textarea:focus,
.designdev_tm_contact .enter_code input:focus{
	outline: none;
}
.designdev_tm_contact .form_wrapper textarea{
	padding: 20px;
	height: 120px;
	background-color: #eaeaea;
	resize: none;
	font-family: var(--font-syne);
	font-size: 16px;
}
.designdev_tm_contact #enter_code{
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 30px;
	margin-top: 2px;
}
.designdev_tm_contact #enter_code span{
	display: inline-block;
	height: 50px;
	line-height: 50px;
	color: #fff;
	background-color: #000;
	padding: 0px 30px;
}
.designdev_tm_contact #enter_code input{
	display: inline-block;
	height: 50px;
	line-height: 50px;
	background-color: #eaeaea;
	padding: 0px 20px;
	font-family: var(--font-syne);
	font-size: 16px;
}
.designdev_tm_contact input::-webkit-outer-spin-button,
.designdev_tm_contact input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

/* Firefox */
.designdev_tm_contact input[type=number] {
  -moz-appearance: textfield;
}

.error_box{
	width: 100%;
	background-color: #fce3e3;
	padding: 20px 10px;
	text-align: center;
	margin-bottom: 10px;
	display: none;
}
.error_box p{
	color: #721c24;
}
.success_box{
	width: 100%;
	display: none;
	background-color: #0ac083;
	padding: 20px 10px;
	text-align: center;
	margin-bottom: 10px;
}
.success_box p{
	color: #fff;
}
.error .cf-form-control {
	border-bottom: 2px solid red;
}
.error select {
	border-bottom: 2px solid red;
}
.success .cf-form-control {
	border-bottom: 2px solid green;
}
.success select {
	border-bottom: 2px solid green;
}
#text-area-w {
	width: 100%;
}