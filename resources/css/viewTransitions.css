@view-transition {
    navigation: auto;
}

::view-transition-old(root),
::view-transition-new(root) {
    animation-duration: 0.5s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Slide effect */
::view-transition-old(root) {
    animation: slide-out 0.5s ease both;
}

::view-transition-new(root) {
    animation: slide-in 0.5s ease both;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slide-out {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

/* Make sure the sliding elements don't create horizontal scrollbars */
html {
    overflow-x: hidden;
}

/* Optional: Add a nice easing curve for smoother animation */
::view-transition-group(*) {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
