document.addEventListener('DOMContentLoaded', () => {
  const canvas = document.getElementById('gradient-canvas')
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')

  let width = canvas.width = window.innerWidth
  let height = canvas.height = window.innerHeight

  const colors = [
    { r: 255, g: 71, b: 87 },
    { r: 255, g: 99, b: 147 },
    { r: 255, g: 99, b: 147 },
    { r: 255, g: 178, b: 125 },
    { r: 255, g: 178, b: 125 },
    { r: 255, g: 71, b: 87 }
  ]

  const particles = []
  const nrOfParticles = colors.length
  const particleMaxWidth = width * 0.86

  class Point {
    constructor(x, y) {
      this.position = { x, y }
      this.velocity = {
        x: (Math.random() - 0.5) * 0.8,
        y: (Math.random() - 0.5) * 0.8
      }
    }

    update() {
      this.position.x += this.velocity.x
      this.position.y += this.velocity.y

      if (this.position.x < 0 || this.position.x > width) this.velocity.x *= -1
      if (this.position.y < 0 || this.position.y > height) this.velocity.y *= -1
    }
  }

  class Particle {
    constructor(fillColor, i) {
      this.fillColor = fillColor
      this.point = new Point(
        (width / (nrOfParticles - 1)) * i,
        height / 2 + (Math.random() - 0.5) * 200
      )
    }

    update() {
      this.point.update()
    }
  }

  function createParticles() {
    for (let i = 0; i < nrOfParticles; i++) {
      particles.push(new Particle(colors[i], i))
    }
  }

  function drawGradient() {
    const gradient = ctx.createLinearGradient(0, 0, width, 0)
    for (let i = 0; i < particles.length; i++) {
      const { r, g, b } = particles[i].fillColor
      const color = `rgb(${r}, ${g}, ${b})`
      gradient.addColorStop(i / (particles.length - 1), color)
    }
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
  }

  function updateParticles() {
    for (let i = 0; i < particles.length; i++) {
      particles[i].update()
    }
  }

  function render() {
    ctx.clearRect(0, 0, width, height)
    drawGradient()
    updateParticles()
    requestAnimationFrame(render)
  }

  function onResize() {
    width = canvas.width = window.innerWidth
    height = canvas.height = window.innerHeight
  }

  window.addEventListener('resize', onResize)
  createParticles()
  render()
})
