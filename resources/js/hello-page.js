import { createApp } from 'vue';
import LavaBalls from './components/LavaBalls.vue';
import CommandPalette from './components/CommandPalette.vue';
import TypedGreeting from './components/TypedGreeting.vue';
import SidebarMenu from './components/SidebarMenu.vue';
import LogoSection from './components/sidebar/LogoSection.vue';
import BackButton from './components/sidebar/BackButton.vue';
import NavItemList from './components/sidebar/NavItemList.vue';
import AdminBadgeSection from './components/sidebar/AdminBadgeSection.vue';
import Bookmarks from './components/Bookmarks.vue';
import HelloTabs from './components/HelloTabs.vue';
import CardEditModal from './components/CardEditModal.vue';

// Create a dedicated app instance for the hello page
document.addEventListener('DOMContentLoaded', () => {
    const app = createApp({});
    
    // Register all components used on this page
    app.component('lava-balls', LavaBalls);
    app.component('command-palette', CommandPalette);
    app.component('typed-greeting', TypedGreeting);
    
    // Register sidebar menu components
    app.component('sidebar-menu', SidebarMenu);
    app.component('logo-section', LogoSection);
    app.component('back-button', BackButton);
    app.component('nav-item-list', NavItemList);
    app.component('admin-badge-section', AdminBadgeSection);
    app.component('bookmarks', Bookmarks);
    app.component('hello-tabs', HelloTabs);
    app.component('card-edit-modal', CardEditModal);
    
    // Check if the target element exists before mounting
    if (document.getElementById('hello-app')) {
        app.mount('#hello-app');
        console.log('Hello page Vue app mounted successfully');
    }
    
    // Simpler helper function to fix z-index issues without DOM manipulation
    window.fixModalZIndex = () => {
        // Force a reflow to ensure proper rendering
        setTimeout(() => {
            // Add a class to the body to indicate a modal is open
            document.body.classList.add('modal-open');
        }, 10);
    };
});

// Add global event listener to handle modals
document.addEventListener('keydown', function(e) {
    // Ensure modals are closed when Escape key is pressed
    if (e.key === 'Escape') {
        document.body.classList.remove('modal-open');
    }
}); 