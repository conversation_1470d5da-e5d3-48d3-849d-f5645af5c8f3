document.addEventListener('DOMContentLoaded', function() {
  // Mobile menu interaction
  const hamburger = document.querySelector('.hamburger');
  const dropdown = document.querySelector('.dropdown');
  
  if (hamburger && dropdown) {
    hamburger.addEventListener('click', function() {
      hamburger.classList.toggle('is-active');
      dropdown.classList.toggle('hidden');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!hamburger.contains(e.target) && !dropdown.contains(e.target)) {
        hamburger.classList.remove('is-active');
        dropdown.classList.add('hidden');
      }
    });
  }
}); 