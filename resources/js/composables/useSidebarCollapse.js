import { ref, onMounted, onUnmounted } from 'vue';

export function useSidebarCollapse() {
  // Initialize from localStorage immediately
  let initialCollapsedState = false;
  try {
    const savedState = localStorage.getItem('sidebarCollapsed');
    initialCollapsedState = savedState === 'true';
  } catch (e) {
    console.warn('Could not read sidebar state from localStorage', e);
  }

  const isCollapsed = ref(initialCollapsedState);

  // Apply sidebar state immediately
  if (typeof window !== 'undefined' && isCollapsed.value) {
    requestAnimationFrame(() => {
      applyCollapseState(isCollapsed.value);
    });
  }

  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
    applyCollapseState(isCollapsed.value);
    
    // Store state in localStorage
    try {
      localStorage.setItem('sidebarCollapsed', isCollapsed.value ? 'true' : 'false');
    } catch (e) {
      console.warn('Could not save sidebar state to localStorage', e);
    }
  };

  const applyCollapseState = (collapsed) => {
    const mainContent = document.getElementById('mainContent');
    const sidebar = document.querySelector('.designdev_tm_leftpart');
    
    if (!sidebar || !mainContent) return;
    
    const isMobile = window.innerWidth <= 768;
    
    if (collapsed) {
      // When sidebar is collapsed
      if (isMobile) {
        sidebar.style.width = '0';
        mainContent.style.paddingLeft = '0';
      } else {
        sidebar.style.width = '80px';
        mainContent.style.paddingLeft = '80px';
      }
      document.body.classList.add('sidebar-collapsed');
    } else {
      // When sidebar is expanded
      if (isMobile) {
        sidebar.style.width = '100%';
        mainContent.style.paddingLeft = '0';
      } else {
        sidebar.style.width = '320px';
        mainContent.style.paddingLeft = '320px';
      }
      document.body.classList.remove('sidebar-collapsed');
    }
  };

  // Handle window resize
  const handleResize = () => {
    applyCollapseState(isCollapsed.value);
  };

  onMounted(() => {
    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  return {
    isCollapsed,
    toggleCollapse
  };
} 