import { ref, computed, watch } from 'vue';
import axios from 'axios';

// Shared state to cache the status options across components
const statusOptions = ref([]);
const isLoading = ref(false);
const error = ref(null);
const lastFetchTime = ref(null);
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Composable for fetching and managing whiteboard status options
 */
export function useWhiteboardStatuses() {
  const defaultStatusSlug = computed(() => {
    const defaultStatus = statusOptions.value.find(status => status.is_default);
    return defaultStatus ? defaultStatus.slug : 'active';
  });

  /**
   * Get the color for a given status slug
   */
  const getStatusColor = (slug) => {
    if (!slug || !statusOptions.value.length) return '#6B7280'; // Default gray
    
    const status = statusOptions.value.find(s => s.slug === slug);
    return status ? status.color : '#6B7280'; // Default gray
  };

  /**
   * Get the name for a given status slug
   */
  const getStatusName = (slug) => {
    if (!slug || !statusOptions.value.length) return 'Status';
    
    const status = statusOptions.value.find(s => s.slug === slug);
    return status ? status.name : 'Unknown Status';
  };

  /**
   * Fetch status options from the API
   */
  const fetchStatusOptions = async (force = false) => {
    // Skip if already loading
    if (isLoading.value) return;

    // Check if we already have data and it's recent enough
    const now = Date.now();
    if (
      !force && 
      statusOptions.value.length > 0 && 
      lastFetchTime.value && 
      now - lastFetchTime.value < CACHE_DURATION
    ) {
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      const response = await axios.get('/api/whiteboard-statuses');
      statusOptions.value = response.data;
      lastFetchTime.value = now;
    } catch (err) {
      error.value = err.message || 'Failed to fetch status options';
      console.error('Error fetching status options:', err);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Get a filtered list of statuses
   */
  const getFilteredStatuses = (predicate) => {
    return statusOptions.value.filter(predicate);
  };

  /**
   * Get all active statuses (exclude archived, etc.)
   */
  const getActiveStatuses = computed(() => {
    return statusOptions.value.filter(s => s.slug !== 'archived');
  });

  /**
   * Get all statuses ordered by their order field
   */
  const getOrderedStatuses = computed(() => {
    return [...statusOptions.value].sort((a, b) => a.order - b.order);
  });

  // Auto-fetch statuses when the composable is used for the first time
  if (statusOptions.value.length === 0 && !isLoading.value) {
    fetchStatusOptions();
  }

  return {
    statusOptions,
    isLoading,
    error,
    defaultStatusSlug,
    getStatusColor,
    getStatusName,
    fetchStatusOptions,
    getFilteredStatuses,
    getActiveStatuses,
    getOrderedStatuses,
  };
} 