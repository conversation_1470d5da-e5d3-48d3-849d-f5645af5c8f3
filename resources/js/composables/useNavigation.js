import { computed } from 'vue';

export function useNavigation(currentRoute) {
  // Check if route is active
  const isActiveRoute = (routePatterns) => {
    if (!routePatterns) return false;
    
    const patterns = Array.isArray(routePatterns) ? routePatterns : [routePatterns];
    return patterns.some(pattern => {
      if (pattern.includes('*')) {
        const baseRoute = pattern.replace('*', '');
        return currentRoute.startsWith(baseRoute);
      }
      return currentRoute === pattern;
    });
  };

  // Safe navigation method
  const navigateTo = (url) => {
    if (typeof window !== 'undefined' && window.location) {
      window.location.href = url;
    }
  };

  // Submit logout form
  const submitLogout = (formId) => {
    const form = document.getElementById(formId);
    if (form) form.submit();
  };

  // Toggle class helpers
  const addBoldClass = (event) => {
    const spanElement = event.currentTarget.querySelector('span');
    if (spanElement) {
      spanElement.classList.add('gant-modern-bold');
    }
  };

  const removeBoldClass = (event) => {
    const spanElement = event.currentTarget.querySelector('span');
    if (spanElement) {
      spanElement.classList.remove('gant-modern-bold');
    }
  };

  return {
    isActiveRoute,
    navigateTo,
    submitLogout,
    addBoldClass,
    removeBoldClass
  };
} 