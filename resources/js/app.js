import './bootstrap';
import 'bootstrap';
import 'tailwindcss/tailwind.css';
import '../css/app.css';
import { createApp } from 'vue';
import { initScrollSpy } from './tools/scroll-spy.js';

// Import and use Vue.js
import CodeTerminal from './components/CodeTerminal.vue';
import DarkModeToggle from './components/DarkModeToggle.vue';
import CardFont from './components/CardFont.vue';
import IconGrid from './components/IconGrid.vue';
import IconFontHeader from './components/IconFontHeader.vue';
import IconGridDetail from './components/IconGridDetail.vue';
import PlaygroundDemo from './components/PlaygroundDemo.vue';
import FontControls from './components/FontControls.vue';
import FontDownloadButton from './components/FontDownloadButton.vue';
import CommandPalette from './components/CommandPalette.vue';
import LoginForm from './components/LoginForm.vue';
import TypedGreeting from './components/TypedGreeting.vue';
import LavaBalls from './components/LavaBalls.vue';
import CookieModal from './components/CookieModal.vue';
import LanguageSwitcher from './components/LanguageSwitcher.vue'
import CardFamily from './components/CardFamily.vue'
import ParticleOrb from './components/ParticleOrb.vue'
import OrdeGroupImage from './components/OrderGroupImage.vue'
import AdminNavigation from './components/AdminNavigation.vue'
import SidebarMenu from './components/SidebarMenu.vue'
import LogoSection from './components/sidebar/LogoSection.vue'
import BackButton from './components/sidebar/BackButton.vue'
import NavItemList from './components/sidebar/NavItemList.vue'
import AdminBadgeSection from './components/sidebar/AdminBadgeSection.vue'
import ProfileEdit from './components/Profile/ProfileEdit.vue'
import ChangelogManager from './components/admin/ChangelogManager.vue'
import UsersTable from './components/UsersTable.vue'
import ProjectsTable from './components/ProjectsTable.vue'
import DefaultPageSelector from './components/Profile/DefaultPageSelector.vue'
import FontIconManager from './components/FontIconManager.vue'
import IconDetailModal from './components/IconDetailModal.vue'
import TagManager from './components/TagManager.vue'
import FontIconDebug from './components/FontIconDebug.vue'
import QrGenerator from './components/QrGenerator.vue'
import BarcodeGenerator from './components/BarcodeGenerator.vue'
import DatatableComponent from './components/Shared/DatatableComponent.vue'

// Import whiteboard components
import WhiteboardContainer from './components/WhiteboardContainer.vue';
import WhiteboardCard from './components/WhiteboardCard.vue';
import WhiteboardCardModal from './components/WhiteboardCardModal.vue';
import ConfirmationModal from './components/ConfirmationModal.vue';

// Import mobile menu functionality
import './mobile-menu';

import SellThroughProgress from './components/SellThroughProgress.vue';

const app = createApp({
    data() {
        return {
            open: false,
            isVisible: false,
            mobileMenuOpen: false,
            darkMode: localStorage.getItem('darkMode') === 'true'
        };
    },
    methods: {
        toggle() {
            this.isVisible = !this.isVisible;
        },
        toggleMobileMenu() {
            this.mobileMenuOpen = !this.mobileMenuOpen;
            const dropdown = document.querySelector('.designdev_tm_mobile_menu .dropdown');
            const hamburger = document.querySelector('.hamburger');
            
            if (this.mobileMenuOpen) {
                dropdown.classList.remove('hidden');
                hamburger.classList.add('is-active');
            } else {
                dropdown.classList.add('hidden');
                hamburger.classList.remove('is-active');
            }
        },
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode);
            document.documentElement.classList.toggle('dark', this.darkMode);
        }
    },
    mounted() {
        const hamburger = document.querySelector('.hamburger');
        if (hamburger) {
            hamburger.addEventListener('click', () => this.toggleMobileMenu());
        }
        // Initialize dark mode
        document.documentElement.classList.toggle('dark', this.darkMode);
    }
});

// Register global components
app.component('icon-font-header', IconFontHeader);

// Register local components
app.component('card-font', CardFont);
app.component('icon-grid', IconGrid);
app.component('icon-grid-detail', IconGridDetail);
app.component('playground-demo', PlaygroundDemo);
app.component('dark-mode-toggle', DarkModeToggle);
app.component('code-terminal', CodeTerminal);
app.component('font-controls', FontControls);
app.component('font-download-button', FontDownloadButton);
app.component('command-palette', CommandPalette);
app.component('login-form', LoginForm);
app.component('typed-greeting', TypedGreeting);
app.component('lava-balls', LavaBalls);
app.component('cookie-modal', CookieModal);
app.component('language-switcher', LanguageSwitcher)
app.component('card-family', CardFamily)
app.component('particle-orb', ParticleOrb)
app.component('ordegroup-image', OrdeGroupImage)
app.component('admin-navigation', AdminNavigation)
app.component('sidebar-menu', SidebarMenu)
app.component('logo-section', LogoSection)
app.component('back-button', BackButton)
app.component('nav-item-list', NavItemList)
app.component('admin-badge-section', AdminBadgeSection)
app.component('profile-edit', ProfileEdit)
app.component('changelog-manager', ChangelogManager)
app.component('users-table', UsersTable)
app.component('projects-table', ProjectsTable)
app.component('default-page-selector', DefaultPageSelector)
app.component('font-icon-manager', FontIconManager)
app.component('icon-detail-modal', IconDetailModal)
app.component('tag-manager', TagManager)
app.component('font-icon-debug', FontIconDebug)
app.component('qr-generator', QrGenerator)
app.component('barcode-generator', BarcodeGenerator)
app.component('datatable-component', DatatableComponent)
app.component('sell-through-progress', SellThroughProgress);

// Register whiteboard components
app.component('whiteboard-container', WhiteboardContainer);
app.component('whiteboard-card', WhiteboardCard);
app.component('whiteboard-card-modal', WhiteboardCardModal);
app.component('confirmation-modal', ConfirmationModal);

// Replace this line:
// app.mount('#app');

// With this code:
document.addEventListener('DOMContentLoaded', () => {
  // Add a small delay to ensure DOM is fully ready
  setTimeout(() => {
    // On the hello page, we need to mount the app differently
    if (document.getElementById('hello-app')) {
      // Mount only to the sidebar container on hello page
      const sidebarContainer = document.getElementById('sidebar-container');
      if (sidebarContainer) {
        app.mount('#sidebar-container');
      }
    } else {
      // Normal mounting on other pages
      app.mount('#app');
    }
  }, 50);
});

// Make admin status available globally
window.isAdmin = document.querySelector('meta[name="is-admin"]')?.content === 'true';

// PWA Install Prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent Chrome 67 and earlier from automatically showing the prompt
    e.preventDefault();
    // Stash the event so it can be triggered later
    deferredPrompt = e;
});

// Register Service Worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                // Registration successful
            })
            .catch(error => {
                // Registration failed silently
            });
    });
}

// Initialize scroll spy after Vue app is mounted
initScrollSpy();

// Import admin-specific components when needed
if (document.querySelector('[data-admin-panel="true"]')) {
    // This file can be used to register additional admin components that aren't already registered above
    import('./admin-components.js').then((module) => {
        module.default(app);
    }).catch(error => {
        // Handle errors silently
    });
}

// Make components available globally
window.IconGrid = IconGrid;
window.IconGridDetail = IconGridDetail;
window.IconDetailModal = IconDetailModal;
window.FontIconDebug = FontIconDebug;
window.app = app; // Export the Vue app instance
