document.addEventListener('DOMContentLoaded', function() {
    // Basic elements
    const sections = document.querySelectorAll('.scroll-section');
    const scrollContainer = document.querySelector('.snap-container');
    const navDots = document.querySelectorAll('.nav-dot');
    
    // Exit if essential elements aren't found
    if (!scrollContainer || sections.length === 0 || navDots.length === 0) {
        console.warn('Snap scroll: Missing required elements');
        return;
    }
    
    // Control variables
    let isScrolling = false;
    let activeIndex = 0;
    
    // Function to highlight the active dot - updated for new structure
    function setActiveDot(index) {
        // Verify index is valid
        if (index < 0 || index >= sections.length) return;
        
        // Set class on all dots
        navDots.forEach((dot, i) => {
            // Handle both mobile and desktop layouts
            if (dot.querySelector('span')) {
                // Desktop layout - dots are spans inside anchor tags
                const dotSpan = dot.querySelector('span:first-child');
                
                // Remove all classes first
                dotSpan.classList.remove('bg-black', 'dark:bg-white', 'bg-gray-300', 'dark:bg-gray-700', 'scale-125');
                
                // Add appropriate classes based on active state
                if (i === index) {
                    dotSpan.classList.add('bg-black', 'dark:bg-white', 'scale-125');
                } else {
                    dotSpan.classList.add('bg-gray-300', 'dark:bg-gray-700');
                }
            } else {
                // Mobile layout - dots are the anchor tags themselves
                // Remove all classes first
                dot.classList.remove('bg-black', 'dark:bg-white', 'bg-gray-300', 'dark:bg-gray-700', 'scale-125');
                
                // Add appropriate classes based on active state
                if (i === index) {
                    dot.classList.add('bg-black', 'dark:bg-white', 'scale-125');
                } else {
                    dot.classList.add('bg-gray-300', 'dark:bg-gray-700');
                }
            }
        });
        
        // Update active index
        activeIndex = index;
    }
    
    // Function to find the current section with improved accuracy
    function findVisibleSection() {
        const scrollTop = scrollContainer.scrollTop;
        const containerHeight = scrollContainer.clientHeight;
        
        // Calculate visibility threshold - a section is considered visible when it occupies 
        // at least 50% of the viewport
        const visibilityThreshold = containerHeight * 0.5;
        
        // Find section with the most visibility
        let maxVisibleSection = 0;
        let maxVisibleArea = 0;
        
        for (let i = 0; i < sections.length; i++) {
            const section = sections[i];
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionBottom = sectionTop + sectionHeight;
            
            // Calculate how much of the section is visible
            const visibleTop = Math.max(scrollTop, sectionTop);
            const visibleBottom = Math.min(scrollTop + containerHeight, sectionBottom);
            const visibleArea = Math.max(0, visibleBottom - visibleTop);
            
            // If this section has more visible area than the previous max, update the max
            if (visibleArea > maxVisibleArea) {
                maxVisibleArea = visibleArea;
                maxVisibleSection = i;
            }
            
            // For exact snap points - if a section occupies most of the viewport, it's definitely the visible one
            if (visibleArea >= visibilityThreshold) {
                return i;
            }
        }
        
        // Return the section with the most visible area
        return maxVisibleSection;
    }
    
    // Scroll to a specific section
    function scrollToSection(index) {
        if (index < 0 || index >= sections.length) return;
        
        isScrolling = true;
        sections[index].scrollIntoView({ behavior: 'smooth' });
        setActiveDot(index);
        
        // Update URL hash without triggering scroll
        const sectionId = sections[index].id;
        if (sectionId) {
            history.replaceState(null, null, `#${sectionId}`);
        }
        
        setTimeout(() => {
            isScrolling = false;
        }, 1000);
    }
    
    // Add event listeners to navigation dots
    navDots.forEach((dot, index) => {
        dot.addEventListener('click', (e) => {
            e.preventDefault();
            if (!isScrolling) {
                scrollToSection(index);
            }
        });
    });
    
    // Mouse wheel navigation with improved handling
    scrollContainer.addEventListener('wheel', (e) => {
        // Only prevent default if we're going to handle the scroll
        const direction = e.deltaY > 0 ? 1 : -1;
        const newIndex = activeIndex + direction;
        
        if (isScrolling || newIndex < 0 || newIndex >= sections.length) {
            return; // Allow default scroll if we're at the boundaries
        }
        
        e.preventDefault();
        scrollToSection(newIndex);
    }, { passive: false });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (isScrolling) return;
        
        let newIndex = activeIndex;
        
        if (e.key === 'ArrowDown' || e.key === 'PageDown') {
            newIndex = Math.min(sections.length - 1, activeIndex + 1);
            e.preventDefault();
        } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
            newIndex = Math.max(0, activeIndex - 1); 
            e.preventDefault();
        } else {
            return; // Exit for other keys
        }
        
        scrollToSection(newIndex);
    });
    
    // Update on scroll with debounce for better performance
    let scrollTimeout;
    scrollContainer.addEventListener('scroll', () => {
        // Clear previous timeout
        clearTimeout(scrollTimeout);
        
        // Set a new timeout to prevent excessive calculations
        scrollTimeout = setTimeout(() => {
            if (!isScrolling) {
                const newIndex = findVisibleSection();
                if (newIndex !== activeIndex) {
                    setActiveDot(newIndex);
                    
                    // Update URL hash without triggering scroll
                    const sectionId = sections[newIndex].id;
                    if (sectionId) {
                        history.replaceState(null, null, `#${sectionId}`);
                    }
                }
            }
        }, 50); // 50ms debounce
    });
    
    // Check for hash in URL on page load to scroll to the right section
    window.addEventListener('load', () => {
        // Get hash from URL
        const hash = window.location.hash;
        if (hash) {
            // Find section index by id
            const targetSection = document.querySelector(hash);
            if (targetSection) {
                const targetIndex = Array.from(sections).indexOf(targetSection);
                if (targetIndex >= 0) {
                    // Small delay to ensure everything is loaded
                    setTimeout(() => {
                        scrollToSection(targetIndex);
                    }, 300);
                }
            }
        } else {
            // Set initial section and dot on page load
            const initialIndex = findVisibleSection();
            setActiveDot(initialIndex);
        }
    });
    
    // Set initial active dot 
    setActiveDot(findVisibleSection());
}); 