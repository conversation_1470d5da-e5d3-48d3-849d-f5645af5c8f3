// Handle all same-origin navigation
document.addEventListener('click', (e) => {
    // Only handle clicks on links
    const link = e.target.closest('a');
    if (!link || !link.href || !isSameOrigin(link.href)) return;

    // Prevent default navigation
    e.preventDefault();

    // Navigate using the API
    navigate(link.href);
});

// Check if URL is same origin
function isSameOrigin(url) {
    try {
        return new URL(url).origin === window.location.origin;
    } catch {
        return false;
    }
}

// Handle navigation with view transitions
async function navigate(url) {
    if (!document.startViewTransition) {
        window.location = url;
        return;
    }

    try {
        // Create new view transition
        const transition = document.startViewTransition(async () => {
            // Fetch new page
            const response = await fetch(url);
            const text = await response.text();
            
            // Get the new page's content
            const tempDoc = new DOMParser().parseFromString(text, 'text/html');
            
            // Update the content that needs to be transitioned
            document.body.innerHTML = tempDoc.body.innerHTML;
            document.title = tempDoc.title;
            
            // Update the URL
            window.history.pushState({}, '', url);
        });

        // Wait for the transition to finish
        await transition.finished;
    } catch (error) {
        // If something goes wrong, fall back to normal navigation
        console.error('View transition failed:', error);
        window.location = url;
    }
}

// Handle back/forward navigation
window.addEventListener('popstate', () => {
    // Start a new view transition
    document.startViewTransition(() => {
        // The history entry has already changed
        // so we can just render the new page
        location.reload();
    });
});
