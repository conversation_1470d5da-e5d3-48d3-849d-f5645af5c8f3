<template>
  <div class="loader-overlay" v-if="visible">
    <div class="loader">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "Loader",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped>
.loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader {
  width: 100px;
  height: 100px;
  position: relative;
}
.loader span {
  position: absolute;
  display: block;
  width: 40px;
  height: 40px;
  background: #262626;
  border-radius: 5px;
  animation: load 2.3s ease-in-out infinite;
}
.loader span:nth-child(1) {
  top: 0;
  left: 0;
  background: #2ecc71;
  animation-delay: 0.2s;
}
.loader span:nth-child(2) {
  top: 0;
  right: 0;
  background: #3498db;
  animation-delay: 0.4s;
}
.loader span:nth-child(3) {
  bottom: 0;
  left: 0;
  background: #e74c3c;
  animation-delay: 0.6s;
}
.loader span:nth-child(4) {
  bottom: 0;
  right: 0;
  background: #f1c40f;
  animation-delay: 0.8s;
}
@keyframes load {
  0% {
    top: 0;
    left: 0;
  }
  12.5% {
    top: 0;
    left: 50%;
  }
  25% {
    top: 0;
    left: 50%;
  }
  37.5% {
    top: 50%;
    left: 50%;
  }
  50% {
    top: 50%;
    left: 50%;
  }
  62.5% {
    top: 50%;
    left: 0;
  }
  75% {
    top: 50%;
    left: 0;
  }
  87.5% {
    top: 0;
    left: 0;
  }
  100% {
    top: 0;
    left: 0;
  }
}
</style> 