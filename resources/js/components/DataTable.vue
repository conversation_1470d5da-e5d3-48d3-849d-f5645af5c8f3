<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useLocalStorage } from '@vueuse/core'

interface Column {
  key: string
  label: string
  sortable?: boolean
  sortKey?: string
  align?: 'left' | 'center' | 'right'
  width?: string
  tdClass?: string
  hidden?: boolean
}

interface Props {
  data: any[]
  columns: Column[]
  defaultSortColumn?: string | null
  defaultSortDirection?: 'asc' | 'desc'
  tableId: string
  persistentState?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultSortColumn: null,
  defaultSortDirection: 'asc',
  persistentState: true,
  tableId: 'data-table'
})

// State management
const searchQuery = ref('')
const sortColumn = ref(props.defaultSortColumn)
const sortDirection = ref(props.defaultSortDirection)
const showColumnSelector = ref(false)
const showColumnToggles = ref(false)

// Improved column visibility management
const defaultVisibleColumns = props.columns
  .filter(col => !col.hidden)
  .map(col => col.key)
const visibleColumns = props.persistentState
  ? useLocalStorage(`${props.tableId}-visible-columns`, defaultVisibleColumns)
  : ref(defaultVisibleColumns)

// Computed properties
const filteredAndSortedData = computed(() => {
  let result = [...props.data]
  
  // Search filtering
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    result = result.filter(item => 
      visibleColumns.value.some(colKey => {
        const column = props.columns.find(col => col.key === colKey)
        if (!column) return false
        const value = getNestedValue(item, column.key)
        return value != null && String(value).toLowerCase().includes(query)
      })
    )
  }
  
  // Sorting
  if (sortColumn.value) {
    const column = props.columns.find(col => col.key === sortColumn.value)
    const sortKey = column?.sortKey || sortColumn.value
    
    result.sort((a, b) => {
      const valueA = getNestedValue(a, sortKey)
      const valueB = getNestedValue(b, sortKey)
      
      // Handle null/undefined values
      if (valueA === valueB) return 0
      if (valueA == null) return 1
      if (valueB == null) return -1
      
      // Compare values
      const comparison = valueA < valueB ? -1 : 1
      return sortDirection.value === 'asc' ? comparison : -comparison
    })
  }
  
  return result
})

// Column management
const visibleColumnsCount = computed(() => visibleColumns.value.length)
const isColumnVisible = computed(() => (columnKey: string) => 
  visibleColumns.value.includes(columnKey)
)

const canHideColumn = computed(() => (columnKey: string) => 
  visibleColumnsCount.value > 1 || !isColumnVisible.value(columnKey)
)

// Methods
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((prev, curr) => 
    prev && prev[curr] !== undefined ? prev[curr] : null
  , obj)
}

function toggleSort(column: Column) {
  if (!column.sortable) return
  
  if (sortColumn.value === column.key) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = column.key
    sortDirection.value = 'asc'
  }
}

function toggleColumnVisibility(columnKey: string) {
  if (visibleColumnsCount.value <= 1 && visibleColumns.value.includes(columnKey)) {
    return // Prevent hiding last visible column
  }
  
  const index = visibleColumns.value.indexOf(columnKey)
  if (index >= 0) {
    visibleColumns.value = visibleColumns.value.filter(key => key !== columnKey)
  } else {
    visibleColumns.value = [...visibleColumns.value, columnKey]
  }
}

function resetColumnVisibility() {
  visibleColumns.value = [...defaultVisibleColumns]
}

// Click outside handler for column selector
const columnSelectorRef = ref<HTMLElement | null>(null)
let clickOutsideHandler: ((event: MouseEvent) => void) | null = null;

onMounted(() => {
  // Set up the event listener in the mounted hook
  clickOutsideHandler = (event) => {
    if (
      showColumnSelector.value && 
      columnSelectorRef.value && 
      !columnSelectorRef.value.contains(event.target as Node)
    ) {
      showColumnSelector.value = false
    }
  };
  
  document.addEventListener('click', clickOutsideHandler)
})

onBeforeUnmount(() => {
  // Clean up the event listener when component is unmounted
  if (clickOutsideHandler) {
    document.removeEventListener('click', clickOutsideHandler)
  }
})

// Add these new methods
function toggleAllInGroup(group, value) {
  const groupColumns = group.columns.map(col => col.key)
  if (value) {
    // Add all columns from group that aren't already visible
    const columnsToAdd = groupColumns.filter(key => !visibleColumns.value.includes(key))
    visibleColumns.value = [...visibleColumns.value, ...columnsToAdd]
  } else {
    // Remove all columns from group except if it's the last visible column
    const otherVisibleColumns = visibleColumns.value.filter(key => !groupColumns.includes(key))
    if (otherVisibleColumns.length > 0) {
      visibleColumns.value = otherVisibleColumns
    }
  }
}

function isGroupFullyVisible(group) {
  return group.columns.every(col => visibleColumns.value.includes(col.key))
}

function isGroupPartiallyVisible(group) {
  const visibleCount = group.columns.filter(col => visibleColumns.value.includes(col.key)).length
  return visibleCount > 0 && visibleCount < group.columns.length
}

// Expose methods and state
defineExpose({
  resetColumns: resetColumnVisibility,
  filteredData: filteredAndSortedData,
  visibleColumns,
  searchQuery,
  sort: (key) => {
    const column = props.columns.find(col => col.key === key);
    if (column) {
      toggleSort(column);
    }
  }
})
</script>

<template>
  <div class="bg-white dark:bg-slate-950 rounded-lg p-6 relative">
    <!-- Pattern Background -->
    <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
          <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#pattern-3)" />
    </svg>
    
    <!-- Table Controls -->
    <div class="flex flex-col gap-4 mb-3 z-10 relative">
      <!-- Search and Column Toggle Button Row -->
      <div class="flex gap-4 items-center">
        <!-- Filter Button/Panel -->
        <div class="relative z-20">
          <!-- Button that transforms into a row -->
          <div 
            class="flex items-center transition-all duration-300 ease-in-out overflow-hidden bg-white dark:bg-slate-950 rounded-lg border border-solid  !border-gray-200 dark:!border-gray-800 " 
            :class="{ 
              'hover:bg-gray-50 dark:hover:bg-gray-900': !showColumnToggles,
              'bg-gray-50 dark:bg-gray-900/50': showColumnToggles
            }"
            :style="{ 
              width: showColumnToggles ? '100%' : 'auto'
            }"
          >
            <!-- Toggle Button (Always Visible) -->
            <div 
              @click="showColumnToggles = !showColumnToggles"
              class="flex items-center gap-2 pr-1.5 pl-3 py-2 cursor-pointer transition-colors duration-150 group flex-shrink-0 text-gray-900 dark:text-gray-300"
            >
              <i class="opacity-50 design" :class="showColumnToggles ? 'design-close' : 'design-options'"></i>
              <span 
                class="whitespace-nowrap transition-all duration-300 ease-in-out gant-modern-regular "
                :class="showColumnToggles ? 'max-w-xs' : 'max-w-0 overflow-hidden group-hover:max-w-xs'"
              >
                {{ showColumnToggles ? 'Skryť filtre' : 'Filtrovať tabuľku' }}
              </span>
            </div>
            
            <!-- Divider (Only visible when expanded) -->
            <div v-show="showColumnToggles" class="h-6 self-center border-r border-gray-200 dark:border-gray-700 mx-1"></div>
            
            <!-- Filter Content (Inline with button) -->
            <div 
              v-show="showColumnToggles" 
              class="flex items-center gap-3 overflow-x-auto py-2 px-2 flex-grow"
            >
              <!-- Column Toggles -->
              <div class="flex items-center gap-2 flex-nowrap">
                <button
                  v-for="column in columns"
                  :key="column.key"
                  @click.stop="toggleColumnVisibility(column.key)"
                  class="flex items-center gap-1 px-2 py-1 gant-modern-regular text-sm whitespace-nowrap transition-colors duration-150 rounded-md"
                  :class="[
                    isColumnVisible(column.key)
                      ? 'text-black dark:text-white border border-gray-100 dark:bg-slate-950 dark:border-slate-600 '
                      : 'text-gray-300 dark:text-gray-600 hover:bg-gray-50 dark:hover:bg-slate-950 dark:border-slate-900',
                    !canHideColumn(column.key) && 'opacity-50 cursor-not-allowed'
                  ]"
                  :disabled="!canHideColumn(column.key)"
                >
                  <i 
                    class="design text-xs" 
                    :class="[
                      isColumnVisible(column.key) 
                        ? 'design-col-visible text-black dark:text-white' 
                        : 'design-col-hidden text-gray-400 dark:text-gray-500'
                    ]"
                  ></i>
                  {{ column.label }}
                </button>
              </div>

              <!-- Divider -->
              <div class="h-6 border-r border-gray-200 dark:border-gray-700 mx-1"></div>

              <!-- Quick Actions -->
              <div class="flex items-center gap-2 flex-shrink-0">
                <button 
                  @click.stop="resetColumnVisibility"
                  class="flex items-center gap-1 px-2 py-1 rounded-md text-sm gant-modern-medium bg-gray-900 dark:bg-gray-200 text-gray-100 dark:text-gray-900 hover:bg-gray-700 dark:hover:bg-gray-900 whitespace-nowrap"
                >
                  <i class="claims claims-refresh text-xs"></i>
                  Resetovať
                </button>
                <button 
                  @click.stop="visibleColumns = columns.map(col => col.key)"
                  class="flex items-center gap-1 px-2 py-1 rounded-md text-sm gant-modern-medium bg-gray-900 dark:bg-gray-200 text-gray-100 dark:text-gray-900 hover:bg-gray-700 dark:hover:bg-gray-900 whitespace-nowrap"
                >
                  <i class="design design-changes text-xs"></i>
                  Zobraziť všetko
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Search Box -->
        <div class="relative w-80">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="design design-search-fill text-gray-400 dark:text-gray-500"></i>
          </div>
          <input 
            v-model="searchQuery" 
            type="search" 
            class="block w-full p-2 pl-10 text-base gant-modern-regular text-gray-900 dark:text-white dark:!bg-slate-950 border border-gray-200 dark:border-gray-800 rounded-sm focus:ring-gray-500 focus:border-gray-500 !bg-white" 
            placeholder="Hľadať vo všetkých stĺpcoch..."
          >
        </div>
        
        <div class="flex-grow"></div>
      </div>
    </div>
    
    <!-- Table -->
    <div class="overflow-x-auto relative z-10">
      <table :id="tableId" class="w-full text-sm sm:text-base gant-modern-regular text-black dark:text-white">
        <thead class="text-xs sm:text-sm md:text-sm gant-modern-bold bg-gray-50 dark:bg-gray-900">
          <tr>
            <th 
              v-for="column in columns" 
              :key="column.key"
              :scope="column.key"
              v-show="isColumnVisible(column.key)"
              :class="[
                'px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 group', 
                column.align ? `text-${column.align}` : '',
                column.width || '',
                column.sortable ? 'cursor-pointer select-none' : ''
              ]"
              @click="toggleSort(column)"
            >
              <div class="flex items-center" :class="{'justify-center': column.align === 'center'}">
                <!-- Sort icon before the label -->
                <template v-if="column.sortable">
                  <i 
                    :class="[
                      'mr-1 sm:mr-1.5 text-xs sm:text-sm',
                      sortColumn === column.key 
                        ? (sortDirection === 'asc' ? 'design design-arrow-up' : 'design design-arrow-down')
                        : 'design design-filter opacity-50 group-hover:opacity-100'
                    ]"
                  ></i>
                </template>
                <span>{{ column.label }}</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-800">
          <tr 
            v-for="(item, index) in filteredAndSortedData" 
            :key="index"
            @click="$emit('row-click', item)"
            class="bg-white !border-b !border-gray-400 dark:!border-gray-600 dark:bg-slate-950 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors duration-150 cursor-pointer"
          >
            <td 
              v-for="column in columns" 
              :key="`${index}-${column.key}`"
              v-show="isColumnVisible(column.key)"
              :class="[
                'px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 text-sm sm:text-base', 
                column.align ? `text-${column.align}` : '',
                column.tdClass || ''
              ]"
            >
              <slot 
                :name="`cell(${column.key})`" 
                :value="getNestedValue(item, column.key)"
                :item="item"
              >
                {{ getNestedValue(item, column.key) || '-' }}
              </slot>
            </td>
          </tr>
          
          <!-- Empty State -->
          <tr v-if="filteredAndSortedData.length === 0" class="bg-white dark:bg-gray-800">
            <td :colspan="visibleColumnsCount" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
              <div class="flex flex-col items-center justify-center">
                <i class="design design-search-document text-4xl mb-3 opacity-30"></i>
                <p class="text-lg sm:text-xl gant-modern-medium">Neboli nájdené žiadne záznamy</p>
                <p v-if="searchQuery" class="text-sm sm:text-base mt-1 gant-modern-regular">
                  Skúste zmeniť kritériá vyhľadávania
                </p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
/* Optional: Add some transitions for smoother toggling */
.design {
  transition: all 0.2s ease;
}

.design-hidden {
  transform: scale(0.9);
  opacity: 0.7;
}

button:not(:disabled):hover .design-hidden {
  transform: scale(1);
  opacity: 1;
}

/* Hide scrollbar but keep functionality */
.overflow-x-auto {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.overflow-x-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style> 