<template>
  <modal-wrapper @keydown="handleKeyDown">
    <!-- Modal structure with header, content and footer -->
    <modal-header 
      :title="isEditing ? 'Edit Card' : 'Add New Card'"
      @close="$emit('close')"
    />
    
    <!-- Tab Navigation with improved styling -->
    <tab-navigation
      :active-tab="activeTab"
      @change-tab="activeTab = $event"
    />
    
    <!-- Content with scrolling and improved padding -->
    <div class="overflow-y-auto flex-1 px-6 py-5 bg-gray-50 dark:bg-slate-900/30">
      <form @submit.prevent="saveCard" class="space-y-6 max-w-4xl mx-auto">
        <!-- Details Tab Content -->
        <details-tab 
          v-show="activeTab === 'details'"
          v-model="formData"
          :category-logos="categoryLogos"
          :projects="projects"
          :status-options="statusOptions"
          :default-status-slug="defaultStatusSlug"
          :get-status-color="getStatusColor"
        />
        
        <!-- Links & Media Tab Content -->
        <links-tab 
          v-show="activeTab === 'links'"
          v-model="formData"
          :logo-icons="logoIcons"
        />
        
        <!-- Assignments Tab Content -->
        <assignments-tab 
          v-show="activeTab === 'assignments'" 
          v-model="formData"
          :users="users"
          :fonts="fonts"
        />

        <!-- Keyboard shortcuts help -->
        <div v-if="showShortcutsHelp" class="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-700 p-3 mt-5 text-xs text-gray-600 dark:text-gray-400">
          <div class="flex items-center mb-2">
            <span class="font-medium">Keyboard Shortcuts</span>
            <button @click="showShortcutsHelp = false" class="ml-auto text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
              <i class="design design-x"></i>
            </button>
          </div>
          <div class="grid grid-cols-2 gap-2">
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Alt+1</span> Details Tab</div>
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Alt+2</span> Links Tab</div>
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Alt+3</span> Assignments Tab</div>
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Ctrl+Enter</span> Save Card</div>
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Esc</span> Close Modal</div>
            <div><span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">?</span> Toggle Shortcuts Help</div>
          </div>
        </div>
      </form>
    </div>
    
    <!-- Footer with actions -->
    <modal-footer
      :is-editing="isEditing"
      :active-tab="activeTab"
      :is-valid="isFormValid"
      @save="saveCard"
      @close="$emit('close')"
      @delete="deleteCard"
      @previous-tab="goToPreviousTab"
      @next-tab="goToNextTab"
    />
  </modal-wrapper>
</template>

<script>
import axios from 'axios';
import { useWhiteboardStatuses } from '../composables/useWhiteboardStatuses';
import DetailsTab from './whiteboardcard/DetailsTab.vue';
import LinksTab from './whiteboardcard/LinksTab.vue';
import AssignmentsTab from './whiteboardcard/AssignmentsTab.vue';
import ModalWrapper from './whiteboardcard/ModalWrapper.vue';
import ModalHeader from './whiteboardcard/ModalHeader.vue';
import ModalFooter from './whiteboardcard/ModalFooter.vue';
import TabNavigation from './whiteboardcard/TabNavigation.vue';

export default {
  components: {
    DetailsTab,
    LinksTab,
    AssignmentsTab,
    ModalWrapper,
    ModalHeader,
    ModalFooter,
    TabNavigation
  },
  props: {
    card: {
      type: Object,
      default: null
    },
    isEditing: {
      type: Boolean,
      default: false
    }
  },
  
  // Explicitly define emits to fix the "Cannot read properties of null (reading 'emitsOptions')" error
  emits: ['close', 'save', 'delete'],
  
  data() {
    return {
      activeTab: 'details',
      showShortcutsHelp: false,
      loadingState: {
        projects: false,
        users: false,
        fonts: false,
        logos: false
      },
      formData: {
        headline: '',
        description: '',
        link: '',
        card_date: new Date().toISOString().split('T')[0],
        deadline: '',
        is_active: true,
        status: 'active',
        project_id: '',
        image: 'link.svg',
        user_ids: [],
        font_ids: [],
        additional_links: [],
        link_target: '_blank'
      },
      projects: [],
      users: [],
      fonts: [],
      logos: [],
    }
  },
  
  computed: {
    // Separate logos into categories and logos based on their type
    categoryLogos() {
      return this.logos.filter(logo => logo.type === 'category');
    },
    
    logoIcons() {
      return this.logos.filter(logo => logo.type === 'logo');
    },

    // Form validation
    isFormValid() {
      return this.formData.headline.trim() !== '' && this.formData.card_date !== '';
    }
  },
  
  created() {
    // Load data for the dropdowns
    this.fetchProjects();
    this.fetchUsers();
    this.fetchFonts();
    this.fetchLogos();
    
    if (this.isEditing && this.card) {
      // Initialize form data from the card prop
      this.initializeFormData();
    }
    
    // Add event listener for keyboard shortcuts
    window.addEventListener('keydown', this.handleKeyDown);
  },
  
  beforeDestroy() {
    // Clean up event listener
    window.removeEventListener('keydown', this.handleKeyDown);
  },
  
  methods: {
    handleKeyDown(event) {
      // Alt + 1 = Details tab
      if (event.altKey && event.key === '1') {
        event.preventDefault();
        this.activeTab = 'details';
      }
      // Alt + 2 = Links tab
      else if (event.altKey && event.key === '2') {
        event.preventDefault();
        this.activeTab = 'links';
      }
      // Alt + 3 = Assignments tab
      else if (event.altKey && event.key === '3') {
        event.preventDefault();
        this.activeTab = 'assignments';
      }
      // Ctrl + → (right arrow) = Next tab
      else if (event.ctrlKey && event.key === 'ArrowRight') {
        event.preventDefault();
        this.goToNextTab();
      }
      // Ctrl + ← (left arrow) = Previous tab
      else if (event.ctrlKey && event.key === 'ArrowLeft') {
        event.preventDefault();
        this.goToPreviousTab();
      }
      // Escape to close
      else if (event.key === 'Escape') {
        event.preventDefault();
        this.$emit('close');
      }
      // Ctrl + Enter to save
      else if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        this.saveCard();
      }
      // ? to toggle shortcuts help
      else if (event.key === '?') {
        event.preventDefault();
        this.showShortcutsHelp = !this.showShortcutsHelp;
      }
    },
    
    initializeFormData() {
      // Clone the card data to avoid modifying the prop directly
      this.formData = {
        id: this.card.id,
        headline: this.card.headline,
        description: this.card.description || '',
        link: this.card.link || '',
        card_date: this.card.card_date ? new Date(this.card.card_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        deadline: this.card.deadline ? new Date(this.card.deadline).toISOString().split('T')[0] : '',
        is_active: this.card.is_active !== undefined ? this.card.is_active : true,
        status: this.card.status || this.defaultStatusSlug,
        project_id: this.card.project_id || '',
        image: this.card.image || 'link.svg',
        additional_links: this.card.additional_links ? [...this.card.additional_links] : [],
        link_target: this.card.link_target || '_blank',
        user_ids: this.card.assignedUsers ? this.card.assignedUsers.map(user => Number(user.id)) : [],
        font_ids: this.card.fonts ? this.card.fonts.map(font => Number(font.id)) : []
      };
    },
    
    saveCard() {
      if (!this.isFormValid) return;
      
      // Set is_active property based on the selected status
      // Default status is typically 'active'
      this.formData.is_active = this.formData.status === this.defaultStatusSlug;
      
      // Make a deep clone to avoid reference issues
      const cardData = {
        id: this.formData.id,
        headline: this.formData.headline,
        description: this.formData.description,
        link: this.formData.link,
        link_target: this.formData.link_target,
        card_date: this.formData.card_date,
        deadline: this.formData.deadline,
        is_active: this.formData.is_active,
        status: this.formData.status,
        project_id: this.formData.project_id,
        image: this.formData.image,
        user_ids: this.formData.user_ids,
        font_ids: this.formData.font_ids,
        additional_links: JSON.parse(JSON.stringify(this.formData.additional_links))
      };
      
      this.$emit('save', cardData);
    },
    
    async fetchProjects() {
      this.loadingState.projects = true;
      try {
        const response = await axios.get('/api/projects-for-whiteboard');
        this.projects = response.data;
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        this.loadingState.projects = false;
      }
    },
    
    async fetchUsers() {
      this.loadingState.users = true;
      try {
        const response = await axios.get('/api/users-for-whiteboard');
        this.users = response.data;
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        this.loadingState.users = false;
      }
    },
    
    async fetchFonts() {
      this.loadingState.fonts = true;
      try {
        const response = await axios.get('/api/fonts-for-whiteboard');
        this.fonts = response.data;
      } catch (error) {
        console.error('Error fetching fonts:', error);
      } finally {
        this.loadingState.fonts = false;
      }
    },
    
    async fetchLogos() {
      this.loadingState.logos = true;
      try {
        const response = await axios.get('/api/logos-for-whiteboard');
        this.logos = response.data;
      } catch (error) {
        console.error('Error fetching logos:', error);
      } finally {
        this.loadingState.logos = false;
      }
    },
    
    goToPreviousTab() {
      if (this.activeTab === 'links') {
        this.activeTab = 'details';
      } else if (this.activeTab === 'assignments') {
        this.activeTab = 'links';
      }
    },
    
    goToNextTab() {
      if (this.activeTab === 'details') {
        this.activeTab = 'links';
      } else if (this.activeTab === 'links') {
        this.activeTab = 'assignments';
      }
    },

    deleteCard() {
      if (this.isEditing && this.formData.id) {
        this.$emit('delete', this.formData.id);
      }
    },
  },
  
  setup() {
    const { 
      statusOptions, 
      defaultStatusSlug, 
      getStatusColor,
      getOrderedStatuses,
      fetchStatusOptions
    } = useWhiteboardStatuses();
    
    // Make sure we have the latest statuses from the database
    fetchStatusOptions(true);
    
    return {
      statusOptions: getOrderedStatuses,
      defaultStatusSlug,
      getStatusColor
    };
  },
}
</script> 