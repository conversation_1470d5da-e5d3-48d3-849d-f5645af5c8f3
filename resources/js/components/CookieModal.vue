<template>
  <div>
    <!-- Modal Trigger Button -->
    <slot 
      name="trigger" 
      :open="openModal"
    >
      <button
        @click="openModal"
        class="retro-button"
      >
        Dear Windows user 
      </button>
    </slot>

    <!-- Modal -->
    <Teleport to="body">
      <div v-if="isOpen" class="retro-modal-backdrop" @click="closeModal">
        <div class="retro-modal" @click.stop>
          <!-- Content -->
          <div class="retro-modal-content">
            <div class="retro-headline"></div>
            <p class="retro-text">
              Warning: Prolonged exposure to poorly designed user interfaces has been linked to increased stress levels, eye strain, and sudden urges to throw your device out the window. Our research shows that 9 out of 10 developers experience elevated blood pressure when encountering non-responsive buttons and confusing navigation menus. <a href="#" class="retro-link">Learn about the symptoms</a>.
            </p>

            <p class="retro-text mt-4">
              ¯\_(ツ)_/¯ By continuing to use this interface, you acknowledge the risks of UI-induced frustration and accept that random popup modals may appear at any time. Side effects may include excessive sighing, keyboard mashing, and spontaneous facepalming. For your safety, we recommend taking frequent breaks and keeping stress balls nearby. Visit our <a href="#" class="retro-link">UI Therapy Center</a> or contact our <a href="#" class="retro-link">Emergency Design Support</a>.
            </p>

            <!-- Cookie Preferences -->
            <div class="mt-4">
              <div v-for="(value, key) in preferences" :key="key" class="flex items-center gap-2 mb-2">
                <input
                  type="checkbox"
                  :id="key"
                  v-model="preferences[key]"
                  :disabled="key === 'necessary'"
                  class="retro-checkbox"
                >
                <label :for="key" class="retro-text capitalize">{{ key }}</label>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="retro-modal-footer">
            <button
              @click="acceptAll"
              class="retro-button retro-button--accept"
            >
              <span class="key-letter">A</span>ccept All
            </button>
            <button
              @click="savePreferences"
              class="retro-button retro-button--manage"
            >
              <span class="key-letter">S</span>ave Settings
            </button>
            <button
              @click="closeModal"
              class="retro-button retro-button--close"
            >
              <span class="key-letter">C</span>lose
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface CookiePreferences {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
}

const props = defineProps({
  modelValue: {
    type: Object as () => CookiePreferences,
    default: () => ({
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: true
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const isOpen = ref(false)
const preferences = reactive<CookiePreferences>({ ...props.modelValue })

const openModal = () => {
  isOpen.value = true
}

const closeModal = () => {
  isOpen.value = false
}

const acceptAll = () => {
  Object.keys(preferences).forEach(key => {
    preferences[key as keyof CookiePreferences] = true
  })
  savePreferences()
}

const savePreferences = () => {
  emit('update:modelValue', { ...preferences })
  closeModal()
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');

.retro-modal-backdrop {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.retro-modal {
  @apply w-full max-w-2xl overflow-hidden relative;
  background: #00b3b3;
  border: 0;
  font-family: 'VT323', monospace;
  letter-spacing: 0.5px;
  box-shadow: 12px 12px 0 0 rgba(0, 0, 0, 0.75);
}

/* Remove the border pseudo-element completely */
.retro-modal::before {
  display: none;
}

.retro-headline {
  @apply text-center;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  margin: 0;
  padding: 0;
  z-index: 10;
}

.retro-headline::after {
  content: 'Dear Windows user ¯\\_(ツ)_/¯';
  @apply text-xl font-bold uppercase;
  background: #000;
  color: #fff;
  padding: 2px 12px;
  white-space: nowrap;
}

.retro-modal-content {
  @apply p-4 space-y-4 m-2 mt-8;
  border: 2px solid #000;
  border-style: double;
  line-height: 1.4;
  background: #00b3b3;
}

.retro-text {
  @apply text-base;
  color: #000;
}

.retro-link {
  @apply underline font-bold;
  color: #000;
  text-decoration-style: double;
  text-underline-offset: 2px;
}

.retro-link:hover {
  @apply no-underline;
  background: #000;
  color: #00b3b3;
  padding: 0 2px;
}

.retro-modal-footer {
  @apply p-4 flex justify-center gap-4 m-2;
  border: 2px solid #000;
  border-style: double;
  background: #00b3b3;
}

.retro-button {
  @apply px-4 py-1 font-bold text-base uppercase tracking-wide;
  background: #fff;
  border: none;
  color: #666;
  min-width: 120px;
  text-align: center;
  position: relative;
  box-shadow: 4px 4px 0 0 rgba(0, 0, 0, 0.8);
  transition: all 0.1s ease-in-out;
}

.retro-button:hover {
  background: #f0f0f0;
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.8);
}

.retro-button:active {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.8);
}

.retro-button--accept {
  background: #ffff00;
  box-shadow: 4px 4px 0 0 rgba(0, 0, 0, 0.8);
}

.retro-button--accept:hover {
  background: #ffff99;
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0 0 rgba(0, 0, 0, 0.8);
}

.retro-button--accept:active {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.8);
}

.retro-button--manage {
  background: #fff;
}

.retro-button--close {
  background: #fff;
}

.key-letter {
  color: #000;
  text-decoration: underline;
  text-underline-offset: 2px;
  text-decoration-thickness: 2px;
  font-weight: 900;
}

.mt-4 {
  margin-top: 1rem;
}

/* Remove the old header styles */
.retro-modal-header,
.retro-modal-header::before,
.retro-modal-header::after {
  display: none;
}

/* Remove the old wrapper styles */
.retro-text-wrapper,
.retro-buttons-wrapper {
  display: none;
}

.retro-checkbox {
  @apply w-4 h-4 border-2 border-black;
  appearance: none;
  background: white;
  position: relative;
}

.retro-checkbox:checked {
  background: #ffff00;
}

.retro-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: black;
  font-weight: bold;
}

.retro-checkbox:disabled {
  background: #cccccc;
  cursor: not-allowed;
}
</style> 