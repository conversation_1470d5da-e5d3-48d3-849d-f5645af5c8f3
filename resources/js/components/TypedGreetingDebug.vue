<template>
    <div>
        <div class="debug-info" style="font-size: 14px; padding: 10px; margin-bottom: 10px; background: #f8f9fa; color: #333; border-radius: 4px; text-align: left;">
            <p><strong>Debug info:</strong></p>
            <p>Name: "{{ name }}"</p>
            <p>Custom Greetings: {{ JSON.stringify(customGreetings) }}</p>
            <p>Full Text: "{{ fullText }}"</p>
            <p>Displayed Text: "{{ displayedText }}"</p>
            <p>Is Typing: {{ isTyping }}</p>
        </div>
        <span class="inline-flex items-baseline whitespace-nowrap">
            <span class="greeting-text">{{ displayedText }}</span>
            <img 
                v-if="showWavingHand" 
                src="/img/waving-hand.png" 
                class="ms-3 w-[0.8em] h-[0.8em] object-contain origin-[70%_70%] animate-subtle-wave inline-block align-baseline cursor-pointer hand-icon"
                alt="Waving hand"
                @click="hideGreeting"
                ref="handIcon"
            >
            <span class="typing-cursor" :class="{ 'blink': isBlinking, 'hide': !showCursor }">|</span>
        </span>
    </div>
</template>

<script>
export default {
    props: {
        name: {
            type: String,
            required: true
        },
        customGreetings: {
            type: Array,
            default: () => []
        },
        generalGreetings: {
            type: Array,
            default: () => [
                'Hello',
                'Hi',
                'Welcome',
                'Greetings',
                'Good to see you'
            ]
        }
    },
    data() {
        return {
            displayedText: '',
            isBlinking: false,
            fullText: '',
            isTyping: false,
            isDeleting: false,
            showCursor: true,
            showWavingHand: false,
            isHidden: false,
            elementWidth: 0,
            elementHeight: 0,
            handPosition: { x: 0, y: 0 }
        }
    },
    created() {
        console.log('TypedGreeting created with props:', {
            name: this.name,
            customGreetings: this.customGreetings
        });
    },
    mounted() {
        console.log('TypedGreeting mounted');
        this.setGreeting();
        this.startTyping();
    },
    methods: {
        setGreeting() {
            console.log('Setting greeting with customGreetings:', this.customGreetings);
            const availableGreetings = this.customGreetings && this.customGreetings.length > 0 ? 
                this.customGreetings : 
                this.generalGreetings;
            
            console.log('Available greetings:', availableGreetings);
            
            // Select a random greeting from the available ones
            const randomIndex = Math.floor(Math.random() * availableGreetings.length);
            const selectedGreeting = availableGreetings[randomIndex];
            
            console.log('Selected greeting:', selectedGreeting);
            this.fullText = `${selectedGreeting} ${this.name}!`;
            console.log('Full text set to:', this.fullText);
        },
        async startTyping() {
            console.log('Starting typing animation for:', this.fullText);
            this.isTyping = true;
            this.isBlinking = false;

            for (let i = 0; i < this.fullText.length; i++) {
                const currentChar = this.fullText[i];
                this.displayedText += currentChar;

                let delay;
                if (currentChar === ' ') {
                    delay = 200;
                } else if ('.!?'.includes(currentChar)) {
                    delay = 300;
                } else {
                    delay = 100;
                }

                await this.wait(delay);
            }

            // Add the waving hand as the last "typed" character
            await this.wait(200);
            this.showWavingHand = true;
            
            // Final pause before hiding cursor
            await this.wait(200);
            this.isBlinking = false;
            await this.wait(300);
            this.showCursor = false;
            
            this.isTyping = false;
            console.log('Typing animation complete');
        },
        hideGreeting() {
            if (this.isDeleting) return;
            console.log('Hiding greeting');
            this.isDeleting = true;
            this.showCursor = true;
            this.isBlinking = false;
            this.startDeleting();
        },
        async startDeleting() {
            console.log('Starting delete animation');
            // Use faster constant delays
            const baseDelay = 15; // Much faster base delay
            
            while (this.displayedText.length > 0) {
                this.displayedText = this.displayedText.slice(0, -1);
                await this.wait(baseDelay);
            }
            
            this.showWavingHand = false;
            this.showCursor = false;
            this.isHidden = true;
            this.isDeleting = false;
            console.log('Delete animation complete');
        },
        wait(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }
}
</script>

<style scoped>
.typing-cursor {
    display: inline-block;
    margin-left: 2px;
    opacity: 1;
    font-weight: 300;
    transition: opacity 0.3s ease-out;
}

.typing-cursor.hide {
    opacity: 0;
}

.typing-cursor.blink {
    animation: blink 0.8s infinite;
}

.hand-icon {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 0 transparent);
    will-change: transform, filter;
}

.hand-icon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.6));
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1.2); }
    50% { transform: scale(1.3); }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    3% { transform: rotate(-6deg); }
    6% { transform: rotate(6deg); }
    9% { transform: rotate(-4deg); }
    12% { transform: rotate(2deg); }
    15%, 100% { transform: rotate(0deg); }
}

.animate-subtle-wave {
    animation: wave 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    animation-delay: 1.5s;
}
</style> 