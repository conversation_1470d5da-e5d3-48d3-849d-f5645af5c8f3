<template>
  <div 
    @click="navigateToLink" 
    :class="[
      'bg-white dark:bg-slate-950 rounded-lg overflow-hidden border border-solid border-gray-200 dark:border-slate-800 h-full flex flex-col relative z-10',
      card.link ? 'cursor-pointer hover:' : ''
    ]"
  >
    <!-- Card header with headline and actions -->
    <div class="px-6 py-4 bg-white dark:bg-slate-950 flex justify-between items-center border-b border-solid border-gray-200 dark:border-slate-700/50">
      <div class="flex items-center">
        <span 
          class="w-2.5 h-2.5 rounded-full mr-2 flex-shrink-0" 
          :style="{ backgroundColor: statusColor }"
          :title="statusName"
        ></span>
        <div class="flex items-center">
          <!-- Show image if it exists -->
          <img 
            v-if="card.image" 
            :src="getMainImagePath()"
            :alt="card.headline"
            class="h-5 w-5 object-contain mr-2 flex-shrink-0"
          />
          <div class="text-xl text-gray-900 dark:text-white gant-modern-bold">{{ card.headline }}</div>
          <!-- Project badge inline with headline -->
          <span 
            v-if="showTags && card.project" 
            class="inline-flex items-center px-2 py-1 rounded-md text-xs gant-modern-medium bg-gray-900 text-gray-100 dark:bg-gray-100 dark:text-gray-900 ml-2"
          >
            <i class="design design-playground-folder-oped opacity-50 me-1 "></i>
            {{ card.project.name }}
          </span>
        </div>
      </div>
      <div v-if="isAdmin" class="flex items-center space-x-1.5">
        <button
          @click.stop="$emit('edit', card)"
          class="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
          aria-label="Edit card"
        >
          <i class="design design-edit text-gray-500 dark:text-gray-400 "></i>
        </button>
        
        <!-- Status dropdown menu -->
        <div class="relative">
          <button
            @click.stop="toggleStatusDropdown"
            :class="[
              'p-1 rounded-full',
              'hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors'
            ]"
            :style="{ color: statusColor }"
            aria-label="Change status"
            :aria-expanded="showStatusDropdown ? 'true' : 'false'"
          >
            <i class="design design-col-visible"></i>
          </button>
          
          <!-- Dropdown menu -->
          <div 
            v-if="showStatusDropdown" 
            class="absolute right-0 mt-1 bg-white dark:bg-slate-800 rounded-md shadow-lg z-20 py-1 min-w-32 border border-gray-200 dark:border-slate-700"
            role="menu"
          >
            <button 
              v-for="option in statusOptions"
              :key="option.slug"
              @click.stop="changeStatus(option.slug)" 
              class="w-full px-4 py-2 text-sm text-left flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-slate-700"
              :class="{'bg-gray-100 dark:bg-slate-700': card.status === option.slug}"
              role="menuitem"
            >
              <span class="w-2 h-2 rounded-full" :style="{ backgroundColor: option.color }"></span>
              <span>{{ option.name }}</span>
            </button>
          </div>
        </div>
        
        <button
          @click.stop="$emit('delete', card)"
          class="p-1 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-full"
          aria-label="Delete card"
        >
          <i class="design design-trash text-gray-500 dark:text-gray-400 "></i>
        </button>
      </div>
    </div>
    
    <!-- Card content -->
    <div class="p-6 flex-1 flex flex-col">
      <div class="text-gray-700 dark:text-gray-300 mb-5 prose dark:prose-invert prose-sm max-w-none flex-1 gant-modern-regular" v-html="formattedDescription"></div>
      
      <div class="space-y-2 mt-auto">
        <!-- Users assigned to the card -->
        <div v-if="showTags && hasAssignedUsers" class="pt-3 mt-3 border-t dark:border-slate-700/50">
          <div class="flex items-center gap-1 mb-2 overflow-x-auto whitespace-nowrap">
            <span class="text-xs text-gray-500 dark:text-gray-400 flex items-center flex-shrink-0">
              <i class="design design-search me-1"></i>Assigned to:
            </span>
            <span 
              v-for="user in card.assignedUsers" 
              :key="user.id"
              class="inline-flex items-center px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 rounded text-xs flex-shrink-0"
            >
              <i class="design design-user me-1"></i>
              {{ user.name }}
            </span>
          </div>
        </div>
        
        <!-- Related Resources Section (Fonts, Links, Dates) -->
        <div class="pt-3 mt-3 border-t dark:border-slate-700/50">
          <div class="text-xs text-gray-500 dark:text-gray-400 mb-2 gant-modern-medium">Related:</div>
          
          <div class="flex flex-wrap gap-2">
            <!-- Date badge -->
            <div v-if="showDates" class="mb-1">
              <div class="inline-flex items-center px-3 py-1.5 rounded-md bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 text-gray-700 dark:text-gray-300 text-sm gant-modern-regular">
                <i class="design design-code h-5 w-5 flex items-center justify-center mr-2 flex-shrink-0"></i>
                <span>{{ formattedDate }}</span>
              </div>
            </div>
            
            <!-- Deadline badge if available -->
            <div v-if="showDeadlines && card.deadline" class="mb-1">
              <div 
                class="inline-flex items-center px-3 py-1.5 rounded-md bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 text-gray-700 dark:text-gray-300 text-sm gant-modern-regular"
                :class="{'bg-red-50 border-red-100 dark:bg-red-900/10 dark:border-red-900/20': isDeadlineNear}"
              >
                <i class="design design-monitor-check h-5 w-5 flex items-center justify-center mr-2 flex-shrink-0" :class="{'text-red-500': isDeadlineNear}"></i>
                <span :class="{'text-red-600 dark:text-red-400': isDeadlineNear}">Deadline: {{ formattedDeadline }}</span>
                <span v-if="isDeadlineNear" class="ml-1.5 animate-pulse">⚠️</span>
              </div>
            </div>
            
            <!-- Additional links -->
            <div 
              v-if="showTags"
              v-for="(link, index) in card.additional_links || []" 
              :key="'link-'+index"
              class="mb-1"
            >
              <a 
                :href="link.url" 
                @click.prevent="openAdditionalLink(link.url)"
                target="_blank" 
                rel="noopener" 
                class="inline-flex items-center px-3 py-1.5 rounded-md bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 hover:bg-gray-100 dark:hover:bg-slate-800 text-gray-700 dark:text-gray-300 transition-colors text-sm gant-modern-regular"
              >
                <img 
                  :src="getLogoImagePath(link.image || 'link.svg')"
                  :alt="link.title"
                  class="h-5 w-5 object-contain mr-2 flex-shrink-0"
                >
                <span class="truncate max-w-[100px]">{{ link.title }}</span>
                <i class="design design-external-link ml-1.5 text-gray-500 dark:text-gray-400 flex-shrink-0"></i>
              </a>
            </div>
            
            <!-- Fonts -->
            <div 
              v-if="showTags"
              v-for="font in card.fonts || []" 
              :key="'font-'+font.id"
              class="mb-1"
            >
              <div 
                class="inline-flex items-center px-3 py-1.5 rounded-md bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-800 text-gray-700 dark:text-gray-300 text-sm gant-modern-regular"
              >
                <i :class="[getFontIconClass(font.name), 'h-5 w-5 flex items-center justify-center mr-2 flex-shrink-0']"></i>
                <span class="truncate max-w-[100px]">{{ font.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- External Link Confirmation Modal -->
  <external-link-modal
    :show="showExternalLinkModal"
    :url="externalLinkUrl" 
    :target="externalLinkTarget"
    @close="showExternalLinkModal = false"
  />
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import ExternalLinkModal from './ExternalLinkModal.vue';
import { useWhiteboardStatuses } from '../composables/useWhiteboardStatuses';

const props = defineProps({
  card: {
    type: Object,
    required: true
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  showTags: {
    type: Boolean,
    default: true
  },
  showDates: {
    type: Boolean,
    default: true
  },
  showDeadlines: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['edit', 'delete', 'status-change']);

// External link handling
const showExternalLinkModal = ref(false);
const externalLinkUrl = ref('');
const externalLinkTarget = ref('_blank');

// Status dropdown state
const showStatusDropdown = ref(false);

// Use our statuses composable
const { statusOptions, getStatusColor, getStatusName } = useWhiteboardStatuses();

// Computed properties
const formattedDescription = computed(() => {
  if (!props.card.description) return '';
  return props.card.description.replace(/\n/g, '<br>');
});

const hasAssignedUsers = computed(() => 
  props.card.assignedUsers && props.card.assignedUsers.length > 0
);

const formattedDate = computed(() => formatDate(props.card.card_date));
const formattedDeadline = computed(() => formatDate(props.card.deadline));

const statusColor = computed(() => getStatusColor(props.card.status));
const statusName = computed(() => getStatusName(props.card.status));

const isDeadlineNear = computed(() => {
  if (!props.card.deadline) return false;
  
  const deadline = new Date(props.card.deadline);
  const today = new Date();
  
  // Calculate the difference in days
  const timeDiff = deadline.getTime() - today.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  
  // Return true if deadline is within 7 days or past due
  return daysDiff <= 7;
});

// Event handlers
const navigateToLink = () => {
  // If admin, let them edit the card (handled by parent component)
  if (props.isAdmin) {
    emit('edit', props.card);
    return;
  }
  
  // For non-admin users, open the card link if available
  if (props.card.link) {
    openExternalLink(props.card.link);
  }
};

const openExternalLink = (url) => {
  externalLinkUrl.value = url;
  externalLinkTarget.value = props.card.link_target || '_blank';
  showExternalLinkModal.value = true;
};

const openAdditionalLink = (url) => {
  if (url) {
    externalLinkUrl.value = url;
    externalLinkTarget.value = '_blank';
    showExternalLinkModal.value = true;
  }
};

const toggleStatusDropdown = (e) => {
  e.stopPropagation();
  showStatusDropdown.value = !showStatusDropdown.value;
};

const hideDropdown = (e) => {
  // Only hide if clicking outside the dropdown
  if (showStatusDropdown.value) {
    showStatusDropdown.value = false;
  }
};

const changeStatus = (status) => {
  emit('status-change', props.card, status);
  showStatusDropdown.value = false;
};

// Helper functions
const getLogoImagePath = (path) => {
  if (!path) return '/img/logos/link.svg';
  
  // For additional link images
  if (path.includes('/')) {
    // This is a path with a directory, keep it as is
    return `/img/${path}`;
  } else {
    // For additional link images (simple filenames), use logos directory
    return `/img/logos/${path}`;
  }
};

// Separate function for main image path that handles the image field correctly
const getMainImagePath = () => {
  if (!props.card.image) return '';
  
  // If the image already contains a path, use it directly
  if (props.card.image.includes('/')) {
    return `/img/${props.card.image}`;
  }
  
  // If it's just a filename, assume it's in the category directory
  return `/img/category/${props.card.image}`;
};

const getFontIconClass = (fontName) => {
  // Convert the font name to lowercase for case-insensitive matching
  const name = fontName.toLowerCase();
  
  // Handle specific exceptions
  if (name.includes('central database') || name.includes('cdb')) return 'cdb cdb-thumbnail';
  if (name.includes('eshop')) return 'vermont-icon vermont-icon-thumbnail';
  if (name.includes('category')) return 'vermont-category vermont-category-thumbnail';
  if (name.includes('order') || name.includes('og')) return 'ordergroup ordergroup-thumbnail';
  
  // For all other fonts, extract base name and use standard pattern
  let baseName = name;
  
  // Extract the base font name before any spaces or special characters
  if (name.includes(' ')) {
    baseName = name.split(' ')[0];
  }
  
  // Clean up any remaining special characters
  baseName = baseName.replace(/[^a-z0-9]/g, '');
  
  // Return the appropriate class with the thumbnail suffix
  return `${baseName} ${baseName}-thumbnail`;
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  return date.toLocaleDateString(undefined, options);
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', hideDropdown);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', hideDropdown);
});
</script> 