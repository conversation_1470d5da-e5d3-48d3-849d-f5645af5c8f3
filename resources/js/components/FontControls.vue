<template>
  <div class="font-controls space-y-6 max-w-xl bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
    <!-- Font Information -->
    <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ fontFamily }}</h3>
      <p class="text-sm text-gray-500 dark:text-gray-400">
        {{ fontDefinitions[fontFamily]?.isVariable ? 'Variable Font' : 'Static Font' }}
      </p>
    </div>

    <!-- Controls -->
    <div class="space-y-6">
      <template v-for="(control, index) in availableControls" :key="index">
        <div v-if="fontSupportsAxis(control.axis)" class="space-y-2">
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ control.label }}
              <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">
                {{ getAxisTag(control.axis) }}
              </span>
            </label>
            <div class="flex items-center space-x-2">
              <input 
                type="number"
                :min="control.min"
                :max="control.max"
                :value="currentValues[control.axis]"
                class="w-20 px-2 py-1 text-sm text-right border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                @input="updateFont($event, control.axis)"
              >
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ control.unit || '' }}
              </span>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <span class="text-xs text-gray-500 dark:text-gray-400">{{ control.min }}</span>
            <input 
              type="range" 
              :min="control.min" 
              :max="control.max" 
              :step="control.step || 1"
              :value="currentValues[control.axis]"
              class="flex-grow h-2 rounded-lg appearance-none cursor-pointer bg-gray-200 dark:bg-gray-700"
              @input="updateFont($event, control.axis)"
            >
            <span class="text-xs text-gray-500 dark:text-gray-400">{{ control.max }}</span>
          </div>

          <!-- Axis Info -->
          <div class="text-xs text-gray-500 dark:text-gray-400">
            <span v-if="control.default" class="mr-2">Default: {{ control.default }}</span>
            <span v-if="control.recommended" class="mr-2">Recommended: {{ control.recommended }}</span>
          </div>
        </div>
      </template>
    </div>

    <!-- Font Features Panel -->
    <div v-if="hasOpenTypeFeatures" class="border-t border-gray-200 dark:border-gray-700 pt-4">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">OpenType Features</h4>
      <div class="grid grid-cols-2 gap-4">
        <label v-for="feature in openTypeFeatures" :key="feature.tag" class="flex items-center">
          <div class="relative inline-flex items-center mr-2">
            <input
              type="checkbox"
              :checked="feature.enabled"
              @change="toggleFeature(feature.tag)"
              class="sr-only peer"
            >
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600 dark:peer-checked:bg-indigo-500"></div>
          </div>
          <span class="text-sm text-gray-700 dark:text-gray-300">
            {{ feature.name }}
            <span class="text-xs text-gray-500 dark:text-gray-400">({{ feature.tag }})</span>
          </span>
        </label>
      </div>
    </div>

    <!-- Reset Button -->
    <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
      <button
        @click="resetToDefaults"
        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
      >
        Reset to Defaults
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FontControls',
  
  props: {
    fontFamily: {
      type: String,
      required: true
    },
    targetId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      currentValues: {},
      hasOpenTypeFeatures: false,
      openTypeFeatures: [
        { tag: 'liga', name: 'Standard Ligatures', enabled: true },
        { tag: 'dlig', name: 'Discretionary Ligatures', enabled: false },
        { tag: 'ss01', name: 'Stylistic Set 1', enabled: false },
        { tag: 'ss02', name: 'Stylistic Set 2', enabled: false },
        { tag: 'tnum', name: 'Tabular Numbers', enabled: false },
        { tag: 'onum', name: 'Oldstyle Numbers', enabled: false }
      ],
      fontDefinitions: {
        'nunito-sans': {
          isVariable: true,
          axes: ['weight', 'width', 'optical-size', 'slnt'],
          weightRange: [200, 1000],
          widthRange: [75, 125],
          opticalSizeRange: [6, 72],
          slantRange: [-10, 0],
          features: ['liga', 'dlig', 'ss01']
        },
        'Gant-Modern': {
          isVariable: false,
          axes: [],
          weights: [300, 500, 700, 900],
          features: ['liga']
        },
        'Gant-Serif': {
          isVariable: false,
          axes: [],
          weights: [900],
          features: ['liga']
        }
      },
      availableControls: [
        {
          axis: 'weight',
          label: 'Weight',
          tag: 'wght',
          min: 200,
          max: 1000,
          default: 400,
          recommended: '400, 700',
          step: 1
        },
        {
          axis: 'width',
          label: 'Width',
          tag: 'wdth',
          min: 75,
          max: 125,
          default: 100,
          recommended: '100',
          step: 0.1,
          unit: '%'
        },
        {
          axis: 'optical-size',
          label: 'Optical Size',
          tag: 'opsz',
          min: 6,
          max: 72,
          default: 14,
          step: 0.1,
          unit: 'pt'
        },
        {
          axis: 'slant',
          label: 'Slant',
          tag: 'slnt',
          min: -10,
          max: 0,
          default: 0,
          step: 0.1,
          unit: '°'
        }
      ]
    }
  },

  methods: {
    getAxisTag(axis) {
      const control = this.availableControls.find(c => c.axis === axis);
      return control ? control.tag : '';
    },

    fontSupportsAxis(axis) {
      const fontDef = this.fontDefinitions[this.fontFamily];
      if (!fontDef) return false;
      
      // For static fonts, only show weight if it has multiple weights
      if (!fontDef.isVariable) {
        return axis === 'weight' && fontDef.weights && fontDef.weights.length > 1;
      }
      
      // For variable fonts, check if the axis is supported
      return fontDef.axes.includes(axis);
    },

    updateFont(event, axis) {
      const value = parseFloat(event.target.value);
      this.currentValues[axis] = value;
      
      const target = document.getElementById(this.targetId);
      if (!target) return;

      const fontDef = this.fontDefinitions[this.fontFamily];
      if (!fontDef) return;

      // For static fonts, only update weight
      if (!fontDef.isVariable) {
        if (axis === 'weight') {
          // Find the closest available weight
          const closestWeight = fontDef.weights.reduce((prev, curr) => {
            return Math.abs(curr - value) < Math.abs(prev - value) ? curr : prev;
          });
          target.style.fontWeight = closestWeight;
        }
        return;
      }

      // For variable fonts, update all properties
      if (axis === 'weight') {
        target.style.fontWeight = value;
      } else if (axis === 'width') {
        target.style.fontStretch = `${value}%`;
      } else if (axis === 'slant') {
        target.style.fontStyle = `oblique ${value}deg`;
      } else if (axis === 'optical-size') {
        target.style.fontOpticalSizing = 'auto';
        target.style.fontSize = `${value}px`;
      }

      this.updateVariationSettings(target);
    },

    updateVariationSettings(target) {
      const settings = Object.entries(this.currentValues)
        .map(([axis, value]) => {
          const control = this.availableControls.find(c => c.axis === axis);
          return `'${control.tag}' ${value}`;
        })
        .join(', ');

      if (settings) {
        target.style.fontVariationSettings = settings;
      }
    },

    toggleFeature(tag) {
      const feature = this.openTypeFeatures.find(f => f.tag === tag);
      if (feature) {
        feature.enabled = !feature.enabled;
        this.updateFeatures();
      }
    },

    updateFeatures() {
      const target = document.getElementById(this.targetId);
      if (!target) return;

      const features = this.openTypeFeatures
        .map(f => `"${f.tag}" ${f.enabled ? '1' : '0'}`)
        .join(', ');

      target.style.fontFeatureSettings = features;
    },

    resetToDefaults() {
      this.availableControls.forEach(control => {
        if (this.fontSupportsAxis(control.axis)) {
          this.currentValues[control.axis] = control.default;
        }
      });

      const target = document.getElementById(this.targetId);
      if (target) {
        this.updateVariationSettings(target);
        this.openTypeFeatures.forEach(feature => {
          feature.enabled = feature.tag === 'liga';
        });
        this.updateFeatures();
      }
    }
  },

  mounted() {
    // Initialize current values with defaults
    this.availableControls.forEach(control => {
      if (this.fontSupportsAxis(control.axis)) {
        this.currentValues[control.axis] = control.default;
      }
    });

    // Check if font has OpenType features
    const fontDef = this.fontDefinitions[this.fontFamily];
    this.hasOpenTypeFeatures = fontDef && fontDef.features && fontDef.features.length > 0;
  }
}
</script>
