<template>
  <div class="font-gant-modern relative">
    <!-- Title Section (Static) -->
    <div class="mb-12">
      <p class="text-normal dark:text-gray-200 opacity-50 mb-2">
        –––––– Vermont webfonts
      </p>
      <h1 :class="['leading-none tracking-tighter text-black dark:text-gray-200 text-5xl lg:text-6xl xl:text-8xl gant-modern-bold']">
        Find My Icon
      </h1>
    </div>

    <!-- Main Content Area -->
    <div class="relative">
      <!-- Search Options Panel -->
      <div class="sticky top-0 z-20 bg-white/95 dark:bg-slate-950/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800">
        <div class="mx-auto px-4 sm:px-6 py-4 sm:py-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-4 sm:gap-6 lg:gap-8">
            <!-- Search Input -->
            <div class="sm:col-span-2 lg:col-span-5">
              <label for="icon-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Search</label>
              <div class="relative">
                <input 
                  id="icon-search"
                  v-model="search" 
                  type="search"
                  placeholder="Search by name, content or tags..." 
                  :disabled="loading"
                  @focus="isProjectDropdownOpen = false"
                  class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
                />
                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <i class="design design-search-fill text-gray-400"></i>
                </div>
              </div>
            </div>

            <!-- Project Select -->
            <div class="sm:col-span-2 lg:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Filter</label>
              <div class="relative">
                <!-- Dropdown Trigger Button -->
                <button 
                  type="button"
                  @click.stop="isProjectDropdownOpen = !isProjectDropdownOpen"
                  :disabled="loading"
                  class="flex h-12 w-full items-center justify-between rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 gant-modern-regular"
                >
                  <div class="flex items-center gap-2">
                    <img v-if="selectedProject" 
                         :src="getProjectImagePath(selectedProject)"
                         :alt="`${selectedProject} icon`"
                         class="w-5 h-5 object-contain"
                         @error="handleImageError"
                    />
                    <span>{{ selectedProject || 'All Icons' }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <button 
                      v-if="selectedProject"
                      type="button"
                      @click.stop="selectProject('')"
                      class="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors duration-200"
                    >
                      <i class="design design-close text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"></i>
                    </button>
                    <i class="design design-chevron-down text-gray-400" :class="{ 'rotate-180': isProjectDropdownOpen }"></i>
                  </div>
                </button>

                <!-- Dropdown Panel -->
                <div 
                  v-if="isProjectDropdownOpen" 
                  v-click-outside="() => isProjectDropdownOpen = false"
                  class="absolute left-0 right-0 z-30 mt-1 p-2 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-slate-900"
                  @click.stop
                >
                  <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2">
                    <!-- All Icons Option -->
                    <button
                      @click.stop="selectProject('')"
                      class="group flex flex-col items-center rounded-lg p-3 transition-colors duration-200"
                      :class="!selectedProject ? 'bg-gray-50 dark:bg-gray-800' : 'hover:bg-gray-50 dark:hover:bg-gray-800'"
                    >
                      <div class="mb-2 flex h-14 w-14 items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <img 
                          src="/img/projects/all_icons_small.png"
                          alt="All Icons"
                          class="w-12 h-12 object-contain"
                          @error="handleImageError"
                        />
                      </div>
                      <span class="text-sm gant-modern-bold text-gray-700 dark:text-gray-300">All Icons</span>
                    </button>

                    <!-- Project Options -->
                    <button
                      v-for="font in uniqueFonts"
                      :key="font"
                      @click.stop="selectProject(font)"
                      class="group flex flex-col items-center rounded-lg p-3 transition-colors duration-200"
                      :class="selectedProject === font ? 'bg-gray-50 dark:bg-gray-800' : 'hover:bg-gray-50 dark:hover:bg-gray-800'"
                    >
                      <div class="mb-2 flex h-14 w-14 items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <img 
                          :src="getProjectImagePath(font)"
                          :alt="`${font} icon`"
                          class="w-12 h-12 object-contain"
                          @error="handleImageError"
                        />
                      </div>
                      <span class="text-sm gant-modern-regular text-gray-700 dark:text-gray-300">{{ font }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Match All Toggle -->
            <div class="sm:col-span-1 lg:col-span-1">
              <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Show all</span>
              <div class="flex items-center h-12">
                <label class="relative inline-block w-[60px] h-[34px]">
                  <input 
                    type="checkbox" 
                    v-model="matchAllWhenSearchEmpty" 
                    class="opacity-0 w-0 h-0 peer"
                  >
                  <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                    <i v-if="!matchAllWhenSearchEmpty" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                    <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                  </span>
                </label>
              </div>
            </div>

            <!-- Exact Match Toggle -->
            <div class="sm:col-span-1 lg:col-span-1">
              <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Exact Match</span>
              <div class="flex items-center h-12">
                <label class="relative inline-block w-[60px] h-[34px]">
                  <input 
                    type="checkbox" 
                    v-model="exactMatch" 
                    class="opacity-0 w-0 h-0 peer"
                  >
                  <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                    <i v-if="!exactMatch" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                    <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                  </span>
                </label>
              </div>
            </div>

            <!-- Icon Size Slider -->
            <div class="sm:col-span-2 lg:col-span-2">
              <label for="icon-size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-1 gant-modern-medium">Size</label>
              <div class="space-y-1.5">
                <input
                  id="icon-size"
                  v-model="iconSize"
                  type="range"
                  min="3"
                  max="9"
                  step="0.25"
                  class="w-full range-slider"
                >
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                  <div class="flex items-center gap-1">
                    <i class="design design-measure2 text-gray-400 text-[20px]"></i>            
                  </div>
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ iconSize }}rem</span>
                  <div class="flex items-center gap-1">
                    <i class="design design-measure3 text-gray-400 text-[20px]"></i>                    
                  </div>
                </div>
              </div>
            </div>

            <!-- Results Counter -->
            <div v-if="filteredIcons.length > 0" class="sm:col-span-2 lg:col-span-1">
              <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Matching</span>
              <div class="flex items-center justify-between h-12 bg-white dark:bg-slate-950 px-3 py-2 text-sm rounded-xl border-solid border border-gray-300 dark:border-gray-700">
                <div class="flex items-center gap-1">
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100">{{ filteredIcons.slice(0, currentPage * pageSize).length }}</span>
                  <span class="text-gray-500 dark:text-gray-400 mx-1">of</span>
                  <span class="gant-modern-regular text-gray-900 dark:text-gray-100">{{ filteredIcons.length }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Icons Grid Content -->
      <div :class="{'opacity-20 pointer-events-none transition-all duration-300': isProjectDropdownOpen}">
        <div v-if="loading || isSearching" class="grid gap-3 grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 px-2 sm:px-4">
          <div v-for="n in pageSize" 
               :key="n" 
               class="text-center mb-5 relative opacity-100 skeleton-wave"
               :style="{ '--item-index': n - 1 }">
            <div class="flex-1 flex items-center justify-center min-h-[var(--icon-base-size)] mx-auto">
              <div class="w-24 h-24 mx-auto mb-1 bg-gray-200/80 dark:bg-gray-700/80 rounded-lg flex items-center justify-center">
                <div class="w-16 h-16 bg-gray-300/80 dark:bg-gray-800/80 rounded"></div>
              </div>
            </div>
            <div class="flex flex-col gap-2 mt-2">
              <div class="h-4 w-24 mx-auto bg-gray-100/80 dark:bg-gray-700/80 rounded"></div>
              <div class="h-6 w-full bg-gray-100/80 dark:bg-gray-800/80 rounded"></div>
            </div>
          </div>
        </div>

        <div v-else 
          class="flex flex-wrap gap-[var(--grid-gap)] w-full justify-start transition-all duration-300 ease-in-out"
          :style="{
            '--icon-base-size': `${iconSize}rem`,
            '--grid-gap': `${Math.max(0.5, iconSize * 0.2)}rem`
          }"
        >
          <div 
            v-for="icon in filteredIcons.slice(0, currentPage * pageSize)" 
            :key="icon.id" 
            class="flex flex-col flex-grow-0 flex-shrink-0 text-center rounded-lg cursor-pointer group hover:bg-gray-100 dark:hover:bg-gray-900 transition-all duration-200 relative"
            :style="{ 
              width: `${iconSize * 2}rem`,
              padding: `${Math.max(0.5, iconSize * 0.1)}rem`
            }"
            @click="openIconModal(icon)"
          >
            <!-- Tag indicator - visible only when tags exist -->
            <template v-if="icon.tags && icon.tags.length > 0">
              <!-- Normal state: just the tag icon -->
              <div class="absolute top-1 right-1 text-gray-400 dark:text-gray-500 group-hover:opacity-0 transition-opacity duration-200 z-10">
                <i class="design design-tag-outline text-xs"></i>
              </div>
              
              <!-- Hover state: circle with count -->
              <div 
                class="absolute top-1 right-1 bg-gray-700 dark:bg-gray-600 text-white text-[10px] w-5 h-5 rounded-full flex items-center justify-center font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
                :title="`${icon.tags.length} tags`"
              >
                {{ icon.tags.length }}
              </div>
            </template>
            
            <div class="flex-1 flex flex-col">
              <div class="flex-1 flex items-center justify-center my-4" 
                   :style="{ height: `${iconSize}rem` }">
                <i :class="[
                  icon.font?.css_class,
                  icon.css_class,
                  'text-gray-900 dark:text-gray-100',
                  'transition-transform duration-200 group-hover:scale-110'
                ]" 
                :style="{ fontSize: `${iconSize}rem` }"
                aria-hidden="true"
                @click="openIconModal(icon)"
                class="cursor-pointer"></i>
              </div>
              <small :class="[
                'text-gray-400 dark:text-gray-500', 
                'text-[10px] sm:text-xs leading-none mb-1 gant-modern-regular',
                'block overflow-hidden text-ellipsis'
              ]">
                <span class="gant-modern-medium text-gray-500 dark:text-gray-400">{{ icon.font?.name || 'Default' }}</span>: {{ icon.css_content }}
              </small>
              <div class="relative">
                <!-- Normal class name display -->
                <p 
                  :class="[
                    'w-full text-[10px] sm:text-xs px-2 sm:px-3 py-1.5 sm:py-2 text-gray-600 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-700 rounded-md transition-all duration-200 gant-modern-medium truncate whitespace-nowrap',
                    copied === icon.css_class ? 'border-green-500 dark:border-green-500 text-green-600 dark:text-green-400' : 'group-hover:border-gray-300 dark:group-hover:border-gray-600',
                    'group-hover:opacity-0'
                  ]"
                  :title="icon.css_class"
                >
                  {{ formatClassName(icon.css_class) }}
                </p>
                
                <!-- Copy button overlay -->
                <button 
                  @click.stop="copyToClipboard(icon.css_class)"
                  :class="[
                    'absolute inset-0 w-full text-[10px] sm:text-xs px-2 sm:px-3 py-1.5 sm:py-2 rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100 flex items-center justify-center gap-1 sm:gap-2',
                    copied === icon.css_class 
                      ? 'bg-green-900 text-white dark:bg-green-900/90 dark:text-green-100 border border-green-500'
                      : 'bg-gray-900 text-gray-100 dark:bg-gray-800 dark:text-gray-100 border border-gray-600 dark:border-gray-700 hover:bg-gray-800 dark:hover:bg-gray-700'
                  ]"
                >
                  <i :class="[
                    copied === icon.css_class ? 'design design-circle-checked' : 'design design-copyclipboard',
                    'text-xs sm:text-sm'
                  ]"></i>
                  {{ copied === icon.css_class ? 'Copied!' : 'Copy class' }}
                </button>
              </div>
            </div>
          </div>

          <div v-if="hasMoreIcons" id="load-more-button" class="w-full col-span-full py-6 sm:py-8 text-center">
            <button 
              @click="loadMore"
              :disabled="isLoadingMore"
              class="inline-flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-medium text-white bg-gray-950 rounded-md hover:bg-gray-700 dark:bg-gray-900 dark:text-gray-200 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <template v-if="isLoadingMore">
                <svg class="animate-spin h-5 w-5 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading more icons...</span>
              </template>
              <template v-else>
                <i class="claims claims-rotate text-lg sm:text-xl"></i>
                <span>Load More Icons</span>
              </template>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Icon Detail Modal -->
    <IconDetailModal
      :show="showIconModal"
      :icon="selectedIcon"
      :font-class="selectedIcon?.font?.css_class || ''"
      :font-id="selectedIcon?.font?.id"
      :initial-tags="selectedIcon?.tags || []"
      @close="closeIconModal"
      @tags-updated="handleTagsUpdated"
      @request-reload="fetchFontData"
    />
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import Fuse from 'fuse.js';
import IconDetailModal from './IconDetailModal.vue';

// Click Outside Directive
const clickOutside = {
  mounted(el, binding) {
    el._clickOutside = (event) => {
      // Check if the click was outside the element and its children
      if (!(el === event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    document.body.addEventListener('click', el._clickOutside);
  },
  unmounted(el) {
    document.body.removeEventListener('click', el._clickOutside);
  }
};

// Custom debounce function
function debounce(fn, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
}

export default {
  name: 'IconGrid',
  components: {
    IconDetailModal
  },
  directives: {
    clickOutside
  },
  data() {
    return {
      fontIcons: [],
      selectedProject: '',
      search: '',
      exactMatch: false,
      matchAllWhenSearchEmpty: true,
      resultLimit: undefined,
      fuse: null,
      debouncedSearch: '',
      loading: true,
      loadingProgress: 0,
      pageSize: 60,
      currentPage: 1,
      copied: null,
      isLoadingMore: false,
      observer: null,
      isSearching: false,
      iconSize: 4.5,
      isProjectDropdownOpen: false,
      showIconModal: false,
      selectedIcon: null,
    };
  },
  computed: {
    uniqueFonts() {
      return [...new Set(this.fontIcons.map(icon => icon.font?.name).filter(Boolean))].sort();
    },
    filteredIcons() {
      if (!this.debouncedSearch && this.matchAllWhenSearchEmpty) {
        return this.fontIcons;
      }

      if (!this.debouncedSearch) {
        return [];
      }

      let results = [];
      if (this.exactMatch) {
        results = this.fontIcons.filter(icon => 
          icon.css_class.toLowerCase().includes(this.debouncedSearch.toLowerCase()) ||
          icon.tags?.some(tag => tag.toLowerCase() === this.debouncedSearch.toLowerCase())
        );
      } else {
        const fuse = this.fuse || this.initializeFuse();
        results = fuse.search(this.debouncedSearch).map(result => result.item);
      }

      if (this.selectedProject) {
        results = results.filter(icon => icon.font?.name === this.selectedProject);
      }

      return results;
    },
    hasMoreIcons() {
      return this.filteredIcons.length > this.currentPage * this.pageSize;
    },
  },
  watch: {
    selectedProject() {
      this.fetchFontData();
    },
    search(value) {
      this.isSearching = true;
      this.handleSearchDebounced(value);
    },
    debouncedSearch() {
      setTimeout(() => {
        this.currentPage = 1;
        this.isSearching = false;
      }, 300);
    },
    fontIcons() {
      this.initializeFuse();
    }
  },
  created() {
    this.handleSearchDebounced = debounce((value) => {
      this.debouncedSearch = value;
    }, 300);
  },
  mounted() {
    this.fetchFontData();
    this.initInfiniteScroll();
  },
  unmounted() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    formatClassName(className) {
      const hyphenIndex = className.indexOf('-');
      return hyphenIndex !== -1 ? className.substring(hyphenIndex + 1) : className;
    },
    initInfiniteScroll() {
      if (this.observer) {
        this.observer.disconnect();
      }

      this.observer = new IntersectionObserver(
        (entries) => {
          const target = entries[0];
          if (target.isIntersecting && this.hasMoreIcons && !this.isLoadingMore) {
            this.loadMore();
          }
        },
        {
          rootMargin: '200px',
          threshold: 0.1
        }
      );

      this.$nextTick(() => {
        const loadMoreButton = document.querySelector('#load-more-button');
        if (loadMoreButton) {
          this.observer.observe(loadMoreButton);
        }
      });
    },
    async loadMore() {
      if (this.isLoadingMore) return;
      this.isLoadingMore = true;
      await new Promise(resolve => setTimeout(resolve, 300));
      this.currentPage++;
      this.isLoadingMore = false;
    },
    async copyToClipboard(iconClass) {
      try {
        await navigator.clipboard.writeText(iconClass);
        this.copied = iconClass;
        setTimeout(() => {
          if (this.copied === iconClass) {
            this.copied = null;
          }
        }, 2000);
      } catch (err) {
        console.error('Failed to copy:', err);
      }
    },
    async fetchFontData() {
      this.loading = true;
      this.loadingProgress = 0;
      try {
        const url = this.selectedProject 
          ? `/font-icons/${this.selectedProject}`
          : '/font-icons';
          
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        this.fontIcons = Array.isArray(data) ? data : [];
        console.log(`Loaded ${this.fontIcons.length} icons`);
      } catch (error) {
        console.error('Error fetching font data:', error);
      } finally {
        this.loading = false;
      }
    },
    initializeFuse() {
      this.fuse = new Fuse(this.fontIcons, {
        keys: [
          'css_class', 
          'css_content',
          'tags'
        ],
        threshold: 0.3,
        ignoreLocation: true,
        useExtendedSearch: true
      });
      return this.fuse;
    },
    selectProject(project) {
      this.selectedProject = project;
      this.isProjectDropdownOpen = false;
    },
    handleImageError(e) {
      const iconElement = document.createElement('i');
      iconElement.className = `${e.target.alt.split(' ')[0].toLowerCase()} text-2xl text-gray-900 dark:text-gray-100`;
      e.target.parentNode.replaceChild(iconElement, e.target);
    },
    getProjectImagePath(font) {
      const specialCases = {
        'OG': 'order-group',
        'CDB': 'central-database',
        'Og': 'order-group',
        'Cdb': 'central-database'
      };
      const baseName = specialCases[font] || font.toLowerCase();
      return `/img/projects/${baseName.replace(/\s+/g, '-')}_small.png`;
    },
    openIconModal(icon) {
      this.selectedIcon = { ...icon };
      this.showIconModal = true;
    },
    closeIconModal() {
      this.showIconModal = false;
      this.selectedIcon = null;
    },
    handleTagsUpdated(data) {
      // Update the icon in the local array
      const iconIndex = this.fontIcons.findIndex(i => i.id === data.icon.id);
      if (iconIndex !== -1) {
        this.fontIcons[iconIndex].tags = [...data.tags];
      }
    }
  }
};
</script>

<style>
/* Dark mode autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgb(55 65 81) inset !important;
  -webkit-text-fill-color: white !important;
  caret-color: white !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* Override plugins.css border-radius reset */
input[type="search"] {
  border-radius: 0.75rem !important; /* matches rounded-xl */
}

/* Minimal Range Slider */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
}

/* Track */
.range-slider::-webkit-slider-runnable-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

.range-slider::-moz-range-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

/* Thumb */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  margin-top: -7px;
  transition: all 0.15s ease;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.15s ease;
}

/* Hover state */
.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.range-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Focus state */
.range-slider:focus {
  outline: none;
}

/* Dark mode */
.dark .range-slider::-webkit-slider-runnable-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-moz-range-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-webkit-slider-thumb {
  background: #ffffff;
  border-color: #000000;
}

.dark .range-slider::-moz-range-thumb {
  background: #ffffff;
  border-color: #000000;
}

/* Remove other styles and replace with Tailwind classes in the template */
.skeleton-wave {
  position: relative;
  overflow: hidden;
}

.skeleton-wave::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  width: 30%;
  transform: translateX(-100%);
  animation: wave 1.5s infinite linear;
  animation-delay: calc(var(--item-index) * 100ms);
  pointer-events: none;
}

/* Dark mode adjustments */
.dark .skeleton-wave::after {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}
</style>
