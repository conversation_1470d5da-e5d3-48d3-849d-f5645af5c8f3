<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    },
    autofocus: {
        type: Boolean,
        default: false
    }
})

const search = ref('')
const isCollapsed = ref(false)
const selectedIndex = ref(-1)
const hoveredIndex = ref(null)
const commandInput = ref(null)
const hasInteracted = ref(false)

const defaultPlaceholder = 'What can I help you with?'
const currentPlaceholder = computed(() => {
    if (!hasInteracted.value) {
        return defaultPlaceholder
    }
    
    const activeIndex = hoveredIndex.value !== null ? hoveredIndex.value : selectedIndex.value
    if (activeIndex >= 0 && filteredRoutes.value[activeIndex]) {
        return `Let's go >> ${filteredRoutes.value[activeIndex].name}`
    }
    return 'Search for icons, fonts, or assets...'
})

const routes = [
    { name: 'Find some class names of Icons', icon: 'design design-figma', route: '/iconsearch' },
    { name: 'Update/upload assets in my Project', icon: 'design design-woff', route: '/projectassets' },
    { name: 'Variables & SCSS Vermont Classes', icon: 'design design-bracket2', route: '/classes' },
    { name: 'Playground, handover & inspiration', icon: 'design design-resolution_monitor', route: '/playground' },
    { name: 'Plugins for software, tools & scripts', icon: 'design design-measuring', route: '/tools' },
    { name: 'Animations...', icon: 'design design-transparency', route: '/animations' },
]

function calculateMatchScore(route, searchTerm) {
    const name = route.name.toLowerCase()
    const search = searchTerm.toLowerCase()
    
    if (name === search) return 100
    if (name.startsWith(search)) return 75
    if (name.includes(` ${search}`)) return 50
    if (name.includes(search)) return 25
    return 0
}

const filteredRoutes = computed(() => {
    if (!search.value) return routes.filter(route => !route.adminOnly || props.isAdmin)
    
    return routes
        .filter(route => {
            if (route.adminOnly && !props.isAdmin) return false
            return calculateMatchScore(route, search.value) > 0
        })
        .sort((a, b) => {
            return calculateMatchScore(b, search.value) - calculateMatchScore(a, search.value)
        })
})

function onSelect(route) {
    window.location.href = route
}

function onKeydown(event) {
    if (event.key === 'h' || event.key === 'H') {
        event.preventDefault()
        handleCollapse()
        return
    }

    if (!hasInteracted.value) {
        hasInteracted.value = true
        selectedIndex.value = 0
    }
    
    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault()
            selectedIndex.value = (selectedIndex.value + 1) % filteredRoutes.value.length
            hoveredIndex.value = null
            break
        case 'ArrowUp':
            event.preventDefault()
            selectedIndex.value = selectedIndex.value - 1 < 0 
                ? filteredRoutes.value.length - 1 
                : selectedIndex.value - 1
            hoveredIndex.value = null
            break
        case 'Enter':
            event.preventDefault()
            if (filteredRoutes.value[selectedIndex.value]) {
                onSelect(filteredRoutes.value[selectedIndex.value].route)
            }
            break
    }
}

function handleMouseOver(index) {
    hasInteracted.value = true
    hoveredIndex.value = index
}

function handleCollapse() {
    if (isAnimating.value) {
        pendingCollapse.value = true
        return
    }
    
    if (!isCollapsed.value) {
        isAnimating.value = true
        const el = commandPaletteContent.value
        
        if (el) {
            // Get references to the input area and options area
            const inputArea = el.querySelector('.px-1\\.5.py-1\\.5')
            const optionsArea = el.querySelector('.mt-4')
            
            // STEP 1: Collapse the options area first
            if (optionsArea) {
                const optionsHeight = optionsArea.offsetHeight
                
                // Create height transition animation for options
                optionsArea.style.overflow = 'hidden'
                optionsArea.animate([
                    { height: `${optionsHeight}px`, opacity: 1 },
                    { height: '0px', opacity: 0 }
                ], {
                    duration: 300,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'forwards'
                }).onfinish = () => {
                    // STEP 2: Now shrink the input area horizontally
                    const fullWidth = el.offsetWidth
                    
                    // Add a transition background that stays behind the shrinking element
                    const bgTransition = document.createElement('div')
                    bgTransition.style.position = 'absolute'
                    bgTransition.style.top = '0'
                    bgTransition.style.left = '0'
                    bgTransition.style.width = '100%'
                    bgTransition.style.height = '100%'
                    bgTransition.style.backgroundColor = 'inherit'
                    bgTransition.style.borderRadius = 'inherit'
                    bgTransition.style.zIndex = '-1'
                    el.style.position = 'relative'
                    el.style.overflow = 'hidden'
                    el.appendChild(bgTransition)
                    
                    // Create center-shrinking animation for the input area
                    const shrinkAnimation = el.animate([
                        { width: '100%', opacity: 1 },
                        { width: '60%', opacity: 0.8, offset: 0.4 },
                        { width: '0%', opacity: 0 }
                    ], {
                        duration: 400,
                        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                        fill: 'forwards'
                    })
                    
                    // Add a subtle scale and position effect - combined to avoid conflicts
                    el.animate([
                        { transform: 'translateX(0) scale(1)' },
                        { transform: 'translateX(10%) scale(0.95)', offset: 0.5 },
                        { transform: 'translateX(0) scale(0)' }
                    ], {
                        duration: 400,
                        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                        fill: 'forwards'
                    }).onfinish = () => {
                        // Cleanup and finish animation
                        optionsArea.style.removeProperty('overflow')
                        if (el.contains(bgTransition)) el.removeChild(bgTransition)
                        
                        isCollapsed.value = true
                        isAnimating.value = false
                        
                        // Handle any pending animation request
                        if (pendingCollapse.value) {
                            pendingCollapse.value = false
                            nextTick(() => expandPalette())
                        }
                    }
                }
            } else {
                // Fallback if structure is different
                isCollapsed.value = true
                isAnimating.value = false
            }
        } else {
            isCollapsed.value = true
            isAnimating.value = false
        }
    } else {
        isCollapsed.value = false
    }
}

function expandPalette() {
    isCollapsed.value = false
    
    nextTick(() => {
        if (commandPaletteContent.value) {
            const el = commandPaletteContent.value
            
            // First appear small and then expand horizontally
            el.style.opacity = '0'
            el.style.transform = 'scale(0.8)'
            
            // Animate expansion
            el.animate([
                { transform: 'scale(0.8)', opacity: 0, width: '0%' },
                { transform: 'scale(0.9)', opacity: 0.5, width: '60%', offset: 0.4 },
                { transform: 'scale(1)', opacity: 1, width: '100%' }
            ], {
                duration: 400,
                easing: 'cubic-bezier(0.25, 0.1, 0.25, 1.5)',
                fill: 'forwards'
            }).onfinish = () => {
                // Clean up inline styles
                el.style.removeProperty('opacity')
                el.style.removeProperty('transform')
                
                // Focus the input field
                setTimeout(() => {
                    if (commandInput.value) {
                        commandInput.value.focus()
                    }
                }, 100)
            }
        }
    })
}

function onAnimationEnd() {
    isAnimating.value = false
}

const commandPaletteContainer = ref(null)
const commandPaletteContent = ref(null)
const isAnimating = ref(false)
const pendingCollapse = ref(false)

function handleGlobalKeypress(e) {
    if ((e.key === 'h' || e.key === 'H') && e.target.tagName !== 'INPUT') {
        e.preventDefault()
        handleCollapse()
    }
}

onMounted(() => {
    window.addEventListener('keydown', handleGlobalKeypress)
})

onUnmounted(() => {
    window.removeEventListener('keydown', handleGlobalKeypress)
})
</script>

<template>
    <div>
        <!-- Full command palette with centered positioning -->
        <div class="w-full max-w-2xl mx-auto" 
             v-if="!isCollapsed"
             ref="commandPaletteContainer">
            <div 
                class="relative backdrop-blur-lg bg-gray-50/80 dark:bg-gray-950/80 rounded-xl shadow-2xl ring-1 ring-gray-200/50 dark:ring-gray-700/50 overflow-hidden"
                ref="commandPaletteContent"
                @animationend="onAnimationEnd"
            >
                <div class="px-1.5 py-1.5">
                    <div class="relative group">
                        <div class="absolute inset-0 backdrop-blur-sm bg-gray-50/90 dark:bg-gray-950/90 rounded-md transition-colors ring-1 ring-gray-200/50 dark:ring-gray-700/50 group-focus-within:ring-gray-300 dark:group-focus-within:ring-gray-600"></div>
                        <div class="relative flex items-center">
                            <input
                                ref="commandInput"
                                v-model="search"
                                type="text"
                                class="input-field w-full text-xl gant-modern-medium placeholder-gray-700 dark:placeholder-gray-300"
                                :placeholder="currentPlaceholder"
                                @keydown="onKeydown"
                                :autofocus="autofocus"
                            />
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="max-h-[300px] overflow-y-auto">
                        <template v-for="(route, index) in filteredRoutes" :key="route.name">
                            <button
                                class="flex items-center w-full px-5 py-2 text-left text-gray-500 dark:text-gray-400 hover:bg-gray-100/80 dark:hover:bg-gray-800/30 hover:gant-modern-bold hover:text-gray-700 dark:hover:text-gray-200 rounded-md transition-all gant-modern-regular group"
                                :class="{ 
                                    'bg-gray-100/80 dark:bg-gray-800/30 gant-modern-medium': index === selectedIndex,
                                    'text-gray-700 dark:text-gray-200': index === selectedIndex
                                }"
                                @click="onSelect(route.route)"
                                @mouseover="handleMouseOver(index)"
                                @mouseleave="hoveredIndex = null"
                            >
                                <Transition
                                    enter-active-class="transition-all duration-200 ease-out delay-75"
                                    enter-from-class="opacity-0 scale-x-0 origin-left"
                                    enter-to-class="opacity-100 scale-x-100 origin-left"
                                    leave-active-class="transition-all duration-150 ease-in"
                                    leave-from-class="opacity-100 scale-x-100 origin-left"
                                    leave-to-class="opacity-0 scale-x-0 origin-left"
                                >
                                    <i v-if="index === selectedIndex || hoveredIndex === index" 
                                       class="design design-arr text-xl me-2 inline-block"></i>
                                </Transition>
                                <i :class="[route.icon, 'text-xl me-3']"></i>
                                <span>{{ route.name }}</span>
                            </button>
                        </template>
                    </div>

                    <div class="px-4 py-4 border border-gray-100/20 dark:border-gray-700/20 ">
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                            <span class="flex items-center gap-2">
                                <span class="text-xs px-1.5 py-0.5 backdrop-blur-sm bg-gray-50/80 dark:bg-gray-800/80 ring-1 ring-gray-200/50 dark:ring-gray-700/50 rounded">↑↓</span>
                                <span class="text-gray-600 dark:text-gray-300">to navigate</span>
                            </span>
                            <span class="flex items-center gap-2 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200 transition-colors group" @click="isCollapsed = !isCollapsed">
                                <span class="text-xs px-1.5 py-0.5 backdrop-blur-sm bg-gray-50/80 dark:bg-gray-800/80 ring-1 ring-gray-200/50 dark:ring-gray-700/50 rounded group-hover:ring-gray-300 dark:group-hover:ring-gray-600 transition-all">H</span>
                                <span class="text-gray-600 dark:text-gray-300 group-hover:underline">to hide</span>
                                <i class="design design-minimize text-sm ml-0.5 opacity-70 group-hover:opacity-100"></i>
                            </span>
                            <span class="flex items-center gap-2">
                                <span class="text-xs px-1.5 py-0.5 backdrop-blur-sm bg-gray-50/80 dark:bg-gray-800/80 ring-1 ring-gray-200/50 dark:ring-gray-700/50 rounded">↵</span>
                                <span class="text-gray-600 dark:text-gray-300">to select</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Collapsed button in fixed position -->
        <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="transform scale-0 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition-all duration-300 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-0 opacity-0"
        >
            <button 
                v-if="isCollapsed" 
                @click="expandPalette"
                class="fixed bottom-6 right-6 z-50 w-14 h-14 rounded-full flex items-center justify-center backdrop-blur-lg bg-gray-50/80 dark:bg-gray-950/80 shadow-2xl ring-1 ring-gray-200/50 dark:ring-gray-700/50 hover:bg-gray-100/90 dark:hover:bg-gray-800/90 transition-all"
            >
                <i class="design design-hello-collegue text-xl text-gray-700 dark:text-gray-300"></i>
            </button>
        </Transition>
    </div>
</template>

<style>
.design {
    line-height: 1;
}

/* Override any browser default styles */
input {
    background-color: transparent !important;
}

input:focus {
    background-color: transparent !important;
}

/* Ensure selection color is consistent */
::selection {
    background-color: rgba(107, 114, 128, 0.1);
}

.dark ::selection {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Add smooth transition for backdrop-blur */
.backdrop-blur-md, .backdrop-blur-sm {
    transition: backdrop-filter 0.3s ease;
}

/* Animation for the circular button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(107, 114, 128, 0.3);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(107, 114, 128, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(107, 114, 128, 0);
    }
}

@keyframes pulse-dark {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

button[v-if="isCollapsed"]:hover {
    animation: pulse 1.5s infinite;
    transform: scale(1.05);
}

.dark button[v-if="isCollapsed"]:hover {
    animation: pulse-dark 1.5s infinite;
}
</style>
