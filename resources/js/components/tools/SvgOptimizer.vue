<template>
  <div class="svg-optimizer font-gant-modern">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8">
      <!-- Left column: Preview & Dropzone -->
      <div class="order-last lg:order-first">
        <div class="sticky top-4 h-full flex flex-col">
          <div class="bg-white dark:bg-slate-950 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col flex-grow">
            <h3 class="text-xl md:text-3xl font-semibold text-gray-900 dark:text-gray-100 mb-4">SVG Preview</h3>

            <!-- Dropzone -->
            <div
              class="dropzone flex-grow flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg transition-colors cursor-pointer"
              :class="{
                'border-blue-500 bg-blue-50 dark:bg-blue-900/20': isDragging,
                'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500': !isDragging
              }"
              @dragover.prevent="onDragOver"
              @dragleave.prevent="onDragLeave"
              @drop.prevent="onDrop"
              @click="triggerFileInput"
            >
              <input
                ref="fileInput"
                type="file"
                multiple
                accept="image/svg+xml"
                class="hidden"
                @change="onFileSelect"
              />

              <div v-if="!selectedFiles.length" class="text-center space-y-2 text-gray-500 dark:text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-lg">Drag & drop SVG files here, or <span class="font-medium text-blue-600 dark:text-blue-400">click to select</span></p>
                <p class="text-sm">Supports multiple SVG files.</p>
              </div>

              <div v-else class="w-full overflow-auto text-sm text-gray-800 dark:text-gray-200">
                <p class="mb-2 font-medium">{{ selectedFiles.length }} file(s) selected:</p>
                <ul class="list-disc list-inside space-y-1">
                  <li v-for="(file, idx) in selectedFiles" :key="idx">
                    {{ file.name }} ({{ formatFileSize(file.size) }})
                  </li>
                </ul>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- Right column: Settings -->
      <div>
        <div class="bg-white dark:bg-slate-950 rounded-2xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
          <!-- Header Section -->
          <div class="mb-6">
            <h3 class="text-xl md:text-3xl gant-modern-bold text-gray-700 dark:text-gray-300 mb-2">
              SVG Optimizer
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Optimize your SVG files for web use with these customizable options.
            </p>
          </div>

          <!-- Options Section -->
          <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5 mb-6">
            <h3 class="text-lg md:text-2xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Optimization Options</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
              
              <!-- Cleanup Section -->
              <fieldset class="space-y-2 border-t pt-2 dark:border-slate-600">
                <legend class="font-medium text-gray-700 dark:text-gray-300">Cleanup</legend>
                <div v-for="(label, key) in optionLabels.cleanup" :key="key" class="flex items-center">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" :id="`opt-${key}`" v-model="options[key]" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-slate-900 dark:peer-checked:bg-gray-700"></div>
                    <span class="ms-3 text-sm text-gray-900 dark:text-gray-200">{{ label }}</span>
                  </label>
                </div>
              </fieldset>

              <!-- Structure Section -->
              <fieldset class="space-y-2 border-t pt-2 dark:border-slate-600">
                <legend class="font-medium text-gray-700 dark:text-gray-300">Structure</legend>
                <div v-for="(label, key) in optionLabels.structure" :key="key" class="flex items-center">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" :id="`opt-${key}`" v-model="options[key]" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-slate-900 dark:peer-checked:bg-gray-700"></div>
                    <span class="ms-3 text-sm text-gray-900 dark:text-gray-200">{{ label }}</span>
                  </label>
                </div>
              </fieldset>

              <!-- Style Section -->
              <fieldset class="space-y-2 border-t pt-2 dark:border-slate-600">
                <legend class="font-medium text-gray-700 dark:text-gray-300">Style</legend>
                <div v-for="(label, key) in optionLabels.style" :key="key" class="flex items-center">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" :id="`opt-${key}`" v-model="options[key]" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-slate-900 dark:peer-checked:bg-gray-700"></div>
                    <span class="ms-3 text-sm text-gray-900 dark:text-gray-200">{{ label }}</span>
                  </label>
                </div>
              </fieldset>

              <!-- Other Section -->
              <fieldset class="space-y-2 border-t pt-2 dark:border-slate-600">
                <legend class="font-medium text-gray-700 dark:text-gray-300">Other</legend>
                <div v-for="(label, key) in optionLabels.other" :key="key" class="flex items-center">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" :id="`opt-${key}`" v-model="options[key]" class="sr-only peer">
                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-slate-900 dark:peer-checked:bg-gray-700"></div>
                    <span class="ms-3 text-sm text-gray-900 dark:text-gray-200">{{ label }}</span>
                  </label>
                </div>
              </fieldset>
            </div>

            <!-- Precision Section -->
            <fieldset class="mt-6 pt-4 border-t dark:border-slate-600">
              <legend class="font-medium text-gray-700 dark:text-gray-300">Precision</legend>
              <div class="mt-2">
                <label for="opt-precision" class="block text-sm text-gray-700 dark:text-gray-300 mb-1">Decimal Places</label>
                <div class="flex items-center">
                  <input 
                    id="opt-precision"
                    type="range" 
                    v-model.number="options.precision" 
                    min="0" 
                    max="8" 
                    step="1"
                    class="w-full range-slider mr-3 md:mr-4" 
                  />
                  <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400 w-10 text-right">{{ options.precision }}</span>
                </div>
              </div>
            </fieldset>
          </div>

          <!-- Process Section -->
          <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5">
            <h3 class="text-lg md:text-2xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4">Process</h3>
            
            <div v-if="selectedFiles.length === 0" class="text-center text-sm text-gray-500 dark:text-gray-400 mb-4">
              Drop files or click the preview area to select SVGs for optimization.
            </div>
            
            <div v-else>
              <div class="flex items-center justify-between mb-4">
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ selectedFiles.length }} file(s) selected</span>
                <button 
                  @click="selectedFiles = []; processedResults = []" 
                  class="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                >
                  Clear all
                </button>
              </div>
              
              <button 
                @click="processFiles" 
                :disabled="processing || selectedFiles.length === 0" 
                class="w-full px-4 py-3 bg-slate-900 dark:bg-white text-white dark:text-black rounded-lg hover:bg-slate-800 dark:hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <svg v-if="processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white dark:text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span v-if="processing">Processing SVGs...</span>
                <span v-else>Optimize {{ selectedFiles.length }} SVG{{ selectedFiles.length > 1 ? 's' : '' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

console.log('SvgOptimizer script setup executing...');

const isDragging = ref(false);
const selectedFiles = ref([]);
const fileInput = ref(null);
const processedResults = ref([]);
const processing = ref(false);

// Define the granular options with default values (most enabled by default)
const options = reactive({
    // Cleanup
    removeDoctype: true,
    removeXMLProcInst: true,
    removeComments: true,
    removeMetadata: true,
    removeEditorsNSData: true,
    cleanupAttrs: true,
    removeEmptyAttrs: true,
    removeEmptyContainers: true,
    removeEmptyText: true,
    removeHiddenElems: true,
    removeUnknownsAndDefaults: true,
    removeUnusedNS: true,
    // Structure
    cleanupIDs: true,
    collapseGroups: true,
    convertShapeToPath: true,
    convertPathData: true, // Optimizes path data
    convertTransform: true,
    mergePaths: true,
    // Style
    cleanupNumericValues: true, // Also affects precision
    inlineStyles: false, // Usually better to use external CSS
    minifyStyles: true,
    moveElemsAttrsToGroup: true,
    moveGroupAttrsToElems: true,
    removeStyleElement: false,
    removeUselessStrokeAndFill: true,
    // Other
    removeDesc: true,
    removeDimensions: false, // Keep width/height by default
    removeTitle: true,
    removeXMLNS: false, // Usually needed
    // Precision
    precision: 3, // Default decimal places
});

// Labels for the UI checkboxes (can be translated later if needed)
const optionLabels = {
    cleanup: {
        removeDoctype: 'Remove Doctype',
        removeXMLProcInst: 'Remove XML Instructions',
        removeComments: 'Remove Comments',
        removeMetadata: 'Remove <metadata>',
        removeEditorsNSData: 'Remove Editor Data',
        cleanupAttrs: 'Cleanup Attributes',
        removeEmptyAttrs: 'Remove Empty Attributes',
        removeEmptyContainers: 'Remove Empty Containers',
        removeEmptyText: 'Remove Empty Text',
        removeHiddenElems: 'Remove Hidden Elements',
        removeUnknownsAndDefaults: 'Remove Unknowns & Defaults',
        removeUnusedNS: 'Remove Unused Namespaces',
    },
    structure: {
        cleanupIDs: 'Cleanup IDs',
        collapseGroups: 'Collapse Useless Groups',
        convertShapeToPath: 'Convert Shapes to Paths',
        convertPathData: 'Optimize Path Data',
        convertTransform: 'Apply Transforms',
        mergePaths: 'Merge Paths',
    },
    style: {
        cleanupNumericValues: 'Cleanup Numeric Values',
        inlineStyles: 'Inline Styles',
        minifyStyles: 'Minify Styles',
        moveElemsAttrsToGroup: 'Move Element Attrs to Group',
        moveGroupAttrsToElems: 'Move Group Attrs to Elements',
        removeStyleElement: 'Remove <style> Elements',
        removeUselessStrokeAndFill: 'Remove Useless Stroke/Fill',
    },
    other: {
        removeDesc: 'Remove <desc>',
        removeDimensions: 'Remove width/height',
        removeTitle: 'Remove <title>',
        removeXMLNS: 'Remove xmlns Attribute',
    }
};

// Drag & drop handlers
function onDragOver() {
  isDragging.value = true;
}
function onDragLeave() {
  isDragging.value = false;
}
function onDrop(e) {
  isDragging.value = false;
  const files = Array.from(e.dataTransfer.files).filter(f => f.type === 'image/svg+xml');
  if (files.length) selectedFiles.value.push(...files);
}

// File input handlers
function triggerFileInput() {
  fileInput.value.click();
}
function onFileSelect(e) {
  const files = Array.from(e.target.files).filter(f => f.type === 'image/svg+xml');
  if (files.length) selectedFiles.value.push(...files);
  e.target.value = '';
}

// Placeholder for actual processing
async function processFiles() {
  // implement API calls...
}

// Utility to format file sizes
function formatFileSize(bytes) {
  if (!bytes) return '0 Bytes';
  const k = 1024, sizes = ['Bytes','KB','MB','GB'];
  const i = Math.floor(Math.log(bytes)/Math.log(k));
  return `${(bytes/Math.pow(k,i)).toFixed(2)} ${sizes[i]}`;
}
</script>

<style scoped>
/* Better dropzone styling */
.dropzone {
  min-height: 200px;
  position: relative;
  overflow: hidden;
}

.dropzone:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.dark .dropzone:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Range slider styles */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
}

/* Track */
.range-slider::-webkit-slider-runnable-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

.range-slider::-moz-range-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

/* Thumb */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  margin-top: -7px;
  transition: all 0.15s ease;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.15s ease;
}

/* Hover state */
.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.range-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Focus state */
.range-slider:focus {
  outline: none;
}

/* Dark mode */
.dark .range-slider::-webkit-slider-runnable-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-moz-range-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-webkit-slider-thumb {
  background: #ffffff;
  border-color: #000000;
}

.dark .range-slider::-moz-range-thumb {
  background: #ffffff;
  border-color: #000000;
}

/* Animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
</style> 