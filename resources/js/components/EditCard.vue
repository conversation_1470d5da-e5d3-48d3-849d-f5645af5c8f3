<template>
  <div class="flex flex-col gap-4">
    <div>
      <!-- Bookmark title input with icon -->
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="design design-letter text-gray-400 dark:text-gray-500"></i>
        </div>
        <input 
          v-model="local.title" 
          placeholder="Bookmark Title" 
          class="w-full border focus:ring px-3 py-2 pl-10 rounded dark:bg-slate-700 dark:text-white gant-modern-regular border-solid border-gray-200 dark:border-gray-800" 
        />
      </div>

      <!-- Icon class input with icon -->
      <div class="relative mt-3">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="design design-component text-gray-400 dark:text-gray-500"></i>
        </div>
        <input 
          v-model="local.icon_class" 
          placeholder="Icon Class (e.g. 'wms wms-thumbnail')" 
          class="w-full border focus:ring px-3 py-2 pl-10 rounded dark:bg-slate-700 dark:text-white gant-modern-regular border-solid border-gray-200 dark:border-gray-800" 
        />
      </div>
      
      <!-- Delete button (only shown for existing bookmarks) -->
      <button 
        v-if="bookmark.id" 
        @click="$emit('delete')" 
        type="button" 
        class="mt-3 inline-flex items-center justify-center text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm gant-modern-regular"
      >
        <i class="design design-trash mr-1.5"></i>
        Delete this bookmark
      </button>
    </div>
    <div class="space-y-2 mt-4 max-h-[50vh] overflow-y-auto pr-2 pb">
      <h4 class="gant-modern-bold dark:text-gray-200">Card links:</h4>
      
      <!-- Link item with side-by-side remove button -->
      <div v-for="(link, idx) in local.links" :key="link.id ?? idx" class="flex flex-col gap-2 w-full bg-white dark:bg-transparent p-3 rounded-lg mb-2 border border-solid border-gray-200 dark:border-gray-800">
        <div class="flex justify-between items-center">
          <div class="flex-grow space-y-2">
            <!-- Link label input with icon -->
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <i class="design design-tag text-gray-400 dark:text-gray-500 text-sm"></i>
              </div>
              <input 
                v-model="link.label" 
                placeholder="Name of Link" 
                class="w-full border focus:ring px-2 py-1 pl-8 rounded dark:bg-slate-600 dark:text-white border-solid border-gray-200 dark:border-gray-800 gant-modern-regular" 
              />
            </div>
            
            <!-- URL input with icon -->
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <i class="design design-link text-gray-400 dark:text-gray-500 text-sm"></i>
              </div>
              <input 
                v-model="link.url" 
                placeholder="Valid URL" 
                class="w-full border focus:ring px-2 py-1 pl-8 rounded dark:bg-slate-600 dark:text-white border-solid border-gray-200 dark:border-gray-800 gant-modern-regular" 
              />
            </div>
          </div>
          
          <!-- Remove button positioned beside inputs and centered vertically -->
          <button 
            @click="removeLink(idx)" 
            type="button" 
            class="ml-3 flex items-center justify-center self-center h-full text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 p-2 border border-solid border-gray-200 dark:border-gray-200/20"
          > 
            <i class="design design-close"></i>
          </button>
        </div>
      </div>
      
      <button @click="addLink" type="button" class="border border-solid border-gray-200 dark:border-gray-200/20 rounded-lg px-3 py-1.5 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300 inline-flex items-center gant-modern-regular">
        <i class="design design-plus text-sm mr-1.5"></i>
        Add Link
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditCard',
  props: {
    bookmark: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      local: {
        id: this.bookmark.id,
        __tempId: this.bookmark.__tempId,
        title: this.bookmark.title || '',
        icon_class: this.bookmark.icon_class || '',
        links: Array.isArray(this.bookmark.links)
          ? this.bookmark.links.map(l => ({ id: l.id, label: l.label, url: l.url }))
          : []
      }
    };
  },
  methods: {
    addLink() {
      this.local.links.push({ label: '', url: '' });
    },
    removeLink(idx) {
      this.local.links.splice(idx, 1);
    }
  },
  watch: {
    local: {
      deep: true,
      handler() {
        this.$emit('update', { bookmark: this.local, links: this.local.links });
      }
    }
  },
  mounted() {
    // Initial emit to sync with parent component
    this.$emit('update', { bookmark: this.local, links: this.local.links });
  }
};
</script>

<style scoped>
input { outline: none; }
</style> 