<template>
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100" 
    leave-to-class="opacity-0"
  >
    <div v-if="show" class="fixed inset-0 bg-gray-900 dark:bg-slate-950 bg-opacity-75 dark:bg-opacity-80 backdrop-blur-md flex items-center justify-center p-4 z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all">
        <div class="text-center">
          <div class="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-500/20 flex items-center justify-center mx-auto mb-4">
            <img src="/img/logos/link.svg" alt="External Link" class="w-8 h-8">
          </div>
          
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            External Link
          </h3>
          
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
            You are about to visit an external website:<br>
            <a :href="url" class="text-blue-600 dark:text-blue-400 font-medium hover:underline break-all" target="_blank" rel="noopener">
              {{ formatUrl(url) }}
            </a>
          </p>
          
          <div class="flex justify-center space-x-3">
            <button 
              @click="$emit('close')" 
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button 
              @click="openLink" 
              class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors flex items-center justify-center"
            >
              Continue
              <img src="/img/logos/link.svg" alt="External Link" class="w-4 h-4 ml-2 invert">
            </button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      required: true
    },
    target: {
      type: String,
      default: '_blank'
    }
  },
  
  emits: ['close'],
  
  methods: {
    openLink() {
      if (this.target === '_blank') {
        window.open(this.url, '_blank');
      } else {
        window.location.href = this.url;
      }
      this.$emit('close');
    },
    
    formatUrl(url) {
      if (!url) return '';
      
      // Remove protocol
      return url.replace(/^https?:\/\//, '');
    }
  }
}
</script> 