<template>
  <a href="#" 
    @click.prevent="$emit('click')"
    class="flex items-center text-xl gant-modern-bold mb-4 text-gray-900 dark:text-gray-100 px-2 group transition-colors duration-200"
    :class="{ 'justify-center': isCollapsed }"
    :data-tooltip="isCollapsed ? section.label : ''">
    <i v-if="!isCollapsed" class="design design-arr text-lg transform rotate-180 transition-transform duration-300 group-hover:-translate-x-0.5 me-2"></i>
    <template v-if="section && section.routeIs === 'projectassets' && !isCollapsed">
      <i class="design design-figma me-2 text-lg opacity-80"></i>
    </template>
    <span v-show="!isCollapsed">{{ section.label }}</span>
  </a>
</template>

<script setup>
defineProps({
  isCollapsed: { type: Boolean, required: true },
  section: { type: Object, required: true }
});

defineEmits(['click']);
</script> 