<template>
  <div class="w-full align-center">
    <p class="text-sm text-gray-900 dark:text-gray-100 mb-4" 
       :class="[isCollapsed ? 'text-center' : 'gant-modern-bold flex items-center']">
      <i class="design design-mn" :class="{'inline-block': isCollapsed}"></i>
      <span v-show="!isCollapsed" class="ml-2">Changelog</span>
    </p>
  </div>
</template>

<script setup>
defineProps({
  isCollapsed: { type: Boolean, required: true }
});
</script> 