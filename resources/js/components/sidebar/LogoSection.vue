<template>
  <div class="logo-wrapper mb-8">
    <a :href="routes.hello" class="block">
      <img 
        :src="isCollapsed ? '/img/devMobileLight.svg' : '/img/devLight.svg'" 
        :class="[isCollapsed ? 'h-6' : 'h-16 sm:h-20 lg:h-24 xl:h-28', 'mx-auto dark:hidden transition-all duration-300']" 
        alt="Vermont Dev Logo">
      <img 
        :src="isCollapsed ? '/img/devMobileDark.svg' : '/img/devDark.svg'" 
        :class="[isCollapsed ? 'h-6' : 'h-16 sm:h-20 lg:h-24 xl:h-28', 'mx-auto hidden dark:block transition-all duration-300']" 
        alt="Vermont Dev Logo">
    </a>
  </div>
</template>

<script setup>
defineProps({
  isCollapsed: { type: Boolean, required: true },
  routes: { type: Object, required: true }
});
</script> 