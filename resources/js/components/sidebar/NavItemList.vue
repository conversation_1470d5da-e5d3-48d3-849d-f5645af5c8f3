<template>
  <ul class="space-y-1.5 mb-6 overflow-visible">
    <li v-for="(item, idx) in items" :key="idx" class="w-full">
      <!-- Profile/Logout special case -->
      <template v-if="item.isLogout">
        <div class="nav-link group relative">
          <!-- Profile link -->
          <a href="/profile" 
             class="flex-grow flex items-center relative"
             :class="{ 'justify-center': isCollapsed }"
             @mouseover="addBoldClass" 
             @mouseout="removeBoldClass">
            <i class="design design-user-outline text-lg opacity-80" :class="isCollapsed ? '' : 'me-3'"></i>
            <span v-show="!isCollapsed">{{ messages?.userLogin || 'Your Login' }}</span>
          </a>
          
          <!-- Logout button (only visible when not collapsed) -->
          <form v-if="!isCollapsed" :id="formId" :action="routes.logout" method="POST" class="absolute right-0 top-0 bottom-0 flex items-center justify-center" @submit.prevent="submitLogout(formId)">
            <input type="hidden" name="_token" :value="csrfToken">
            <button type="submit" class="focus:outline-none w-10 h-full opacity-0 group-hover:opacity-100 transition-opacity duration-200" :title="messages && messages.logout">
              <i class="design design-logout text-base"></i>
            </button>
          </form>
          
          <!-- Flyout menu with logout for collapsed mode -->
          <div v-if="isCollapsed" 
               class="flyout-menu absolute left-full top-1/2 -translate-y-1/2 ml-2 bg-white dark:bg-slate-900 shadow-lg rounded-md py-2 px-1 z-[1000] min-w-[180px] 
                      opacity-0 invisible group-hover:opacity-100 group-hover:visible
                      hover:opacity-100 hover:visible transition-all duration-150 border border-gray-100 dark:border-gray-800">
            <div class="font-bold px-3 pb-1 mb-1 border-b border-gray-100 dark:border-gray-800">
              {{ user?.login || messages?.userLogin || 'Your Login' }}
            </div>
            <ul class="space-y-1">
              <li class="px-2">
                <a href="/profile"
                   class="block px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm transition-colors duration-150 whitespace-nowrap">
                  <i class="design design-edit text-sm mr-2 opacity-80"></i>
                  {{ messages?.userLogin || 'Your Login' }}
                </a>
              </li>
              <li class="px-2">
                <form :id="`${formId}-flyout`" :action="routes.logout" method="POST" @submit.prevent="submitLogout(`${formId}-flyout`)">
                  <input type="hidden" name="_token" :value="csrfToken">
                  <button type="submit" 
                     class="w-full text-left block px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm transition-colors duration-150 whitespace-nowrap">
                    <i class="design design-logout text-sm mr-2 opacity-80"></i>
                    {{ messages && messages.logout }}
                  </button>
                </form>
              </li>
            </ul>
          </div>
        </div>
      </template>
      
      <!-- Regular menu item -->
      <template v-else>
        <div class="nav-link group relative" 
             :class="{ 'nav-link-active': isActiveRoute(item.routeIs) }">
          
          <!-- Menu item main link -->
          <a :href="item.externalUrl || item.route" 
             class="flex-grow flex items-center relative"
             :class="{ 'justify-center': isCollapsed }"
             :target="item.target"
             @click="handleClick($event, item)"
             @mouseover="addBoldClass" 
             @mouseout="removeBoldClass">
            
            <!-- Project assets special case -->
            <template v-if="sectionType === 'projectassets'">
              <img :src="getItemIcon(item)" 
                   :class="['opacity-80', isCollapsed ? 'w-6 h-6' : 'w-5 h-5 me-3']" 
                   :alt="`${item.label} icon`">
            </template>
            
            <!-- Standard icon -->
            <template v-else>
              <i :class="['design', item.icon || 'design-cube', 'text-lg', 'opacity-80', isCollapsed ? '' : 'me-3']"></i>
            </template>
            
            <span v-show="!isCollapsed">{{ item.label }}</span>
            
            <!-- Badge (notification) -->
            <span v-if="item.badge && !isCollapsed" class="ml-auto inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full min-w-[20px] h-5">
              {{ item.badge }}
            </span>
            <span v-else-if="item.badge && isCollapsed" class="absolute top-0 right-0 w-2 h-2 bg-red-600 rounded-full"></span>
            
            <!-- Simple tooltip for items without subsections in collapsed mode -->
            <div v-if="isCollapsed && (!item.subsections || !item.subsections.length)" 
                 class="flyout-menu absolute left-full top-1/2 -translate-y-1/2 ml-2 bg-white dark:bg-slate-900 shadow-lg rounded-md py-2 px-3 z-[1000]
                        opacity-0 invisible group-hover:opacity-100 group-hover:visible
                        transition-all duration-150 border border-gray-100 dark:border-gray-800 whitespace-nowrap">
              {{ item.label }}
            </div>
          </a>
          
          <!-- Flyout submenu for collapsed mode with subsections -->
          <div v-if="isCollapsed && item.subsections && item.subsections.length" 
               class="flyout-menu absolute left-full top-1/2 -translate-y-1/2 ml-2 bg-white dark:bg-slate-900 shadow-lg rounded-md py-2 px-1 z-[1000] min-w-[180px] 
                      opacity-0 invisible group-hover:opacity-100 group-hover:visible
                      hover:opacity-100 hover:visible transition-all duration-150 border border-gray-100 dark:border-gray-800">
            <div class="font-bold px-3 pb-1 mb-1 border-b border-gray-100 dark:border-gray-800">
              {{ item.label }}
            </div>
            <ul class="space-y-1">
              <li v-for="(subitem, subidx) in item.subsections" :key="subidx"
                  class="px-2">
                <a :href="subitem.route"
                   @click.prevent="navigateTo(subitem.route)"
                   class="block px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-sm transition-colors duration-150 whitespace-nowrap">
                  <!-- Use images for Project assets subsections -->
                  <template v-if="item.routeIs === 'projectassets'">
                    <img :src="getProjectIconPath(subitem.label)" 
                         class="w-5 h-5 inline-block mr-2 opacity-80" 
                         :alt="`${subitem.label} icon`">
                  </template>
                  <!-- Use font icons for all other subsections -->
                  <template v-else>
                    <i :class="['design', subitem.icon || 'design-cube', 'text-sm mr-2 opacity-80']"></i>
                  </template>
                  {{ subitem.label }}
                </a>
              </li>
            </ul>
          </div>
          
          <!-- Only show subsection indicator when not collapsed -->
          <button 
            v-if="item.subsections && item.subsections.length && !isCollapsed"
            @click.prevent.stop="openSubsections(item)" 
            class="absolute right-0 top-0 bottom-0 flex items-center justify-center focus:outline-none w-10 h-full"
            :title="`Show ${item.label} subsections`">
            <i class="design design-arr text-base opacity-0 group-hover:opacity-100 transition-opacity duration-200"></i>
          </button>
        </div>
      </template>
    </li>
  </ul>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useNavigation } from '../../composables/useNavigation';

const props = defineProps({
  items: { type: Array, required: true },
  isCollapsed: { type: Boolean, required: true },
  currentRoute: { type: String, default: '' },
  sectionType: { type: String, default: '' },
  csrfToken: { type: String, default: '' },
  messages: { type: Object, default: () => ({}) },
  inSubsection: { type: Boolean, default: false },
  routes: { type: Object, default: () => ({}) },
  user: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['section-click']);

const { isActiveRoute, navigateTo, submitLogout, addBoldClass, removeBoldClass } = useNavigation(props.currentRoute);

// Form ID for logout
const formId = computed(() => props.inSubsection ? 'logout-form-subsection' : 'logout-form');

// Handle opening subsections - only triggered by the arrow button
const openSubsections = (item) => {
  emit('section-click', item);
};

// Get icon path for projects
const getProjectIconPath = (label) => {
  const labelLower = label.toLowerCase();
  
  // Map of special cases for better performance
  const specialCases = {
    'ordergroup': '/img/projects/order-group_small.png',
    'cdb': '/img/projects/central-database_small.png',
    'gant fonts': '/img/projects/gant-preview_small.png',
    'nunito sans': '/img/projects/nunito-preview_small.png'
  };
  
  return specialCases[labelLower] || 
         `/img/projects/${labelLower.replace(/\s+/g, '-')}_small.png`;
};

// Get icon for non-projectassets items
const getItemIcon = (item) => {
  if (props.sectionType === 'projectassets') {
    return getProjectIconPath(item.label);
  }
  return '';
};

// Handle click event
const handleClick = (event, item) => {
  if (item.externalUrl) {
    // Let the browser handle external links natively
    return;
  } else {
    // Handle internal link
    event.preventDefault();
    navigateTo(item.route);
  }
};
</script>

<style>
/* Add a global style to make the flyout menus appear above everything else */
.flyout-menu {
  z-index: 1000 !important; /* Use !important to override any other z-index */
  position: absolute;
}

/* Make sure sidebar container has proper positioning and z-index */
.designdev_tm_leftpart {
  z-index: 40;
}

.designdev_tm_sidebar_menu {
  position: relative;
  z-index: 40;
}
</style> 