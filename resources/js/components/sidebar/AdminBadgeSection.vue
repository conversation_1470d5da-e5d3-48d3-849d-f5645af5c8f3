<template>
  <div class="w-full flex flex-col justify-center items-center mt-3">
    <dark-mode-toggle :is-collapsed="isCollapsed"></dark-mode-toggle>
    <img 
      v-if="isAdmin" 
      src="https://img.shields.io/endpoint?url=https%3A%2F%2Fforge.laravel.com%2Fsite-badges%2F5aaafdad-4865-4ab7-8b47-e21744d57f10%3Fdate%3D1&style=flat-square" 
      alt="Forge Deploy" 
      class="inline-block">
  </div>
</template>

<script setup>
import DarkModeToggle from '../DarkModeToggle.vue';

defineProps({
  isCollapsed: { type: Boolean, required: true },
  isAdmin: { type: Boolean, required: true }
});
</script> 