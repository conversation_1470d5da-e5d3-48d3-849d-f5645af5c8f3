<template>
  <div class="flex flex-col border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
    <!-- Window controls -->
    <div class="flex items-center gap-2 p-2">
      <span class="w-3 h-3 rounded-full bg-red-500"></span>
      <span class="w-3 h-3 rounded-full bg-yellow-500"></span>
      <span class="w-3 h-3 rounded-full bg-green-500"></span>
      <span class="w-full gant-modern-bold text-gray-400 dark:text-gray-700 text-center me-12">Playground</span>
    </div>
    
    <!-- Tabs -->
    <div class="flex space-x-1 p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        @click="updateActiveTab(tab.id)"
        :class="[
          'flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
          'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white',
          { 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm': activeTab === tab.id },
          activeTab === tab.id ? 'gant-modern-medium' : 'gant-modern-regular'
        ]"
        :title="tab.title"
      >
        <div class="flex items-center gap-1">
          <i class="design text-sm"></i>
          <i :class="[
            'design text-sm',
            tab.icon,
            activeTab === tab.id ? 'text-current' : 'text-gray-400 dark:text-gray-500'
          ]"></i>
        </div>
        {{ tab.label }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:active-tab'])

// Tab definitions
const tabs = [
  {
    id: 'preview',
    label: 'Preview',
    icon: 'design-prototype2',
    title: 'View the live preview of your component'
  },
  {
    id: 'code',
    label: 'Code',
    icon: 'design-bracket3',
    title: 'View and copy the source code'
  }
]

// Update active tab with two-way binding
const updateActiveTab = (tabId) => {
  emit('update:active-tab', tabId)
}
</script>

<style scoped>
/* Typography classes */
.gant-modern-regular {
  font-family: 'gant-modern-regular', sans-serif;
}

.gant-modern-medium {
  font-family: 'gant-modern-medium', sans-serif;
}

.gant-modern-bold {
  font-family: 'gant-modern-bold', sans-serif;
}
</style> 