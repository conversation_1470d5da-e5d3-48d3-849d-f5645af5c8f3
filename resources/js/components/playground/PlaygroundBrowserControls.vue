<template>
  <div class="flex flex-wrap items-center justify-between gap-4 p-3 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <!-- Left Section: Navigation Buttons -->
    <div class="flex items-center gap-1">
      <button 
        class="p-1.5 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors tooltip-wrapper"
        @click="emit('navigate-back')"
        aria-label="Go back"
      >
        <i class="design design-arrow-left text-lg"></i>
        <span class="tooltip">Back</span>
      </button>
      <button 
        class="p-1.5 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors tooltip-wrapper"
        @click="emit('navigate-forward')"
        aria-label="Go forward"
      >
        <i class="design design-arrow-right text-lg"></i>
        <span class="tooltip">Forward</span>
      </button>
      <button 
        class="p-1.5 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors tooltip-wrapper"
        @click="emit('refresh')"
        aria-label="Refresh preview"
      >
        <i class="design design-refresh text-lg" :class="{ 'animate-spin': isRefreshing }"></i>
        <span class="tooltip">Refresh preview</span>
      </button>
      <button 
        class="p-1.5 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors tooltip-wrapper"
        v-if="activeTab === 'code'"
        @click="emit('copy-code')"
        aria-label="Copy code"
      >
        <i class="design design-clipboard-line text-lg"></i>
        <span class="tooltip">Copy code</span>
      </button>
    </div>

    <!-- Middle Section: Address Bar -->
    <div class="flex items-center gap-2 flex-1 px-3 py-1.5 mx-4 rounded-md bg-white dark:bg-gray-700 text-sm text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 transition-colors gant-modern-regular">
      <i class="design design-global-line text-gray-500 dark:text-gray-400"></i>
      <span class="font-mono text-xs truncate">https://playground.demo/component</span>
    </div>

    <!-- Right Section: Device & Theme Switchers -->
    <div class="flex items-center gap-3">
      <!-- Device Switcher -->
      <div 
        v-if="activeTab === 'preview'" 
        class="flex items-center gap-1 bg-white dark:bg-gray-700 rounded-md p-1 shadow-sm transition-colors"
      >
        <button
          v-for="device in devices"
          :key="device.name"
          @click="emit('update:active-device', device.name)"
          :class="[
            'p-1.5 rounded transition-colors',
            activeDevice === device.name 
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300' 
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          ]"
          :aria-label="device.description"
          class="tooltip-wrapper"
        >
          <i :class="['design text-lg', device.icon]"></i>
          <span class="tooltip">{{ device.description }}</span>
        </button>
      </div>

      <!-- Theme Switcher -->
      <div class="flex items-center gap-1 bg-white dark:bg-gray-700 rounded-md p-1 shadow-sm transition-colors">
        <button
          v-for="theme in themes"
          :key="theme.name"
          @click="emit('update:active-theme', theme.name)"
          :class="[
            'p-1.5 rounded transition-colors',
            activeTheme === theme.name 
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300' 
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          ]"
          :aria-label="theme.description"
          class="tooltip-wrapper"
        >
          <i :class="['design text-lg', theme.icon]"></i>
          <span class="tooltip">{{ theme.description }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  },
  activeDevice: {
    type: String,
    required: true
  },
  activeTheme: {
    type: String,
    required: true
  },
  devices: {
    type: Array,
    required: true
  },
  themes: {
    type: Array,
    required: true
  },
  isRefreshing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:active-device',
  'update:active-theme',
  'refresh',
  'copy-code',
  'navigate-back',
  'navigate-forward'
])
</script>

<style scoped>
/* Typography classes */
.gant-modern-regular {
  font-family: 'gant-modern-regular', sans-serif;
}

.gant-modern-medium {
  font-family: 'gant-modern-medium', sans-serif;
}

.gant-modern-bold {
  font-family: 'gant-modern-bold', sans-serif;
}

.tooltip-wrapper {
  position: relative;
}

.tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.75rem;
  font-family: 'gant-modern-regular', sans-serif;
  border-radius: 0.25rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  z-index: 50;
  pointer-events: none;
}

.tooltip-wrapper:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .tooltip {
    display: none;
  }
}
</style> 