<template>
  <div class="fixed bottom-4 right-4 flex flex-col gap-2 max-w-sm z-50" aria-live="polite">
    <transition-group 
      name="notification" 
      tag="div" 
      class="space-y-2"
    >
      <div 
        v-for="notification in notifications" 
        :key="notification.id"
        class="flex items-start gap-3 p-4 rounded-lg shadow-lg border animate-slide-in"
        :class="[
          'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700',
          notificationTypeClasses(notification.type)
        ]"
        role="alert"
      >
        <i :class="['design text-xl', notificationIconClasses(notification.type)]"></i>
        <div class="flex-1">
          <strong class="block text-sm font-medium text-gray-900 dark:text-white">{{ notification.title }}</strong>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ notification.message }}</p>
          <div v-if="notification.actions" class="mt-2 flex space-x-2">
            <button 
              v-for="action in notification.actions" 
              :key="action.id"
              @click="handleAction(notification.id, action.id)"
              class="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
        <button 
          class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          @click="$emit('remove', notification.id)"
          aria-label="Close notification"
        >
          <i class="design design-close-line"></i>
        </button>

        <!-- Auto-dismiss progress bar -->
        <div 
          v-if="notification.autoDismiss" 
          class="absolute bottom-0 left-0 h-1 bg-gray-300 dark:bg-gray-600 transition-all"
          :style="{ width: getProgressWidth(notification), 'transition-duration': `${notification.timeout}ms` }"
        ></div>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  notifications: {
    type: Array,
    required: true
  }
})

// Emit events
const emit = defineEmits(['remove', 'action'])

// Track notification entry times
const notificationTimes = ref({})

// Helper functions for dynamic classes
const notificationTypeClasses = (type) => {
  const classes = {
    success: 'border-l-4 border-l-green-500',
    error: 'border-l-4 border-l-red-500',
    info: 'border-l-4 border-l-blue-500',
    warning: 'border-l-4 border-l-amber-500'
  }
  
  return classes[type] || ''
}

const notificationIconClasses = (type) => {
  const classes = {
    success: 'design-check-line text-green-500',
    error: 'design-error-warning-line text-red-500', 
    info: 'design-information-line text-blue-500',
    warning: 'design-alert-line text-amber-500'
  }
  
  return classes[type] || ''
}

// Handle notification action click
const handleAction = (notificationId, actionId) => {
  emit('action', { notificationId, actionId })
  // Optionally automatically close the notification after action
  emit('remove', notificationId)
}

// Calculate progress bar width
const getProgressWidth = (notification) => {
  const startTime = notificationTimes.value[notification.id]
  if (!startTime) return '100%'
  
  const elapsed = Date.now() - startTime
  const remaining = Math.max(0, 1 - (elapsed / notification.timeout))
  return `${remaining * 100}%`
}

// Track notification appearance times for progress bars
onMounted(() => {
  props.notifications.forEach(notification => {
    if (!notificationTimes.value[notification.id]) {
      notificationTimes.value[notification.id] = Date.now()
    }
  })
})

// Update notification times when new notifications appear
const trackNewNotifications = () => {
  props.notifications.forEach(notification => {
    if (!notificationTimes.value[notification.id]) {
      notificationTimes.value[notification.id] = Date.now()
    }
  })
}

// Watch for new notifications
let observer = null
onMounted(() => {
  if (typeof MutationObserver !== 'undefined') {
    observer = new MutationObserver(trackNewNotifications)
    observer.observe(document.querySelector('[aria-live="polite"]'), {
      childList: true,
      subtree: true
    })
  }
})

onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect()
  }
})
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out forwards;
}
</style> 