import { ref, onMounted, watch } from 'vue'

/**
 * Composable for managing theme state and preferences
 * Handles light/dark theme with system preference detection and local storage persistence
 * @returns {Object} containing activeTheme ref and methods
 */
export function useTheme() {
  const activeTheme = ref('light')

  // Store theme preference in localStorage
  const saveThemePreference = (theme) => {
    localStorage.setItem('theme', theme)
  }

  // Watch for theme changes and persist them
  watch(activeTheme, saveThemePreference)

  // Initialize theme from saved preference or system setting
  onMounted(() => {
    // Check for stored theme preference
    const storedTheme = localStorage.getItem('theme')
    if (storedTheme) {
      activeTheme.value = storedTheme
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      activeTheme.value = prefersDark ? 'dark' : 'light'
    }

    // Optional: Add system theme change listener
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) { // Only auto-switch if user hasn't set a preference
        activeTheme.value = e.matches ? 'dark' : 'light'
      }
    })
  })

  return {
    activeTheme,
    setTheme: (theme) => {
      activeTheme.value = theme
    }
  }
} 