import { ref, onMounted, onUnmounted } from 'vue'

/**
 * Composable for simulating progress with random updates
 * @param {Object} options - Configuration options
 * @param {Number} options.interval - Update interval in ms (default: 1000)
 * @param {Number} options.minIncrement - Minimum progress increment (default: 1)
 * @param {Number} options.maxIncrement - Maximum progress increment (default: 5)
 * @param {Number} options.messageChangeChance - Probability of changing message (0-1) (default: 0.3)
 * @returns {Object} Progress state and controls
 */
export function useProgress(options = {}) {
  const {
    interval = 1000,
    minIncrement = 1,
    maxIncrement = 5,
    messageChangeChance = 0.3,
  } = options

  const progress = ref(0)
  const currentStatus = ref('Initializing...')
  let intervalId = null

  const statusMessages = [
    'Checking system configuration...',
    'Loading necessary components...',
    'Preparing installation...',
    'Almost there...',
    'Just a few more moments...',
    'Still working...',
    'This might take a while...',
  ]

  const updateProgress = () => {
    try {
      // Randomly increment progress by minIncrement-maxIncrement
      const increment = Math.floor(Math.random() * (maxIncrement - minIncrement + 1)) + minIncrement
      progress.value = Math.min(progress.value + increment, 99) // Never reach 100%
      
      // Randomly update status message based on messageChangeChance
      if (Math.random() < messageChangeChance) {
        const randomIndex = Math.floor(Math.random() * statusMessages.length)
        currentStatus.value = statusMessages[randomIndex]
      }
    } catch (error) {
      console.error('Error in updateProgress:', error)
    }
  }

  const start = () => {
    try {
      if (intervalId) return
      progress.value = 0
      currentStatus.value = 'Initializing...'
      intervalId = setInterval(updateProgress, interval)
    } catch (error) {
      console.error('Error starting progress:', error)
    }
  }

  const stop = () => {
    try {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    } catch (error) {
      console.error('Error stopping progress:', error)
    }
  }

  const reset = () => {
    try {
      stop()
      progress.value = 0
      currentStatus.value = 'Initializing...'
    } catch (error) {
      console.error('Error resetting progress:', error)
    }
  }

  onMounted(() => {
    try {
      start()
    } catch (error) {
      console.error('Error in onMounted:', error)
    }
  })

  onUnmounted(() => {
    try {
      stop()
    } catch (error) {
      console.error('Error in onUnmounted:', error)
    }
  })

  return {
    progress,
    currentStatus,
    start,
    stop,
    reset
  }
} 