import { ref, onMounted, onUnmounted, watch } from 'vue'

/**
 * Composable for observing element size changes
 * @param {Object} elementRef - Vue ref containing the element to observe
 * @returns {Object} - Width and height refs that update when the element size changes
 */
export function useResizeObserver(elementRef) {
  const width = ref(0)
  const height = ref(0)
  let observer = null
  
  // Helper to get the actual DOM element from a ref (handles Vue component refs)
  const getDOMElement = (ref) => {
    if (!ref) return null
    
    // If it's a Vue component instance with $el, use that
    if (ref.$el) return ref.$el
    
    // If it's already a DOM element, use it directly
    if (ref instanceof Element) return ref
    
    // Return null for invalid refs
    return null
  }
  
  // Function to safely disconnect any existing observer
  const safelyDisconnect = () => {
    try {
      if (observer) {
        observer.disconnect()
        observer = null
      }
    } catch (error) {
      console.error('Error disconnecting ResizeObserver:', error)
    }
  }
  
  // Function to set up the observer
  const setupObserver = () => {
    try {
      // Safely clean up first
      safelyDisconnect()
      
      // Get the DOM element from the ref
      const element = getDOMElement(elementRef.value)
      
      // Make sure we have a valid element to observe
      if (!element) {
        console.warn('ResizeObserver: No valid DOM element found to observe')
        return
      }
      
      // Create a new observer
      observer = new ResizeObserver(entries => {
        try {
          if (!entries || entries.length === 0) return
          
          const entry = entries[0]
          
          // Handle different browser implementations of ResizeObserver
          if (entry.contentRect) {
            // Standard implementation
            width.value = entry.contentRect.width
            height.value = entry.contentRect.height
          } else if (entry.contentBoxSize) {
            // Firefox implementation
            const contentBoxSize = Array.isArray(entry.contentBoxSize) 
              ? entry.contentBoxSize[0] 
              : entry.contentBoxSize
            
            width.value = contentBoxSize.inlineSize
            height.value = contentBoxSize.blockSize
          } else if (entry.target) {
            // Fallback to getBoundingClientRect
            const rect = entry.target.getBoundingClientRect()
            width.value = rect.width
            height.value = rect.height
          }
        } catch (error) {
          console.error('Error in ResizeObserver callback:', error)
        }
      })
      
      // Start observing
      observer.observe(element)
    } catch (error) {
      console.error('Error setting up ResizeObserver:', error)
    }
  }
  
  // Watch for changes to the element reference
  watch(() => elementRef.value, (newVal, oldVal) => {
    try {
      // If old element exists, disconnect observer from it
      if (oldVal) {
        const oldElement = getDOMElement(oldVal)
        if (oldElement && observer) {
          try {
            observer.unobserve(oldElement)
          } catch (e) {
            // Ignore errors when unobserving
          }
        }
      }
      
      // If new element exists, set up observer again
      if (newVal) {
        setupObserver()
      }
    } catch (error) {
      console.error('Error in elementRef watcher:', error)
    }
  }, { immediate: false })
  
  // Set up on component mount
  onMounted(() => {
    try {
      if (elementRef.value) {
        // Use nextTick to ensure the component is fully mounted
        setTimeout(() => {
          setupObserver()
        }, 0)
      }
    } catch (error) {
      console.error('Error in onMounted:', error)
    }
  })
  
  // Clean up on component unmount
  onUnmounted(() => {
    try {
      safelyDisconnect()
    } catch (error) {
      console.error('Error in onUnmounted:', error)
    }
  })
  
  return {
    width,
    height
  }
} 