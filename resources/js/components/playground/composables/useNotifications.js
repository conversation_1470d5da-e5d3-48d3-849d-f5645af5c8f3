import { ref } from 'vue'

/**
 * Composable for managing notification system
 * Provides functionality to add, remove, and access notifications
 * @param {Object} options - Configuration options
 * @param {Number} options.timeout - Default timeout for notifications in ms (default: 5000)
 * @returns {Object} notifications utilities
 */
export function useNotifications(options = {}) {
  const { timeout = 5000 } = options
  const notifications = ref([])
  let notificationId = 0

  /**
   * Add a new notification
   * @param {String} type - Notification type ('success', 'error', 'info', etc.)
   * @param {String} title - Notification title
   * @param {String} message - Notification message
   * @param {String} icon - Optional icon class
   * @returns {Number} The ID of the created notification
   */
  const addNotification = (type, title, message, icon) => {
    const id = notificationId++
    const notification = { id, type, title, message, icon, createdAt: new Date() }
    
    notifications.value.push(notification)
    
    // Auto-remove after timeout if positive
    if (timeout > 0) {
      setTimeout(() => removeNotification(id), timeout)
    }
    
    return id
  }

  /**
   * Remove a notification by ID
   * @param {Number} id - Notification ID to remove
   */
  const removeNotification = (id) => {
    notifications.value = notifications.value.filter(n => n.id !== id)
  }

  /**
   * Clear all notifications
   */
  const clearNotifications = () => {
    notifications.value = []
  }

  // Helper methods for common notification types
  const success = (title, message, icon = 'design-check-line') => 
    addNotification('success', title, message, icon)
  
  const error = (title, message, icon = 'design-error-warning-line') => 
    addNotification('error', title, message, icon)
  
  const info = (title, message, icon = 'design-information-line') => 
    addNotification('info', title, message, icon)
  
  const warning = (title, message, icon = 'design-alert-line') => 
    addNotification('warning', title, message, icon)

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    success,
    error,
    info,
    warning
  }
} 