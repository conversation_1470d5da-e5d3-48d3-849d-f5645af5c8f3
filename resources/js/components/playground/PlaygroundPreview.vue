<template>
  <div class="h-full flex flex-col overflow-hidden bg-gray-900">
    <!-- Preview Content Area -->
    <div class="flex-1 overflow-auto bg-white dark:bg-gray-900 transition-colors duration-300 relative">
      <!-- Refresh Overlay -->
      <div v-if="isRefreshing" class="absolute inset-0 bg-gray-900/20 dark:bg-gray-800/40 backdrop-blur-sm z-10 flex items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 flex items-center">
          <i class="design design-refresh text-blue-500 animate-spin mr-2 text-xl"></i>
          <span class="text-gray-700 dark:text-gray-300">Refreshing...</span>
        </div>
      </div>
      
      <!-- Device frame with transition effects -->
      <div 
        class="transition-all duration-300 bg-white dark:bg-gray-800 mx-auto my-4 shadow-xl rounded-lg overflow-hidden" 
        :class="{
          'w-full max-w-[1200px]': activeDevice === 'desktop',
          'w-full max-w-[768px]': activeDevice === 'tablet',
          'w-full max-w-[375px]': activeDevice === 'mobile'
        }"
      >
        <!-- Preview Content - Will be the application preview -->
        <div class="relative demo-frame p-6">
          <!-- Browser-like application view -->
          <div class="app-mockup">
            <!-- App Header Bar -->
            <div class="app-header flex justify-between items-center p-4 bg-blue-600 dark:bg-blue-700 text-white rounded-t-lg">
              <div class="flex items-center">
                <i class="design design-code-box-line text-xl mr-2"></i>
                <h3 class="font-medium">My Application</h3>
              </div>
              <div class="flex items-center space-x-3">
                <button class="text-white/80 hover:text-white">
                  <i class="design design-user-line"></i>
                </button>
                <button class="text-white/80 hover:text-white">
                  <i class="design design-settings-4-line"></i>
                </button>
                <button class="text-white/80 hover:text-white">
                  <i class="design design-menu-line"></i>
                </button>
              </div>
            </div>
            
            <!-- App Content Area -->
            <div class="app-content p-6 bg-white dark:bg-gray-800 min-h-[300px]">
              <div class="card bg-white dark:bg-gray-700 rounded-lg shadow-md p-5 max-w-lg mx-auto">
                <div class="card-header mb-5">
                  <h3 class="text-xl font-bold dark:text-white mb-2">Create project</h3>
                  <p class="text-gray-500 dark:text-gray-300">Deploy your new project in one-click.</p>
                </div>

                <div class="form-group mb-4">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Project name</label>
                  <div class="input-wrapper relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                      <i class="design design-folder-add-line"></i>
                    </span>
                    <input 
                      type="text"
                      placeholder="my-awesome-project"
                      class="w-full py-2 pl-10 pr-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                    />
                  </div>
                </div>

                <div class="form-group mb-5">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Framework</label>
                  <div class="input-wrapper relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                      <i class="design design-code-box-line"></i>
                    </span>
                    <select 
                      class="w-full py-2 pl-10 pr-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 appearance-none"
                    >
                      <option value="next">Next.js</option>
                      <option value="vue">Vue</option>
                      <option value="nuxt">Nuxt.js</option>
                      <option value="react">React</option>
                    </select>
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
                      <i class="design design-arrow-down-s-line"></i>
                    </span>
                  </div>
                </div>

                <div class="card-footer flex justify-end space-x-3">
                  <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md flex items-center text-gray-700 dark:text-gray-300">
                    <i class="design design-close-circle-line mr-1"></i>
                    Cancel
                  </button>
                  <button class="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center hover:bg-blue-700">
                    <i class="design design-rocket-line mr-1"></i>
                    Deploy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Status Bar -->
    <div class="preview-status-bar px-4 py-1 flex justify-between items-center bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
      <div class="flex items-center space-x-4">
        <span>Preview Mode: Live</span>
        <span>{{ activeDevice }}</span>
      </div>
      <div class="flex items-center space-x-4">
        <span>{{ activeTheme }} Theme</span>
        <span class="flex items-center">
          <i class="design design-check-line text-green-500 mr-1"></i> 
          Connected
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  activeDevice: {
    type: String,
    required: true
  },
  activeTheme: {
    type: String,
    required: true
  },
  progress: {
    type: Number,
    required: true
  },
  currentStatus: {
    type: String,
    required: true
  },
  isRefreshing: {
    type: Boolean,
    default: false
  }
})

// Emit events for interaction with parent
const emit = defineEmits(['dimension-change'])

// When mounted, trigger a dimension update
onMounted(() => {
  nextTick(() => {
    emit('dimension-change')
  })
})

// Watch for device changes to recalculate dimensions
watch(() => props.activeDevice, () => {
  nextTick(() => {
    emit('dimension-change')
  })
})
</script>

<style scoped>
/* Simple transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Device-specific styles maintained for responsive behavior */
.demo-frame {
  transition: all 0.3s ease;
}

/* Status bar styles */
.preview-status-bar {
  height: 22px;
  font-size: 0.75rem;
}
</style> 