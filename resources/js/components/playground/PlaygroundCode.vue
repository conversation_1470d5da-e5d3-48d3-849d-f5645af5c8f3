<template>
  <div class="relative h-full flex overflow-hidden bg-gray-900">
    <!-- File Explorer Panel -->
    <div class="file-explorer w-64 flex-shrink-0 border-r border-gray-700 flex flex-col overflow-hidden bg-slate-900/95">
      <!-- Explorer Header -->
      <div class="p-3 border-b border-gray-700 text-gray-300 flex items-center justify-between bg-slate-900/95">
        <div class="text-sm font-medium uppercase gant-modern-medium"><i class="design design-design_invert text-lg"></i> Explorer</div>
        <div class="flex space-x-2">
          <button class="text-gray-400 hover:text-white" title="New File" @click="showNewFileModal = true">
            <i class="design design-document text-lg"></i>
          </button>
          <button class="text-gray-400 hover:text-white" title="New Folder">
            <i class="design design-folder text-lg"></i>
          </button>
          <button class="text-gray-400 hover:text-white" title="Download File" @click="downloadCurrentFile">
            <i class="design design-playground-download text-lg"></i>
          </button>
          <button class="text-gray-400 hover:text-white" title="Refresh">
            <i class="design design-refresh text-lg"></i>
          </button>
        </div>
      </div>
      
      <!-- Files Tree with Collapsible Folders -->
      <div class="flex-1 overflow-y-auto">
        <div class="project-tree py-2">
          <!-- Project Root -->
          <div 
            class="folder-item px-3 py-1 text-gray-300 flex items-center"
            @click="toggleFolder('root')"
          >
            <i class="design mr-2 text-lg" :class="[
              folderStates.root ? 'design-playground-folder-oped text-blue-400' : 'design-playground-folder-closed text-blue-400'
            ]"></i>
            <span class="text-sm font-medium gant-modern-medium">my-project</span>
          </div>
          
          <div v-if="folderStates.root">
            <!-- Source Folder -->
            <div 
              class="folder-item pl-6 pr-3 py-1 text-gray-300 flex items-center"
              @click="toggleFolder('src')"
            >
              <i class="design mr-2 text-lg" :class="[
                folderStates.src ? 'design-playground-folder-oped text-yellow-500' : 'design-playground-folder-closed text-yellow-500'
              ]"></i>
              <span class="text-sm gant-modern-medium">src</span>
            </div>
            
            <div v-if="folderStates.src">
              <!-- Components Folder -->
              <div 
                class="folder-item pl-9 pr-3 py-1 text-gray-300 flex items-center"
                @click="toggleFolder('components')"
              >
                <i class="design mr-2 text-lg" :class="[
                  folderStates.components ? 'design-playground-folder-oped text-yellow-500' : 'design-playground-folder-closed text-yellow-500'
                ]"></i>
                <span class="text-sm gant-modern-medium">components</span>
              </div>
              
              <div v-if="folderStates.components">
                <!-- Component Files -->
                <div 
                  v-for="file in componentFiles" 
                  :key="file.name"
                  class="file-item pl-12 pr-3 py-1 text-gray-300 flex items-center"
                  :class="{'bg-gray-800': activeFile === file.name}"
                  @click="openFile(file)"
                >
                  <i :class="['design mr-2 text-lg', file.icon]"></i>
                  <span class="text-sm">{{ file.name }}</span>
                </div>
              </div>
              
              <!-- Other Source Files -->
              <div 
                class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center" 
                :class="{'bg-gray-800': activeFile === 'App.vue'}"
                @click="openFile({name: 'App.vue', language: 'vue', icon: 'design-playground-file-code text-green-500'})"
              >
                <i class="design design-playground-file-code text-green-500 mr-2 text-lg"></i>
                <span class="text-sm">App.vue</span>
              </div>
              
              <div 
                class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center"
                :class="{'bg-gray-800': activeFile === 'main.js'}"
                @click="openFile({name: 'main.js', language: 'javascript', icon: 'design-playground-file-code text-yellow-400'})"
              >
                <i class="design design-playground-file-code text-yellow-400 mr-2 text-lg"></i>
                <span class="text-sm">main.js</span>
              </div>
            </div>
            
            <!-- Assets Folder -->
            <div 
              class="folder-item pl-6 pr-3 py-1 text-gray-300 flex items-center"
              @click="toggleFolder('assets')"
            >
              <i class="design mr-2 text-lg" :class="[
                folderStates.assets ? 'design-playground-folder-oped text-yellow-500' : 'design-playground-folder-closed text-yellow-500'
              ]"></i>
              <span class="text-sm gant-modern-medium">assets</span>
            </div>
            
            <div v-if="folderStates.assets">
              <div class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center">
                <i class="design design-images mr-2 text-lg text-purple-400"></i>
                <span class="text-sm">logo.svg</span>
              </div>
              <div class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center">
                <i class="design design-playground-file-text mr-2 text-lg text-blue-300"></i>
                <span class="text-sm">styles.css</span>
              </div>
            </div>
            
            <!-- Public Folder -->
            <div 
              class="folder-item pl-6 pr-3 py-1 text-gray-300 flex items-center"
              @click="toggleFolder('public')"
            >
              <i class="design mr-2 text-lg" :class="[
                folderStates.public ? 'design-playground-folder-oped text-yellow-500' : 'design-playground-folder-closed text-yellow-500'
              ]"></i>
              <span class="text-sm gant-modern-medium">public</span>
            </div>
            
            <div v-if="folderStates.public">
              <div class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center">
                <i class="design design-playground-file-code mr-2 text-lg text-orange-500"></i>
                <span class="text-sm">index.html</span>
              </div>
              <div class="file-item pl-9 pr-3 py-1 text-gray-300 flex items-center">
                <i class="design design-images mr-2 text-lg text-purple-400"></i>
                <span class="text-sm">favicon.ico</span>
              </div>
            </div>
            
            <!-- Config Files -->
            <div 
              class="file-item pl-6 pr-3 py-1 text-gray-300 flex items-center"
              :class="{'bg-gray-800': activeFile === 'package.json'}"
              @click="openFile({name: 'package.json', language: 'json', icon: 'design-playground-file-text text-gray-400'})"
            >
              <i class="design design-playground-file-text mr-2 text-lg text-gray-400"></i>
              <span class="text-sm">package.json</span>
            </div>
            
            <div class="file-item pl-6 pr-3 py-1 text-gray-300 flex items-center">
              <i class="design design-playground-file-text mr-2 text-lg text-gray-400"></i>
              <span class="text-sm">README.md</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Explorer Panel Footer -->
      <div class="border-t border-gray-700 py-1 px-3 text-xs text-gray-500 flex items-center justify-between gant-modern-regular">
        <span>{{ projectFiles.length }} files</span>
        <button class="text-gray-400 hover:text-white" title="Collapse All">
          <i class="design design-arrow-left text-sm"></i>
        </button>
      </div>
    </div>
    
    <!-- Code Editor Panel -->
    <div class="flex-1 flex flex-col overflow-hidden gant-modern-regular">
      <!-- Editor Tabs Bar -->
      <div class="tabs-bar flex border-b border-slate-900/95 overflow-x-auto bg-slate-900/95">
        <div 
          v-for="tab in openTabs" 
          :key="tab.name"
          class="tab px-4 py-2 text-white border-r border-gray-700 flex items-center whitespace-nowrap"
          :class="{'bg-gray-900': tab.name === activeFile, 'bg-slate-900/95': tab.name !== activeFile}"
          @click="activeFile = tab.name"
        >
          <i :class="['design mr-2 text-sm', tab.icon]"></i>
          <span class="text-sm gant-modern-medium">{{ tab.name }}</span> 
          <div class="ml-2 flex items-center">
            <button class="text-gray-500 hover:text-white mr-1" @click.stop="downloadFile(tab)" title="Download file">
              <i class="design design-playground-download text-xs"></i>
            </button>
            <button class="text-gray-500 hover:text-white" @click.stop="closeTab(tab)" title="Close tab">
              <i class="design design-close text-xs"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Editor Controls Bar -->
      <div class="editor-controls px-4 py-2 flex justify-between items-center border-b border-gray-700 ">
        <div class="flex space-x-3">
          <button 
            class="px-2 py-1 bg-gray-700 rounded text-gray-300 hover:bg-gray-600 flex items-center text-xs gant-modern-medium"
            @click="$emit('copy-code')"
          >
            <i class="design mr-1 text-sm" :class="copied ? 'design-playground-copy text-green-400' : 'design-playground-copy text-white'"></i>
            {{ copied ? 'Copied!' : 'Copy Code' }}
          </button>
          
          <button class="px-2 py-1 bg-gray-700 rounded text-gray-300 hover:bg-gray-600 flex items-center text-xs gant-modern-medium" @click="toggleLineNumbers">
            <i class="design design-check-list mr-1 text-sm"></i>
            {{ showLineNumbers ? 'Hide' : 'Show' }} Line Numbers
          </button>
        </div>
        
        <div class="flex items-center space-x-2">
          <!-- Search in file button -->
          <button 
            class="p-1 text-gray-400 hover:text-white" 
            title="Search in file (Ctrl+F)"
            @click="showSearch = !showSearch"
          >
            <i class="design design-search-fill text-lg"></i>
          </button>
          
          <span class="text-xs text-gray-500 gant-modern-regular">
            {{ activeFileData.language || language }} · UTF-8
          </span>
        </div>
      </div>
      
      <!-- Search Bar (toggleable) -->
      <div v-if="showSearch" class="search-bar px-4 py-2 flex items-center space-x-2 bg-gray-800 border-b border-gray-700">
        <i class="design design-search-fill text-gray-400"></i>
        <input 
          ref="searchInput"
          v-model="searchQuery" 
          type="text" 
          class="flex-1 bg-gray-700 text-white text-sm px-2 py-1 rounded border border-gray-600 focus:border-blue-500 focus:outline-none gant-modern-regular"
          placeholder="Search"
        />
        <span class="text-xs text-gray-400 gant-modern-regular">{{ searchMatches.length }} matches</span>
        <button class="p-1 text-gray-400 hover:text-white" title="Previous match">
          <i class="design design-arrow-up text-sm"></i>
        </button>
        <button class="p-1 text-gray-400 hover:text-white" title="Next match">
          <i class="design design-arrow-down text-sm"></i>
        </button>
        <button class="p-1 text-gray-400 hover:text-white" title="Close search" @click="showSearch = false">
          <i class="design design-close text-sm"></i>
        </button>
      </div>
      
      <!-- Code Content Area -->
      <div class="code-content-area flex-1 overflow-hidden">
        <!-- Main Editor View -->
        <div class="editor-container flex-1 relative h-full overflow-hidden">
          <!-- Code Content with syntax highlight -->
          <div class="relative h-full flex overflow-hidden">
            <!-- Line numbers gutter -->
            <div 
              v-if="showLineNumbers" 
              ref="lineNumbers"
              class="line-numbers py-4 px-2 text-right select-none text-gray-500 bg-gray-900 border-r border-gray-700 flex-shrink-0 overflow-y-auto"
              @scroll="syncScroll"
            >
              <div 
                v-for="i in totalLines" 
                :key="i" 
                class="line-number relative"
                :class="{'has-error': lineHasError(i), 'has-warning': lineHasWarning(i)}"
              >
                {{ i }}
              </div>
            </div>
            
            <!-- Main code area -->
            <pre 
              ref="codeContainer"
              class="code-content flex-1 p-0 m-0 text-gray-100 font-mono leading-relaxed h-full overflow-auto"
              @scroll="syncScroll"
            >
              <code 
                ref="codeElement"
                :class="['language-' + (activeFileData.language || language)]" 
                class="block p-4 overflow-visible whitespace-pre w-full"
              >{{ activeFileData.content || demoCode }}</code>
            </pre>
          </div>
        </div>
      </div>
      
      <!-- Enhanced Status Bar -->
      <div class="status-bar px-4 py-1 flex justify-between items-center bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div class="flex items-center space-x-4 gant-modern-regular">
          <button class="flex items-center hover:text-white" title="Go Back" @click="goBack">
            <i class="design design-arrow-left mr-1"></i>
            <span>Back</span>
          </button>
          <span>Ln {{ cursorPosition.line }}, Col {{ cursorPosition.column }}</span>
          <span>Spaces: 2</span>
          <div class="flex items-center">
            <i class="design design-github mr-1"></i>
            <span>main</span>
          </div>
        </div>
        <div class="flex items-center space-x-4 gant-modern-regular">
          <div v-if="errors.length > 0 || warnings.length > 0" class="flex items-center space-x-1">
            <span v-if="errors.length > 0" class="text-red-500 flex items-center">
              <i class="design design-error-404 mr-1"></i>
              {{ errors.length }}
            </span>
            <span v-if="warnings.length > 0" class="text-yellow-500 flex items-center">
              <i class="design design-error-500 mr-1"></i>
              {{ warnings.length }}
            </span>
          </div>
          <span>{{ activeFileData.language || language }}</span>
          <span>UTF-8</span>
          <span>LF</span>
        </div>
      </div>
    </div>
    
    <!-- New File Modal -->
    <div v-if="showNewFileModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-gray-800 rounded-lg shadow-xl w-96 overflow-hidden">
        <div class="p-4 border-b border-gray-700">
          <h3 class="text-lg font-medium text-white gant-modern-bold">New File</h3>
        </div>
        <div class="p-4">
          <input 
            v-model="newFileName" 
            type="text" 
            class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none gant-modern-regular"
            placeholder="Filename with extension..."
          />
        </div>
        <div class="p-4 border-t border-gray-700 flex justify-end space-x-2">
          <button 
            class="px-4 py-2 text-gray-300 hover:text-white gant-modern-medium"
            @click="showNewFileModal = false"
          >
            Cancel
          </button>
          <button 
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 gant-modern-medium"
            @click="createNewFile"
          >
            Create
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import Prism from 'prismjs'
// Import Prism language components
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-tsx'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-yaml'
import 'prismjs/components/prism-markdown'
// If you want to support Vue specifically, you'd need a custom Vue language definition for Prism

const props = defineProps({
  demoCode: {
    type: String,
    required: true
  },
  copied: {
    type: Boolean,
    default: false
  },
  language: {
    type: String,
    default: 'markup'
  },
  fileName: {
    type: String,
    default: 'PlaygroundDemo.vue'
  }
})

defineEmits(['copy-code'])

// Editor UI state
const showLineNumbers = ref(true)
const showSearch = ref(false)
const searchQuery = ref('')
const searchMatches = ref([])
const searchInput = ref(null)
const codeElement = ref(null)
const cursorPosition = ref({ line: 1, column: 1 })
const activeFile = ref(props.fileName)
const showNewFileModal = ref(false)
const newFileName = ref('')

// File explorer state
const folderStates = ref({
  root: true,
  src: true,
  components: true,
  assets: false,
  public: false
})

// Toggle folder expand/collapse
const toggleFolder = (folder) => {
  folderStates.value[folder] = !folderStates.value[folder]
}

// Demo files data - with properly escaped script tags
const componentFiles = [
  {
    name: props.fileName,
    language: 'vue',
    icon: 'design-playground-file-code text-green-500',
    content: props.demoCode
  },
  {
    name: 'Button.vue',
    language: 'vue',
    icon: 'design-playground-file-code text-green-500',
    content: `<template>
  <button 
    :class="[
      'px-4 py-2 rounded-md flex items-center transition-colors',
      variantClasses,
      { 'opacity-50 cursor-not-allowed': disabled }
    ]"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <i v-if="icon" :class="['design mr-2', icon]"></i>
    <slot></slot>
  </button>
</template>

<script ${"setup"}>
const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: value => ['primary', 'secondary', 'danger', 'ghost'].includes(value)
  },
  icon: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'primary':
      return 'bg-blue-600 hover:bg-blue-700 text-white'
    case 'secondary':
      return 'bg-gray-200 hover:bg-gray-300 text-gray-800'
    case 'danger':
      return 'bg-red-600 hover:bg-red-700 text-white'
    case 'ghost':
      return 'bg-transparent hover:bg-gray-100 text-gray-700'
    default:
      return 'bg-blue-600 hover:bg-blue-700 text-white'
  }
})

defineEmits(['click'])
</${"script"}>`
  },
  {
    name: 'Card.vue',
    language: 'vue',
    icon: 'design-playground-file-code text-green-500',
    content: `<template>
  <div class="card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div v-if="$slots.header" class="card-header p-4 border-b border-gray-200 dark:border-gray-700">
      <slot name="header"></slot>
    </div>
    <div class="card-body p-4">
      <slot></slot>
    </div>
    <div v-if="$slots.footer" class="card-footer p-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer"></slot>
    </div>
  </div>
</template>`
  }
]

// All project files for reference
const projectFiles = computed(() => [
  ...componentFiles,
  {
    name: 'App.vue',
    language: 'vue',
    icon: 'design-playground-file-code text-green-500',
    content: `<template>
  <div class="app">
    <header>
      <h1>My Vue App</h1>
      <nav>
        <router-link to="/">Home</router-link>
        <router-link to="/about">About</router-link>
      </nav>
    </header>
    <main>
      <router-view />
    </main>
  </div>
</template>`
  },
  {
    name: 'main.js',
    language: 'javascript',
    icon: 'design-playground-file-code text-yellow-400',
    content: `import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './assets/styles.css'

const app = createApp(App)
app.use(router)
app.mount('#app')`
  },
  {
    name: 'package.json',
    language: 'json',
    icon: 'design-playground-file-text text-gray-400',
    content: `{
  "name": "my-vue-app",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.2.41",
    "vue-router": "^4.1.6"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^3.2.0",
    "vite": "^3.2.0"
  }
}`
  }
])

// Currently open tabs
const openTabs = ref([
  {
    name: props.fileName,
    language: 'vue',
    icon: 'design-playground-file-code text-green-500'
  }
])

// Open a file in the editor
const openFile = (file) => {
  activeFile.value = file.name
  
  // Check if the tab is already open
  const tabExists = openTabs.value.some(tab => tab.name === file.name)
  
  if (!tabExists) {
    openTabs.value.push({
      name: file.name,
      language: file.language,
      icon: file.icon
    })
  }
  
  // Update highlighting after file change
  nextTick(() => {
    highlightCode()
  })
}

// Close a tab
const closeTab = (tab) => {
  // Don't allow closing all tabs
  if (openTabs.value.length <= 1) return
  
  const index = openTabs.value.findIndex(t => t.name === tab.name)
  if (index !== -1) {
    openTabs.value.splice(index, 1)
    
    // If we closed the active tab, switch to another one
    if (activeFile.value === tab.name) {
      activeFile.value = openTabs.value[0].name
    }
  }
}

// Get currently active file data
const activeFileData = computed(() => {
  return projectFiles.value.find(file => file.name === activeFile.value) || { 
    name: props.fileName,
    language: props.language,
    content: props.demoCode
  }
})

// Toggle line numbers
const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

// Calculate total lines for line numbers
const totalLines = computed(() => {
  return (activeFileData.value.content || props.demoCode).split('\n').length
})

// Create and add a new file (demo only)
const createNewFile = () => {
  if (!newFileName.value.trim()) return
  
  // Add the new file with basic content
  projectFiles.value.push({
    name: newFileName.value,
    language: newFileName.value.endsWith('.vue') ? 'vue' : 
              newFileName.value.endsWith('.js') ? 'javascript' : 
              newFileName.value.endsWith('.css') ? 'css' : 'markup',
    icon: newFileName.value.endsWith('.vue') ? 'design-playground-file-code text-green-500' :
          newFileName.value.endsWith('.js') ? 'design-playground-file-code text-yellow-400' : 
          newFileName.value.endsWith('.css') ? 'design-css text-blue-400' : 'design-playground-file-text text-gray-400',
    content: `// ${newFileName.value} - New file created in playground`
  })
  
  // Open the new file
  openFile(projectFiles.value[projectFiles.value.length - 1])
  
  // Reset and close the modal
  showNewFileModal.value = false
  newFileName.value = ''
}

// Demo errors/warnings for specific lines
const errors = ref([
  { line: 15, message: 'Property "variantClasses" does not exist on type' }
])

const warnings = ref([
  { line: 28, message: 'Unused variable' }
])

// Check if a line has errors or warnings
const lineHasError = (line) => errors.value.some(error => error.line === line)
const lineHasWarning = (line) => warnings.value.some(warning => warning.line === line)

// Refs for scroll synchronization
const lineNumbers = ref(null)
const codeContainer = ref(null)
let isScrolling = false

// Synchronize scrolling between line numbers and code content
const syncScroll = (event) => {
  if (isScrolling) return
  
  isScrolling = true
  
  const target = event.target
  const isLineNumbers = target === lineNumbers.value
  const scrollTarget = isLineNumbers ? codeContainer.value : lineNumbers.value
  
  if (scrollTarget) {
    scrollTarget.scrollTop = target.scrollTop
  }
  
  setTimeout(() => {
    isScrolling = false
  }, 50)
}

// Initialize with syntax highlighting
onMounted(() => {
  highlightCode()
  
  // Focus search input when search is shown
  watch(showSearch, () => {
    if (showSearch.value) {
      nextTick(() => {
        searchInput.value?.focus()
      })
    }
  })
  
  // Handle resize to update scroll positions
  window.addEventListener('resize', () => {
    if (codeContainer.value && lineNumbers.value) {
      lineNumbers.value.scrollTop = codeContainer.value.scrollTop
    }
  })
})

// Highlight code function using Prism.js
const highlightCode = () => {
  if (!codeElement.value) return
  
  const language = activeFileData.value.language || props.language
  const content = activeFileData.value.content || props.demoCode
  
  // Update content
  codeElement.value.textContent = content
  
  // Apply highlighting based on language
  if (Prism.languages[language]) {
    codeElement.value.innerHTML = Prism.highlight(
      content,
      Prism.languages[language],
      language
    )
  }
}

// Re-highlight when active file changes
watch(() => activeFile.value, () => {
  nextTick(highlightCode)
})

// Re-highlight when code changes
watch(() => props.demoCode, () => {
  nextTick(highlightCode)
})

// History for back navigation
const fileHistory = ref([])

// Download the current file
const downloadCurrentFile = () => {
  if (!activeFile.value) return
  
  const file = projectFiles.value.find(f => f.name === activeFile.value)
  if (!file) return
  
  downloadFile(file)
}

// Download a file
const downloadFile = (file) => {
  try {
    const content = file.content || ''
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    
    // Clean up
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    // Show notification
    // You could emit an event here to show a notification
  } catch (error) {
    console.error('Error downloading file:', error)
  }
}

// Go back in file history
const goBack = () => {
  if (fileHistory.value.length <= 1) return
  
  // Remove current file
  fileHistory.value.pop()
  
  // Get previous file
  const previousFile = fileHistory.value[fileHistory.value.length - 1]
  if (previousFile && previousFile !== activeFile.value) {
    activeFile.value = previousFile
  }
}

// Track file history when active file changes
watch(() => activeFile.value, (newFile, oldFile) => {
  if (newFile && newFile !== oldFile) {
    fileHistory.value.push(newFile)
  }
  
  nextTick(highlightCode)
})
</script>

<style scoped>
/* Global typography settings */
:deep(*) {
  font-family: 'gant-modern-regular', sans-serif;
}

/* Keep code areas in monospace */
.code-content,
.code-content code,
.line-numbers {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.gant-modern-regular {
  font-family: 'gant-modern-regular', sans-serif;
}

.gant-modern-medium {
  font-family: 'gant-modern-medium', sans-serif;
}

.gant-modern-bold {
  font-family: 'gant-modern-bold', sans-serif;
}

.file-explorer {
  min-width: 240px;
}

.folder-item, .file-item {
  cursor: pointer;
  font-family: 'gant-modern-regular', sans-serif;
}

.folder-item:hover, .file-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.folder-item span {
  font-family: 'gant-modern-medium', sans-serif;
}

.file-item span {
  font-family: 'gant-modern-regular', sans-serif;
}

.line-numbers {
  min-width: 3rem;
  overflow-y: auto;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.line-number {
  line-height: 1.6;
  font-size: 0.85rem;
}

.has-error::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3rem;
  width: 0.75rem;
  height: 0.75rem;
  background-color: #f43f5e;
  border-radius: 50%;
}

.has-warning::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3rem;
  width: 0.75rem;
  height: 0.75rem;
  background-color: #eab308;
  border-radius: 50%;
}

.code-content {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  tab-size: 2;
  overflow-y: auto;
  height: 100%;
  max-height: 100%;
}

.code-content code {
  white-space: pre;
  display: block;
  min-width: 100%;
  min-height: 100%;
}

/* VS Code-like theme for code */
:deep(.token.comment) {
  color: #6a9955;
}

:deep(.token.string) {
  color: #ce9178;
}

:deep(.token.number) {
  color: #b5cea8;
}

:deep(.token.keyword) {
  color: #569cd6;
}

:deep(.token.function) {
  color: #dcdcaa;
}

:deep(.token.operator) {
  color: #d4d4d4;
}

:deep(.token.punctuation) {
  color: #d4d4d4;
}

:deep(.token.class-name) {
  color: #4ec9b0;
}

:deep(.token.tag) {
  color: #569cd6;
}

:deep(.token.attr-name) {
  color: #9cdcfe;
}

:deep(.token.attr-value) {
  color: #ce9178;
}

/* Make sure the code container allows proper scrolling */
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Ensure proper scrolling */
.code-content-area {
  height: calc(100% - 80px); /* Adjust based on your header heights */
  min-height: 0;
  overflow: hidden;
}

.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.code-content {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  tab-size: 2;
  overflow-y: auto;
  height: 100%;
  max-height: 100%;
  flex: 1 1 auto;
}

.code-content code {
  white-space: pre;
  display: block;
  min-width: 100%;
}

/* UI elements use Gant Modern, but code stays monospace */
.tabs-bar, 
.editor-controls,
.search-bar,
.status-bar {
  font-family: 'gant-modern-regular', sans-serif;
}
</style> 