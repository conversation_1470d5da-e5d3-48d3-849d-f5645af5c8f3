<template>
  <div class="w-full h-full flex flex-col justify-between p-6" :class="[isCollapsed ? 'px-4' : 'px-12', 'transition-all duration-300']">
    <!-- Header with logo - different alignment based on state -->
    <div class="w-full align-center cursor-pointer" @click="toggleCollapse">
      <div :class="[
        'text-sm text-gray-900 dark:text-gray-100 mb-4',
        isCollapsed ? 'flex justify-center' : 'flex items-center'
      ]">
        <img 
          src="/img/symbol.svg" 
          :class="[isCollapsed ? 'h-7' : 'h-8', 'inline-block align-middle transition-all duration-300', !isCollapsed && 'me-2']" 
          alt="Vermont Logo"> 
        <template v-if="!isCollapsed">
          <span class="gant-modern-bold">Vermont</span>
          <i class="design design-arr ml-2 text-sm transition-transform duration-300 opacity-0 group-hover:opacity-100 rotate-180"></i>
        </template>
      </div>
    </div>
    
    <!-- Menu Content Area -->
    <div class="flex-grow flex flex-col justify-center">
      <!-- Logo - always visible -->
      <logo-section :is-collapsed="isCollapsed" :routes="routes" />
      
      <!-- Menu Items Container -->
      <div class="min-h-[300px] relative overflow-visible" :class="[isCollapsed ? 'w-12' : 'w-full']">
        <transition name="fade" mode="out-in">
          <!-- Subsections View -->
          <div v-if="activeSection" key="subsection" class="w-full">
            <back-button 
              :is-collapsed="isCollapsed" 
              :section="activeSection" 
              @click="resetActiveSection" 
            />
            
            <nav-item-list 
              :items="activeSection.subsections" 
              :is-collapsed="isCollapsed"
              :current-route="currentRoute"
              :section-type="Array.isArray(activeSection.routeIs) ? activeSection.routeIs[0] : activeSection.routeIs"
            />
            
            <!-- Additional Items in Subsection View -->
            <nav-item-list 
              :items="otherNavItems" 
              :is-collapsed="isCollapsed"
              :current-route="currentRoute"
              :csrf-token="csrfToken"
              :messages="messages"
              :routes="routes"
              in-subsection 
            />
            
            <!-- Language Switcher in Subsection View -->
            <div class="mt-4">
              <language-switcher></language-switcher>
            </div>
          </div>
          
          <!-- Main Menu -->
          <div v-else key="main-menu" class="w-full">
            <!-- Main Navigation Items with section-click handler -->
            <nav-item-list 
              :items="mainNavItems" 
              :is-collapsed="isCollapsed"
              :current-route="currentRoute"
              @section-click="setActiveSection"
            />
            
            <!-- Other Menu Items -->
            <nav-item-list 
              :items="otherNavItems" 
              :is-collapsed="isCollapsed"
              :current-route="currentRoute"
              :csrf-token="csrfToken"
              :messages="messages"
              :routes="routes"
              :user="props.user"
            />
            
            <!-- Language Switcher -->
            <div class="mt-4">
              <language-switcher></language-switcher>
            </div>
          </div>
        </transition>
      </div>
      
      <!-- Admin Badge Area -->
      <admin-badge-section :is-collapsed="isCollapsed" :is-admin="isAdmin" />
    </div>

    <!-- Footer - fixed changelog for collapsed mode -->
    <div class="w-full align-center">
      <a :href="'/changelog'" class="block">
        <p class="text-sm text-gray-900 dark:text-gray-100 mb-4 flex items-center cursor-pointer hover:text-primary-600 dark:hover:text-primary-500 transition-colors" 
           :class="{'justify-center': isCollapsed, 'gant-modern-bold': !isCollapsed}">
          <i class="design design-mn text-center" :class="{'mx-auto': isCollapsed}"></i>
          <span class="ml-2" v-if="!isCollapsed">{{ props.messages.changelog }}</span>
        </p>
      </a>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, shallowRef } from 'vue';
import LanguageSwitcher from './LanguageSwitcher.vue';
import DarkModeToggle from './DarkModeToggle.vue';
import { useSidebarCollapse } from '../composables/useSidebarCollapse';
import { useNavigation } from '../composables/useNavigation';
import LogoSection from './sidebar/LogoSection.vue';
import BackButton from './sidebar/BackButton.vue';
import NavItemList from './sidebar/NavItemList.vue';
import AdminBadgeSection from './sidebar/AdminBadgeSection.vue';

// Props with proper TypeScript types
const props = defineProps({
  messages: { type: Object, default: () => ({}) },
  currentRoute: { type: String, default: '' },
  csrfToken: { type: String, required: true },
  routes: { type: Object, default: () => ({}) },
  user: { type: Object, default: () => ({}) },
  adminUserLogins: { type: Array, default: () => [] },
  assetPrefix: { type: String, default: '' },
  previousUrl: { type: String, default: null }
});

// Use composables for sidebar state management
const { isCollapsed, toggleCollapse } = useSidebarCollapse();

// Use navigation composable
const { isActiveRoute, navigateTo } = useNavigation(props.currentRoute);

// Active section for subsection display - using shallowRef for better performance
const activeSection = shallowRef(null);

// Both navigate to the section and optionally display subsections
// This more sophisticated approach handles various edge cases
const setActiveSection = (section) => {
  // Check if we're already on the section's page
  const isCurrent = isActiveRoute(section.routeIs);
  
  // If we're already on this page, toggle subsection display
  if (isCurrent) {
    // If section has subsections, show them
    if (section.subsections && section.subsections.length) {
      activeSection.value = section;
    }
  } else {
    // For new navigation, always show subsections if available
    if (section.subsections && section.subsections.length) {
      activeSection.value = section;
    }
  }
};

const resetActiveSection = () => {
  activeSection.value = null;
};

// Check if user is admin - memoized computation
const isAdmin = computed(() => 
  props.user?.login && props.adminUserLogins?.includes(props.user.login)
);

// Memoized project icon path helper
const getProjectIconPath = (label) => {
  const labelLower = label.toLowerCase();
  
  // Map of special cases for better performance
  const specialCases = {
    'ordergroup': '/img/projects/order-group_small.png',
    'cdb': '/img/projects/central-database_small.png',
    'gant fonts': '/img/projects/gant-preview_small.png',
    'nunito sans': '/img/projects/nunito-preview_small.png'
  };
  
  return specialCases[labelLower] || 
         `/img/projects/${labelLower.replace(/\s+/g, '-')}_small.png`;
};

// Check if menu item should be shown - memoized helper
const canShow = (item) => {
  if (!item) return false;
  if (item.adminOnly && !isAdmin.value) return false;
  return true;
};

// Main navigation items - memoized and optimized
const mainNavItems = computed(() => {
  if (!props.routes || !props.messages) return [];
  
  const allItems = [
    props.routes.iconsearch && {
      label: props.messages.search_icons,
      icon: 'design-search-fill',
      route: props.routes.iconsearch,
      routeIs: 'iconsearch'
    },
    props.routes.projectassets && {
      label: props.messages.assets,
      icon: 'design-figma',
      route: props.routes.projectassets,
      routeIs: 'projectassets',
      subsections: [
        { label: 'WMS', icon: 'design-box', route: '/wms_icons', routeIs: 'wms_icons' },
        { label: 'TOS', icon: 'design-tag', route: '/tos_icons', routeIs: 'tos_icons' },
        { label: 'Claims', icon: 'design-document', route: '/claims_icons', routeIs: 'claims_icons' },
        { label: 'Retail', icon: 'design-store', route: '/retail_icons', routeIs: 'retail_icons' },
        { label: 'Matrix', icon: 'design-grid', route: '/matrix_icons', routeIs: 'matrix_icons' },
        { label: 'HRMS', icon: 'design-user', route: '/hrms_icons', routeIs: 'hrms_icons' },
        { label: 'Eshop', icon: 'design-cart', route: '/eshop_icons', routeIs: 'eshop_icons' },
        { label: 'Ordergroup', icon: 'design-order', route: '/ordergroup_icons', routeIs: 'ordergroup_icons' },
        { label: 'CDB', icon: 'design-database', route: '/cdb_icons', routeIs: 'cdb_icons' },
        { label: 'Category', icon: 'design-folder', route: '/category_icons', routeIs: 'category_icons' },
        { label: 'Gant Fonts', icon: 'design-typography', route: '/fonts/gant', routeIs: 'fonts.gant' },
        { label: 'Nunito Sans', icon: 'design-text', route: '/fonts/nunito', routeIs: 'fonts.nunito' }
      ],
      getIconPath: getProjectIconPath
    },
    props.routes.whiteboard && {
      label: props.messages.todolist,
      icon: 'design-unchecked',
      route: props.routes.whiteboard,
      routeIs: 'whiteboard'
    },
    props.routes.code && {
      label: props.messages.code,
      icon: 'design-bracket2',
      route: props.routes.code,
      routeIs: 'code',
      adminOnly: true
    },
    props.routes.animations && {
      label: props.messages.animations,
      icon: 'design-transparency',
      route: props.routes.animations,
      routeIs: 'animations',
      adminOnly: true
    },
    props.routes.tools && {
      label: props.messages.tools,
      icon: 'design-measuring',
      route: props.routes.tools,
      routeIs: ['tools', 'tools.*'],
      subsections: [
        { label: 'EshopResizer.jsx', icon: 'design-eshopresizer', route: `${props.routes.tools}/eshop-resizer/documentation`, routeIs: 'tools.eshop-resizer.documentation' },
        { label: 'InstaResizer.jsx', icon: 'design-instaresizer', route: `${props.routes.tools}/insta-resizer/documentation`, routeIs: 'tools.insta-resizer.documentation' },
        { label: 'Animate.svg', icon: 'design-animatesvg', route: `${props.routes.tools}/svg-animation/documentation`, routeIs: 'tools.svg-animation.documentation' },
        { label: 'OrderGroup.img', icon: 'design-ordergroup', route: `${props.routes.tools}/ordergroup-image/documentation`, routeIs: 'tools.ordergroup-image.documentation' },
        { label: 'QR generator', icon: 'design-qrgenerator', route: `${props.routes.tools}/qr-generator/documentation`, routeIs: 'tools.qr-generator.documentation' },
        { label: 'Barcode Generator', icon: 'design-barcode', route: `${props.routes.tools}/barcode-generator/documentation`, routeIs: 'tools.barcode-generator.documentation' },
        { label: 'SVG Optimizer', icon: 'design-gallery', route: `${props.routes.tools}/svg-optimizer/documentation`, routeIs: 'tools.svg-optimizer.documentation' }
      ],
      getIconPath: getProjectIconPath
    },
    {
      label: 'Old website',
      icon: 'design-clock',
      externalUrl: 'https://vermont.mareknovy.sk/',
      target: '_blank'
    }
  ].filter(Boolean);
  
  return allItems.filter(item => canShow(item));
});

// Other menu items - memoized
const otherNavItems = computed(() => {
  if (!props.routes || !props.messages) return [];
  
  const allItems = [
    props.routes.logout && {
      isLogout: true
    },
    (props.routes.admin && isAdmin.value) && {
      label: props.messages.administration,
      icon: 'design-customization',
      route: props.routes.admin,
      routeIs: 'admin.index',
      adminOnly: true,
      badge: '1'
    }
  ].filter(Boolean);
  
  return allItems.filter(item => canShow(item));
});
</script>

<style scoped>
/* Simple fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Better handling for active nav items */
.active-nav-item {
  @apply bg-gray-100 dark:bg-gray-800;
  position: relative;
  z-index: 1;
}

/* Transitions for collapse state */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>

<style>
/* Global styles for sidebar collapse state - unchanged */
body.sidebar-collapsed .designdev_tm_leftpart {
  width: 80px !important;
}

body.sidebar-collapsed .designdev_tm_sidebar_menu {
  width: 80px !important;
}

body.sidebar-collapsed .designdev_tm_mainpart {
  padding-left: 80px !important;
  width: 100% !important;
}

/* Mobile styles - unchanged */
@media (max-width: 768px) {
  body.sidebar-collapsed .designdev_tm_mainpart,
  .designdev_tm_mainpart {
    padding-left: 0 !important;
    width: 100% !important;
  }

  body.sidebar-collapsed .designdev_tm_leftpart,
  body.sidebar-collapsed .designdev_tm_sidebar_menu {
    width: 0 !important;
    overflow: hidden;
  }
}

/* Default transitions for all elements - unchanged */
.designdev_tm_leftpart,
.designdev_tm_sidebar_menu,
.designdev_tm_mainpart {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Default state for main content - unchanged */
.designdev_tm_mainpart {
  width: 100%;
  padding-left: 320px;
}

/* Default state for sidebar - unchanged */
.designdev_tm_leftpart,
.designdev_tm_sidebar_menu {
  width: 320px;
}

/* Ensure smooth transitions for all elements - unchanged */
.designdev_tm_leftpart *,
.designdev_tm_sidebar_menu *,
.designdev_tm_mainpart * {
  transition: inherit;
}

/* Ensure sidebar elements have proper z-index stacking */
.designdev_tm_leftpart {
  position: fixed;
  z-index: 40;
}

.designdev_tm_sidebar_menu {
  position: fixed;
  z-index: 40;
}

/* Flyout menu needs to be above everything */
.flyout-menu {
  z-index: 1000 !important;
}
</style>


