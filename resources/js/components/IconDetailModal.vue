<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black/50 dark:bg-black/70 z-50 flex items-center justify-center p-4 sm:p-6 backdrop-blur-sm transition-opacity duration-300"
    @click.self="close"
  >
    <div 
      class="bg-white dark:bg-slate-950 rounded-xl shadow-xl w-full md:w-auto min-w-[320px] md:min-w-[600px] md:max-w-3xl overflow-hidden transform transition-all duration-300 ease-out"
      @click.stop
    >
      <!-- Modal Header -->
      <div class="px-6 pb-3 pt-4 sm:px-8 sm:pb-4 sm:pt-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-xl sm:text-2xl gant-modern-bold text-gray-900 dark:text-gray-100">
          Icon details <span class="gant-modern-regular text-gray-700 dark:text-gray-400">– Search Tag management</span>
        </h3>
        <button 
          @click="close" 
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <i class="design design-close text-lg"></i>
        </button>
      </div>

      
      <!-- Modal Body -->
      <div class="px-6 py-3 sm:px-8 sm:py-4">
        <!-- Two-column layout with more responsive sizing -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6">
          <!-- Left Column: Icon Display - make it responsive -->
          <div class="flex flex-col items-center justify-center space-y-4">
            <div class="flex items-center justify-center border border-solid border-gray-200 dark:border-gray-700 rounded-lg p-4 md:p-8 w-full max-w-[280px]">
              <i 
                v-if="icon"
                :class="[
                  fontClass,
                  icon.css_class || icon.cssClass,
                  'text-gray-900 dark:text-gray-100 text-7xl md:text-9xl'
                ]" 
              ></i>
            </div>
            
            <!-- Font info with copy functionality - adjust width -->
            <div class="text-center space-y-3 w-full max-w-[280px]">
              <!-- Font and content info -->
              <small class="text-slate-400 dark:text-slate-600 text-base leading-none mb-1 gant-modern-regular block overflow-hidden text-ellipsis">
                <span class="gant-modern-bold text-slate-500 dark:text-slate-500">
                  {{ icon && icon.font ? (icon.font.name || 'Default') : 'Default' }}
                </span>: {{ icon ? (icon.css_content || icon.cssContent) : '' }}
              </small>
              
              <!-- Class name with copy button -->
              <div class="relative group">
                <!-- Normal class name display -->
                <p 
                  :class="[
                    'w-full text-lg px-3 py-2 text-gray-900 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/30 border border-solid border-gray-200 dark:border-gray-700 rounded-md transition-all duration-200 gant-modern-bold truncate whitespace-nowrap',
                    copied ? 'border-green-500 dark:border-green-500 text-green-600 dark:text-green-400' : 'group-hover:border-gray-300 dark:group-hover:border-gray-600',
                    'group-hover:opacity-0'
                  ]"
                  :title="icon ? (icon.css_class || icon.cssClass) : ''"
                >
                  <i class="design design-copyclipboard-outline text-gray-400 dark:text-gray-500 mr-1 text-lg"></i>
                  {{ icon ? (icon.css_class || icon.cssClass) : '' }}
                </p>
                
                <!-- Copy button overlay -->
                <button 
                  @click.stop="copyToClipboard(icon ? (icon.css_class || icon.cssClass) : '')"
                  :class="[
                    'absolute inset-0 w-full text-base px-3 py-2 rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100 flex items-center justify-center gap-2',
                    copied 
                      ? 'bg-green-900 text-white dark:bg-green-900/90 dark:text-green-100 border border-green-500'
                      : 'bg-gray-900 text-gray-100 dark:bg-gray-800 dark:text-gray-100 border border-gray-600 dark:border-gray-700 hover:bg-gray-800 dark:hover:bg-gray-700'
                  ]"
                >
                  <i :class="[
                    copied ? 'design design-circle-checked' : 'design design-copyclipboard',
                    'text-sm'
                  ]"></i>
                  {{ copied ? 'Copied!' : 'Copy class' }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- Right Column: Tags Management - adjust min-height -->
          <div class="space-y-4">
            <div class="text-center">
              <i class="design design-hello-collegue text-gray-400 dark:text-gray-600 text-center text-4xl mb-3"></i>
            <p class="text-md gant-modern-bold text-gray-900 dark:text-gray-200 text-center">Please add Tags to help with search,</p>
            <!-- <p class="text-md gant-modern-regular text-gray-900 dark:text-gray-200 text-center">don't forget to <span class="gant-modern-bold">Save Changes... 😉 </span></p> -->
          </div>
            <div class="flex items-center justify-center">
              

              <label class="block text-md gant-modern-regular text-gray-400 dark:text-gray-600">Tags for search:</label>
            </div>
            
            <!-- Current tags - adjust height on mobile -->
            <div class="flex flex-wrap gap-2 overflow-y-auto">
              <span 
                v-for="(tag, tagIndex) in currentTags" 
                :key="`modal-tag-${tagIndex}`"
                class="inline-flex items-center px-2 py-1 rounded-md text-lg border border-solid border-gray-950 dark:border-gray-700 bg-gray-900 dark:bg-slate-100 text-gray-100 dark:text-slate-900 transition-all hover:bg-slate-600 dark:hover:bg-white gant-modern-medium"
              >
                {{ tag }}
                <button 
                  @click="removeTag(tagIndex)" 
                  class="ml-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-600 focus:outline-none"
                  aria-label="Remove tag"
                >
                  <i class="design design-circle-remove text-lg"></i>
                </button>
              </span>
              <span 
                v-if="currentTags.length === 0" 
                class="text-gray-900 dark:text-gray-200 text-lg gant-modern-bold"
              >
                No tags 😔
              </span>
            </div>
          </div>
        </div>
        
        <!-- Add new tag - adjust width responsively -->
        <label class="block text-md gant-modern-regular text-gray-400 dark:text-gray-600 mb-1.5">Keywords, synonyms, usage in projects...</label>
        <div class="flex justify-center mb-4">
          <div class="flex space-x-2 w-full">
            <div class="flex-1 relative">
              <input
                v-model="newTag"
                @keydown.enter="addTag"
                @keydown.down.prevent="navigateSuggestion(1)"
                @keydown.up.prevent="navigateSuggestion(-1)"
                @input="filterSuggestions"
                placeholder="Add a Tag where did you use it..."
                class="focus:ring-2 focus:ring-gray-500 focus:border-gray-500 block w-full sm:text-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md py-2 px-3 text-gray-900 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 gant-modern-regular"
              />
              
              <!-- Tag suggestions dropdown -->
              <div 
                v-if="showSuggestions && filteredSuggestions.length > 0" 
                class="absolute z-50 w-full bg-white dark:bg-gray-800 mt-1 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 max-h-48 overflow-y-auto"
              >
                <div
                  v-for="(suggestion, index) in filteredSuggestions"
                  :key="suggestion"
                  @click="selectSuggestion(suggestion)"
                  :class="[
                    'px-3 py-2 cursor-pointer text-gray-900 dark:text-gray-200',
                    selectedSuggestionIndex === index ? 'bg-gray-100 dark:bg-gray-700' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  ]"
                >
                  {{ suggestion }}
                </div>
              </div>
            </div>
            <button 
              @click="addTag"
              class="inline-flex items-center px-4 py-2 border border-transparent text-md rounded-md text-white bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:text-gray-900 dark:hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 gant-modern-medium"
              aria-label="Add tag"
            >
              <i class="design design-add mr-1.5 text-md opacity-50"></i>
              Add tag 
            </button>
          </div>
        </div>

        <!-- Save button - moved from footer -->
        <div class="flex justify-center mt-6 mb-2" v-if="isEdited">
          
          <button 
            @click="saveTags" 
            class="px-6 py-2.5 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 text-base gant-modern-medium w-full max-w-[220px] bg-gray-900 hover:bg-gray-700 text-white dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-900 focus:ring-gray-500"
          >
            Save Changes
          </button>
        </div>
      </div>
      
      <!-- Toast Message -->
      <div 
        v-if="showToast" 
        class="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-lg p-4 max-w-xs flex items-start space-x-3 z-50 transform transition-all duration-300 ease-out"
      >
        <div :class="[
          'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',
          toastType === 'success' ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
        ]">
          <i :class="[
            toastType === 'success' ? 'design design-circle-checked' : 'design design-circle-remove',
            'text-sm'
          ]"></i>
        </div>
        <div class="flex-1">
          <p class="text-sm gant-modern-medium text-gray-900 dark:text-gray-100">{{ toastMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IconDetailModal',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    icon: {
      type: Object,
      default: null
    },
    fontClass: {
      type: String,
      default: ''
    },
    fontId: {
      type: [Number, String],
      default: null
    },
    initialTags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentTags: [],
      originalTags: [],
      newTag: '',
      showToast: false,
      toastMessage: '',
      toastType: 'success',
      copied: false,
      globalTags: [], // For tag suggestions
      filteredSuggestions: [],
      showSuggestions: false,
      selectedSuggestionIndex: -1,
    };
  },
  computed: {
    isEdited() {
      // Check if arrays have different lengths
      if (this.currentTags.length !== this.originalTags.length) return true;
      
      // Check if any elements are different
      return this.currentTags.some((tag, index) => tag !== this.originalTags[index]);
    },
    cssClass() {
      return this.icon ? (this.icon.css_class || this.icon.cssClass) : null;
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm();
      }
    },
    initialTags: {
      handler(newTags) {
        this.currentTags = [...newTags];
        this.originalTags = [...newTags];
      },
      immediate: true
    }
  },
  mounted() {
    // Fetch all available tags for suggestions
    this.fetchTagSuggestions();
  },
  methods: {
    close() {
      this.$emit('close');
    },
    
    resetForm() {
      this.currentTags = [...this.initialTags];
      this.originalTags = [...this.initialTags];
      this.newTag = '';
    },
    
    async fetchTagSuggestions() {
      try {
        const response = await fetch('/api/tags/list');
        const data = await response.json();
        this.globalTags = data;
      } catch (error) {
        console.error('Error fetching tag suggestions:', error);
      }
    },
    
    filterSuggestions() {
      if (!this.newTag.trim()) {
        this.showSuggestions = false;
        return;
      }
      
      const input = this.newTag.toLowerCase();
      
      // Filter out tags that are already added
      this.filteredSuggestions = this.globalTags
        .filter(tag => 
          tag.toLowerCase().includes(input) && 
          !this.currentTags.includes(tag)
        )
        .slice(0, 5); // Limit to 5 suggestions
      
      this.showSuggestions = this.filteredSuggestions.length > 0;
      this.selectedSuggestionIndex = -1;
    },
    
    navigateSuggestion(direction) {
      if (!this.showSuggestions) return;
      
      const newIndex = this.selectedSuggestionIndex + direction;
      if (newIndex >= -1 && newIndex < this.filteredSuggestions.length) {
        this.selectedSuggestionIndex = newIndex;
        
        if (newIndex !== -1) {
          this.newTag = this.filteredSuggestions[newIndex];
        }
      }
    },
    
    selectSuggestion(suggestion) {
      this.newTag = suggestion;
      this.showSuggestions = false;
      this.addTag();
    },
    
    addTag() {
      const tagToAdd = this.newTag.trim();
      if (!tagToAdd) return;
      
      // Skip if tag already exists
      if (this.currentTags.includes(tagToAdd)) {
        this.showToastMessage(`Tag "${tagToAdd}" already exists for this icon`, 'error');
        return;
      }
      
      this.currentTags.push(tagToAdd);
      this.newTag = '';
      this.showSuggestions = false;
    },
    
    removeTag(tagIndex) {
      this.currentTags.splice(tagIndex, 1);
    },
    
    async getFontIdByName(fontName) {
      try {
        console.log('Looking up font by name:', fontName);
        const response = await fetch(`/api/fonts/lookup?name=${encodeURIComponent(fontName)}`);
        
        if (!response.ok) {
          console.warn(`Font lookup failed for name: ${fontName}`);
          
          // More comprehensive fallback mapping for all fonts
          const fontMapping = {
            'wms': 1,
            'tos': 2,
            'claims': 3,
            'retail': 4,
            'matrix': 5,
            'ordergroup': 6, 
            'og': 6,           // Alternative name for Order Group
            'vermont-icon': 7, // Eshop
            'eshop': 7,        // Alternative name
            'cdb': 8,
            'design': 9,
            'vermont-category': 10, // Category
            'category': 10,    // Alternative name
            'hrms': 11
          };
          
          // Try exact match first
          if (fontMapping[fontName.toLowerCase()]) {
            console.log(`Using hardcoded ID for ${fontName}: ${fontMapping[fontName.toLowerCase()]}`);
            return fontMapping[fontName.toLowerCase()];
          }
          
          // Try extracting font name from icon classes like "design-something"
          const fontPrefix = fontName.split('-')[0];
          if (fontPrefix && fontMapping[fontPrefix.toLowerCase()]) {
            console.log(`Using hardcoded ID for prefix ${fontPrefix}: ${fontMapping[fontPrefix.toLowerCase()]}`);
            return fontMapping[fontPrefix.toLowerCase()];
          }
          
          // As a last resort, check if font name is part of any keys in the mapping
          for (const key in fontMapping) {
            if (fontName.toLowerCase().includes(key)) {
              console.log(`Matched partial font name ${key} for ${fontName}: ${fontMapping[key]}`);
              return fontMapping[key];
            }
          }
          
          console.error(`Could not determine font ID for: ${fontName}`);
          return null;
        }
        
        const data = await response.json();
        return data.id || null;
      } catch (error) {
        console.error('Error looking up font ID:', error);
        return null;
      }
    },
    
    async saveTags() {
      if (!this.icon) return;
      
      try {
        // Get the font ID - try different sources
        let fontId = this.fontId;
        let fontName = null;
        
        // If fontId is not provided, try to extract it from the icon
        if (!fontId && this.icon) {
          // Check if the icon has a font property with an id
          if (this.icon.font && this.icon.font.id) {
            fontId = this.icon.font.id;
          } else if (this.icon.font && this.icon.font.name) {
            // If we have the font name but not ID, store it for lookup
            fontName = this.icon.font.name;
          } else if (this.fontClass) {
            // Last resort - the font class might be the font name
            fontName = this.fontClass;
          }
        }
        
        // If we still don't have a fontId but have a name, try to look it up
        if (!fontId && fontName) {
          fontId = await this.getFontIdByName(fontName);
        }
        
        if (!fontId) {
          this.showToastMessage('Font ID is missing. Cannot save tags.', 'error');
          return;
        }

        const response = await fetch(`/fonts/${fontId}/update-icon-tags`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            css_class: this.cssClass,
            tags: this.currentTags
          })
        });
        
        // If we get a 422 validation error, parse it properly
        if (response.status === 422) {
          const errorData = await response.json();
          
          let errorMessage = 'Validation error';
          if (errorData.message) {
            errorMessage = errorData.message;
          }
          
          // Show more specific errors if available
          if (errorData.errors) {
            if (errorData.errors.tags) {
              errorMessage = errorData.errors.tags[0];
            }
          }
          
          this.showToastMessage(errorMessage, 'error');
          return;
        }
        
        // For other non-OK responses
        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server error:', response.status, errorText.substring(0, 100) + '...');
          this.showToastMessage(`Server error: ${response.status} ${response.statusText}`, 'error');
          return;
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          console.error('Response is not JSON:', contentType);
          this.showToastMessage('Unexpected response format from server', 'error');
          return;
        }
        
        const data = await response.json();
        
        if (data.success) {
          // Update the original tags reference
          this.originalTags = [...this.currentTags];
          this.showToastMessage('Tags updated successfully', 'success');
          
          // Emit an event to notify parent component
          this.$emit('tags-updated', {
            icon: this.icon,
            tags: [...this.currentTags]
          });
          
          // Close modal after short delay
          setTimeout(() => {
            this.close();
          }, 1500);
        } else {
          this.showToastMessage(`Failed to save: ${data.message || 'Unknown error'}`, 'error');
        }
      } catch (error) {
        console.error('Error saving tags:', error);
        this.showToastMessage('Network error while saving tags', 'error');
      }
    },
    
    showToastMessage(message, type = 'success') {
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;
      
      // Auto-hide toast after 3 seconds
      setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },
    
    formatClassName(className) {
      if (!className) return '';
      const hyphenIndex = className.indexOf('-');
      return hyphenIndex !== -1 ? className.substring(hyphenIndex + 1) : className;
    },
    
    async copyToClipboard(iconClass) {
      if (!iconClass) return;
      
      try {
        await navigator.clipboard.writeText(iconClass);
        this.copied = true;
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (err) {
        console.error('Failed to copy:', err);
      }
    }
  }
};
</script> 