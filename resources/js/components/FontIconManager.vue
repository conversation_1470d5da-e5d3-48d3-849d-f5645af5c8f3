<template>
  <div class="gant-modern-regular ">
    <div class="bg-white dark:bg-slate-950 rounded-lg mb-6">
      <div class="flex flex-wrap gap-3 lg:gap-4 items-start">
        <!-- Search input -->
        <div class="flex-grow min-w-[240px] max-w-sm">
          <label for="search" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Search</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="design design-search-fill text-slate-400"></i>
            </div>
            <input
              id="search"
              v-model="search"
              type="text"
              placeholder="Search by icon name, content or tags..."
              class="focus:ring-slate-500 focus:border-slate-500 block w-full pl-10 pr-3 sm:text-sm border-slate-300 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-200 rounded-md py-3.5"
            />
          </div>
        </div>
        
        <!-- Display options -->
        <div class="w-40 min-w-[260px]">
          <label for="display-option" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Display</label>
          <select 
            id="display-option" 
            v-model="displayOption"
            class="block w-full py-3.5 px-3 border border-solid border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 rounded-md focus:outline-none focus:ring-slate-500 focus:border-slate-500 sm:text-sm text-slate-900 dark:text-slate-200"
          >
            <option value="all">All Icons</option>
            <option value="with-tags">With Tags</option>
            <option value="without-tags">Without Tags</option>
          </select>
        </div>
        
        <!-- View mode toggle -->
        <div class="w-40 min-w-[260px]">
          <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">View Mode</label>
          <div class="flex rounded-md overflow-hidden border border-solid border-slate-300 dark:border-slate-700 p-1">
            <button 
              @click="viewMode = 'grid'" 
              class="flex-1 py-2.5 px-3 text-sm flex items-center justify-center gap-2 bg-white dark:bg-slate-950 hover:bg-slate-50 dark:hover:bg-slate-800 transition"
              :class="viewMode === 'grid' ? 'text-slate-900 dark:text-slate-100 gant-modern-bold border border-solid border-slate-300 dark:border-slate-700' : 'text-slate-300 dark:text-slate-700'"
            >
              <i v-if="viewMode === 'grid'" class="design design-circle-checked text-slate-900 dark:text-slate-100 mr-1"></i>
              <i class="design design-gridview text-sm"></i> Grid
            </button>
            <button 
              @click="viewMode = 'table'" 
              class="flex-1 py-2.5 px-3 text-sm flex items-center justify-center gap-2 bg-white dark:bg-slate-950 hover:bg-slate-50 dark:hover:bg-slate-800 transition"
              :class="viewMode === 'table' ? 'text-slate-900 dark:text-slate-100 gant-modern-bold border border-solid border-slate-300 dark:border-slate-700' : 'text-slate-300 dark:text-slate-700'"
            >
              <i v-if="viewMode === 'table'" class="design design-circle-checked text-slate-900 dark:text-slate-100 mr-1"></i>
              <i class="design design-listview text-sm"></i> Table
            </button>
          </div>
        </div>
        
        <!-- Column count selector - Only visible in grid view -->
        <div v-if="viewMode === 'grid'" class="w-32 min-w-[128px]">
          <label for="column-count" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Grid Columns</label>
          <select 
            id="column-count" 
            v-model="columnCount"
            class="block w-full py-3.5 px-3 border border-solid border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 rounded-md focus:outline-none focus:ring-slate-500 focus:border-slate-500 sm:text-sm text-slate-900 dark:text-slate-200"
          >
            <option :value="2">2 Columns</option>
            <option :value="3">3 Columns</option>
            <option :value="4">4 Columns</option>
            <option :value="5">5 Columns</option>
            <option :value="6">6 Columns</option>
            <option :value="8">8 Columns</option>
          </select>
        </div>
        
        <!-- Pagination controls (top) -->
        <div class="flex-grow min-w-[240px]">
          <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            Page {{ currentPage }} of {{ totalPages }} ({{ filteredIcons.length }} icons)
          </label>
          <div class="flex items-center">
            <button 
              @click="goToPage(currentPage - 1)" 
              :disabled="currentPage === 1"
              class="px-4 py-3.5 rounded-l border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
            >
              <i class="design design-arrow-left text-sm"></i> <span class="gant-modern-regular text-sm">Previous</span>
            </button>
            
            <!-- Page size dropdown -->
            <select 
              v-model="pageSize"
              class="border-y border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 py-3.5 ps-4 pe-8 text-sm"
            >
              <option :value="20">20</option>
              <option :value="50">50</option>
              <option :value="100">100</option>
              <option :value="200">200</option>
            </select>
            
            <button 
              @click="goToPage(currentPage + 1)" 
              :disabled="currentPage === totalPages"
              class="px-4 py-3.5  rounded-r border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
            >
              <span class="gant-modern-regular text-sm">Next</span> <i class="design design-arrow-right text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Grid view -->
    <div v-if="viewMode === 'grid'" :class="[
      'grid gap-4 sm:gap-6',
      gridColumnsClass
    ]">
      <div 
        v-for="icon in paginatedIcons" 
        :key="icon.css_class" 
        class="bg-white dark:bg-slate-950 overflow-hidden rounded-lg border border-solid border-slate-200 dark:border-slate-700 flex flex-col"
      >
        <!-- Icon display -->
        <div class="px-4 pt-4 flex flex-col items-center flex-grow">
          <div class="h-16 w-16 flex items-center justify-center text-gray-600 dark:text-slate-300 my-3">
            <i :class="`${fontClass} ${icon.css_class}`" style="font-size: 4rem;"></i>
          </div>
          <p class="text-sm gant-modern-bold text-slate-900 dark:text-slate-200 truncate max-w-full mb-1">{{ icon.css_class }}</p>
          <p class="text-xs text-slate-500 dark:text-slate-400">{{ icon.css_content }}</p>
        </div>

        <!-- Tag management -->
        <div class="border-t border-slate-200 dark:border-slate-700 px-4 pb-4">
          <div class="mb-3 flex justify-between items-center">
            <label class="block text-base font-medium text-slate-700 dark:text-slate-300">Tags</label>
            <button 
              @click="saveIconTags(icon)" 
              :disabled="!isIconEdited(icon.css_class)"
              :class="['text-xs px-2.5 py-1.5 rounded transition', 
                isIconEdited(icon.css_class) 
                  ? 'bg-slate-900 hover:bg-slate-100 text-white dark:text-slate-900 dark:bg-slate-100' 
                  : 'border border-solid border-slate-200 dark:border-slate-700 text-slate-200 dark:text-slate-700 cursor-not-allowed']"
            >
              Save
            </button>
          </div>

          <!-- Show existing tags with ability to remove -->
          <div class="flex flex-wrap gap-2 mb-3 min-h-[30px]">
            <span 
              v-for="(tag, tagIndex) in getIconTags(icon.css_class)" 
              :key="`${icon.css_class}-${tag}-${tagIndex}`"
              class="inline-flex items-center px-2.5 py-1 rounded text-xs bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900"
            >
              {{ tag }}
              <button 
                @click="removeTag(icon.css_class, tagIndex)" 
                class="ml-1.5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                aria-label="Remove tag"
              >
                <i class="design design-circle-remove text-xs"></i>
              </button>
            </span>
            <span 
              v-if="getIconTags(icon.css_class).length === 0" 
              class="text-slate-400 dark:text-slate-500 text-xs italic"
            >
              No tags
            </span>
          </div>

          <!-- Add new tag input -->
          <div class="flex space-x-2 mt-3">
            <input
              v-model="newTags[icon.css_class]"
              @keydown.enter="addTag(icon.css_class)"
              placeholder="Add a tag..."
              class="flex-1 focus:ring-slate-500 focus:border-slate-500 block w-full sm:text-sm text-slate-900 dark:text-slate-200 border-slate-300 dark:border-slate-700 dark:bg-slate-800 rounded-md py-1.5 px-3"
            />
            <button 
              @click="addTag(icon.css_class)"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm rounded text-slate-900 dark:text-slate-200 bg-slate-200 dark:bg-slate-900 hover:bg-slate-700 dark:hover:bg-slate-300"
              aria-label="Add tag"
            >
              <i class="design design-add mr-1.5"></i>
              Add
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Table view -->
    <div v-else class="bg-white dark:bg-slate-950 rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700 border-b-2 border-slate-300 dark:border-slate-600">
          <thead class="bg-slate-50 dark:bg-slate-900">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400  tracking-wider w-16">Icon</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400  tracking-wider w-64">CSS Details</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400  tracking-wider w-auto">Tags</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 dark:text-slate-400  tracking-wider w-24">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-slate-950 divide-y divide-slate-200 dark:divide-slate-800">
            <tr v-for="icon in paginatedIcons" :key="icon.css_class" class="hover:bg-slate-50 dark:hover:bg-slate-900/50 border-b border-solid border-slate-200 dark:border-slate-700">
              <td class="px-6 py-4 align-middle">
                <div class="h-10 w-10 flex items-center justify-center text-slate-900 dark:text-slate-100 mx-auto">
                  <i :class="`${fontClass} ${icon.css_class}`" style="font-size: 2rem;"></i>
                </div>
              </td>
              <td class="px-6 py-4 align-middle">
                <div class="flex flex-col justify-center">
                  <p class="text-sm gant-modern-bold font-medium text-slate-900 dark:text-slate-200 mb-0.5">{{ icon.css_class }}</p>
                  <p class="text-xs text-slate-500 dark:text-slate-400 mt-0">{{ icon.css_content }}</p>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex flex-wrap gap-2 items-center">
                  <span 
                    v-for="(tag, tagIndex) in getIconTags(icon.css_class)" 
                    :key="`${icon.css_class}-${tag}-${tagIndex}`"
                    class="inline-flex items-center px-2.5 py-1 rounded text-base bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900"
                  >
                    {{ tag }}
                    <button 
                      @click="removeTag(icon.css_class, tagIndex)" 
                      class="ml-1.5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                      aria-label="Remove tag"
                    >
                      <i class="design design-circle-remove text-xs"></i>
                    </button>
                  </span>
                  
                  <div class="flex items-center gap-2 min-w-[200px]">
                    <input
                      v-model="newTags[icon.css_class]"
                      @keydown.enter="addTag(icon.css_class)"
                      placeholder="Add a tag..."
                      class="flex-1 text-sm border border-solid border-slate-300 dark:border-slate-700 focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-800 rounded-md py-1.5 px-3 text-slate-900 dark:text-slate-200 min-w-[150px]"
                    />
                    <button 
                      @click="addTag(icon.css_class)"
                      class="inline-flex items-center px-2.5 py-1.5 border border-solid border-slate-100 dark:border-slate-700 text-xs rounded text-slate-950 dark:text-slate-200 bg-slate-50 dark:bg-slate-800 hover:bg-slate-700 hover:text-white dark:hover:bg-slate-300 transition-all duration-200"
                      aria-label="Add tag"
                    >
                      <i class="design design-add text-xs mr-1"></i>
                      Add
                    </button>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium align-middle">
                <button 
                  @click="saveIconTags(icon)" 
                  :disabled="!isIconEdited(icon.css_class)"
                  :class="['text-xs px-3 py-2 rounded transition', 
                    isIconEdited(icon.css_class) 
                      ? 'bg-slate-900 hover:bg-slate-800 text-white dark:text-slate-900 dark:bg-slate-100 dark:hover:bg-slate-200' 
                      : 'border border-solid border-slate-200 dark:border-slate-700 text-slate-200 dark:text-slate-700 cursor-not-allowed']"
                >
                  Save
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="filteredIcons.length === 0" class="text-center py-12 px-4">
      <i class="design design-magnifier text-slate-400 dark:text-slate-600 text-5xl mb-4"></i>
      <p class="text-slate-500 dark:text-slate-400 text-lg">No icons match your search criteria.</p>
    </div>

    <!-- Pagination controls (bottom) -->
    <div v-if="filteredIcons.length > 0" class="mt-6 flex justify-center">
      <div class="flex items-center space-x-1">
        <button 
          @click="goToPage(1)" 
          :disabled="currentPage === 1"
          class="px-3 py-3 rounded border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
        >
          <i class="design design-double-arrow-left"></i>
        </button>
        
        <button 
          @click="goToPage(currentPage - 1)" 
          :disabled="currentPage === 1"
          class="px-3 py-3  rounded border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
        >
          <i class="design design-arrow-left"></i>
        </button>
        
        <!-- Page numbers -->
        <template v-for="page in displayedPageNumbers" :key="page">
          <button 
            v-if="page !== '...'"
            @click="goToPage(page)" 
            :class="[
              'px-3 py-3  min-w-[3rem] rounded border text-sm',
              currentPage === page 
                ? 'bg-slate-950 dark:bg-slate-700 text-white border-slate-900 dark:border-slate-700' 
                : 'bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 border-slate-300 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800'
            ]"
          >
            {{ page }}
          </button>
          <span v-else class="px-3 py-3  text-slate-500 dark:text-slate-400">...</span>
        </template>
        
        <button 
          @click="goToPage(currentPage + 1)" 
          :disabled="currentPage === totalPages"
          class="px-3 py-3 rounded border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
        >
          <i class="design design-arrow-right"></i>
        </button>
        
        <button 
          @click="goToPage(totalPages)" 
          :disabled="currentPage === totalPages"
          class="px-3 py-3  rounded border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 disabled:bg-slate-100 dark:disabled:bg-slate-800 disabled:text-slate-400 dark:disabled:text-slate-600 disabled:cursor-not-allowed"
        >
          <i class="design design-double-arrow-right"></i>
        </button>
      </div>
    </div>

    <!-- Toast notifications -->
    <div 
      v-if="showToast" 
      class="fixed bottom-4 right-4 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 shadow-lg rounded-lg p-4 max-w-md flex items-start space-x-4 z-50"
    >
      <div :class="[
        'flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center',
        toastType === 'success' ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
      ]">
        <i :class="[
          toastType === 'success' ? 'design design-circle-checked' : 'design design-circle-remove',
          'text-sm'
        ]"></i>
      </div>
      <div class="flex-1">
        <p class="text-sm font-medium text-slate-900 dark:text-slate-100">{{ toastMessage }}</p>
      </div>
      <button @click="showToast = false" class="flex-shrink-0 text-slate-400 hover:text-slate-500 dark:hover:text-slate-300">
        <i class="design design-close text-sm"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FontIconManager',
  
  props: {
    fontId: {
      type: Number,
      required: true
    },
    fontName: {
      type: String,
      required: true
    },
    fontClass: {
      type: String,
      required: true
    },
    initialIcons: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      icons: [],
      search: '',
      displayOption: 'all',
      columnCount: 5,
      newTags: {},
      editedIcons: {},
      originalIconTags: {},
      showToast: false,
      toastMessage: '',
      toastType: 'success',
      currentPage: 1,
      pageSize: 50,
      viewMode: 'grid'
    };
  },
  
  computed: {
    filteredIcons() {
      if (!this.icons || !Array.isArray(this.icons)) {
        return [];
      }
      
      let result = this.icons;
      
      // Apply search filter
      if (this.search) {
        const searchLower = this.search.toLowerCase();
        result = result.filter(icon => 
          (icon.css_class && icon.css_class.toLowerCase().includes(searchLower)) || 
          (icon.css_content && icon.css_content.toLowerCase().includes(searchLower)) ||
          this.getIconTags(icon.css_class).some(tag => tag.toLowerCase().includes(searchLower))
        );
      }
      
      // Apply display option filter
      if (this.displayOption === 'with-tags') {
        result = result.filter(icon => this.getIconTags(icon.css_class).length > 0);
      } else if (this.displayOption === 'without-tags') {
        result = result.filter(icon => this.getIconTags(icon.css_class).length === 0);
      }
      
      return result;
    },
    
    // Calculate total number of pages
    totalPages() {
      return Math.max(1, Math.ceil(this.filteredIcons.length / this.pageSize));
    },
    
    // Get icons for the current page only
    paginatedIcons() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredIcons.slice(startIndex, endIndex);
    },
    
    // Generate page numbers for display (with ellipsis)
    displayedPageNumbers() {
      const total = this.totalPages;
      const current = this.currentPage;
      
      if (total <= 7) {
        // If 7 or fewer pages, show all page numbers
        return Array.from({ length: total }, (_, i) => i + 1);
      }
      
      // Always show first and last page
      const pages = [1, total];
      
      // Show pages around current page
      const startPage = Math.max(2, current - 1);
      const endPage = Math.min(total - 1, current + 1);
      
      // Add page numbers between start and end
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Sort and remove duplicates
      const sortedPages = [...new Set(pages)].sort((a, b) => a - b);
      
      // Add ellipsis where needed
      const result = [];
      let prevPage = 0;
      
      for (const page of sortedPages) {
        if (prevPage && page - prevPage > 1) {
          result.push('...');
        }
        result.push(page);
        prevPage = page;
      }
      
      return result;
    },
    
    gridColumnsClass() {
      const smColumns = Math.min(this.columnCount, 3);
      const mdColumns = Math.min(this.columnCount, 4);
      const lgColumns = Math.min(this.columnCount, 6);
      const xlColumns = this.columnCount;
      
      return `grid-cols-2 sm:grid-cols-${smColumns} md:grid-cols-${mdColumns} lg:grid-cols-${lgColumns} xl:grid-cols-${xlColumns}`;
    }
  },
  
  created() {
    try {
      console.log('FontIconManager created with initialIcons:', this.initialIcons);
      
      // Make sure we have a valid array of icons
      if (Array.isArray(this.initialIcons)) {
        console.log('initialIcons is an array with length:', this.initialIcons.length);
        this.icons = JSON.parse(JSON.stringify(this.initialIcons)); // Deep clone to avoid reference issues
      } else if (typeof this.initialIcons === 'string') {
        // Try to parse the string as JSON
        try {
          console.log('initialIcons is a string, attempting to parse as JSON');
          this.icons = JSON.parse(this.initialIcons);
          console.log('Successfully parsed initialIcons string to array with length:', this.icons.length);
        } catch (e) {
          console.error('Failed to parse initialIcons string as JSON:', e);
          this.icons = [];
        }
      } else {
        console.error('Invalid initialIcons prop:', this.initialIcons);
        this.icons = [];
      }
      
      // Initialize tags data structures
      this.icons.forEach(icon => {
        if (icon && icon.css_class) {
          this.newTags[icon.css_class] = '';
          this.originalIconTags[icon.css_class] = Array.isArray(icon.tags) ? [...icon.tags] : [];
          this.editedIcons[icon.css_class] = false;
        }
      });
      
      // Load column count and page size from local storage if available
      const savedColumnCount = localStorage.getItem('fontIconManagerColumnCount');
      if (savedColumnCount) {
        this.columnCount = parseInt(savedColumnCount, 10) || 5;
      }
      
      const savedPageSize = localStorage.getItem('fontIconManagerPageSize');
      if (savedPageSize) {
        this.pageSize = parseInt(savedPageSize, 10) || 50;
      }
      
      // Load view mode from local storage if available
      const savedViewMode = localStorage.getItem('fontIconManagerViewMode');
      if (savedViewMode && ['grid', 'table'].includes(savedViewMode)) {
        this.viewMode = savedViewMode;
      }
    } catch (error) {
      console.error('Error initializing component:', error);
      this.icons = [];
    }
  },
  
  watch: {
    columnCount(newValue) {
      // Save column count preference to local storage
      localStorage.setItem('fontIconManagerColumnCount', newValue.toString());
    },
    
    pageSize(newValue) {
      // Save page size preference to local storage
      localStorage.setItem('fontIconManagerPageSize', newValue.toString());
    },
    
    filteredIcons() {
      // Reset to first page when filters change
      this.currentPage = 1;
    },
    
    viewMode(newValue) {
      // Save view mode preference to local storage
      localStorage.setItem('fontIconManagerViewMode', newValue);
    }
  },
  
  methods: {
    // Get current tags for an icon
    getIconTags(cssClass) {
      if (!cssClass) return [];
      
      const icon = this.icons.find(i => i && i.css_class === cssClass);
      return (icon && Array.isArray(icon.tags)) ? icon.tags : [];
    },
    
    // Add a new tag
    addTag(cssClass) {
      if (!cssClass) return;
      
      const newTag = this.newTags[cssClass]?.trim();
      if (!newTag) return;
      
      const icon = this.icons.find(i => i && i.css_class === cssClass);
      if (!icon) return;
      
      if (!Array.isArray(icon.tags)) {
        icon.tags = [];
      }
      
      // Skip if tag already exists
      if (icon.tags.includes(newTag)) {
        this.showToastMessage(`Tag "${newTag}" already exists for this icon`, 'error');
        return;
      }
      
      icon.tags.push(newTag);
      this.newTags[cssClass] = '';
      this.editedIcons[cssClass] = true;
    },
    
    // Remove a tag
    removeTag(cssClass, tagIndex) {
      if (!cssClass) return;
      
      const icon = this.icons.find(i => i && i.css_class === cssClass);
      if (!icon || !Array.isArray(icon.tags)) return;
      
      icon.tags.splice(tagIndex, 1);
      this.editedIcons[cssClass] = true;
    },
    
    // Check if an icon has been edited
    isIconEdited(cssClass) {
      return this.editedIcons[cssClass] === true;
    },
    
    // Save tags for an icon
    async saveIconTags(icon) {
      if (!icon || !icon.css_class) {
        this.showToastMessage('Invalid icon data', 'error');
        return;
      }
      
      try {
        const response = await fetch(`/fonts/${this.fontId}/update-icon-tags`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          },
          body: JSON.stringify({
            css_class: icon.css_class,
            tags: Array.isArray(icon.tags) ? icon.tags : []
          })
        });
        
        const data = await response.json();
        
        if (data.success) {
          // Update original tags reference
          this.originalIconTags[icon.css_class] = Array.isArray(icon.tags) ? [...icon.tags] : [];
          // Mark icon as not edited anymore
          this.editedIcons[icon.css_class] = false;
          this.showToastMessage('Tags updated successfully', 'success');
        } else {
          this.showToastMessage(`Failed to save: ${data.message || 'Unknown error'}`, 'error');
        }
      } catch (error) {
        console.error('Error saving tags:', error);
        this.showToastMessage('Network error while saving tags', 'error');
      }
    },
    
    // Show toast message
    showToastMessage(message, type = 'success') {
      this.toastMessage = message;
      this.toastType = type;
      this.showToast = true;
      
      // Auto-hide toast after 3 seconds
      setTimeout(() => {
        this.showToast = false;
      }, 3000);
    },
    
    // Go to a specific page
    goToPage(page) {
      if (page < 1 || page > this.totalPages) return;
      this.currentPage = page;
      
      // Scroll to top of icon grid
      window.scrollTo({
        top: this.$el.offsetTop,
        behavior: 'smooth'
      });
    }
  }
};
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style> 