<template>
  <div 
    class="grid grid-cols-1 sm:grid-cols-2 gap-3 p-3 sm:p-4 md:p-5 bg-white dark:bg-transparent border border-gray-200 dark:border-gray-900 rounded-lg"
    v-bind="$attrs"
  >
    <div class="w-full aspect-square flex items-center justify-center order-1 sm:order-none">
      <h1 class="text-black dark:text-gray-200 w-full h-full max-w-[300px] mx-auto">
        <a :href="link" class="w-full h-full flex items-center justify-center">
          <img v-if="imagePath" 
               :src="imagePath" 
               :alt="title" 
               class="w-full h-full object-contain transition-all duration-200 hover:scale-105" 
               @error="handleImageError">
          <i v-else 
             :class="icons + ' text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-7xl xl:text-8xl transition-all duration-200'" 
             aria-hidden="true">
          </i>
        </a>
      </h1>
    </div>
    <div class="flex flex-col justify-center order-2 sm:order-none">
      <h2 class="text-3xl xs:text-3xl sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold leading-none gant-modern-bold tracking-tighter dark:text-gray-200 mb-2">
        <a :href="link">{{ title }}</a>
      </h2>
      
      <div class="mb-4">
        <div>
          <p class="text-3xs xs:text-2xs sm:text-xs md:text-sm font-medium leading-none text-gray-300 dark:text-gray-700 gant-modern-regular mb-1">
            Weights:
          </p>
          <p class="text-sm xs:text-base sm:text-lg md:text-xl font-medium leading-none text-black dark:text-neutral-200 mb-2">
            <i class="design design-letter opacity-25 me-2"></i>
            <span class="gant-modern-bold">{{ weights }}</span>
          </p>
        </div>
        <div>
          <p class="text-2xs xs:text-xs sm:text-sm md:text-base font-medium leading-none text-black dark:text-neutral-200 mb-4">
            <i class="design design-info opacity-25 me-2"></i>
            <span class="gant-modern-regular">{{ description }}</span>
          </p>
        </div>
      </div>

      <div class="flex gap-1.5 sm:gap-2 relative min-h-[28px] sm:min-h-[32px] md:min-h-[36px] w-full">
        <a 
          :href="link"
          class="inline-flex items-center justify-center px-3 xs:px-3 sm:px-4 md:px-4 py-1.5 xs:py-1.5 sm:py-2 transition-all duration-300 ease-out rounded-lg text-2xs xs:text-xs sm:text-sm gant-modern-bold hover:shadow-sm bg-gray-100 hover:bg-gray-50 dark:bg-gray-900 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200 dark:border-gray-600 scale-100 flex-1"
        >
          <div class="relative inline-flex items-center justify-center w-full">
            <div class="inline-flex items-center">
              <i class="design design-font text-gray-400 mr-1.5 group-hover:text-gray-500"></i>
              <span>View Documentation</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardFamily',
  inheritAttrs: false,
  data() {
    return {
      imageError: false
    };
  },
  props: {
    title: {
      type: String,
      default: 'Font Family'
    },
    icons: {
      type: String,
      default: 'design design-font'
    },
    imagePath: {
      type: String,
      default: null
    },
    link: {
      type: String,
      default: '#'
    },
    descriptionTitle: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    weights: {
      type: String,
      default: 'Regular, Medium, Bold'
    }
  },
  methods: {
    handleImageError() {
      this.imageError = true;
    }
  }
}
</script> 