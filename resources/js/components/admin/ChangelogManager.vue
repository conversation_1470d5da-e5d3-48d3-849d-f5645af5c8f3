<template>
  <div class="">
    <!-- Header Section - Simplified to match projects.blade.php -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 lg:mb-8">
      <div>
        <div class="flex items-center gap-3">
          <h2 class="text-3xl lg:text-4xl gant-modern-bold text-gray-900 dark:text-white">Changelog</h2>
          <button 
            type="button" 
            @click="toggleNewEntryForm"
            class="inline-flex items-center px-2 py-1.5 bg-slate-900 dark:bg-slate-200 text-white dark:text-slate-900 border rounded-md gant-modern-regular text-sm hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
          >
            <i class="design design-plus mr-2"></i>
            Add New Entry
          </button>
        </div>
        <div class="text-gray-500 dark:text-gray-400 mt-2 gant-modern-regular">
          <i class="wms wms-browse-history-outline opacity-50 mr-1"></i>Total entries: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ entries.length }}</span>
        </div>
      </div>
    </div>
    <div class="relative p-6 overflow-hidden bg-gray-50/50 dark:bg-gray-900/20 rounded-xl min-h-screen">
      <!-- Pattern SVG -->
      <svg class="absolute inset-0 w-full h-full pointer-events-none text-gray-900/[0.07] dark:text-gray-100/[0.08] z-0" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="pattern-3" patternUnits="userSpaceOnUse" width="8" height="8" patternTransform="rotate(0)">
            <path d="M-1,1 l4,-4 M0,8 l8,-8 M6,10 l4,-4" stroke="currentColor" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#pattern-3)" />
      </svg>

      <!-- Entries List -->
      <div class="relative z-10 max-w-5xl mx-auto">
        <div v-if="showNewEntryForm" class="max-w-5xl mx-auto mb-8 bg-white dark:bg-slate-950 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
      <div class="border-b border-gray-200 dark:border-gray-700/50 p-6">
        <h3 class="text-3xl gant-modern-bold text-gray-900 dark:text-white">
          {{ currentEntry ? 'Edit Changelog Entry' : 'Add New Changelog Entry' }}
        </h3>
      </div>
      <div class="p-6">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Version</label>
              <input
                v-model="form.version"
                type="text"
                class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"
                required
              >
            </div>
            <div>
              <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Release Date</label>
              <input
                v-model="form.release_date"
                type="date"
                class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"
                required
              >
            </div>
          </div>

          <div>
            <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Title</label>
            <input
              v-model="form.title"
              type="text"
              class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"
              required
            >
          </div>

          <div>
            <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
            <textarea
              v-model="form.description"
              rows="3"
              class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-slate-950 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"
            ></textarea>
          </div>

          <div>
            <label class="block text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-2">Changes</label>
            <div class="space-y-3">
              <div v-for="(change, index) in form.changes" :key="index" class="flex gap-2">
                <input
                  v-model="form.changes[index]"
                  type="text"
                  class="flex-1 rounded-lg border-gray-300 dark:border-gray-700 dark:bg-gray-800/50 focus:border-gray-500 dark:focus:border-gray-400 focus:ring-gray-500 dark:focus:ring-gray-400"
                  placeholder="Enter a change..."
                >
                <button 
                  type="button"
                  @click="removeChange(index)"
                  class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                >
                  <i class="design design-trash text-lg"></i>
                </button>
              </div>
              <button 
                type="button"
                @click="addChange"
                class="inline-flex items-center px-3 py-1.5 text-sm gant-modern-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700/50 transition-colors duration-200"
              >
                <i class="design design-plus mr-1.5"></i>
                Add Change
              </button>
            </div>
          </div>

          <div class="flex items-center">
            <input
              v-model="form.is_major"
              type="checkbox"
              class="rounded border-gray-300 dark:border-gray-700 text-gray-600 focus:ring-gray-500"
            >
            <label class="ml-2 text-sm gant-modern-medium text-gray-700 dark:text-gray-300">
              Mark as major update
            </label>
          </div>

          <div class="flex justify-end gap-3">
            <button
              type="button"
              @click="toggleNewEntryForm"
              class="px-4 py-2 text-sm gant-modern-medium rounded-xl border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm gant-modern-medium rounded-xl bg-gray-800 dark:bg-gray-700 text-white hover:bg-gray-700 dark:hover:bg-gray-600"
            >
              {{ currentEntry ? 'Update Entry' : 'Save Entry' }}
            </button>
          </div>
        </form>
      </div>
    </div>
        <div class="space-y-2">
          <div v-for="entry in entries" :key="entry.id" class="bg-white dark:bg-gray-950 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex flex-col gap-2 p-4">
              <!-- Header with Version and Actions -->
              <div class="flex items-start justify-between gap-4">
                <!-- Left side: Title and Description -->
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg gant-modern-bold text-gray-900 dark:text-white truncate">
                    {{ entry.title }}
                  </h3>
                  <p v-if="entry.description" class="text-sm text-gray-600 dark:text-gray-400 gant-modern-regular mt-1">
                    {{ entry.description }}
                  </p>
                </div>

                <!-- Right side: Version Info and Actions -->
                <div class="flex flex-col items-end gap-2">
                  <div class="flex items-center gap-1">
                    <div class="flex items-center gap-1.5">
                      <span v-if="entry.is_major" class="inline-flex items-center px-2 py-1 text-xs gant-modern-medium text-purple-800 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/10 rounded-full border border-purple-200 dark:border-purple-800/30">
                        Major Update
                      </span>
                      <span class="inline-flex items-center px-2 py-1 text-xs gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                        v{{ entry.version }}
                      </span>
                      <span class="inline-flex items-center px-2 py-1 text-xs gant-modern-medium text-gray-800 dark:text-gray-300 bg-gray-100 dark:bg-gray-800/50 rounded-full border border-gray-200 dark:border-gray-700/50">
                        <i class="claims claims-clock-outline mr-1"></i>
                        {{ formatDate(entry.release_date) }}
                      </span>
                    </div>
                    <div class="flex gap-1 ml-2">
                      <button 
                        @click="editEntry(entry)"
                        class="p-1.5 text-gray-500 dark:text-gray-400"
                      >
                        <i class="design design-edit text-lg"></i>
                      </button>
                      <button 
                        @click="deleteEntry(entry)"
                        class="p-1.5 text-red-500 dark:text-red-400"
                      >
                        <i class="design design-trash text-lg"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Changes List -->
              <div v-if="entry.changes && entry.changes.length" class="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 mt-1">
                <h4 class="text-xs gant-modern-medium text-gray-900 dark:text-white mb-2">Changes in this version:</h4>
                <ul class="space-y-1.5">
                  <li v-for="(change, idx) in entry.changes" :key="idx" class="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <i class="design design-component2 mt-0.5 text-gray-500 dark:text-gray-400"></i>
                    <span>{{ change }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="!entries.length" class="text-center py-8">
            <div class="flex justify-center mb-3">
              <i class="wms wms-browse-history-outline text-5xl text-gray-400 dark:text-gray-600"></i>
            </div>
            <h3 class="text-lg gant-modern-medium text-gray-900 dark:text-white mb-1">
              No Changelog Entries
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Start by adding your first changelog entry.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

const props = defineProps({
  initialEntries: {
    type: Array,
    required: true
  }
});

const entries = ref(props.initialEntries);
const showNewEntryForm = ref(false);
const currentEntry = ref(null);

const form = reactive({
  version: '',
  title: '',
  description: '',
  release_date: '',
  changes: [''],
  is_major: false,
  order: 0
});

const resetForm = () => {
  form.version = '';
  form.title = '';
  form.description = '';
  form.release_date = '';
  form.changes = [''];
  form.is_major = false;
  form.order = 0;
};

const toggleNewEntryForm = () => {
  showNewEntryForm.value = !showNewEntryForm.value;
  if (!showNewEntryForm.value) {
    resetForm();
    currentEntry.value = null;
  }
};

const addChange = () => {
  form.changes.push('');
};

const removeChange = (index) => {
  form.changes.splice(index, 1);
  if (form.changes.length === 0) {
    form.changes.push('');
  }
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('sk-SK', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const editEntry = (entry) => {
  currentEntry.value = entry;
  form.version = entry.version;
  form.title = entry.title;
  form.description = entry.description || '';
  form.release_date = entry.release_date;
  form.is_major = entry.is_major;
  form.order = entry.order;
  form.changes = entry.changes && entry.changes.length ? [...entry.changes] : [''];
  showNewEntryForm.value = true;
};

const handleSubmit = async () => {
  // Filter out empty changes
  const changes = form.changes.filter(change => change.trim() !== '');

  const data = {
    version: form.version,
    title: form.title,
    description: form.description || '',
    release_date: form.release_date,
    is_major: form.is_major ? 1 : 0,
    order: parseInt(form.order),
    changes: changes
  };

  try {
    const url = currentEntry.value
      ? `/admin/changelog/${currentEntry.value.id}`
      : '/admin/changelog';
    
    const response = await fetch(url, {
      method: currentEntry.value ? 'PUT' : 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (!response.ok) {
      const result = await response.json();
      if (result.errors) {
        throw new Error(Object.values(result.errors).flat().join('\n'));
      } else if (result.message) {
        throw new Error(result.message);
      }
      throw new Error('Failed to save entry');
    }

    const result = await response.json();
    
    if (currentEntry.value) {
      const index = entries.value.findIndex(e => e.id === currentEntry.value.id);
      if (index !== -1) {
        entries.value[index] = result.entry;
      }
    } else {
      entries.value.unshift(result.entry);
    }
    
    toggleNewEntryForm();
  } catch (error) {
    console.error('Error submitting form:', error);
    alert(error.message || 'Failed to save changelog entry. Please try again.');
  }
};

const deleteEntry = async (entry) => {
  if (!confirm('Are you sure you want to delete this entry?')) return;

  try {
    const response = await fetch(`/admin/changelog/${entry.id}`, {
      method: 'DELETE',
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (!response.ok) {
      const result = await response.json();
      throw new Error(result.message || 'Failed to delete entry');
    }

    entries.value = entries.value.filter(e => e.id !== entry.id);
  } catch (error) {
    console.error('Error deleting entry:', error);
    alert(error.message || 'Failed to delete changelog entry. Please try again.');
  }
};
</script> 