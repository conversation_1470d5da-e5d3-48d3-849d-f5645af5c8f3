<template>
  <div>
    <!-- Modal for Database Operations -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div class="bg-white dark:bg-slate-950 rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ modalTitle }}</h3>
          <button @click="closeModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="design design-close text-xl"></i>
          </button>
        </div>
        
        <div class="space-y-4">
          <!-- Import Form -->
          <div v-if="modalType === 'import'" class="space-y-4">
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 text-center">
              <input
                type="file"
                ref="fileInput"
                accept=".sqlite,.db,.sqlite3,.db3,application/x-sqlite3,application/vnd.sqlite3,application/sqlite,application/db,application/octet-stream"
                class="hidden"
                @change="handleFileSelect"
              />
              <button
                @click="$refs.fileInput.click()"
                class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
              >
                {{ trans('admin.select_database_file') }}
              </button>
              <p v-if="selectedFile" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {{ selectedFile.name }}
              </p>
            </div>
            <button
              v-if="selectedFile"
              @click="importDatabase"
              class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
              :disabled="isImporting"
            >
              {{ isImporting ? trans('admin.importing') : trans('admin.import_database') }}
            </button>
          </div>

          <!-- Export Confirmation -->
          <div v-if="modalType === 'export'" class="space-y-4">
            <p class="text-gray-600 dark:text-gray-400">
              {{ trans('admin.export_confirmation') }}
            </p>
            <button
              @click="exportDatabase"
              class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
              :disabled="isExporting"
            >
              {{ isExporting ? trans('admin.exporting') : trans('admin.export_database') }}
            </button>
          </div>
        </div>

        <!-- Status Messages -->
        <div v-if="message" :class="['mt-4 p-4 rounded-md', messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700']">
          {{ message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'

const showModal = ref(false)
const modalType = ref('')
const modalTitle = ref('')
const isExporting = ref(false)
const isImporting = ref(false)
const selectedFile = ref(null)
const message = ref('')
const messageType = ref('success')

// Helper function for translations
const trans = (key) => {
  // Access Laravel's global translations
  return window._translations && window._translations[key] 
    ? window._translations[key] 
    : key.split('.').pop()
}

const openModal = (type) => {
  modalType.value = type
  modalTitle.value = type === 'import' ? trans('admin.import_database') : trans('admin.export_database')
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedFile.value = null
  message.value = ''
}

const exportDatabase = async () => {
  try {
    isExporting.value = true
    message.value = ''
    
    const response = await axios.get('/admin/database/export', {
      responseType: 'blob'
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'database_backup.sqlite')
    document.body.appendChild(link)
    link.click()
    link.remove()
    
    message.value = trans('admin.database_exported')
    messageType.value = 'success'
    setTimeout(closeModal, 2000)
  } catch (error) {
    message.value = error.response?.data?.error || trans('admin.export_failed')
    messageType.value = 'error'
  } finally {
    isExporting.value = false
  }
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    // Accept files with .sqlite extension or any file with sqlite MIME type
    const fileExtension = file.name.split('.').pop().toLowerCase()
    const validTypes = ['application/x-sqlite3', 'application/vnd.sqlite3', 'application/sqlite', 'application/db', 'application/octet-stream']

    if (fileExtension === 'sqlite' || fileExtension === 'db' || validTypes.includes(file.type)) {
      selectedFile.value = file
      message.value = ''
    } else {
      message.value = trans('admin.invalid_database_file')
      messageType.value = 'error'
      selectedFile.value = null
    }
  }
}

const importDatabase = async () => {
  if (!selectedFile.value) return

  try {
    isImporting.value = true
    message.value = ''

    const formData = new FormData()
    formData.append('database', selectedFile.value)

    await axios.post('/admin/database/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    message.value = trans('admin.database_imported')
    messageType.value = 'success'
    setTimeout(closeModal, 2000)
  } catch (error) {
    message.value = error.response?.data?.error || trans('admin.import_failed')
    messageType.value = 'error'
  } finally {
    isImporting.value = false
  }
}

// Expose methods to parent
defineExpose({
  openModal
})
</script> 