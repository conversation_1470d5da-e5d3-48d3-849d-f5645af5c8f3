<template>
    <div class="datatable-component" :class="{'responsive-table': responsive}">
        <!-- Header with hide columns dropdown -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <slot name="header-actions"></slot>
            </div>
            <div class="d-flex gap-2">
                <slot name="table-tools"></slot>
                <div class="dropdown" v-if="showColumnHiding">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle d-flex align-items-center" 
                        type="button" 
                        :id="`table_settings_${uid}`"
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                        <i class="tos tos-cells-setup-outline me-2"></i> Columns
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end shadow-sm" :aria-labelledby="`table_settings_${uid}`">
                        <li v-for="col in localColumns" :key="col.key" class="px-2 py-1">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="col.visible"
                                    :id="`col_vis_${col.key}_${uid}`">
                                <label class="form-check-label ms-2" :for="`col_vis_${col.key}_${uid}`">
                                    {{ col.label }}
                                </label>
                            </div>
                        </li>
                        <li class="text-center mt-2">
                            <button @click.prevent="resetColumns" class="btn btn-outline-secondary btn-sm w-75">Reset</button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- The Table -->
        <div class="table-container border rounded overflow-auto" :class="{'sticky-header': stickyHeader}">
            <table class="table table-sm mb-0" :class="[
                tableClasses,
                hover ? 'table-hover' : '',
                striped ? 'table-striped' : ''
            ]">
                <thead class="bg-light text-uppercase fw-semibold small border-bottom" :class="stickyHeader ? 'sticky-top' : ''">
                    <tr class="text-nowrap">
                        <th v-for="col in visibleColumns" :key="col.key" scope="col" 
                            :class="[
                                getColumnClass(col),
                                'align-middle',
                                'py-3'
                            ]">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i v-if="col.icon" :class="[col.icon, 'me-2 opacity-75']"></i>
                                    <span>{{ col.label }}</span>
                                    <i v-if="col.sortable" class="tos tos-sort-by ms-2 opacity-50" style="font-size: 0.8rem;"></i>
                                    <span v-if="col.align" class="badge bg-light text-secondary border ms-2 small">{{ col.align }}</span>
                                </div>
                                <a v-if="showColumnHiding" href="#" @click.prevent="hideColumn(col.key)"
                                    class="link-secondary text-decoration-none ms-2 opacity-50 hide-column-btn" title="Hide column">
                                    <i class="tos tos-hidden"></i>
                                </a>
                            </div>
                        </th>
                        <th v-if="$slots.actions" :class="[actionsColumnClass, 'align-middle', 'py-3']">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Actions</span>
                                <span class="badge bg-light text-dark border ms-2">{{ visibleColumns.length }}</span>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, itemIndex) in data" :key="item.id || itemIndex" 
                        :class="[
                            getRowClass(item), 
                            responsive ? 'd-block d-md-table-row mb-3 border rounded shadow-sm' : ''
                        ]">
                        <td v-for="(col, colIndex) in visibleColumns" :key="col.key" 
                            :data-label="col.label"
                            :class="[
                                getCellClass(col),
                                'align-middle',
                                responsive ? 'd-block d-md-table-cell position-relative border-top' : '',
                                responsive && colIndex === 0 ? 'border-top-0' : ''
                            ]">
                            <slot :name="`cell(${col.key})`" :item="item" :value="getValue(item, col.key)">
                                {{ getValue(item, col.key) }}
                            </slot>
                        </td>
                        <td v-if="$slots.actions" 
                            data-label="Actions"
                            :class="[
                                actionsColumnClass,
                                'align-middle',
                                responsive ? 'd-block d-md-table-cell position-relative border-top' : '',
                                responsive && visibleColumns.length === 0 ? 'border-top-0' : ''
                            ]">
                            <slot name="actions" :item="item"></slot>
                        </td>
                    </tr>
                    <tr v-if="!data || data.length === 0">
                        <td :colspan="visibleColumns.length + ($slots.actions ? 1 : 0)"
                            class="text-center py-5 bg-light">
                            <slot name="empty-state">
                                <div class="py-4 text-muted">
                                    <i class="tos tos-document-search fs-2 d-block mb-2"></i>
                                    No data available.
                                </div>
                            </slot>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex flex-wrap justify-content-between align-items-center mt-3" v-if="pagination">
            <div class="text-muted small mb-2 mb-md-0" v-if="pagination.total">
                <span class="fw-semibold">{{ pagination.total }}</span> entries found, showing <span class="fw-semibold">{{ pagination.from }}-{{ pagination.to }}</span>
            </div>
            <nav v-if="pagination.links && pagination.links.length > 1" class="ms-auto">
                <ul class="pagination pagination-sm mb-0 flex-wrap">
                    <li v-for="(link, index) in pagination.links" :key="index"
                        :class="['page-item', { 'active': link.active, 'disabled': !link.url }]">
                        <a class="page-link rounded-1 mx-1 shadow-sm" href="#" @click.prevent="onPageChange(link.url)" v-html="link.label"></a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</template>

<script>
import {
    ref,
    computed,
    watch,
    getCurrentInstance
} from 'vue';

export default {
    name: 'DatatableComponent',
    props: {
        columns: {
            type: Array,
            required: true,
        },
        data: {
            type: Array,
            required: true,
        },
        pagination: {
            type: Object,
            default: null,
        },
        showColumnHiding: {
            type: Boolean,
            default: true,
        },
        rowClass: {
            type: Function,
            default: null,
        },
        responsive: {
            type: Boolean,
            default: true,
        },
        stickyHeader: {
            type: Boolean,
            default: false,
        },
        tableClasses: {
            type: [String, Array, Object],
            default: 'table-bordered'
        },
        actionsColumnClass: {
            type: String,
            default: 'text-end'
        },
        defaultColumnAlign: {
            type: String,
            default: '' // Options: 'start', 'center', 'end'
        },
        striped: {
            type: Boolean,
            default: false
        },
        hover: {
            type: Boolean,
            default: true
        }
    },
    emits: ['page-change', 'columns-updated'],
    setup(props, {
        emit
    }) {
        const uid = getCurrentInstance().uid;
        const localColumns = ref(JSON.parse(JSON.stringify(props.columns)));
        
        // Process columns to ensure they have alignment and width properties
        const processColumns = (columns) => {
            return columns.map(col => {
                // Ensure column has alignment property
                if (!col.hasOwnProperty('align')) {
                    col.align = props.defaultColumnAlign;
                }
                
                // Ensure column has width property if specified
                if (!col.hasOwnProperty('width') && col.width) {
                    col.width = null;
                }
                
                return col;
            });
        };
        
        watch(
            () => props.columns,
            (newColumns) => {
                localColumns.value = processColumns(JSON.parse(JSON.stringify(newColumns)));
            }, {
                deep: true,
                immediate: true
            }
        );

        const visibleColumns = computed(() => {
            return localColumns.value.filter((c) => c.visible);
        });

        const hideColumn = (key) => {
            const col = localColumns.value.find((c) => c.key === key);
            if (col) {
                col.visible = false;
            }
        };

        const resetColumns = () => {
            localColumns.value = processColumns(JSON.parse(JSON.stringify(props.columns)));
        };

        const getValue = (item, key) => {
            return key.split('.').reduce((acc, part) => acc && acc[part], item);
        };
        
        const onPageChange = (url) => {
            if (url) {
                emit('page-change', url);
            }
        };
        
        const getColumnClass = (col) => {
            let classes = [];
            
            // Add alignment class if specified
            if (col.align) {
                switch(col.align) {
                    case 'start':
                        classes.push('text-start');
                        break;
                    case 'center':
                        classes.push('text-center');
                        break;
                    case 'end':
                        classes.push('text-end');
                        break;
                }
            }
            
            // Add width style if specified
            if (col.width) {
                classes.push(`w-${col.width}`);
            }
            
            return classes.join(' ');
        };
        
        const getCellClass = (col) => {
            let classes = [];
            
            // Add alignment class if specified
            if (col.align) {
                switch(col.align) {
                    case 'start':
                        classes.push('text-start');
                        break;
                    case 'center':
                        classes.push('text-center');
                        break;
                    case 'end':
                        classes.push('text-end');
                        break;
                }
            }
            
            return classes.join(' ');
        };
        
        const getRowClass = (item) => {
            let classes = [];
            
            // Add custom row class if function provided
            if (props.rowClass) {
                const customClass = props.rowClass(item);
                if (customClass) {
                    classes.push(customClass);
                }
            }
            
            // Add striped class if enabled
            if (props.striped) {
                classes.push('table-striped');
            }
            
            return classes.join(' ');
        };

        watch(
            localColumns,
            (newCols) => {
                emit('columns-updated', JSON.parse(JSON.stringify(newCols)));
            }, {
                deep: true
            }
        );

        return {
            uid,
            localColumns,
            visibleColumns,
            hideColumn,
            resetColumns,
            getValue,
            onPageChange,
            getColumnClass,
            getCellClass,
            getRowClass
        };
    },
}
</script>

<style scoped>
/* Only keep minimal custom styling that can't be achieved with Bootstrap classes */

/* Hide column button styling */
.hide-column-btn {
    transition: opacity 0.2s ease;
}

.table th:hover .hide-column-btn {
    opacity: 1 !important;
}

/* Table container styling */
.table-container {
    position: relative;
    max-width: 100%;
    overflow-x: auto;
}

/* Sticky header styling */
.table-container.sticky-header {
    max-height: 70vh;
    overflow-y: auto;
}

.table-container.sticky-header thead.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--bs-light);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Responsive styling */
@media screen and (max-width: 768px) {
    .datatable-component.responsive-table .table td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        padding-left: 1rem;
        font-weight: bold;
        text-align: left;
        white-space: normal;
    }
    
    .datatable-component.responsive-table .table td {
        padding-left: 50%;
    }
    
    /* Hide headers only on mobile responsive view */
    .datatable-component.responsive-table .table thead {
        display: none !important;
    }
    
    /* Card-like rows on mobile */
    .datatable-component.responsive-table .table tbody tr {
        margin-bottom: 1rem;
    }
}

/* Non-responsive mode - ensure headers are always visible */
.datatable-component:not(.responsive-table) .table thead {
    display: table-header-group !important;
}
</style> 