<template>
  <div class="bg-white dark:bg-slate-950 rounded-xl shadow-sm overflow-hidden">
    <!-- Header with Title and Action Buttons -->
    <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700 flex justify-between items-center">
      <h2 class="text-xl gant-modern-bold text-slate-900 dark:text-slate-100">Tag Management</h2>
      <div class="flex items-center space-x-2">
        <button
          @click="showCreateTagModal = true"
          class="inline-flex items-center px-3 py-2 border border-slate-300 dark:border-slate-700 rounded-md bg-slate-900 dark:bg-slate-800 text-white hover:bg-slate-800 dark:hover:bg-slate-700"
        >
          <i class="design design-add mr-1.5 text-slate-400"></i>
          New Tag
        </button>
      </div>
    </div>
    
    <!-- Tag DataTable -->
    <DataTable
      ref="dataTable"
      :data="tags"
      :columns="columns"
      default-sort-column="name"
      default-sort-direction="asc"
      table-id="tags-table"
      @row-click="editTag"
      class="min-h-[calc(100vh-220px)]"
    >
      <!-- Custom header template -->
      <template #header="{ column, sort }">
        <div class="flex items-center gap-2">
          <span class="text-xs text-slate-500 dark:text-slate-400 opacity-70 font-medium">{{ column.label }}</span>
          <button 
            v-if="column.sortable" 
            @click="sort(column.key)"
            class="group flex items-center gap-1 text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-colors duration-150"
          >
            <i class="design design-sort text-xs"></i>
            <span class="text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-150">Sort</span>
          </button>
        </div>
      </template>
      
      <!-- Tag name cell template -->
      <template #cell(name)="{ value, item }">
        <div class="flex items-center">
          <div class="w-10 h-10 flex items-center justify-center bg-slate-100 dark:bg-slate-800 rounded-md mr-3">
            <i class="design design-tag text-slate-600 dark:text-slate-400 text-xl"></i>
          </div>
          <span class="gant-modern-bold text-slate-900 dark:text-slate-100">{{ value }}</span>
        </div>
      </template>
      
      <!-- Icon count cell template -->
      <template #cell(count)="{ value, item }">
        <div class="flex flex-col items-center justify-center">
          <div v-if="value > 0" class="px-4 py-2 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center">
            <i class="design design-check-list text-lg text-slate-600 dark:text-slate-400 mr-2"></i>
            <span class="gant-modern-bold text-slate-900 dark:text-slate-100 text-lg">{{ value }}</span>
          </div>
          <div v-else class="px-4 py-2 bg-slate-100 dark:bg-slate-800 rounded-lg flex items-center">
            <i class="design design-cancel text-lg mr-2 opacity-70 text-slate-500 dark:text-slate-400"></i>
            <span class="text-slate-500 dark:text-slate-400">None</span>
          </div>
          <div class="text-xs text-slate-500 dark:text-slate-400 mt-1">
            {{ value === 1 ? '1 icon' : (value > 0 ? value + ' icons' : 'No icons') }}
          </div>
        </div>
      </template>
      
      <!-- Icon count text cell template -->
      <template #cell(iconCount)="{ value, item }">
        <div class="text-center">
          <span class="px-3 py-1.5 rounded-full text-sm font-medium" 
                :class="item.count > 0 
                  ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400' 
                  : 'bg-slate-100 text-slate-500 dark:bg-slate-800 dark:text-slate-400'">
            {{ item.count === 1 ? '1 icon' : (item.count > 0 ? item.count + ' icons' : 'No icons') }}
          </span>
        </div>
      </template>
      
      <!-- Font usage cell template -->
      <template #cell(fonts)="{ item }">
        <div class="px-2">
          <div v-if="item.count > 0 && item.usage && !item.usage.error" class="flex flex-col">
            <!-- Font count badge -->
            <div class="flex items-center mb-3">
              <span class="px-3 py-1 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-400 text-sm font-medium">
                {{ item.usage?.fontCount || 0 }} {{ item.usage?.fontCount === 1 ? 'font' : 'fonts' }}
              </span>
            </div>
            
            <!-- Font distribution list -->
            <div v-if="item.usage?.fontDistribution && item.usage.fontDistribution.length > 0"
                 class="flex flex-row gap-2 ">
              <div v-for="(font, idx) in item.usage.fontDistribution" :key="idx"
                   class="inline-flex items-center border border-solid border-slate-300 dark:border-slate-600 rounded-md overflow-hidden">
                <!-- Font thumbnail -->
                <div class="w-6 h-6 flex items-center justify-center border-r border-solid border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-800">
                  <i :class="[font.name.toLowerCase(), font.name.toLowerCase() + '-thumbnail', 'text-sm text-slate-700 dark:text-slate-300']"></i>
                </div>
                
                <!-- Font name and count -->
                <div class="px-2 py-1 text-sm text-slate-700 dark:text-slate-300 gant-modern-bold">
                  {{ font.name }}:<span class="gant-modern-regular ms-1">{{ font.count }}</span>
                </div>
              </div>
            </div>
            <div v-else-if="item.usage?.fontCount === 0" class="text-sm text-slate-500 dark:text-slate-400 italic">
              No fonts use this tag
            </div>
          </div>
          <div v-else-if="item.usage?.loading" class="flex items-center py-2">
            <i class="design design-loading animate-spin mr-2 text-slate-400 dark:text-slate-600"></i>
            <span class="text-sm text-slate-500 dark:text-slate-400">Loading font data...</span>
          </div>
          <div v-else-if="item.usage?.error" class="flex items-center py-2">
            <div class="flex flex-col">
              <div class="flex items-center text-amber-600 dark:text-amber-500">
                <i class="design design-warning mr-2"></i>
                <span class="text-sm">Unable to load font data</span>
              </div>
              <span v-if="item.usage?.errorMessage" class="text-xs text-slate-500 dark:text-slate-400 mt-1 ml-6">
                {{ item.usage.errorMessage }}
              </span>
              <!-- Special message for numeric tags -->
              <span v-if="/^\d+$/.test(item.name)" class="text-xs text-slate-500 dark:text-slate-400 mt-1 ml-6 italic">
                Numeric tag names may not be supported
              </span>
            </div>
          </div>
          <div v-else class="text-sm text-slate-500 dark:text-slate-400 italic py-2">
            Not used in any fonts
          </div>
        </div>
      </template>
      
      <!-- Actions cell template -->
      <template #cell(actions)="{ item }">
        <div class="flex items-center justify-end space-x-3">
          <button
            @click.stop="editTag(item)"
            class="p-2 text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200 rounded-md hover:bg-slate-100 dark:hover:bg-slate-800"
            title="Edit tag"
          >
            <i class="design design-edit text-lg"></i>
          </button>
          <button
            @click.stop="confirmDeleteTag(item)"
            class="p-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20"
            title="Delete tag"
          >
            <i class="design design-trash text-lg"></i>
          </button>
        </div>
      </template>
      
      <!-- Empty state template -->
      <template #empty>
        <div class="px-6 py-12 text-center">
          <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-slate-100 dark:bg-slate-800">
            <i class="design design-tag text-4xl text-slate-300 dark:text-slate-700"></i>
          </div>
          <h3 class="text-lg gant-modern-medium text-slate-800 dark:text-slate-200 mb-2">No Tags Found</h3>
          <p class="text-slate-500 dark:text-slate-400 max-w-md mx-auto">
            {{ dataTable?.searchQuery ? 'No tags match your search criteria.' : 'There are no tags in the system yet. Create your first tag to get started.' }}
          </p>
          <button 
            v-if="!dataTable?.searchQuery"
            @click="showCreateTagModal = true"
            class="mt-4 px-4 py-2 bg-slate-900 dark:bg-slate-700 text-white rounded-md hover:bg-slate-800 dark:hover:bg-slate-600 inline-flex items-center"
          >
            <i class="design design-add mr-1.5"></i>
            Create Tag
          </button>
        </div>
      </template>
      
      <!-- Loading state template -->
      <template #loading>
        <div class="px-6 py-12 text-center">
          <div class="inline-block animate-spin w-12 h-12 border-3 border-slate-300 dark:border-slate-700 border-t-slate-900 dark:border-t-slate-300 rounded-full mb-4"></div>
          <p class="text-slate-500 dark:text-slate-400">Loading tags...</p>
        </div>
      </template>
    </DataTable>
    
    <!-- Tag Stats Footer -->
    <div class="px-6 py-3 bg-slate-50 dark:bg-slate-900/50 border-t border-slate-200 dark:border-slate-700 flex justify-between">
      <div class="text-sm text-slate-500 dark:text-slate-400">
        <span class="gant-modern-bold text-slate-700 dark:text-slate-300">{{ filteredTagsCount }}</span> tags found
      </div>
      <div class="text-sm text-slate-500 dark:text-slate-400">
        Total usage: <span class="gant-modern-bold text-slate-700 dark:text-slate-300">{{ totalUsage }}</span> icons
      </div>
    </div>
    
    <!-- Create/Edit Tag Modal -->
    <div v-if="showTagModal" class="fixed inset-0 bg-black/50 dark:bg-black/70 z-50 flex items-center justify-center p-4 sm:p-6">
      <div class="bg-white dark:bg-slate-950 rounded-xl shadow-xl w-full max-w-md p-6">
        <h3 class="text-xl gant-modern-bold text-slate-900 dark:text-slate-100">{{ editingTag ? 'Edit Tag' : 'Create New Tag' }}</h3>
        <div class="mt-4">
          <label for="tagName" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Tag Name</label>
          <input
            v-model="tagName"
            id="tagName"
            ref="tagNameInput"
            type="text"
            class="w-full border border-slate-300 dark:border-slate-700 rounded-md dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-slate-500 focus:border-slate-500 px-3 py-2"
            @keydown.enter="saveTag"
            @keydown.esc="closeTagModal"
            placeholder="Enter tag name"
          />
          <p v-if="tagError" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ tagError }}</p>
        </div>
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="closeTagModal"
            class="px-4 py-2 border border-slate-300 dark:border-slate-700 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
          >
            Cancel
          </button>
          <button
            @click="saveTag"
            class="px-4 py-2 bg-slate-900 text-white rounded-md hover:bg-slate-800 dark:bg-slate-700 dark:hover:bg-slate-600"
          >
            {{ editingTag ? 'Save Changes' : 'Create Tag' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black/50 dark:bg-black/70 z-50 flex items-center justify-center p-4 sm:p-6">
      <div class="bg-white dark:bg-slate-950 rounded-xl shadow-xl w-full max-w-md p-6">
        <div class="flex items-center">
          <div class="w-10 h-10 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mr-3">
            <i class="design design-warning text-xl text-red-600 dark:text-red-500"></i>
          </div>
          <h3 class="text-xl gant-modern-bold text-red-600 dark:text-red-500">Delete Tag</h3>
        </div>
        
        <div class="my-4">
          <p class="text-slate-700 dark:text-slate-300 mb-3">
            Are you sure you want to delete the tag <span class="gant-modern-bold">{{ selectedTag.name }}</span>?
          </p>
          
          <div class="p-3 my-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <p class="text-sm text-red-700 dark:text-red-400 flex items-start">
              <i class="design design-warning text-lg mr-2 mt-0.5"></i>
              <span>This will remove the tag from <span class="gant-modern-bold">{{ selectedTag.count }}</span> icons across your icon library and cannot be undone.</span>
            </p>
            
            <!-- Show error state if usage fetching failed -->
            <div v-if="tagUsage.error" class="mt-3 ml-6">
              <p class="text-sm text-red-700 dark:text-red-400 flex items-start">
                <i class="design design-circle-remove text-sm mr-1.5 mt-0.5"></i>
                <span>{{ tagUsage.errorMessage || 'Could not fetch additional usage information' }}</span>
              </p>
            </div>
            
            <!-- Show font distribution if available -->
            <div v-else-if="tagUsage.fontCount && tagUsage.fontCount > 0" class="mt-3 ml-6">
              <p class="text-sm text-red-700 dark:text-red-400 mb-2">Affected font libraries:</p>
              <ul class="text-xs text-red-600 dark:text-red-300 space-y-1 ml-3">
                <li v-for="font in tagUsage.fontDistribution?.slice(0, 3)" :key="font.name" class="flex items-center">
                  <i class="design design-circle-remove-outline text-sm mr-1.5"></i>
                  {{ font.name }} ({{ font.count }} {{ font.count === 1 ? 'icon' : 'icons' }})
                </li>
                <li v-if="tagUsage.fontCount > 3" class="text-xs italic ml-5">
                  and {{ tagUsage.fontCount - 3 }} more font libraries...
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-slate-300 dark:border-slate-700 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
          >
            Cancel
          </button>
          <button
            @click="deleteTag"
            :disabled="/^\d+$/.test(selectedTag.name)"
            :class="[
              'px-4 py-2 text-white rounded-md flex items-center',
              /^\d+$/.test(selectedTag.name) 
                ? 'bg-red-400 dark:bg-red-500 cursor-not-allowed' 
                : 'bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800'
            ]"
          >
            <i class="design design-trash mr-1.5"></i>
            Delete Tag
          </button>
        </div>
      </div>
    </div>
    
    <!-- Toast Messages -->
    <div
      v-if="toast.show"
      :class="[
        'fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-md transition-all duration-300',
        toast.type === 'success' ? 'bg-green-50 border border-green-200 dark:bg-green-900/30 dark:border-green-800' : 'bg-red-50 border border-red-200 dark:bg-red-900/30 dark:border-red-800'
      ]"
    >
      <div class="flex items-start">
        <div :class="[
          'flex-shrink-0 w-5 h-5 mr-3 mt-0.5',
          toast.type === 'success' ? 'text-green-500 dark:text-green-400' : 'text-red-500 dark:text-red-400'
        ]">
          <i :class="[
            toast.type === 'success' ? 'design design-circle-checked' : 'design design-circle-remove',
            'text-lg'
          ]"></i>
        </div>
        <div class="flex-1">
          <p class="text-sm gant-modern-medium text-slate-800 dark:text-slate-200">{{ toast.message }}</p>
        </div>
        <button @click="toast.show = false" class="ml-4 text-slate-400 hover:text-slate-500 dark:hover:text-slate-300">
          <i class="design design-close text-sm"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import DataTable from './DataTable.vue';

// Table columns
const columns = [
  {
    key: 'name',
    label: 'Tag Name',
    sortable: true,
    tdClass: 'text-slate-900 dark:text-slate-100 pl-6',
    width: 'w-1/4'
  },
  {
    key: 'count',
    label: 'Icon Usage',
    align: 'center',
    sortable: true,
    width: 'w-1/4',
    tdClass: 'align-middle'
  },
  {
    key: 'fonts',
    label: 'Font Usage',
    align: 'left',
    sortable: true,
    width: 'w-1/3',
    tdClass: 'align-middle py-3'
  },
  {
    key: 'actions',
    label: '',
    align: 'right',
    width: 'w-1/6',
    sortable: false,
    tdClass: 'align-middle pr-6'
  }
];

// State
const tags = ref([]);
const isLoading = ref(true);
const dataTable = ref(null);
const showTagModal = ref(false);
const showCreateTagModal = ref(false);
const showDeleteModal = ref(false);
const showUsageModal = ref(false);
const tagName = ref('');
const tagError = ref('');
const editingTag = ref(null);
const selectedTag = ref({ name: '', count: 0 });
const tagUsage = ref({});
const toast = ref({
  show: false,
  message: '',
  type: 'success'
});
const tagNameInput = ref(null);

// Computed
const filteredTagsCount = computed(() => {
  return dataTable.value?.filteredData?.value?.length || 0;
});

const totalUsage = computed(() => {
  return (dataTable.value?.filteredData?.value || []).reduce((sum, tag) => sum + (tag.count || 0), 0);
});

// Methods
const fetchTags = async () => {
  isLoading.value = true;
  
  try {
    const response = await fetch('/api/tags');
    
    if (!response.ok) {
      throw new Error(`API returned ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    tags.value = data;
    
    // Fetch usage information for all tags
    await fetchAllTagsUsage();
  } catch (error) {
    console.error('Error fetching tags:', error);
    showToast('Failed to load tags. Please try again.', 'error');
  } finally {
    isLoading.value = false;
  }
};

// New method to fetch usage data for all tags
const fetchAllTagsUsage = async () => {
  try {
    // Filter tags with count > 0 that need usage data
    const tagsWithUsage = tags.value.filter(tag => tag.count > 0);
    
    // Set default usage structure for all tags to prevent UI errors
    for (const tag of tags.value) {
      if (!tag.usage) {
        tag.usage = {
          fontCount: 0,
          fontDistribution: [],
          loading: tagsWithUsage.some(t => t.name === tag.name)
        };
      }
    }
    
    // Process tags in smaller batches to avoid overwhelming the server
    const batchSize = 3;
    for (let i = 0; i < tagsWithUsage.length; i += batchSize) {
      const batch = tagsWithUsage.slice(i, i + batchSize);
      
      // Process batch in parallel
      await Promise.all(batch.map(async (tag) => {
        // Skip purely numeric tags as they seem to cause backend issues
        if (/^\d+$/.test(tag.name)) {
          console.warn(`Skipping numeric tag "${tag.name}" due to known backend issues`);
          tag.usage = {
            fontCount: 0,
            fontDistribution: [],
            error: true,
            errorMessage: 'Numeric tag names are not supported by the backend'
          };
          return;
        }
        
        try {
          const response = await fetch('/api/tags/usage', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({ name: tag.name })
          });
          
          if (response.ok) {
            const usageData = await response.json();
            tag.usage = {
              ...usageData,
              loading: false,
              error: false
            };
          } else {
            console.error(`Failed to fetch usage data for tag ${tag.name}: ${response.status}`);
            tag.usage = {
              fontCount: 0,
              fontDistribution: [],
              loading: false,
              error: true,
              errorMessage: `API Error (${response.status})`
            };
          }
        } catch (tagError) {
          console.error(`Error processing tag ${tag.name}:`, tagError);
          tag.usage = {
            fontCount: 0,
            fontDistribution: [],
            loading: false,
            error: true,
            errorMessage: 'Network error'
          };
        }
      }));
      
      // Small delay between batches to give the server a break
      if (i + batchSize < tagsWithUsage.length) {
        await new Promise(resolve => setTimeout(resolve, 250));
      }
    }
  } catch (error) {
    console.error('Error fetching tag usage information:', error);
    // Make sure all tags have a valid usage object
    for (const tag of tags.value) {
      if (!tag.usage) {
        tag.usage = {
          fontCount: 0,
          fontDistribution: [],
          loading: false,
          error: true,
          errorMessage: 'Failed to load usage data'
        };
      }
    }
  }
};

const editTag = (tag) => {
  editingTag.value = {
    name: String(tag.name || ''), 
    count: Number(tag.count || 0)
  };
  tagName.value = String(tag.name || '');
  tagError.value = '';
  showTagModal.value = true;
  
  nextTick(() => {
    if (tagNameInput.value) {
      tagNameInput.value.focus();
      tagNameInput.value.select();
    }
  });
};

const createTag = () => {
  editingTag.value = null;
  tagName.value = '';
  tagError.value = '';
  showTagModal.value = true;
  
  nextTick(() => {
    if (tagNameInput.value) {
      tagNameInput.value.focus();
    }
  });
};

const closeTagModal = () => {
  showTagModal.value = false;
  tagError.value = '';
};

const saveTag = async () => {
  const name = tagName.value.trim();
  
  if (!name) {
    tagError.value = 'Tag name cannot be empty';
    return;
  }
  
  // Prevent creating numeric tags that cause backend errors
  if (/^\d+$/.test(name)) {
    tagError.value = 'Purely numeric tag names are not supported';
    return;
  }
  
  const tagExists = tags.value.some(tag => {
    if (typeof tag.name !== 'string') return false;
    
    return tag.name.toLowerCase() === name.toLowerCase() && 
           (!editingTag.value || tag.name !== editingTag.value.name);
  });
  
  if (tagExists) {
    tagError.value = `Tag "${name}" already exists`;
    return;
  }
  
  try {
    if (editingTag.value) {
      // Prevent renaming to numeric tags
      if (/^\d+$/.test(name) && !/^\d+$/.test(editingTag.value.name)) {
        tagError.value = 'Cannot rename to a purely numeric tag name';
        return;
      }
      
      const response = await fetch('/api/tags/rename', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({
          oldName: String(editingTag.value.name || ''),
          newName: String(name)
        })
      });
      
      if (!response.ok) {
        const contentType = response.headers.get('content-type');
        let errorMsg = `Request failed with status ${response.status}`;
        
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMsg = errorData.message || errorMsg;
          throw new Error(errorMsg);
        } else {
          throw new Error('Server returned an invalid response format');
        }
      }
      
      const data = await response.json();
      
      if (data.success) {
        const index = tags.value.findIndex(tag => tag.name === editingTag.value.name);
        if (index !== -1) {
          tags.value[index].name = name;
        }
        
        showToast(data.message || 'Tag renamed successfully', 'success');
      } else {
        showToast(data.message || 'Failed to rename tag', 'error');
      }
    } else {
      const response = await fetch('/api/tags/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({ name })
      });
      
      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        tags.value.push({ name, count: 0 });
        showToast(data.message || 'Tag created successfully', 'success');
      } else {
        showToast(data.message || 'Failed to create tag', 'error');
      }
    }
    
    showTagModal.value = false;
  } catch (error) {
    console.error('Error saving tag:', error);
    showToast('Error: ' + error.message, 'error');
  }
};

const confirmDeleteTag = async (tag) => {
  selectedTag.value = { ...tag };
  
  // Early check for numeric tags to avoid 500 errors
  if (/^\d+$/.test(selectedTag.value.name)) {
    tagUsage.value = {
      fontCount: 0,
      fontDistribution: [],
      error: true,
      errorMessage: 'Cannot fetch usage data for numeric tag names'
    };
    showDeleteModal.value = true;
    return;
  }
  
  try {
    // Show delete modal immediately, then fetch usage in background
    showDeleteModal.value = true;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch('/api/tags/usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        name: selectedTag.value.name
      }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      tagUsage.value = await response.json();
    } else {
      console.error(`Failed to fetch tag usage: ${response.status}`);
      tagUsage.value = {
        fontCount: 0,
        fontDistribution: [],
        error: true,
        errorMessage: `Could not retrieve usage details (${response.status})`
      };
    }
  } catch (error) {
    console.error('Error fetching tag usage:', error);
    tagUsage.value = {
      fontCount: 0,
      fontDistribution: [],
      error: true,
      errorMessage: error.name === 'AbortError' ? 'Request timed out' : 'Network error'
    };
  }
};

const deleteTag = async () => {
  try {
    // Early check for numeric tags to avoid 500 errors
    if (/^\d+$/.test(selectedTag.value.name)) {
      throw new Error('Cannot delete tags with numeric names due to server limitations');
    }
    
    // Show progress indication
    toast.value.message = 'Deleting tag...';
    toast.value.type = 'success';
    toast.value.show = true;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout
    
    const response = await fetch('/api/tags/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
      },
      body: JSON.stringify({
        name: selectedTag.value.name
      }),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Handle non-200 responses properly
    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      let errorMsg = `Request failed with status ${response.status}`;
      
      if (contentType && contentType.includes('application/json')) {
        try {
          const errorData = await response.json();
          errorMsg = errorData.message || errorMsg;
        } catch (e) {
          console.error('Failed to parse error response:', e);
        }
      }
      
      throw new Error(errorMsg);
    }
    
    // Process successful response
    const data = await response.json();
    
    if (data.success) {
      tags.value = tags.value.filter(tag => tag.name !== selectedTag.value.name);
      showToast(data.message || 'Tag deleted successfully', 'success');
    } else {
      showToast(data.message || 'Failed to delete tag', 'error');
    }
  } catch (error) {
    console.error('Error deleting tag:', error);
    
    let errorMessage = 'Network error while deleting tag';
    if (error.name === 'AbortError') {
      errorMessage = 'Request timed out. The server may be busy.';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    showToast(errorMessage, 'error');
  } finally {
    showDeleteModal.value = false;
  }
};

const showToast = (message, type = 'success') => {
  toast.value.message = message;
  toast.value.type = type;
  toast.value.show = true;
  
  setTimeout(() => {
    toast.value.show = false;
  }, 3000);
};

// Watch for create tag modal
watch(showCreateTagModal, (value) => {
  if (value) {
    createTag();
    showCreateTagModal.value = false;
  }
});

// Lifecycle hooks
onMounted(() => {
  fetchTags();
});
</script>

<style scoped>
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
</style>  