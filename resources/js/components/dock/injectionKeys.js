import { inject, provide } from 'vue';

export const MOUSE_X_INJECTION_KEY = Symbol('mouse-x');
export const MOUSE_Y_INJECTION_KEY = Symbol('mouse-y');
export const MAGNIFICATION_INJECTION_KEY = Symbol('magnification');
export const DISTANCE_INJECTION_KEY = Symbol('distance');
export const ORIENTATION_INJECTION_KEY = Symbol('orientation');

// For backward compatibility
export const DOCK_INJECTION_KEY = Symbol('dock-injection-key'); 