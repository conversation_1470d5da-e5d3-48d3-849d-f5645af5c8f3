/**
 * Combines multiple class values into a single string, filtering out falsy values
 * 
 * @param  {...any} classes - Class names or objects to combine
 * @returns {string} - Combined class string
 */
export function cn(...classes) {
  return classes
    .filter(Boolean)
    .map(c => {
      if (typeof c === 'string') return c;
      if (typeof c === 'object') {
        return Object.entries(c)
          .filter(([_, value]) => Boolean(value))
          .map(([key]) => key)
          .join(' ');
      }
      return '';
    })
    .join(' ');
} 