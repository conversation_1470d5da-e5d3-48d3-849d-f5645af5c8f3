<template>
  <div class="dock-example">
    <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-200">macOS-Style Dock</h2>
    
    <div class="settings">
      <div class="setting">
        <label for="magnification">Magnification:</label>
        <input type="range" id="magnification" v-model="magnification" min="0" max="100" step="5" />
        <span>{{ magnification }}%</span>
      </div>
      
      <div class="setting">
        <label for="distance">Distance:</label>
        <input type="range" id="distance" v-model="distance" min="50" max="300" step="10" />
        <span>{{ distance }}px</span>
      </div>
      
      <div class="setting">
        <label for="direction">Direction:</label>
        <select id="direction" v-model="direction">
          <option value="top">Top</option>
          <option value="middle">Middle</option>
          <option value="bottom">Bottom</option>
        </select>
      </div>
      
      <div class="setting">
        <label for="orientation">Orientation:</label>
        <select id="orientation" v-model="orientation">
          <option value="horizontal">Horizontal</option>
          <option value="vertical">Vertical</option>
        </select>
      </div>
    </div>
    
    <div class="dock-container" :class="{'h-96': orientation === 'vertical'}">
      <Dock 
        :magnification="Number(magnification)" 
        :distance="Number(distance)"
        :direction="direction"
        :orientation="orientation"
        class="dark:bg-slate-900/50 dark:border-slate-800 bg-white/70 border-gray-200"
      >
        <DockIcon v-for="(icon, index) in icons" :key="index">
          <div class="icon-content" :style="{ backgroundColor: icon.color }">
            <span>{{ icon.label }}</span>
          </div>
        </DockIcon>
        
        <DockSeparator />
        
        <DockIcon v-for="(icon, index) in icons2" :key="`second-${index}`">
          <div class="icon-content" :style="{ backgroundColor: icon.color }">
            <span>{{ icon.label }}</span>
          </div>
        </DockIcon>
      </Dock>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Dock, DockIcon, DockSeparator } from './index';

const magnification = ref(60);
const distance = ref(140);
const direction = ref('middle');
const orientation = ref('horizontal');

const icons = ref([
  { label: 'A', color: '#FF5733' },
  { label: 'B', color: '#33FF57' },
  { label: 'C', color: '#3357FF' },
]);

const icons2 = ref([
  { label: 'D', color: '#F733FF' },
  { label: 'E', color: '#FF33A8' },
]);
</script>

<style scoped>
.dock-example {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
}

.settings {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  max-width: 800px;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 8px;
}

.setting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting label {
  font-weight: 500;
  min-width: 100px;
}

.dock-container {
  display: flex;
  justify-content: center;
  min-height: 100px;
  width: 100%;
  margin-top: 1rem;
}

.icon-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}
</style> 