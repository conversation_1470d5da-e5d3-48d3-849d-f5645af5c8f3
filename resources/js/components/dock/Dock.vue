<template>
  <div
    ref="dockRef"
    :class="
      cn(
        'supports-backdrop-blur:bg-white/10 supports-backdrop-blur:dark:bg-black/10 mx-auto mt-8 flex h-[58px] w-max rounded-2xl border p-2 backdrop-blur-md transition-all gap-4',
        orientation === 'vertical' && 'flex-col w-[58px] h-max',
        props.class,
        dockClass,
      )
    "
    @mousemove="onMouseMove"
    @mouseleave="onMouseLeave"
  >
    <slot />
  </div>
</template>

<script setup>
import { ref, computed, provide } from "vue";
import { cn } from "./utils";
import { DataOrientation, Direction } from "./types";
import {
  MOUSE_X_INJECTION_KEY,
  MOUSE_Y_INJECTION_KEY,
  MAGNIFICATION_INJECTION_KEY,
  DISTANCE_INJECTION_KEY,
  ORIENTATION_INJECTION_KEY,
} from "./injectionKeys";

const props = defineProps({
  class: {
    type: String,
    default: ''
  },
  magnification: {
    type: Number,
    default: 60
  },
  distance: {
    type: Number,
    default: 140
  },
  direction: {
    type: String,
    default: 'middle',
    validator: (value) => ['top', 'middle', 'bottom'].includes(value)
  },
  orientation: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  }
});

const dockRef = ref(null);
const mouseX = ref(Infinity);
const mouseY = ref(Infinity);
const magnification = computed(() => props.magnification);
const distance = computed(() => props.distance);

const dockClass = computed(() => ({
  "items-start": props.direction === "top",
  "items-center": props.direction === "middle",
  "items-end": props.direction === "bottom",
}));

function onMouseMove(e) {
  requestAnimationFrame(() => {
    mouseX.value = e.pageX;
    mouseY.value = e.pageY;
  });
}

function onMouseLeave() {
  requestAnimationFrame(() => {
    mouseX.value = Infinity;
    mouseY.value = Infinity;
  });
}

provide(MOUSE_X_INJECTION_KEY, mouseX);
provide(MOUSE_Y_INJECTION_KEY, mouseY);
provide(ORIENTATION_INJECTION_KEY, props.orientation);
provide(MAGNIFICATION_INJECTION_KEY, magnification);
provide(DISTANCE_INJECTION_KEY, distance);
</script>

<style scoped>
.dock-container {
  display: flex;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 200ms ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dock-horizontal {
  flex-direction: row;
  justify-content: center;
  min-height: 70px;
}

.dock-vertical {
  flex-direction: column;
  justify-content: center;
  min-width: 70px;
}

.dock-horizontal-top {
  align-items: flex-start;
}

.dock-horizontal-middle {
  align-items: center;
}

.dock-horizontal-bottom {
  align-items: flex-end;
}

.dock-vertical-top {
  align-items: flex-start;
}

.dock-vertical-middle {
  align-items: center;
}

.dock-vertical-bottom {
  align-items: flex-end;
}
</style> 