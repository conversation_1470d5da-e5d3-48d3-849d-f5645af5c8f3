<template>
  <div
    :class="
      cn('relative block bg-secondary', orientation === 'vertical' ? 'w-4/5 h-0.5' : 'h-4/5 w-0.5')
    "
  ></div>
</template>

<script setup>
import { inject } from 'vue';
import { ORIENTATION_INJECTION_KEY } from './injectionKeys';
import { cn } from './utils';

const orientation = inject(ORIENTATION_INJECTION_KEY, 'horizontal');
</script>

<style scoped>
.dock-separator {
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0.25rem;
}

.dock-horizontal .dock-separator {
  width: 1px;
  height: 70%;
  margin: 0 0.5rem;
}

.dock-vertical .dock-separator {
  height: 1px;
  width: 70%;
  margin: 0.5rem 0;
}
</style> 