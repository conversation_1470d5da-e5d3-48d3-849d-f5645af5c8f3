<template>
  <div class="relative">
    <!-- Filter tabs and buttons -->
    <div class="flex flex-wrap items-center justify-between mb-6 relative z-20">
      <!-- Filter tabs -->
      <div class="flex-wrap gap-3 items-center">
        <div class="flex items-center gap-3 p-1 bg-white dark:bg-slate-950 rounded-xl border border-gray-200 dark:border-slate-800">
          <button 
            v-for="status in getOrderedStatuses"
            :key="status.slug"
            @click="currentFilter = status.slug"
            :class="[
              'inline-flex items-center px-4 py-2 rounded-lg text-lg gant-modern-medium transition-colors',
              currentFilter === status.slug
                ? 'bg-gray-100 dark:bg-slate-900 text-slate-900 dark:text-slate-200' 
                : 'bg-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-900/50'
            ]"
          >
            <span 
              class="inline-block w-2.5 h-2.5 rounded-full me-2"
              :style="{ backgroundColor: status.color }"
              :class="{ 'animate-pulse': status.slug === 'active' }"
            ></span>
            {{ status.name }}
            <span :class="[
              'ml-2 px-1.5 py-0.5 text-xs rounded-full transition-colors', 
              getStatusCountClass(status.slug)
            ]">{{ getStatusCount(status.slug) }}</span>
          </button>
          
          <button 
            @click="currentFilter = 'all'"
            :class="[
              'inline-flex items-center px-4 py-2 rounded-lg text-lg gant-modern-medium transition-colors',
              currentFilter === 'all' 
                ? 'bg-gray-100 dark:bg-slate-900 text-slate-900 dark:text-slate-200' 
                : 'bg-transparent text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-900/50'
            ]"
          >
            All 
            <span :class="[
              'ml-2 px-1.5 py-0.5 text-xs rounded-full transition-colors', 
              'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200'
            ]">{{ totalCardsCount }}</span>
          </button>
        </div>
      </div>

      <!-- Add button and View controls -->
      <div class="flex items-center gap-3">
        
        <div class="flex items-center h-12 px-4 py-2 bg-white dark:bg-slate-950 rounded-xl border border-gray-300 dark:border-gray-700 text-sm">
          <span class="text-gray-500 dark:text-gray-400 mr-3 gant-modern-medium">View:</span>
          <div class="flex items-center gap-2">
            <button 
              @click="viewMode = 'grid'" 
              :class="[
                'p-1.5 rounded-md transition-colors',
                viewMode === 'grid' 
                  ? 'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-200' 
                  : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-800/50'
              ]"
              title="Grid view"
            >
              <i class="design design-gridview"></i>
            </button>
            <button 
              @click="viewMode = 'table'" 
              :class="[
                'p-1.5 rounded-md transition-colors',
                viewMode === 'table' 
                  ? 'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-200' 
                  : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-800/50'
              ]"
              title="Table view"
            >
              <i class="design design-listview"></i>
            </button>
            <button 
              @click="viewMode = 'kanban'" 
              :class="[
                'p-1.5 rounded-md transition-colors',
                viewMode === 'kanban' 
                  ? 'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-200' 
                  : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-800/50'
              ]"
              title="Kanban view"
            >
              <i class="design design-kanbanview"></i>
            </button>
            <button 
              @click="viewMode = 'roadmap'" 
              :class="[
                'p-1.5 rounded-md transition-colors',
                viewMode === 'roadmap' 
                  ? 'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-200' 
                  : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-800/50'
              ]"
              title="Roadmap view"
            >
              <i class="design design-roadmapview"></i>
            </button>
          </div>
        </div>
        
        <!-- Add new card button - consistent location across all views -->
        <button 
          v-if="isAdmin"
          @click="showAddCardModal = true"
          class="inline-flex items-center h-12 px-4 py-2 bg-gray-950 dark:bg-slate-200 text-white dark:text-slate-900 rounded-xl text-sm font-medium hover:bg-gray-800 dark:hover:bg-slate-300 transition-colors"
        >
          <i class="design design-plus mr-2"></i>
          Add new card
        </button>
      </div>
    </div>
    
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center py-12 relative z-20">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600 dark:border-blue-400 mb-2"></div>
        <div class="text-sm text-gray-500 dark:text-gray-400">Loading cards...</div>
      </div>
    </div>
    
    <!-- Empty state -->
    <div v-else-if="filteredCards.length === 0" class="py-12 text-center relative z-20">
      <div class="bg-white dark:bg-slate-900 border border-gray-200 dark:border-slate-800 rounded-lg p-8 max-w-md mx-auto">
        <div class="text-gray-400 dark:text-gray-500 mb-3">
          <i class="design design-card-search text-3xl"></i>
        </div>
        <p class="text-lg text-gray-700 dark:text-gray-300 mb-1 font-medium">No cards found</p>
        <p class="text-sm text-gray-500 dark:text-gray-500 mb-3">
          {{ searchQuery ? 'Try adjusting your search or filters' : 'There are no cards matching the current filter' }}
        </p>
        <button 
          v-if="isAdmin"
          @click="showAddCardModal = true"
          class="inline-flex items-center px-3 py-2 bg-slate-900 dark:bg-slate-200 text-white dark:text-slate-900 rounded-md text-sm hover:bg-slate-700 dark:hover:bg-slate-300 transition-colors"
        >
          <i class="design design-plus mr-2"></i>
          Add new card
        </button>
      </div>
    </div>
    
    <!-- Cards container (grid or table view) -->
    <template v-else>
      <!-- Grid view -->
      <div v-if="viewMode === 'grid'">
        <!-- Search, sort, and appearance options for grid view -->
        <div class=" mb-6 pb-4">
          <div class="flex items-end gap-4 overflow-x-auto pb-2">
            <!-- Search input -->
            <div class="flex-shrink-0">
              <label for="card-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Search</label>
              <div class="relative w-64">
                <input 
                  id="card-search"
                  v-model="searchQuery" 
                  type="search"
                  placeholder="Search by headline, description or project..." 
                  class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
                />
                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <i class="design design-search-fill text-gray-400"></i>
                </div>
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch()" 
                  class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  <i class="design design-close"></i>
                </button>
              </div>
            </div>
            
            <!-- Sort options -->
            <div class="flex-shrink-0">
              <label for="sort-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Sort by</label>
              <div class="relative h-12 w-48">
                <select 
                  id="sort-select"
                  v-model="sortBy" 
                  class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 appearance-none pl-3 pr-10 gant-modern-regular"
                >
                  <option value="headline">Headline</option>
                  <option value="card_date">Date</option>
                  <option value="deadline">Deadline</option>
                  <option value="project">Project</option>
                  <option value="assigned">Assigned Users</option>
                  <option value="status">Status</option>
                </select>
                <div class="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                  <i class="design design-chevron-down text-gray-400"></i>
                </div>
              </div>
            </div>
            
            <!-- Sort direction -->
            <div class="flex-shrink-0">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Direction</label>
              <button 
                @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'" 
                class="flex h-12 w-12 rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-900"
              >
                <i :class="[
                  'design text-xl', 
                  sortDirection === 'asc' ? 'design-arrow-up' : 'design-arrow-down'
                ]"></i>
              </button>
            </div>
            
            <!-- Tags visibility toggle -->
            <div class="flex-shrink-0">
              <div class="flex items-center gap-2 h-12">
                 <label for="show-tags-toggle" class="text-sm font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Tags</label>
                 <label class="relative inline-block w-[60px] h-[34px]">
                   <input 
                     id="show-tags-toggle"
                     type="checkbox" 
                     v-model="showTags" 
                     class="opacity-0 w-0 h-0 peer"
                   >
                   <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                     <i v-if="!showTags" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                     <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                   </span>
                 </label>
               </div>
             </div>
            
            <!-- Dates visibility toggle -->
            <div class="flex-shrink-0">
              <div class="flex items-center gap-2 h-12">
                 <label for="show-dates-toggle" class="text-sm font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Dates</label>
                 <label class="relative inline-block w-[60px] h-[34px]">
                   <input 
                     id="show-dates-toggle"
                     type="checkbox" 
                     v-model="showDates" 
                     class="opacity-0 w-0 h-0 peer"
                   >
                   <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                     <i v-if="!showDates" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                     <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                   </span>
                 </label>
               </div>
             </div>
            
            <!-- Deadlines visibility toggle -->
            <div class="flex-shrink-0">
               <div class="flex items-center gap-2 h-12">
                 <label for="show-deadlines-toggle" class="text-sm font-medium text-gray-700 dark:text-gray-300 gant-modern-medium">Deadlines</label>
                 <label class="relative inline-block w-[60px] h-[34px]">
                   <input 
                     id="show-deadlines-toggle"
                     type="checkbox" 
                     v-model="showDeadlines" 
                     class="opacity-0 w-0 h-0 peer"
                   >
                   <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                     <i v-if="!showDeadlines" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                     <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                   </span>
                 </label>
               </div>
             </div>
            
            <!-- Card size slider -->
            <div class="flex-shrink-0">
              <label for="card-size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Card size</label>
              <div class="space-y-1 w-48">
                <input
                  id="card-size"
                  v-model="cardSize"
                  type="range"
                  min="1"
                  max="3"
                  step="0.1"
                  class="w-full range-slider"
                >
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                  <i class="design design-zoom-out text-gray-400"></i>
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ cardSizeText }}</span>
                  <i class="design design-zoom-in text-gray-400"></i>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Search results info -->
          <div v-if="debouncedSearchQuery.trim()" class="mt-4 text-sm py-1 px-3 bg-blue-50 dark:bg-blue-900/10 text-blue-600 dark:text-blue-300 rounded-md inline-flex items-center">
            <i class="design design-info-circle me-1 opacity-70"></i>
            Found {{ sortedCards.length }} result{{ sortedCards.length !== 1 ? 's' : '' }} for "{{ debouncedSearchQuery }}"
            <button @click="clearSearch()" class="ml-2 text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline">
              Clear
            </button>
          </div>
        </div>
        
        <div 
          class="grid gap-5 relative z-20"
          :class="gridColumnsClass"
        >
          <whiteboard-card 
            v-for="card in sortedCards" 
            :key="card.id" 
            :card="card"
            :is-admin="isAdmin"
            :show-tags="showTags"
            :show-dates="showDates"
            :show-deadlines="showDeadlines"
            @edit="editCard"
            @delete="deleteCard"
            @status-change="changeCardStatus"
          />
        </div>
      </div>
      
      <!-- Table view -->
      <div v-else-if="viewMode === 'table'" class="w-full relative z-20">
        <!-- Search input for table view -->
        <div class="sticky top-0 z-20 bg-white/95 dark:bg-slate-950/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800 mb-6 pb-4">
          <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
            <!-- Search input -->
            <div class="md:col-span-4">
              <label for="table-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Search</label>
              <div class="relative">
                <input 
                  id="table-search"
                  v-model="searchQuery" 
                  type="search"
                  placeholder="Search by headline, description or project..." 
                  class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
                />
                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <i class="design design-search text-gray-400"></i>
                </div>
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch()" 
                  class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  <i class="design design-close"></i>
                </button>
              </div>
            </div>
            
            <!-- Sort options -->
            <div class="md:col-span-2">
              <label for="table-sort-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Sort by</label>
              <div class="relative h-12">
                <select 
                  id="table-sort-select"
                  v-model="sortBy" 
                  class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 appearance-none pl-3 pr-10 gant-modern-regular"
                >
                  <option value="headline">Headline</option>
                  <option value="card_date">Date</option>
                  <option value="deadline">Deadline</option>
                  <option value="project">Project</option>
                  <option value="assigned">Assigned Users</option>
                  <option value="status">Status</option>
                </select>
                <div class="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                  <i class="design design-chevron-down text-gray-400"></i>
                </div>
              </div>
            </div>
            
            <!-- Sort direction -->
            <div class="md:col-span-1">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium">Direction</label>
              <button 
                @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'" 
                class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-900"
              >
                <i :class="[
                  'design text-xl', 
                  sortDirection === 'asc' ? 'design-arrow-up' : 'design-arrow-down'
                ]"></i>
              </button>
            </div>
          </div>
          
          <!-- Search results info -->
          <div v-if="debouncedSearchQuery.trim()" class="mt-4 text-sm py-1 px-3 bg-blue-50 dark:bg-blue-900/10 text-blue-600 dark:text-blue-300 rounded-md inline-flex items-center">
            <i class="design design-info-circle me-1 opacity-70"></i>
            Found {{ sortedCards.length }} result{{ sortedCards.length !== 1 ? 's' : '' }} for "{{ debouncedSearchQuery }}"
            <button @click="clearSearch()" class="ml-2 text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline">
              Clear
            </button>
          </div>
        </div>
        
        <div class="bg-white dark:bg-slate-950 shadow-sm rounded-lg overflow-hidden">
          <DataTable
            :data="sortedCards"
            :columns="tableColumns"
            tableId="whiteboard-cards-table"
            defaultSortColumn="card_date"
            defaultSortDirection="desc"
            @row-click="handleRowClick"
            @sort="toggleSort"
          >
            <!-- Status cell template -->
            <template #cell(status)="{ item }">
              <span 
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                ]"
              >
                <span class="w-1.5 h-1.5 rounded-full mr-1.5" :style="{ backgroundColor: getStatusColor(item.status) }"></span>
                {{ getStatusName(item.status) }}
              </span>
            </template>
            
            <!-- Headline cell template -->
            <template #cell(headline)="{ item }">
              <div class="font-medium text-gray-900 dark:text-white">{{ item.headline }}</div>
              <div v-if="item.description" class="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
                {{ item.description.replace(/<br\s*\/?>/g, ' ') }}
              </div>
            </template>
            
            <!-- Project cell template -->
            <template #cell(project)="{ item }">
              <div v-if="item.project" class="inline-flex items-center">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 rounded-md text-xs">
                  <i class="design design-table-settings me-1"></i>
                  {{ item.project.name }}
                </span>
              </div>
              <span v-else class="text-gray-400 dark:text-gray-600">-</span>
            </template>
            
            <!-- Date cell template -->
            <template #cell(card_date)="{ value }">
              {{ new Date(value).toLocaleDateString() }}
            </template>
            
            <!-- Deadline cell template -->
            <template #cell(deadline)="{ value, item }">
              <template v-if="value">
                <span 
                  :class="[
                    'px-2 py-0.5 rounded text-xs',
                    isDeadlineNear(value) ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200' : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-200'
                  ]"
                >
                  {{ new Date(value).toLocaleDateString() }}
                  <span v-if="isDeadlineNear(value)" class="ml-1">⚠️</span>
                </span>
              </template>
              <span v-else class="text-gray-400 dark:text-gray-600">-</span>
            </template>
            
            <!-- Assigned Users cell template -->
            <template #cell(assigned)="{ item }">
              <div v-if="item.assignedUsers && item.assignedUsers.length" class="flex flex-wrap gap-1">
                <span 
                  v-for="user in item.assignedUsers.slice(0, 2)" 
                  :key="user.id"
                  class="inline-flex items-center px-1.5 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 rounded text-xs"
                >
                  {{ user.name }}
                </span>
                <span 
                  v-if="item.assignedUsers.length > 2"
                  class="inline-flex items-center px-1.5 py-0.5 bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200 rounded text-xs"
                >
                  +{{ item.assignedUsers.length - 2 }}
                </span>
              </div>
              <span v-else class="text-gray-400 dark:text-gray-600">-</span>
            </template>
            
            <!-- Logo cell template -->
            <template #cell(image)="{ item }">
              <img 
                v-if="item.image" 
                :src="`/img/logos/${item.image}`" 
                :alt="item.headline" 
                class="h-8 w-8 object-contain"
              >
            </template>
            
            <!-- Link cell template -->
            <template #cell(link)="{ value }">
              <a 
                v-if="value" 
                @click.prevent="openExternalLink(value)"
                href="#"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                View
              </a>
              <span v-else class="text-gray-400 dark:text-gray-600">-</span>
            </template>
            
            <!-- Actions cell template -->
            <template v-if="isAdmin" #cell(actions)="{ item }">
              <div class="flex space-x-2 justify-end">
                <!-- Edit button -->
                <button 
                  @click.stop="editCard(item)" 
                  title="Edit card"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <i class="design design-edit"></i>
                </button>
                
                <!-- Status change dropdown -->
                <div class="relative">
                  <button 
                    @click.stop="item.showStatusDropdown = !item.showStatusDropdown"
                    title="Change status"
                    :style="{ color: getStatusColor(item.status) }"
                    class="hover:opacity-80 transition-opacity"
                  >
                    <i class="design design-col-visible"></i>
                  </button>
                  
                  <!-- Status dropdown menu -->
                  <div 
                    v-if="item.showStatusDropdown" 
                    class="absolute right-0 mt-1 bg-white dark:bg-slate-800 rounded-md shadow-lg z-40 py-1 min-w-32 border border-gray-200 dark:border-slate-700"
                    @click.stop
                  >
                    <button 
                      v-for="status in statusOptions"
                      :key="status.slug"
                      @click.stop="changeCardStatus(item, status.slug)" 
                      class="w-full px-4 py-2 text-sm text-left flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-slate-700"
                      :class="{'bg-gray-100 dark:bg-slate-700': item.status === status.slug}"
                    >
                      <span class="w-2 h-2 rounded-full" :style="{ backgroundColor: status.color }"></span>
                      <span>{{ status.name }}</span>
                    </button>
                  </div>
                </div>
                
                <!-- Delete button -->
                <button 
                  @click.stop="deleteCard(item)" 
                  title="Delete card"
                  class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  <i class="design design-trash"></i>
                </button>
              </div>
            </template>
          </DataTable>
        </div>
      </div>
      
      <!-- Kanban view -->
      <div v-else-if="viewMode === 'kanban'" class="w-full relative z-20">
        <!-- Search input for kanban view -->
        <div class="sticky top-0 z-20 bg-white/95 dark:bg-slate-950/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800 mb-6 pb-4">
          <div class="flex items-center justify-between flex-wrap gap-4">
            <!-- Search input -->
            <div class="w-full md:w-auto relative">
                <input 
                  v-model="searchQuery" 
                  type="search"
                  placeholder="Search cards..." 
                  class="flex h-12 w-full md:w-80 rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
                />
                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <i class="design design-search text-gray-400"></i>
                </div>
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch()" 
                  class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  <i class="design design-close"></i>
                </button>
            </div>
            
            <!-- Column management info -->
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500 dark:text-gray-400 gant-modern-regular">
                <i class="design design-info-circle me-1 opacity-70"></i>
                Columns from database
              </span>
              <button 
                  v-if="isAdmin"
                  @click="goToStatusAdmin"
                class="text-xs text-blue-600 dark:text-blue-400 hover:underline gant-modern-medium"
                >
                Manage columns
              </button>
            </div>
          </div>
          
          <!-- Kanban view options -->
          <div class="mt-4 flex flex-wrap items-center gap-4">
            <!-- Granularity selector -->
            <div class="w-48">
              <label for="kanban-granularity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 gant-modern-medium">
                Card detail level
              </label>
              <div class="space-y-1">
                <input
                  id="kanban-granularity"
                  v-model="kanbanGranularity"
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  class="w-full range-slider"
                >
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                  <span>Compact</span>
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ kanbanGranularityText }}</span>
                  <span>Detailed</span>
                </div>
              </div>
            </div>
          
            <!-- Sort options dropdown -->
            <div class="relative w-48">
              <label for="kanban-sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 gant-modern-medium">
                Sort cards by
              </label>
              <div class="relative">
                <select 
                  id="kanban-sort"
                  v-model="sortBy" 
                  class="flex h-10 w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 appearance-none pl-3 pr-10 gant-modern-regular"
                >
                  <option value="headline">Headline</option>
                  <option value="card_date">Date</option>
                  <option value="deadline">Deadline</option>
                  <option value="project">Project</option>
                  <option value="assigned">Assigned Users</option>
                </select>
                <div class="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                  <i class="design design-chevron-down text-gray-400"></i>
                </div>
              </div>
            </div>
            
            <!-- Sort direction button -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 gant-modern-medium">Direction</label>
              <button 
                @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'" 
                class="flex h-10 w-10 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 items-center justify-center text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-900"
              >
                <i :class="[
                  'design text-xl', 
                  sortDirection === 'asc' ? 'design-arrow-up' : 'design-arrow-down'
                ]"></i>
              </button>
            </div>
          
            <!-- Filter indicator with accent color based on selected status -->
            <div class="flex items-center gap-2 ml-auto">
              <span class="text-sm text-gray-500 dark:text-gray-400 gant-modern-regular">
                Showing:
              </span>
              <span 
                v-if="currentFilter === 'all'"
                class="px-2 py-1 text-xs rounded-md font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 gant-modern-medium"
              >
                All tasks
              </span>
              <span 
                v-else
                class="px-2 py-1 text-xs rounded-md font-medium flex items-center gant-modern-medium"
                :style="{ 
                  backgroundColor: `${getStatusColor(currentFilter)}20`, 
                  color: getStatusColor(currentFilter) 
                }"
              >
                <span 
                  class="inline-block w-2 h-2 rounded-full mr-1.5" 
                  :style="{ backgroundColor: getStatusColor(currentFilter) }"
                ></span>
                {{ getStatusName(currentFilter) }}
              </span>
            </div>
          </div>
          
          <!-- Search results info -->
          <div v-if="debouncedSearchQuery.trim()" class="mt-4 text-sm py-1 px-3 bg-blue-50 dark:bg-blue-900/10 text-blue-600 dark:text-blue-300 rounded-md inline-flex items-center">
            <i class="design design-info-circle me-1 opacity-70"></i>
            Found {{ sortedCards.length }} result{{ sortedCards.length !== 1 ? 's' : '' }} for "{{ debouncedSearchQuery }}"
            <button @click="clearSearch()" class="ml-2 text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline">
              Clear
            </button>
          </div>
        </div>
        
        <!-- Loading state -->
        <div v-if="!getOrderedStatuses || getOrderedStatuses.length === 0" class="py-8 text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-slate-800 dark:border-slate-200"></div>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading kanban columns from database...</p>
        </div>
        
        <!-- Kanban board with enhanced design -->
        <div v-else class="kanban-board grid grid-cols-1 mb-8" :class="kanbanGridClass">
          <!-- Drag indicator overlay that follows cursor -->
          <div v-if="isDragging" class="fixed pointer-events-none z-50 kanban-drag-preview" :style="dragPreviewStyle">
            <div class="p-2 bg-white dark:bg-slate-900 border rounded-lg shadow-lg max-w-xs truncate">
              <span class="inline-block w-2 h-2 rounded-full mr-1.5" 
                    :style="{ backgroundColor: getStatusColor(currentDraggedCard?.status || 'active') }"></span>
              {{ currentDraggedCard?.headline || 'Card' }}
            </div>
          </div>
          
          <!-- Dynamic columns based on database status options -->
          <div
            v-for="status in getOrderedStatuses"
            :key="status.slug"
            class="kanban-column bg-white dark:bg-slate-950 rounded-xl border border-gray-200 dark:border-slate-800 overflow-hidden flex flex-col"
            @dragover.prevent="handleDragOver($event, status.slug)"
            @dragleave="handleDragLeave($event, status.slug)"
            @drop="handleDrop($event, status.slug)"
            :class="{'kanban-column-drag-over': dragOverStatus === status.slug}"
            :data-status="status.slug"
          >
            <div class="p-4 border-b border-gray-200 dark:border-slate-800 bg-gray-50 dark:bg-slate-900" 
                  :style="{ borderBottom: `2px solid ${status.color}` }">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center gant-modern-medium">
                  <span 
                    class="inline-block w-3 h-3 rounded-full mr-2" 
                    :style="{ backgroundColor: status.color }"
                    :class="{ 'animate-pulse': status.slug === 'active' }"
                  ></span>
                  {{ status.name }}
                </h3>
                <span 
                  class="px-2 py-1 text-xs rounded-full"
                  :style="{ backgroundColor: `${status.color}20`, color: status.color }"
                >
                  {{ sortedCards.filter(card => card.status === status.slug).length }}
                </span>
              </div>
            </div>
            <div class="p-4 h-[calc(100vh-220px)] overflow-y-auto flex-grow kanban-column-content bg-gray-50/50 dark:bg-slate-950/50"
                 @dragover.prevent="handleDragOver($event, status.slug)"
                 @dragleave="handleDragLeave($event, status.slug)"
                 @drop="handleDrop($event, status.slug)">
              <div class="space-y-3">
                <div
                  v-for="(card, cardIndex) in sortedCards.filter(card => card.status === status.slug)"
                  :key="`${card.id}-${card.status}`"
                  class="p-3 bg-white dark:bg-slate-950 border rounded-lg shadow-sm hover:shadow-md transition-all transform hover:-translate-y-1 cursor-grab active:cursor-grabbing"
                  :style="{ borderLeft: `4px solid ${status.color}` }" 
                  @click.stop="isAdmin ? editCard(card) : card.link ? openExternalLink(card.link) : null"
                  draggable="true"
                  @dragstart="handleDragStart($event, card, cardIndex)"
                  @dragend="handleDragEnd($event)"
                  @dragover.prevent="handleItemDragOver($event, status.slug, cardIndex)"
                  @dragenter.prevent
                  :class="{ 
                    'opacity-50': isDragging && currentDraggedCard && currentDraggedCard.id === card.id,
                    'kanban-card-dragging': isDragging && currentDraggedCard && currentDraggedCard.id === card.id,
                    'insert-above': isDragging && dragInsertIndex === cardIndex && currentDraggedCard && currentDraggedCard.id !== card.id,
                    'insert-below': isDragging && dragInsertIndex === cardIndex + 1 && currentDraggedCard && currentDraggedCard.id !== card.id
                  }"
                  :data-card-index="cardIndex"
                  :data-status="status.slug"
                  :data-card-id="card.id"
                  @touchstart="handleTouchStart($event, card, cardIndex)"
                  @touchmove="handleTouchMove($event)"
                  @touchend="handleTouchEnd($event, status.slug)"
                >
                  <!-- Card header with title and badge for assigned users (all granularity levels) -->
                  <div class="relative">
                    <div class="font-medium text-gray-900 dark:text-white mb-1 pr-6 truncate gant-modern-medium">{{ card.headline }}</div>
                    
                    <!-- Always show assigned users count if there are any -->
                    <div v-if="card.assignedUsers && card.assignedUsers.length" 
                        class="absolute top-0 right-0 flex items-center justify-center rounded-full shadow-sm"
                        :style="{ backgroundColor: `${status.color}30` }">
                      <span class="flex items-center justify-center w-6 h-6 text-xs" :style="{ color: status.color }">
                        <i class="design design-user mr-0.5"></i>{{ card.assignedUsers.length }}
                      </span>
                    </div>
                  </div>
                  
                  <!-- Only show description in Standard or Detailed modes -->
                  <div v-if="kanbanGranularity >= 2 && card.description" 
                      class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2 mb-2 mt-1 gant-modern-regular">
                    {{ card.description.replace(/<br\s*\/?>/g, ' ') }}
                  </div>
                  
                  <!-- Show metadata in all views -->
                  <div class="flex flex-wrap items-center gap-2 mt-3">
                    <!-- Project tag -->
                    <div v-if="card.project" class="inline-flex items-center">
                      <span class="px-2 py-1 bg-gray-100 text-gray-700 dark:bg-slate-800 dark:text-gray-300 rounded-md text-xs flex items-center gant-modern-regular">
                        <i class="design design-playground-folder-oped opacity-70 mr-1 text-xs"></i>
                        {{ card.project.name }}
                      </span>
                    </div>
                    
                    <!-- Deadline -->
                    <div v-if="card.deadline" 
                      :class="[
                        'px-2 py-1 text-xs rounded-md flex items-center gant-modern-regular',
                        isDeadlineNear(card.deadline) 
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200' 
                          : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-200'
                      ]"
                    >
                      <i class="design design-schedule-add mr-1"></i>
                      {{ new Date(card.deadline).toLocaleDateString() }}
                      <span v-if="isDeadlineNear(card.deadline)" class="ml-1">⚠️</span>
                    </div>
                  </div>
                  
                  <!-- Only show quick actions in Detailed mode -->
                  <div v-if="kanbanGranularity === 3 && isAdmin" class="mt-3 pt-2 border-t border-gray-100 dark:border-slate-800 flex justify-end space-x-2">
                    <button
                      @click.stop="editCard(card)"
                      class="p-1.5 rounded-md text-gray-500 hover:bg-gray-100 dark:hover:bg-slate-800"
                      title="Edit card"
                    >
                      <i class="design design-edit text-sm"></i>
                    </button>
                    <button
                      @click.stop="$emit('status-change', card)"
                      class="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-slate-800"
                      :style="{ color: getStatusColor(card.status) }"
                      title="Change status"
                    >
                      <i class="design design-col-visible text-sm"></i>
                    </button>
                    <button
                      @click.stop="deleteCard(card)"
                      class="p-1.5 rounded-md text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20"
                      title="Delete card"
                    >
                      <i class="design design-trash text-sm"></i>
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Add button for admin when column is empty -->
              <div v-if="isAdmin && sortedCards.filter(card => card.status === status.slug).length === 0" 
                class="mt-4 flex justify-center"
              >
                <button 
                  @click.stop="showAddCardWithStatus(status.slug)"
                  class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-slate-800 text-gray-600 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors gant-modern-medium"
                >
                  <i class="design design-plus mr-1"></i>
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Roadmap view -->
      <div v-else-if="viewMode === 'roadmap'" class="w-full relative z-20">
        <!-- Search and filter options -->
        <div class="sticky top-0 z-20 bg-white/95 dark:bg-slate-950/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-800 mb-6 pb-4">
          <div class="flex items-center justify-between flex-wrap gap-4">
            <!-- Search input -->
            <div class="w-full md:w-auto">
              <div class="relative">
                <input 
                  v-model="searchQuery" 
                  type="search"
                  placeholder="Search cards..." 
                  class="flex h-12 w-full md:w-80 rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
                />
                <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <i class="design design-search text-gray-400"></i>
                </div>
                <button 
                  v-if="searchQuery" 
                  @click="clearSearch()" 
                  class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-500"
                >
                  <i class="design design-close"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Roadmap view options -->
          <div class="mt-4 flex flex-wrap items-center gap-4">
            <!-- Granularity slider -->
            <div class="w-48">
              <label for="roadmap-granularity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 gant-modern-medium">
                Display detail level
              </label>
              <div class="space-y-1">
                <input
                  id="roadmap-granularity"
                  v-model="roadmapGranularity"
                  type="range"
                  min="1"
                  max="3"
                  step="1"
                  class="w-full range-slider"
                >
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                  <span>Compact</span>
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ roadmapGranularityText }}</span>
                  <span>Detailed</span>
                </div>
              </div>
            </div>
            
            <!-- Time scale slider -->
            <div class="w-48 ml-4">
              <label for="roadmap-scale" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 gant-modern-medium">
                Time scale
              </label>
              <div class="space-y-1">
                <input
                  id="roadmap-scale"
                  v-model="timeScale"
                  type="range"
                  min="1"
                  max="10"
                  step="1"
                  class="w-full range-slider"
                  @change="updateTimeScale"
                >
                <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                  <span>Zoom out</span>
                  <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ timeScaleText }}</span>
                  <span>Zoom in</span>
                </div>
              </div>
            </div>
          
            <!-- Filter indicator -->
            <div class="flex items-center gap-2 ml-auto">
              <span class="text-sm text-gray-500 dark:text-gray-400">
                Currently viewing:
              </span>
              <span 
                v-if="currentFilter === 'all'"
                class="px-2 py-1 text-xs rounded-md font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200"
              >
                All tasks
              </span>
              <span 
                v-else
                class="px-2 py-1 text-xs rounded-md font-medium flex items-center"
                :style="{ 
                  backgroundColor: `${getStatusColor(currentFilter)}20`, 
                  color: getStatusColor(currentFilter) 
                }"
              >
                <span 
                  class="inline-block w-2 h-2 rounded-full mr-1.5" 
                  :style="{ backgroundColor: getStatusColor(currentFilter) }"
                ></span>
                {{ getStatusName(currentFilter) }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- Timeline view -->
        <div class="bg-white dark:bg-slate-900 rounded-xl border border-gray-200 dark:border-slate-800 overflow-hidden">
          <!-- Timeline headers -->
          <div class="flex border-b border-gray-200 dark:border-slate-800 bg-gray-50 dark:bg-slate-950 p-3">
            <div class="w-[200px] shrink-0 pr-4 font-medium text-gray-700 dark:text-gray-300">Project</div>
            <div class="flex-grow">
              <!-- Year view (timeScale <= 3) -->
              <div v-if="timeScale <= 3" class="grid grid-cols-4 gap-1">
                <div v-for="quarter in 4" :key="`q${quarter}`" class="text-center text-xs text-gray-500 dark:text-gray-400">
                  Q{{ quarter }}
                </div>
              </div>
              
              <!-- Quarter view (timeScale > 3 && <= 6) -->
              <div v-else-if="timeScale <= 6" class="grid grid-cols-12 gap-1">
                <div v-for="month in 12" :key="`m${month}`" class="text-center text-xs text-gray-500 dark:text-gray-400">
                  {{ new Date(new Date().getFullYear(), month-1, 1).toLocaleDateString('default', { month: 'short' }) }}
                </div>
              </div>
              
              <!-- Month view (timeScale > 6 && <= 8) -->
              <div v-else-if="timeScale <= 8" class="flex">
                <div v-for="week in 52" :key="`w${week}`" class="text-center text-xs text-gray-500 dark:text-gray-400 flex-1">
                  {{ week % 4 === 0 ? 'W' + week : '' }}
                </div>
              </div>
              
              <!-- Week view (timeScale > 8) -->
              <div v-else class="flex">
                <div v-for="day in 7" :key="`d${day}`" class="text-center text-xs text-gray-500 dark:text-gray-400 flex-1">
                  {{ ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day-1] }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- Timeline content -->
          <div class="divide-y divide-gray-200 dark:divide-slate-800">
            <div 
              v-for="(project, index) in getUniqueProjects()" 
              :key="index" 
              class="flex hover:bg-gray-50 dark:hover:bg-slate-900/50"
            >
              <div class="w-[200px] shrink-0 p-4 border-r border-gray-200 dark:border-slate-800">
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ project ? project.name : 'No Project' }}
                </div>
              </div>
              
              <div class="flex-grow p-2 relative min-h-[80px]">
                <!-- Timeline items for this project -->
                <div 
                  v-for="card in getProjectCards(project)" 
                  :key="card.id"
                  class="absolute rounded-md px-2 flex items-center shadow-sm border"
                  :class="[roadmapItemClass]"
                  :style="getTimelineItemStyle(card)"
                  :title="`${card.headline} - ${getStatusName(card.status)}`"
                  @click="isAdmin ? editCard(card) : card.link ? openExternalLink(card.link) : null"
                >
                  <!-- Status indicator - shown in all granularity levels -->
                  <span 
                    class="w-2 h-2 rounded-full mr-1.5 flex-shrink-0" 
                    :style="{ backgroundColor: getStatusColor(card.status) }"
                  ></span>
                  
                  <!-- Card title - shown in all granularity levels -->
                  <span class="truncate max-w-[120px] md:max-w-[200px]">{{ card.headline }}</span>
                  
                  <!-- Medium detail: Add deadline badge if present -->
                  <span 
                    v-if="roadmapGranularity >= 2 && card.deadline"
                    :class="[
                      'ml-2 rounded px-1 text-2xs inline-flex items-center',
                      isDeadlineNear(card.deadline) ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' : 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300'
                    ]"
                  >
                    {{ new Date(card.deadline).toLocaleDateString('default', {month: 'short', day: 'numeric'}) }}
                  </span>
                  
                  <!-- Detailed: Add user count badge -->
                  <span 
                    v-if="roadmapGranularity === 3 && card.assignedUsers && card.assignedUsers.length"
                    class="ml-auto bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 rounded-full px-1 text-2xs flex items-center"
                  >
                    <i class="design design-user text-xs mr-0.5"></i>{{ card.assignedUsers.length }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    
    <!-- Add/Edit Modal -->
    <whiteboard-card-modal
      v-if="showAddCardModal"
      :card="editingCard"
      :is-editing="!!editingCard"
      @close="closeModal"
      @save="saveCard"
    />
    
    <!-- Delete Confirmation Modal -->
    <confirmation-modal
      v-if="showDeleteModal"
      :card="cardToDelete"
      @close="showDeleteModal = false"
      @confirm="confirmDelete"
    />
    
    <!-- External Link Confirmation Modal -->
    <external-link-modal
      v-if="showExternalLinkModal"
      :show="showExternalLinkModal"
      :url="externalLinkUrl"
      :target="externalLinkTarget"
      @close="showExternalLinkModal = false"
    />
  </div>
</template>

<script>
// @ts-ignore - TS errors are expected if using JS in a TS component
import DataTable from './DataTable.vue';
import ExternalLinkModal from './ExternalLinkModal.vue';
import { useWhiteboardStatuses } from '../composables/useWhiteboardStatuses';

export default {
  components: {
    DataTable,
    ExternalLinkModal
  },
  
  props: {
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  
  setup() {
    const { 
      statusOptions, 
      getOrderedStatuses, 
      getStatusColor, 
      getStatusName,
      defaultStatusSlug
    } = useWhiteboardStatuses();
    
    return {
      statusOptions,
      getOrderedStatuses,
      getStatusColor,
      getStatusName,
      defaultStatusSlug
    };
  },
  
  data() {
    return {
      cards: [],
      loading: true,
      currentFilter: 'active',
      searchQuery: '',
      debouncedSearchQuery: '',
      searchDebounceTimer: null,
      showAddCardModal: false,
      showDeleteModal: false,
      showExternalLinkModal: false,
      externalLinkUrl: '',
      externalLinkTarget: '_blank',
      editingCard: null,
      cardToDelete: null,
      viewMode: 'grid',
      sortBy: 'card_date', // Default sort field
      sortDirection: 'desc', // Default sort direction
      showTags: true, // Toggle for showing/hiding tags
      showDates: true, // Toggle for showing/hiding dates
      showDeadlines: true, // Toggle for showing/hiding deadlines
      cardSize: 2, // Card size (1-small, 2-medium, 3-large)
      kanbanGranularity: 2, // Kanban detail level (1-compact, 2-standard, 3-detailed)
      roadmapGranularity: 2, // Roadmap detail level (1-compact, 2-standard, 3-detailed)
      timelineStartDate: new Date(new Date().getFullYear(), 0, 1), // January 1st of current year
      dayWidth: 2.5, // Width in pixels per day on the timeline
      tableColumns: [
        { key: 'status', label: 'Status', sortable: true },
        { key: 'headline', label: 'Headline', sortable: true },
        { key: 'project', label: 'Project', sortable: true },
        { key: 'card_date', label: 'Date', sortable: true },
        { key: 'deadline', label: 'Deadline', sortable: true },
        { key: 'assigned', label: 'Assigned To', sortable: false },
        { key: 'image', label: 'Logo', sortable: false },
        { key: 'link', label: 'Link', sortable: false },
        { key: 'actions', label: 'Actions', sortable: false, align: 'right' }
      ],
      availableStatuses: ['all'],
      timeScale: 5, // Default time scale (1-10)
      isDragging: false,
      currentDraggedCard: null,
      dragOverStatus: null,
      dragInsertIndex: null,
    }
  },
  
  computed: {
    filteredCards() {
      if (!this.cards) return [];
      
      let filtered = [...this.cards];
      
      // Apply status filter
      if (this.currentFilter !== 'all') {
        filtered = filtered.filter(card => card.status === this.currentFilter);
      }
      
      // Apply search query filter
      if (this.debouncedSearchQuery) {
        const query = this.debouncedSearchQuery.toLowerCase().trim();
        filtered = filtered.filter(card => {
          // Search in headline
          if (card.headline && card.headline.toLowerCase().includes(query)) return true;
          
          // Search in description
          if (card.description && card.description.toLowerCase().includes(query)) return true;
          
          // Search in project name
          if (card.project && card.project.name && card.project.name.toLowerCase().includes(query)) return true;
          
          return false;
        });
      }
      
      return filtered;
    },
    sortedCards() {
      let filteredCards = [...this.cards];
      
      // Log card counts by status for debugging
      if (this.cards.length > 0 && this.isDragging) {
        console.debug('Card counts by status:');
        const statusCounts = {};
        this.getOrderedStatuses.forEach(status => {
          statusCounts[status.slug] = this.cards.filter(c => c.status === status.slug).length;
        });
        console.debug(statusCounts);
      }
      
      // Apply status filter if set
      if (this.currentFilter !== 'all') {
        filteredCards = filteredCards.filter(card => card.status === this.currentFilter);
      }
      
      // Filter by search query if set
      if (this.debouncedSearchQuery.trim()) {
        const query = this.debouncedSearchQuery.toLowerCase().trim();
        filteredCards = filteredCards.filter(card => {
          // Search in headline
          if (card.headline.toLowerCase().includes(query)) return true;
          
          // Search in description
          if (card.description && card.description.toLowerCase().includes(query)) return true;
          
          // Search in project
          if (card.project && card.project.name.toLowerCase().includes(query)) return true;
          
          return false;
        });
      }
      
      // Apply sorting
      if (this.sortBy === 'headline') {
        filteredCards.sort((a, b) => {
          return this.sortDirection === 'asc' 
            ? a.headline.localeCompare(b.headline)
            : b.headline.localeCompare(a.headline);
        });
      } else if (this.sortBy === 'card_date') {
        filteredCards.sort((a, b) => {
          const dateA = new Date(a.card_date);
          const dateB = new Date(b.card_date);
          return this.sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
        });
      } else if (this.sortBy === 'deadline') {
        filteredCards.sort((a, b) => {
          // Cards without deadline go last
          if (!a.deadline && !b.deadline) return 0;
          if (!a.deadline) return this.sortDirection === 'asc' ? 1 : -1;
          if (!b.deadline) return this.sortDirection === 'asc' ? -1 : 1;
          
          const dateA = new Date(a.deadline);
          const dateB = new Date(b.deadline);
          return this.sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
        });
      } else if (this.sortBy === 'project') {
        filteredCards.sort((a, b) => {
          // Cards without project go last
          if (!a.project && !b.project) return 0;
          if (!a.project) return this.sortDirection === 'asc' ? 1 : -1;
          if (!b.project) return this.sortDirection === 'asc' ? -1 : 1;
          
          return this.sortDirection === 'asc' 
            ? a.project.name.localeCompare(b.project.name)
            : b.project.name.localeCompare(a.project.name);
        });
      } else if (this.sortBy === 'assigned') {
        filteredCards.sort((a, b) => {
          const countA = a.assignedUsers ? a.assignedUsers.length : 0;
          const countB = b.assignedUsers ? b.assignedUsers.length : 0;
          return this.sortDirection === 'asc' ? countA - countB : countB - countA;
        });
      } else if (this.sortBy === 'status') {
        // Sort by status order from getOrderedStatuses
        filteredCards.sort((a, b) => {
          // Get the index of the status in the ordered array
          const statusOrder = this.getOrderedStatuses.reduce((acc, status, index) => {
            acc[status.slug] = index;
            return acc;
          }, {});
          
          const orderA = statusOrder[a.status] !== undefined ? statusOrder[a.status] : 999;
          const orderB = statusOrder[b.status] !== undefined ? statusOrder[b.status] : 999;
          
          return this.sortDirection === 'asc' ? orderA - orderB : orderB - orderA;
        });
      }
      
      return filteredCards;
    },
    activeCardsCount() {
      return this.cards.filter(card => card.is_active).length
    },
    archivedCardsCount() {
      return this.cards.filter(card => !card.is_active).length
    },
    totalCardsCount() {
      return this.cards.length
    },
    cardSizeText() {
      const sizeValue = parseFloat(this.cardSize);
      if (sizeValue <= 1.3) {
        return 'Compact';
      } else if (sizeValue <= 2.3) {
        return 'Medium';
      } else {
        return 'Large';
      }
    },
    gridColumnsClass() {
      const sizeValue = parseFloat(this.cardSize);
      if (sizeValue <= 1.3) {
        return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'; // Compact - more columns
      } else if (sizeValue <= 2.3) {
        return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'; // Medium
      } else {
        return 'grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'; // Large - fewer columns
      }
    },
    kanbanGridClass() {
      // Get the number of status columns
      const numColumns = this.getOrderedStatuses.length;
      
      // Add gap-6 for spacing between columns
      let baseClass = 'gap-6 ';
      
      // For 1-3 columns, use fixed widths
      if (numColumns <= 3) {
        return baseClass + `md:grid-cols-${numColumns}`;
      }
      
      // For 4 columns
      if (numColumns === 4) {
        return baseClass + 'md:grid-cols-2 lg:grid-cols-4';
      }
      
      // For 5 or more columns, use a responsive approach
      if (numColumns >= 5) {
        return baseClass + 'md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
      }
      
      // Fallback
      return baseClass + 'md:grid-cols-3';
    },
    kanbanGranularityText() {
      switch (parseInt(this.kanbanGranularity)) {
        case 1:
          return 'Compact';
        case 2:
          return 'Standard';
        case 3:
          return 'Detailed';
        default:
          return 'Standard';
      }
    },
    roadmapGranularityText() {
      switch (parseInt(this.roadmapGranularity)) {
        case 1:
          return 'Compact';
        case 2:
          return 'Standard';
        case 3:
          return 'Detailed';
        default:
          return 'Standard';
      }
    },
    roadmapItemClass() {
      switch (parseInt(this.roadmapGranularity)) {
        case 1:
          return 'h-7 text-xs font-medium';
        case 2:
          return 'h-8 text-xs font-medium';
        case 3:
          return 'h-9 text-xs font-medium';
        default:
          return 'h-7 text-xs font-medium';
      }
    },
    timeScaleText() {
      const scale = parseInt(this.timeScale);
      if (scale <= 3) return 'Year';
      if (scale <= 6) return 'Quarter';
      if (scale <= 8) return 'Month';
      return 'Week';
    },
    dragPreviewStyle() {
      return {
        left: `${this.currentDraggedCard.x}px`,
        top: `${this.currentDraggedCard.y}px`,
        width: '200px',
        height: '80px',
        border: '2px dashed black',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderRadius: '5px',
        padding: '10px',
        position: 'fixed',
        zIndex: 50,
        pointerEvents: 'none',
      };
    },
  },
  
  mounted() {
    this.fetchCards();
    this.fetchStatusesForKanban();
    
    // Initialize timeline for roadmap view
    this.initializeTimeline();
    
    // Add event listener to close dropdowns when clicking outside
    document.addEventListener('click', this.closeAllStatusDropdowns);
  },
  
  beforeUnmount() {
    // Clean up the event listener
    document.removeEventListener('click', this.closeAllStatusDropdowns);
  },
  
  watch: {
    searchQuery(newVal) {
      // Clear the existing timer
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }
      
      // Set a new timer
      this.searchDebounceTimer = setTimeout(() => {
        this.debouncedSearchQuery = newVal;
      }, 300); // 300ms debounce
    },
    viewMode(newMode) {
      if (newMode === 'kanban') {
        this.fetchStatusesForKanban();
      }
      
      if (newMode === 'roadmap') {
        this.initializeTimeline();
      }
    }
  },
  
  methods: {
    async fetchCards() {
      this.loading = true;
      try {
        const response = await axios.get('/api/whiteboard-cards');
        this.cards = response.data;
      } catch (error) {
        console.error('Error fetching cards:', error);
      } finally {
        this.loading = false;
      }
    },
    
    handleRowClick(card) {
      // Open the card details or perform an action when a row is clicked
      if (this.isAdmin) {
        this.editCard(card)
      } else if (card.link) {
        this.openExternalLink(card.link);
      }
    },
    
    editCard(card) {
      this.editingCard = { ...card }
      this.showAddCardModal = true
    },
    
    closeModal() {
      this.showAddCardModal = false
      this.editingCard = null
    },
    
    async saveCard(cardData) {
      try {
        // Ensure user_ids is properly formatted
        if (!cardData.user_ids) {
          cardData.user_ids = [];
        }
        
        // Force user_ids to be an array of numbers if it's not already
        if (cardData.user_ids && !Array.isArray(cardData.user_ids)) {
          cardData.user_ids = [];
        } else if (cardData.user_ids) {
          // Ensure all IDs are numbers
          cardData.user_ids = cardData.user_ids.map(id => Number(id));
        }
        
        let response;
        
        if (cardData.id) {
          // Update existing card
          response = await axios.put(`/api/whiteboard-cards/${cardData.id}`, cardData);
        } else {
          // Create new card
          response = await axios.post('/api/whiteboard-cards', cardData);
        }
        
        // Refresh cards
        await this.fetchCards();
        this.closeModal();
      } catch (error) {
        if (error.response) {
          // Handle error response
        }
      }
    },
    
    deleteCard(card) {
      this.cardToDelete = card
      this.showDeleteModal = true
    },
    
    async confirmDelete() {
      try {
        await axios.delete(`/api/whiteboard-cards/${this.cardToDelete.id}`)
        this.cards = this.cards.filter(c => c.id !== this.cardToDelete.id)
        this.showDeleteModal = false
        this.cardToDelete = null
      } catch (error) {
        console.error('Error deleting card:', error)
      }
    },
    
    async toggleCardStatus(card) {
      try {
        const response = await axios.patch(`/api/whiteboard-cards/${card.id}/toggle-status`);
        
        // Update local card status using the server response
        const index = this.cards.findIndex(c => c.id === card.id);
        if (index !== -1) {
          this.cards[index].is_active = response.data.is_active;
          this.cards[index].status = response.data.status;
        }
      } catch (error) {
        // Handle error
        console.error('Error toggling card status:', error);
      }
    },
    
    async changeCardStatus(card, status) {
      try {
        const response = await axios.patch(`/api/whiteboard-cards/${card.id}/status`, {
          status: status
        });
        
        // Update local card status using the server response
        const index = this.cards.findIndex(c => c.id === card.id);
        if (index !== -1) {
          this.cards[index].status = response.data.status;
          this.cards[index].is_active = response.data.is_active;
        }
      } catch (error) {
        console.error('Error changing card status:', error);
      }
    },
    
    isDeadlineNear(deadlineStr) {
      if (!deadlineStr) return false
      
      const deadline = new Date(deadlineStr)
      const today = new Date()
      
      // Calculate the difference in days
      const timeDiff = deadline.getTime() - today.getTime()
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))
      
      // Return true if deadline is within 7 days or past due
      return daysDiff <= 7
    },
    
    openExternalLink(url) {
      if (url) {
        this.externalLinkUrl = url;
        this.externalLinkTarget = '_blank';
        this.showExternalLinkModal = true;
      }
    },
    
    toggleSort(column) {
      if (column.key === this.sortBy) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortBy = column.key;
        this.sortDirection = 'asc';
      }
    },
    
    clearSearch() {
      this.searchQuery = '';
      this.debouncedSearchQuery = '';
    },
    
    getUniqueProjects() {
      // Get unique projects
      const uniqueProjects = [];
      const projectNames = new Set();
      
      this.sortedCards.forEach(card => {
        if (card.project && !projectNames.has(card.project.name)) {
          projectNames.add(card.project.name);
          uniqueProjects.push(card.project);
        }
      });
      
      // Add "No Project" entry
      if (this.sortedCards.some(card => !card.project)) {
        uniqueProjects.push({ name: 'No Project', id: 'no-project' });
      }
      
      return uniqueProjects;
    },
    
    getProjectCards(project) {
      if (project.id === 'no-project') {
        return this.sortedCards.filter(card => !card.project);
      }
      return this.sortedCards.filter(card => card.project && card.project.id === project.id);
    },
    
    getTimelineItemStyle(card) {
      let backgroundColor = '#e5e7eb'; // Default light gray
      let textColor = '#374151';       // Default dark text
      
      if (card.status) {
        const statusColor = this.getStatusColor(card.status);
        backgroundColor = statusColor + '20'; // Add transparency
        textColor = '#000000';
      } else if (card.project && this.projects[card.project] && this.projects[card.project].color) {
        backgroundColor = this.projects[card.project].color + '20'; // Add transparency
        textColor = '#000000';
      }
      
      return {
        ...this.getTimelineItemPosition(card),
        backgroundColor,
        color: textColor,
        borderColor: card.status ? this.getStatusColor(card.status) : '#d1d5db'
      };
    },
    
    getTimelineItemPosition(card) {
      if (!card.card_date) {
        // Return a default position if the card doesn't have a date
        return {
          left: '0px',
          top: '0px'
        };
      }
      
      // Calculate position based on the card date
      const startDate = new Date(card.card_date);
      const timeDiff = startDate.getTime() - this.timelineStartDate.getTime();
      const days = Math.max(0, Math.floor(timeDiff / (1000 * 60 * 60 * 24)));
      const left = (days * this.dayWidth) + 'px';
      
      // Calculate the vertical position (top) based on the card's index in the project
      const projectCards = this.getProjectCards(card.project || { id: 'no-project' });
      const index = projectCards.findIndex(c => c.id === card.id);
      
      // Adjust the spacing based on granularity
      let itemHeight = 28; // Default (h-7)
      let itemMargin = 7;  // Default margin
      
      switch (parseInt(this.roadmapGranularity)) {
        case 1:
          itemHeight = 28; // h-7
          itemMargin = 7;
          break;
        case 2:
          itemHeight = 32; // h-8
          itemMargin = 8;
          break;
        case 3:
          itemHeight = 36; // h-9
          itemMargin = 9;
          break;
      }
      
      const top = ((index >= 0 ? index : 0) * (itemHeight + itemMargin)) + 'px';
      
      return {
        left,
        top
      };
    },
    
    getStatusCount(statusSlug) {
      if (statusSlug === 'all') {
        return this.totalCardsCount;
      }
      return this.cards.filter(card => card.status === statusSlug).length;
    },
    
    getStatusCountClass(statusSlug) {
      switch (statusSlug) {
        case 'active':
          return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-200';
        case 'completed':
          return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-200';
        case 'archived':
          return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-200';
        default:
          return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-200';
      }
    },
    
    closeAllStatusDropdowns(event) {
      if (!this.$el.contains(event.target)) {
        this.cards.forEach(card => {
          card.showStatusDropdown = false;
        });
      }
    },
    
    fetchStatusesForKanban() {
      const { fetchStatusOptions } = useWhiteboardStatuses();
      // Force fetch the latest statuses from the database
      fetchStatusOptions(true);
    },
    
    initializeTimeline() {
      // Set the start date to January 1st of the current year
      this.timelineStartDate = new Date(new Date().getFullYear(), 0, 1);
      
      // Set default time scale
      this.timeScale = 5;
      this.updateTimeScale();
      
      // Calculate an appropriate day width based on the number of cards
      // and their distribution throughout the year
      this.calculateTimelineScale();
    },
    
    calculateTimelineScale() {
      // Find the earliest and latest dates in the dataset
      let earliestDate = new Date();
      let latestDate = new Date(0); // Jan 1, 1970
      
      this.cards.forEach(card => {
        if (card.card_date) {
          const date = new Date(card.card_date);
          if (date < earliestDate) earliestDate = date;
          if (date > latestDate) latestDate = date;
        }
        
        if (card.deadline) {
          const date = new Date(card.deadline);
          if (date > latestDate) latestDate = date;
        }
      });
      
      // If we found valid dates, adjust the timeline start date and day width
      if (latestDate > new Date(0)) {
        // Set timeline start to the beginning of the earliest date's month,
        // or January 1st of the current year, whichever is earlier
        const yearStart = new Date(new Date().getFullYear(), 0, 1);
        const monthStart = new Date(earliestDate.getFullYear(), earliestDate.getMonth(), 1);
        this.timelineStartDate = monthStart < yearStart ? monthStart : yearStart;
        
        // Add a buffer of days to the calculated day width
        const daysDifference = Math.ceil((latestDate - this.timelineStartDate) / (1000 * 60 * 60 * 24)) + 30;
        
        // Calculate dayWidth based on available space and days to display
        // Assuming the timeline container is about 1000px wide
        const timelineWidth = 1000;
        this.dayWidth = Math.max(2, Math.min(5, timelineWidth / daysDifference));
      }
    },

    goToStatusAdmin() {
      // Navigate to the status admin page
      window.location.href = '/admin/whiteboard-statuses';
    },

    updateTimeScale() {
      // Update the day width based on the time scale
      const scale = parseInt(this.timeScale);
      // Exponential scale to make the changes more dramatic
      this.dayWidth = Math.pow(1.3, scale - 1);
    },
    
    /**
     * Show add card modal with a specific status pre-selected
     */
    showAddCardWithStatus(status) {
      // Set default status and show modal
      this.defaultStatusSlug = status;
      this.showAddCardModal = true;
    },
    
    /**
     * Handle the start of a drag operation
     */
    handleDragStart(event, card, cardIndex) {
      this.isDragging = true;
      this.currentDraggedCard = { 
        ...card,
        x: event.clientX,
        y: event.clientY,
        initialX: event.clientX,
        initialY: event.clientY,
        cardIndex: cardIndex,
      };
      
      // Set data for the drag operation
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', JSON.stringify({
        id: card.id,
        status: card.status
      }));
      
      // Create a custom drag image (optional - browser default can be used)
      const dragImg = document.createElement('div');
      dragImg.classList.add('kanban-drag-ghost');
      dragImg.textContent = card.headline;
      dragImg.style.position = 'absolute';
      dragImg.style.top = '-1000px';
      document.body.appendChild(dragImg);
      
      // Set the drag image with offset (default one often looks better)
      try {
        event.dataTransfer.setDragImage(dragImg, 10, 10);
      } catch (e) {
        console.warn('Custom drag image not supported in this browser', e);
      }
      
      // Add a class to style the dragged element
      event.target.classList.add('dragging');
      
      // Add event listener for tracking mouse movement during drag
      document.addEventListener('dragover', this.trackDragPosition);
    },
    
    /**
     * Track the mouse position during drag for custom preview
     */
    trackDragPosition(event) {
      if (this.isDragging && this.currentDraggedCard) {
        this.currentDraggedCard.x = event.clientX;
        this.currentDraggedCard.y = event.clientY;
      }
    },
    
    /**
     * Handle column dragover for visual feedback
     */
    handleDragOver(event, status) {
      event.preventDefault();
      this.dragOverStatus = status;
      
      // Get column element for scroll handling
      const column = event.currentTarget;
      
      // Auto-scroll when near top or bottom of column
      const { top, bottom, height } = column.getBoundingClientRect();
      const scrollSpeed = 10;
      
      if (event.clientY < top + 50 && column.scrollTop > 0) {
        // Scroll up when near top
        column.scrollTop -= scrollSpeed;
      } else if (event.clientY > bottom - 50 && column.scrollTop < column.scrollHeight - height) {
        // Scroll down when near bottom
        column.scrollTop += scrollSpeed;
      }
    },
    
    /**
     * Handle column dragleave event
     */
    handleDragLeave(event, status) {
      // Make sure we're not leaving to a child element
      const relatedTarget = event.relatedTarget;
      if (relatedTarget && event.currentTarget.contains(relatedTarget)) {
        return; // Ignore if moving to a child element
      }
      
      this.dragOverStatus = null;
    },
    
    /**
     * Handle the end of a drag operation
     */
    handleDragEnd(event) {
      // Clean up
      this.isDragging = false;
      this.dragOverStatus = null;
      document.removeEventListener('dragover', this.trackDragPosition);
      
      // Remove any ghost elements
      const ghost = document.querySelector('.kanban-drag-ghost');
      if (ghost) ghost.remove();
      
      // Remove dragging class from all elements
      document.querySelectorAll('.dragging').forEach(el => {
        el.classList.remove('dragging');
      });
    },
    
    /**
     * Handle dropping a card in a column
     */
    handleDrop(event, newStatus) {
      event.preventDefault();
      
      // Early return if not dragging
      if (!this.isDragging || !this.currentDraggedCard) {
        this.dragOverStatus = null;
        this.dragInsertIndex = null;
        return;
      }
      
      const oldStatus = this.currentDraggedCard.status;
      const cardId = this.currentDraggedCard.id;
      
      // Reset drop-related states
      this.dragOverStatus = null;
      
      // First case: Moving between columns (status change)
      if (oldStatus !== newStatus) {
        // Get a copy of the card before modifying it
        const cardToMove = this.cards.find(c => c.id === cardId);
        
        if (!cardToMove) {
          console.error('Card not found in cards array:', cardId);
          this.handleDragEnd(event);
          return;
        }
        
        // Store original status in case we need to revert
        const originalStatus = cardToMove.status;
        
        // Create a deep copy of the card to avoid reference issues
        const cardCopy = JSON.parse(JSON.stringify(cardToMove));
        
        // Update status in the copy first, not the original card
        cardCopy.status = newStatus;
        
        // Optimistic UI update - keep the original card in its column until API confirms
        // but show a temporary copy in the new column
        this.cards = [
          ...this.cards.filter(c => c.id !== cardId), // Keep all other cards
          cardCopy // Add the card with updated status
        ];
        
        // Provide haptic feedback on mobile devices if supported
        if (window.navigator && window.navigator.vibrate) {
          window.navigator.vibrate(50);
        }
        
        // Call API to update status
        axios.patch(`/api/whiteboard-cards/${cardId}/status`, {
          status: newStatus
        })
        .then(response => {
          // Successful update - update the actual card
          cardToMove.status = newStatus;
          this.showNotification(`Card moved to ${this.getStatusName(newStatus)}`, 'success');
        })
        .catch(error => {
          // Failed update - restore original state
          console.error('Error updating card status:', error);
          
          // Remove the temporary card copy and revert to original state
          this.cards = this.cards.filter(c => !(c.id === cardId && c.status === newStatus));
          
          this.showNotification('Failed to update card status. Please try again.', 'error');
        })
        .finally(() => {
          // Clean up state
          this.handleDragEnd(event);
        });
        
        return;
      }
      
      // Second case: Reordering within the same column
      if (oldStatus === newStatus && this.dragInsertIndex !== null) {
        // Get cards in the current column
        const columnCards = this.sortedCards.filter(card => card.status === newStatus);
        const sourceIndex = this.currentDraggedCard.cardIndex;
        const targetIndex = this.dragInsertIndex;
        
        // Don't reorder if dropping on the same position
        if (sourceIndex === targetIndex || sourceIndex === targetIndex - 1) {
          this.dragInsertIndex = null;
          this.handleDragEnd(event);
          return;
        }
        
        // Perform the reordering
        this.reorderCards(newStatus, this.currentDraggedCard.id, targetIndex);
        
        // Provide haptic feedback
        if (window.navigator && window.navigator.vibrate) {
          window.navigator.vibrate(50);
        }
      }
      
      // Clean up state
      this.dragInsertIndex = null;
      this.handleDragEnd(event);
    },
    
    /**
     * Touch support - handle touch start for mobile drag and drop
     */
    handleTouchStart(event, card, cardIndex) {
      // Don't start drag on multitouch or right-click
      if (event.touches.length > 1) return;
      
      // Store initial touch position
      const touch = event.touches[0];
      
      this.currentDraggedCard = { 
        ...card,
        x: touch.clientX,
        y: touch.clientY,
        initialX: touch.clientX,
        initialY: touch.clientY,
        cardIndex: cardIndex,
        isDragging: false, // Start with false until we confirm it's a drag
      };
      
      // Don't prevent default here to allow scrolling
    },
    
    /**
     * Touch support - handle touch move
     */
    handleTouchMove(event) {
      if (!this.currentDraggedCard) return;
      
      const touch = event.touches[0];
      
      // Calculate distance moved
      const dx = touch.clientX - this.currentDraggedCard.initialX;
      const dy = touch.clientY - this.currentDraggedCard.initialY;
      
      // Determine if we're dragging (moved more than 10px)
      if (!this.isDragging && Math.sqrt(dx * dx + dy * dy) > 10) {
        this.isDragging = true;
        event.preventDefault(); // Prevent scrolling once we're dragging
      }
      
      if (this.isDragging) {
        // Update position for drag preview
        this.currentDraggedCard.x = touch.clientX;
        this.currentDraggedCard.y = touch.clientY;
        
        // Find the column we're over based on element detection
        this.detectColumnFromTouch(touch);
        
        event.preventDefault(); // Prevent scrolling during drag
      }
    },
    
    /**
     * Touch support - detect which column the touch is over
     */
    detectColumnFromTouch(touch) {
      // Get all column elements
      const columns = document.querySelectorAll('.kanban-column');
      
      // Check which column contains the touch point
      columns.forEach(column => {
        const rect = column.getBoundingClientRect();
        if (
          touch.clientX >= rect.left &&
          touch.clientX <= rect.right &&
          touch.clientY >= rect.top &&
          touch.clientY <= rect.bottom
        ) {
          // Get status from data attribute
          const status = column.getAttribute('data-status');
          
          if (status) {
            this.dragOverStatus = status;
          }
        }
      });
    },
    
    /**
     * Touch support - handle touch end
     */
    handleTouchEnd(event, currentStatus) {
      // Only handle if we were actually dragging
      if (this.isDragging && this.currentDraggedCard) {
        const newStatus = this.dragOverStatus || currentStatus;
        const oldStatus = this.currentDraggedCard.status;
        
        // Don't do anything if status hasn't changed
        if (oldStatus !== newStatus && newStatus) {
          // Provide haptic feedback if supported
          if (window.navigator && window.navigator.vibrate) {
            window.navigator.vibrate(50);
          }
          
          // Update card status
          this.updateCardStatus(this.currentDraggedCard, newStatus, oldStatus);
        }
      }
      
      // Reset state
      this.isDragging = false;
      this.dragOverStatus = null;
      this.currentDraggedCard = null;
    },
    
    /**
     * Update card status via API
     */
    updateCardStatus(card, newStatus, oldStatus) {
      // Show notification (safely handle if toast library isn't available)
      this.showNotification(`Moving card to ${this.getStatusName(newStatus)}...`, 'info');
      
      axios.patch(`/api/whiteboard-cards/${card.id}/status`, {
        status: newStatus
      })
      .then(response => {
        // Successful update
        this.showNotification(`Card moved to ${this.getStatusName(newStatus)}`, 'success');
      })
      .catch(error => {
        // Revert UI change on error
        card.status = oldStatus;
        console.error('Error updating card status:', error);
        this.showNotification('Failed to update card status. Please try again.', 'error');
      });
    },
    
    /**
     * Safe notification method that works whether a toast library is available or not
     */
    showNotification(message, type = 'info') {
      // Check if the toast library is available
      if (this.$toast && typeof this.$toast[type] === 'function') {
        this.$toast[type](message);
      } else {
        // Fallback to console
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // Optional: Show a simple browser notification for the user
        if (type === 'error') {
          alert(message);
        }
      }
    },
    
    /**
     * Reorder cards within the same column
     */
    reorderCards(column, cardId, newIndex) {
      // Get all cards
      const allCards = [...this.cards];
      
      // Get cards in the current column
      const columnCards = allCards.filter(card => card.status === column);
      
      // Find the card we're moving
      const cardIndex = columnCards.findIndex(card => card.id === cardId);
      if (cardIndex === -1) {
        console.error('Card not found for reordering:', cardId);
        return;
      }
      
      // Get the card
      const card = columnCards[cardIndex];
      
      // Create a copy of the column cards for reordering
      const newColumnCards = [...columnCards];
      
      // Remove the card from its current position
      newColumnCards.splice(cardIndex, 1);
      
      // Calculate the actual target index (which might be different after removal)
      const targetIndex = newIndex > cardIndex ? newIndex - 1 : newIndex;
      
      // Insert the card at the new index
      newColumnCards.splice(targetIndex, 0, card);
      
      // Update the order property for all cards in the column
      newColumnCards.forEach((card, index) => {
        card.order = index + 1;
      });
      
      // Create a new array with all cards in other columns + the reordered column cards
      const updatedCards = [
        ...allCards.filter(card => card.status !== column),
        ...newColumnCards
      ];
      
      // Update the cards in the main cards array
      this.cards = updatedCards;
      
      // Show notification
      this.showNotification('Reordering card...', 'info');
      
      // Optionally save the new order to the server
      this.saveCardOrder(column, newColumnCards)
        .then(() => {
          this.showNotification('Card order updated', 'success');
        })
        .catch(error => {
          // If there's an error, revert to original order
          this.cards = allCards;
          this.showNotification('Failed to update card order', 'error');
        });
    },
    
    /**
     * Save the new card order to the server
     */
    saveCardOrder(column, cards) {
      // Create an array of {id, order} objects
      const orderData = cards.map((card, index) => ({
        id: card.id,
        order: index + 1
      }));
      
      // Return the promise so we can handle it in the calling function
      return axios.post('/api/whiteboard-cards/reorder', {
        status: column,
        cards: orderData
      });
    },
    handleItemDragOver(event, status, cardIndex) {
      event.preventDefault();
      this.dragInsertIndex = cardIndex;
    },
  }
}
</script>

<style scoped>
/* Range slider styling */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #e5e7eb;
  border-radius: 5px;
  outline: none;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #1f2937;
  border-radius: 50%;
  cursor: pointer;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #1f2937;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Dark mode range slider */
:deep(.dark) .range-slider {
  background: #334155;
}

:deep(.dark) .range-slider::-webkit-slider-thumb {
  background: #e2e8f0;
}

:deep(.dark) .range-slider::-moz-range-thumb {
  background: #e2e8f0;
}

/* Kanban board styling */
.kanban-board {
  gap: 20px;
  margin-bottom: 32px;
  min-height: calc(100vh - 300px);
}

.kanban-column {
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.kanban-column:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

:deep(.dark) .kanban-column {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

:deep(.dark) .kanban-column:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Drag and drop styles */
.dragging {
  opacity: 0.5;
  transform: scale(1.02);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

:deep(.dark) .dragging {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
}

/* Enhanced drag and drop styles */
.kanban-column-drag-over {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.05);
  transition: all 0.2s ease;
}

:deep(.dark) .kanban-column-drag-over {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.1);
}

.kanban-drag-preview {
  pointer-events: none;
  z-index: 50;
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease;
  will-change: transform;
}

.kanban-card-dragging {
  visibility: visible !important;
  opacity: 0.6 !important;
}

/* Card placeholder animation */
@keyframes pulse-border {
  0% {
    border-color: rgba(59, 130, 246, 0.3);
  }
  50% {
    border-color: rgba(59, 130, 246, 0.8);
  }
  100% {
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.kanban-column-drag-over .kanban-column-content > div:empty {
  animation: pulse-border 1.5s infinite;
  border: 2px dashed rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 0.5rem;
  min-height: 100px;
}

/* Card insertion indicators */
.insert-above {
  position: relative;
}

.insert-above::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 0;
  right: 0;
  height: 4px;
  background-color: #3b82f6;
  border-radius: 2px;
  z-index: 10;
}

.insert-below {
  position: relative;
}

.insert-below::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 4px;
  background-color: #3b82f6;
  border-radius: 2px;
  z-index: 10;
}

/* Touch device enhancements */
@media (pointer: coarse) {
  .kanban-drag-preview {
    display: block !important;
  }
  
  .kanban-card-dragging {
    opacity: 0.2 !important;
  }
}

/* Style improvements to match the rest of the site */
.kanban-column-content > div > div {
  border-radius: 0.75rem;
  border-width: 1px;
  overflow: hidden;
  transition: all 0.25s ease;
}

.kanban-column-content > div > div:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

:deep(.dark) .kanban-column-content > div > div:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* Status badge styling to match other badges in the app */
.kanban-column h3 span.rounded-full {
  transition: all 0.3s ease;
}

.kanban-column:hover h3 span.rounded-full {
  transform: scale(1.1);
}

/* Empty state styling improvements */
.kanban-column-content > div:empty + div {
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.5);
  border-style: dashed;
}

:deep(.dark) .kanban-column-content > div:empty + div {
  background-color: rgba(30, 41, 59, 0.5);
}

.kanban-column-content > div:empty + div:hover {
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.5);
}

:deep(.dark) .kanban-column-content > div:empty + div:hover {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(59, 130, 246, 0.5);
}

@media (max-width: 768px) {
  .kanban-board {
    gap: 16px;
  }
}
</style> 