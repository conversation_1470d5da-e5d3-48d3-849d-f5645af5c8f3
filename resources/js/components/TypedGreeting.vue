<template>
    <span class="inline-flex items-baseline whitespace-nowrap">
        <span class="greeting-text">{{ displayedText }}</span>
        <img 
            v-if="showWavingHand" 
            src="/img/waving-hand.png" 
            class="ms-3 w-[0.8em] h-[0.8em] object-contain origin-[70%_70%] animate-subtle-wave inline-block align-baseline cursor-pointer hand-icon"
            alt="Waving hand"
            @click="hideGreeting"
            ref="handIcon"
        >
        <span class="typing-cursor" :class="{ 'blink': isBlinking, 'hide': !showCursor }">|</span>
    </span>
</template>

<script>
export default {
    props: {
        name: {
            type: String,
            required: true
        },
        customGreetings: {
            type: Array,
            default: () => []
        },
        generalGreetings: {
            type: Array,
            default: () => [
                'Hello',
                'Hi',
                'Welcome',
                'Greetings',
                'Good to see you'
            ]
        }
    },
    data() {
        return {
            displayedText: '',
            isBlinking: false,
            fullText: '',
            isTyping: false,
            isDeleting: false,
            showCursor: true,
            showWavingHand: false,
            isHidden: false,
            elementWidth: 0,
            elementHeight: 0,
            handPosition: { x: 0, y: 0 }
        }
    },
    mounted() {
        try {
            this.setGreeting();
            this.startTyping();
        } catch (error) {
            // Set fallback text in case of error
            this.displayedText = `Hello ${this.name}!`;
            this.showCursor = false;
        }
    },
    methods: {
        setGreeting() {
            try {
                const availableGreetings = Array.isArray(this.customGreetings) && this.customGreetings.length > 0 ? 
                    this.customGreetings : 
                    this.generalGreetings;
                
                // Add fallback for empty array
                if (!availableGreetings || !availableGreetings.length) {
                    this.fullText = `Hello ${this.name}!`;
                    return;
                }
                
                // Select a random greeting from the available ones
                const randomIndex = Math.floor(Math.random() * availableGreetings.length);
                const selectedGreeting = availableGreetings[randomIndex] || 'Hello';
                
                this.fullText = `${selectedGreeting} ${this.name}!`;
            } catch (error) {
                this.fullText = `Hello ${this.name}!`;
            }
        },
        hideGreeting() {
            if (this.isDeleting) return
            this.isDeleting = true
            this.showCursor = true
            this.isBlinking = false
            this.startDeleting()
        },
        async startDeleting() {
            // Show cursor first - no initial delay
            this.showCursor = true
            this.isBlinking = false
            
            // Use faster constant delays
            const baseDelay = 15; // Much faster base delay
            
            // Start deleting characters one by one - fast mode
            while (this.displayedText.length > 0) {
                // Delete a character
                this.displayedText = this.displayedText.slice(0, -1)
                
                // Very short delay between deletions
                // Using slightly different delays for different character types
                // but keeping everything very fast
                const currentChar = this.displayedText.slice(-1)
                if ('.!?'.includes(currentChar)) {
                    await this.wait(baseDelay * 1.5)
                } else if (currentChar === ' ') {
                    await this.wait(baseDelay * 1.2)
                } else {
                    await this.wait(baseDelay)
                }
            }
            
            // Hide elements quickly
            this.showWavingHand = false
            this.showCursor = false
            this.isHidden = true
            this.isDeleting = false

            // Emit event to parent when greeting is fully deleted
            this.$emit('greeting-hidden')
        },
        refreshGreeting() {
            if (!this.isHidden && !this.isDeleting) {
                this.hideGreeting()
                // Wait for deletion to complete before starting a new greeting
                this.$on('greeting-hidden', () => {
                    this.setGreeting()
                    this.isHidden = false
                    this.startTyping()
                })
            } else if (this.isHidden) {
                this.setGreeting()
                this.isHidden = false
                this.startTyping()
            }
        },
        getRandomDelay() {
            const baseDelay = 70
            const variance = 100
            return baseDelay + Math.random() * variance
        },
        getLongerDelay() {
            return 200 + Math.random() * 100
        },
        async startTyping() {
            try {
                if (!this.fullText) {
                    this.displayedText = `Hello ${this.name}!`;
                    return;
                }
                
                this.isTyping = true;
                this.isBlinking = false;

                for (let i = 0; i < this.fullText.length; i++) {
                    const currentChar = this.fullText[i];
                    this.displayedText += currentChar;

                    let delay;
                    if (currentChar === ' ') {
                        delay = this.getLongerDelay();
                    } else if ('.!?'.includes(currentChar)) {
                        delay = this.getLongerDelay() * 1.5;
                    } else {
                        delay = this.getRandomDelay();
                        
                        if (Math.random() < 0.05 && i < this.fullText.length - 1) {
                            const wrongChar = String.fromCharCode(
                                97 + Math.floor(Math.random() * 26)
                            );
                            this.displayedText += wrongChar;
                            await this.wait(300);
                            this.displayedText = this.displayedText.slice(0, -1);
                            await this.wait(200);
                        }
                    }

                    await this.wait(delay);
                }

                // Add the waving hand as the last "typed" character
                await this.wait(this.getLongerDelay());
                this.showWavingHand = true;
                
                // Final pause before hiding cursor
                await this.wait(200);
                this.isBlinking = false;
                await this.wait(300);
                this.showCursor = false;
                
                this.isTyping = false;
            } catch (error) {
                // Set fallback text in case of error
                this.displayedText = this.fullText || `Hello ${this.name}!`;
                this.showCursor = false;
                this.isTyping = false;
            }
        },
        wait(ms) {
            return new Promise(resolve => setTimeout(resolve, ms))
        }
    }
}
</script>

<style scoped>
.typing-cursor {
    display: inline-block;
    margin-left: 2px;
    opacity: 1;
    font-weight: 300;
    transition: opacity 0.3s ease-out;
}

.typing-cursor.hide {
    opacity: 0;
}

.typing-cursor.blink {
    animation: blink 0.8s infinite;
}

.hand-icon {
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 0 transparent);
    will-change: transform, filter;
}

.hand-icon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.6));
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1.2); }
    50% { transform: scale(1.3); }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    3% { transform: rotate(-6deg); }
    6% { transform: rotate(6deg); }
    9% { transform: rotate(-4deg); }
    12% { transform: rotate(2deg); }
    15%, 100% { transform: rotate(0deg); }
}

.animate-subtle-wave {
    animation: wave 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    animation-delay: 1.5s;
}
</style>
