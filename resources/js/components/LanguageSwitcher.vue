<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

const currentLocale = ref(window.localStorage.getItem('locale') || document.documentElement.lang || 'en')
const isPreviewingSwitch = ref(false)

const locales = [
  { 
    code: 'en',
    name: 'English',
    iconLight: '/img/enLight.svg',
    iconDark: '/img/enDark.svg'
  },
  { 
    code: 'sk',
    name: '<PERSON><PERSON><PERSON>',
    iconLight: '/img/skLight.svg',
    iconDark: '/img/skDark.svg'
  }
]

const switchLocale = async (locale: string) => {
  if (!isPreviewingSwitch.value) {
    isPreviewingSwitch.value = true
    return
  }
  try {
    const response = await fetch(`/language/${locale}`, {
      method: 'GET'
    })
    
    if (response.ok) {
      window.localStorage.setItem('locale', locale)
      window.location.reload()
    }
  } catch (error) {
    console.error('Failed to switch locale:', error)
  }
}

const resetPreview = () => {
  isPreviewingSwitch.value = false
}

// Reset preview state when clicking outside
const handleClickOutside = () => {
  resetPreview()
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const targetLocale = computed(() => currentLocale.value === 'en' ? 'sk' : 'en')
const currentLocaleData = computed(() => locales.find(l => l.code === currentLocale.value))
const targetLocaleData = computed(() => locales.find(l => l.code === targetLocale.value))
</script>

<template>
  <a 
    @click.stop="switchLocale(targetLocale)"
    @mouseleave="resetPreview"
    href="#"
    class="nav-link group"
    :class="{ 'nav-link-active': isPreviewingSwitch }"
    :title="isPreviewingSwitch ? 'Click again to confirm' : 'Click to change language'"
  >
    <span class="inline-flex items-center">
      <!-- Default State: Current Language Icon -->
      <template v-if="!isPreviewingSwitch">
        <img :src="currentLocaleData?.iconLight" class="w-6 h-6 dark:hidden" :alt="currentLocaleData?.name">
        <img :src="currentLocaleData?.iconDark" class="w-6 h-6 hidden dark:block" :alt="currentLocaleData?.name">
        <span class="ms-1 opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-200">
          {{ currentLocaleData?.name }}
        </span>
      </template>

      <!-- Preview State: Target Language Icon with Question Mark -->
      <template v-else>
        <img :src="targetLocaleData?.iconLight" class="w-6 h-6 dark:hidden" :alt="targetLocaleData?.name">
        <img :src="targetLocaleData?.iconDark" class="w-6 h-6 hidden dark:block" :alt="targetLocaleData?.name">
        <span class="ms-1">
          {{ targetLocaleData?.name }}
          <span class="opacity-80">?</span>
        </span>
      </template>
    </span>
  </a>
</template> 