<template>
  <div 
    class="grid grid-cols-1 sm:grid-cols-2 gap-3 p-3 sm:p-4 md:p-5 bg-white dark:bg-transparent border border-gray-200 dark:border-gray-900 rounded-lg"
    v-bind="$attrs"
  >
    <div class="w-full aspect-square flex items-center justify-center order-1 sm:order-none">
      <h1 class="text-black dark:text-gray-200 w-full h-full max-w-[300px] mx-auto">
        <a :href="link1" class="block w-full h-full items-center justify-center">
          <img v-if="imagePath" 
               :src="imagePath" 
               :alt="title" 
               class="w-full h-full object-contain transition-all duration-200 hover:scale-105" 
               @error="handleImageError">
          <i v-else 
             :class="icons + ' text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-7xl xl:text-8xl transition-all duration-200'" 
             aria-hidden="true">
          </i>
        </a>
      </h1>
    </div>
    <div class="flex flex-col justify-center order-2 sm:order-none">
      <h2 class="text-3xl xs:text-3xl sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold leading-none gant-modern-bold tracking-tighter dark:text-gray-200 mb-2">
        <a :href="link1">{{ 
          ['Component Design Base', 'Central Database', 'Component Database'].includes(title) 
            ? 'CDB' 
            : title === 'Order Group' 
              ? 'Ordergroup' 
              : title 
        }}</a>
      </h2>
      <div class="">
        <div class="">
          <p class="text-3xs xs:text-2xs sm:text-xs md:text-sm font-medium leading-none text-gray-300 dark:text-gray-700 gant-modern-regular mb-1">
            Icon count:
          </p>
          <p class="text-sm xs:text-base sm:text-lg md:text-xl font-medium leading-none text-black dark:text-neutral-200 mb-2">
            <i class="design design-component opacity-25 me-2"></i>
            <span class="gant-modern-bold">{{ count }}</span>
          </p>
        </div>
        <div class="">
          <p class="text-3xs xs:text-2xs sm:text-xs md:text-sm font-medium leading-none text-gray-300 dark:text-gray-700 gant-modern-regular mb-1">
            Last modified:
          </p>
          <p class="text-2xs xs:text-xs sm:text-sm md:text-base font-medium leading-none text-black dark:text-neutral-200 mb-4">
            <i class="design design-commit opacity-25 me-2"></i>
            <span class="gant-modern-bold">{{ update.split(' ')[0] }}</span>
            <span class="gant-modern-regular opacity-75 ms-1">{{ update.split(' ')[1] }}</span>
          </p>
        </div>
      </div>
      <div class="flex gap-1.5 sm:gap-2 relative min-h-[28px] sm:min-h-[32px] md:min-h-[36px] w-full">
        <button 
          @click="copyToFile(font.hash)" 
          :disabled="copySuccess || downloadSuccess"
          :class="[
            'inline-flex items-center justify-center px-3 xs:px-3 sm:px-4 md:px-4 py-1.5 xs:py-1.5 sm:py-2 transition-all duration-300 ease-out rounded-lg text-2xs xs:text-xs sm:text-sm gant-modern-bold hover:shadow-sm',
            copySuccess 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 absolute inset-0 z-10 scale-100' 
              : downloadSuccess
                ? 'scale-95'
                : 'bg-gray-100 hover:bg-gray-50 dark:bg-gray-900 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200 dark:border-gray-600 scale-100 flex-1'
          ]"
        >
          <div class="relative inline-flex items-center justify-center w-full">
            <div class="inline-flex items-center">
              <i :class="[
                'design transition-all duration-300 ease-out',
                copySuccess ? 'design-circle-checked text-green-500 mr-2' : 'design-scss text-gray-400 mr-1.5 group-hover:text-gray-500'
              ]"></i>
              <div class="relative">
                <span :class="{ 'opacity-0 scale-90 -translate-y-1': copySuccess, 'opacity-100 scale-100 translate-y-0': !copySuccess, 'transition-all duration-300 ease-out': true }">.scss</span>
                <span :class="{ 'opacity-100 scale-100 translate-y-0': copySuccess, 'opacity-0 scale-90 translate-y-1': !copySuccess, 'transition-all duration-300 ease-out absolute left-0': true }">Copied!</span>
              </div>
            </div>
          </div>
        </button>
        <button 
          @click="downloadFont"
          :disabled="downloadSuccess || copySuccess"
          :class="[
            'inline-flex items-center justify-center px-3 xs:px-3 sm:px-4 md:px-4 py-1.5 xs:py-1.5 sm:py-2 transition-all duration-300 ease-out rounded-lg text-2xs xs:text-xs sm:text-sm gant-modern-bold hover:shadow-sm',
            downloadSuccess 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 absolute inset-0 z-10 scale-100' 
              : copySuccess
                ? 'scale-95'
                : 'bg-gray-100 hover:bg-gray-50 dark:bg-gray-900 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-200 dark:border-gray-600 scale-100 flex-1'
          ]"
        >
          <div class="relative inline-flex items-center justify-center w-full">
            <div class="inline-flex items-center">
              <i :class="[
                'design transition-all duration-300 ease-out',
                downloadSuccess ? 'design-circle-checked text-green-500 mr-2' : 'design-woff text-gray-400 mr-1.5 group-hover:text-gray-500'
              ]"></i>
              <div class="relative">
                <span :class="{ 'opacity-0 scale-90 -translate-y-1': downloadSuccess, 'opacity-100 scale-100 translate-y-0': !downloadSuccess, 'transition-all duration-300 ease-out': true }">.woff2</span>
                <span :class="{ 'opacity-100 scale-100 translate-y-0': downloadSuccess, 'opacity-0 scale-90 translate-y-1': !downloadSuccess, 'transition-all duration-300 ease-out absolute left-0': true }">Downloaded!</span>
              </div>
            </div>
          </div>
        </button>
      </div>
      <input type="hidden" :id="'content_' + font.hash" :value="scssContent">
    </div>
  </div>
  <div v-if="showModal" class="modal">
    <div class="modal-content">
      <span @click="closeModal" class="close">&times;</span>
      <p>Obsah súboru skopírovaný!</p>
    </div> 
  </div>
</template>

<script>
export default {
  name: 'CardComponent',
  inheritAttrs: false,
  data() {
    return {
      showModal: false,
      copySuccess: false,
      downloadSuccess: false,
      imageError: false
    };
  },
  props: {
    title: {
      type: String,
      default: 'Default Title'
    },
    icons: {
      type: String,
      default: 'wms wms-scan_ean'
    },
    imagePath: {
      type: String,
      default: null
    },
    count: {
      type: String,
      default: 'Default Subtitle'
    },
    update: {
      type: String,
      default: 'Default content here...'
    },
    link1: {
      type: String,
      default: '#'
    },
    link2: {
      type: String,
      default: '#'
    },
    link1Text: {
      type: String,
      default: 'Preview'
    },
    link2Text: {
      type: String,
      default: 'Download'
    },
    font: {
      type: Object,
      default: () => ({
        hash: '',
        scssContent: ''
      })
    },
    scssContent: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleImageError() {
      this.imageError = true;
    },
    copyToFile(hash) {
        const scssContent = this.scssContent; // make sure this is the correct reference to your scss content
        if (navigator.clipboard && scssContent) {
            navigator.clipboard.writeText(scssContent)
                .then(() => {
                    this.copySuccess = true;
                    setTimeout(() => {
                      this.copySuccess = false;
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                });
        } else {
            console.error('Clipboard API not available');
        }
    },
    downloadFont() {
      window.location.href = this.link2;
      this.downloadSuccess = true;
      setTimeout(() => {
        this.downloadSuccess = false;
      }, 2000);
    },
    closeModal() {
      this.showModal = false; // Method to close the modal
    }
  }}
</script>