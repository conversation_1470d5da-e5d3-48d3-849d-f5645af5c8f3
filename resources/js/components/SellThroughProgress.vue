<template>
  <div class="progress-wrapper" :class="{ 'dark-mode': dark }">
    <div class="stats">
      <div class="stat-item">
        <span class="label">Sales</span>
        <span class="value">{{ formattedSales }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
      <div class="stat-item">
        <span class="label">Stock</span>
        <span class="value">{{ formattedStock }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
      <div class="stat-item">
        <span class="label">Order</span>
        <span class="value">{{ formattedOrder }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
    </div>
    <div class="progress-bar-container">
      <div class="progress">
        <div
          class="progress-bar"
          role="progressbar"
          :style="progressBarStyle"
          :aria-valuenow="percentage"
          aria-valuemin="0"
          aria-valuemax="100"
        ></div>
      </div>
      <span class="percentage-label">{{ percentage }}%</span>
    </div>
  </div>
</template>

<script>
export default {
    name: "SellThroughProgress",
    props: {
        sales: {
            type: [Number, String],
            required: true,
        },
        order: {
            type: [Number, String],
            required: true,
        },
        stock: {
            type: [Number, String],
            required: true,
        },
        dark: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        percentage() {
            const sales = parseFloat(String(this.sales).replace(/[,\s]/g, ''));
            const order = parseFloat(String(this.order).replace(/[,\s]/g, ''));

            if (isNaN(sales) || isNaN(order) || order === 0) {
                return 0;
            }
            return Math.round((sales / order) * 100);
        },
        progressBarStyle() {
            const p = this.percentage;
            let color;
            if (p <= 5) {
                color = '#f63a0f';
            } else if (p <= 25) {
                color = '#f27011';
            } else if (p <= 50) {
                color = '#f2b01e';
            } else if (p <= 75) {
                color = '#f2d31b';
            } else {
                color = '#86e01e';
            }
            return {
                width: p + '%',
                backgroundColor: color,
            };
        },
        formattedSales() {
            const sales = parseFloat(String(this.sales).replace(/[,\s]/g, ''));
            if (isNaN(sales)) return '0';
            return sales.toLocaleString();
        },
        formattedOrder() {
            const order = parseFloat(String(this.order).replace(/[,\s]/g, ''));
            if (isNaN(order)) return '0';
            return order.toLocaleString();
        },
        formattedStock() {
            const stock = parseFloat(String(this.stock).replace(/[,\s]/g, ''));
            if (isNaN(stock)) return '0';
            return stock.toLocaleString();
        }
    }
};
</script>

<style scoped>
.progress-wrapper {
  width: 100%;
  padding: 0.4rem 0.5rem;
  background: rgba(255,255,255,0.92);
  border-radius: 6px;
  border: 1px solid #f1f1f1;
  min-height: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.3rem;
}
.dark-mode.progress-wrapper {
  background: rgba(30,30,30,0.92);
  border: 0px solid #eee;
}
.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  gap: 0.8rem;
  align-items: center;
}
.stat-item {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-item .label {
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.02em;
  line-height: 1.2;
  margin-bottom: 0.1rem;
}
.stat-item .value {
  font-weight: 600;
  color: #212529;
  line-height: 1.2;
}
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  height: 1rem;
}
.percentage-label {
  font-weight: 600;
  min-width: 35px;
  text-align: right;
  color: #212529;
  line-height: 1.2;
}

/* Dark Mode Styles */
.dark-mode .stat-item .label {
  color: #adb5bd;
}
.dark-mode .stat-item .value,
.dark-mode .percentage-label {
  color: #f8f9fa;
}

/* Progress Bar Styles - Based on the example */
.progress {
  flex-grow: 1;
  padding: 4px;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
  overflow: hidden;
  height: 1.5rem!important;
}

.progress-bar {
  height: 16px;
  border-radius: 4px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
  transition: 0.4s linear;
  transition-property: width, background-color;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.25), inset 0 1px rgba(255, 255, 255, 0.1);
}
</style> 