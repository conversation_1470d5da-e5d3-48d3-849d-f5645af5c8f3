<template>
  <div class="particle-orb" ref="container">
    <div class="wrap">
      <div v-for="i in 300" :key="i" :class="`c c-${i}`" :style="getParticleStyle(i)"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// Get dark mode preference
const isDarkMode = ref(document.querySelector('html').classList.contains('dark') || 
                       document.querySelector('.dark') !== null);

// Watch for dark mode changes by observing DOM
let observer;
let resizeObserver;

// Responsive orb size
const orbSize = ref(180); // Increased default size
const container = ref(null);

// Generate random angles for each particle
const particles = Array.from({ length: 300 }, () => ({
  z: Math.floor(Math.random() * 360),
  y: Math.floor(Math.random() * 360)
}));

// Calculate responsive orb size
const calculateOrbSize = () => {
  // Get container or viewport dimensions
  const containerEl = container.value;
  if (!containerEl) return;
  
  const containerWidth = containerEl.clientWidth;
  const containerHeight = containerEl.clientHeight;
  
  // Use the smaller dimension to ensure it fits
  const minDimension = Math.min(containerWidth, containerHeight);
  
  // Calculate responsive size (larger range: 120px-250px)
  const size = Math.max(120, Math.min(250, minDimension / 6)); // Changed divisor from 10 to 6 for larger size
  
  // Update orbSize
  orbSize.value = size;
  
  // Regenerate keyframes with new size
  generateKeyframes();
};

// Generate styles for each particle - with updated colors for light mode
const getParticleStyle = (index) => {
  const i = index - 1; // 0-based index
  
  // Different color handling based on mode
  let backgroundColor;
  
  if (isDarkMode.value) {
    // Dark mode - use original blue hues
    const hue = ((40/300 * index) + 180); // Blue base (180)
    backgroundColor = `hsla(${hue}, 100%, 50%, 1)`;
  } else {
    // Light mode - use grayscale
    // Convert the index-based gradient to grayscale (15-75% lightness)
    const lightness = 15 + ((60 / 300) * index);
    backgroundColor = `hsla(0, 0%, ${lightness}%, 1)`;
  }
  
  return {
    'animation': `orbit${index} 14s infinite`,
    'animation-delay': `${i * 0.01}s`,
    'background-color': backgroundColor,
    'width': '4px', // Increased from 2px to 4px
    'height': '4px', // Increased from 2px to 4px
    'box-shadow': isDarkMode.value 
      ? `0 0 3px 1px ${backgroundColor}` // Subtle glow for dark mode
      : `0 0 2px 1px rgba(0,0,0,0.3)` // Subtle shadow for light mode
  };
};

// Check for dark mode
const checkDarkMode = () => {
  isDarkMode.value = document.querySelector('html').classList.contains('dark') || 
                     document.querySelector('.dark') !== null;
};

// Generate keyframes for the animation
const generateKeyframes = () => {
  // Remove existing keyframes if any
  const existingStyle = document.getElementById('orbit-keyframes');
  if (existingStyle) {
    existingStyle.remove();
  }
  
  // Dynamically add keyframes for each particle - exactly matching original SASS
  const style = document.createElement('style');
  style.id = 'orbit-keyframes';
  let keyframes = '';
  
  particles.forEach((particle, index) => {
    const i = index + 1;
    // Use responsive orbSize value
    const currentOrbSize = orbSize.value;
    
    keyframes += `
      @keyframes orbit${i} { 
        20% {
          opacity: 1; 
        }
        30% {
          transform: rotateZ(-${particle.z}deg) rotateY(${particle.y}deg) translateX(${currentOrbSize}px) rotateZ(${particle.z}deg);
        }
        80% {
          transform: rotateZ(-${particle.z}deg) rotateY(${particle.y}deg) translateX(${currentOrbSize}px) rotateZ(${particle.z}deg);
          opacity: 1;
        }
        100% {
          transform: rotateZ(-${particle.z}deg) rotateY(${particle.y}deg) translateX(${currentOrbSize * 3}px) rotateZ(${particle.z}deg);
        }
      }
    `;
  });
  
  style.textContent = keyframes;
  document.head.appendChild(style);
};

onMounted(() => {
  // Check dark mode on mount
  checkDarkMode();
  
  // Set up observer to detect dark mode changes
  observer = new MutationObserver(checkDarkMode);
  observer.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class'] 
  });
  
  // Also observe body for .dark class changes
  const body = document.querySelector('body');
  if (body) {
    observer.observe(body, { 
      attributes: true, 
      attributeFilter: ['class'],
      subtree: true
    });
  }
  
  // Initial calculation and keyframe generation
  calculateOrbSize();
  
  // Set up resize observer
  if (window.ResizeObserver && container.value) {
    resizeObserver = new ResizeObserver(calculateOrbSize);
    resizeObserver.observe(container.value);
  }
  
  // Fallback to window resize event
  window.addEventListener('resize', calculateOrbSize);
});

onUnmounted(() => {
  // Clean up observers
  if (observer) {
    observer.disconnect();
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  
  // Remove resize event listener
  window.removeEventListener('resize', calculateOrbSize);
  
  // Remove keyframes
  const style = document.getElementById('orbit-keyframes');
  if (style) {
    style.remove();
  }
});
</script>

<style scoped>
.particle-orb {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  overflow: hidden;
  z-index: 0;
}

.wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0; 
  height: 0; 
  transform-style: preserve-3d;
  perspective: 1000px;
  animation: rotate 14s infinite linear;
}

@keyframes rotate {
  100% {
    transform: rotateY(360deg) rotateX(360deg);
  }
}

.c {
  position: absolute;
  width: 4px; /* Increased from 2px to 4px */
  height: 4px; /* Increased from 2px to 4px */
  border-radius: 50%;
  opacity: 0;
}
</style> 