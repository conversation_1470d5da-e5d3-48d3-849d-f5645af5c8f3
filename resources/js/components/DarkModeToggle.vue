<template>
  <div class="flex flex-col items-center" :class="[isCollapsed ? 'scale-75 -mt-2' : '']">
    <label class="switch">
      <input type="checkbox" v-model="isDark">
      <span class="slider round">
        <i v-if="!isDark" class="design design-sun text-[#868a9b]"></i>
        <i v-else class="design design-moon text-gray-200"></i>
      </span>
    </label>
    <span v-if="!isCollapsed" class="text-xs text-gray-400 dark:text-gray-500 my-2 gant-modern-regular">{{ isDark ? 'Dark Mode' : 'Light Mode' }}</span>
  </div>
</template>

<script setup>
import { useDark, useToggle } from '@vueuse/core';
import { ref, watch, onMounted, computed } from 'vue';

// Get collapsed state from parent
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
});

const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: '',
  storageKey: 'darkMode',
});

// Watch for changes and ensure body class stays in sync
watch(isDark, (newValue) => {
  if (newValue) {
    document.body.classList.add('dark');
  } else {
    document.body.classList.remove('dark');
  }
}, { immediate: true });

// Initialize on mount to ensure consistency
onMounted(() => {
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  if (!localStorage.getItem('darkMode')) {
    isDark.value = prefersDark;
  }
});
</script>

<style scoped>
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  transition: all 0.3s ease;
}

.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #F3F4F6;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #0D1220;
}

input:checked + .slider:before {
  transform: translateX(26px);
  background-color: #313131;
}

.slider i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.1s;
}

.slider .design-sun {
  left: 8px;
  opacity: 1;
}

.slider .design-moon {
  right: 8px;
  opacity: 0;
}

input:checked + .slider .design-sun {
  opacity: 0;
}

input:checked + .slider .design-moon {
  opacity: 1;
}
</style>
