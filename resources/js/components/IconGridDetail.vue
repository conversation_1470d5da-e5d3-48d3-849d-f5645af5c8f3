<template>
  <div class="font-gant-modern relative">
    <!-- Improved Filter Panel -->
    <div class=" bg-white dark:bg-slate-950 border-b border-gray-200 dark:border-gray-800 mb-4">
      <div class="px-2 py-6">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-4 items-end">
          <!-- Search Input -->
          <div class="col-span-1 md:col-span-5 lg:col-span-5">
            <label for="icon-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Search</label>
            <div class="relative">
              <input 
                id="icon-search"
                v-model="search" 
                type="search"
                placeholder="Search icons in this font..." 
                aria-label="Search icons"
                :disabled="loading"
                class="flex h-12 w-full rounded-xl border border-gray-300 dark:border-gray-700 bg-white dark:bg-slate-950 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-gray-400 dark:focus:ring-offset-slate-950 pl-10 pr-4 gant-modern-regular"
              />
              <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                <i class="design design-search-fill text-gray-400"></i>
              </div>
            </div>
          </div>
          <!-- TAGS TOGGLE -->
          <div class="col-span-1">
            <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Show Tags</span>
            <div class="flex items-center h-12">
              <label class="relative inline-block w-[60px] h-[34px]">
                <input 
                  type="checkbox" 
                  v-model="showTagsFilter" 
                  class="opacity-0 w-0 h-0 peer"
                >
                <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                  <i v-if="!showTagsFilter" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                  <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                </span>
              </label>
            </div>
          </div>
          <!-- Combined Toggle Controls -->
          <div class="col-span-2">
            <div class="flex justify-between gap-4">
              <!-- Match All Toggle -->
              <div class="flex-1">
                <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium truncate">Show all</span>
                <div class="flex items-center h-12">
                  <label class="relative inline-block w-[60px] h-[34px]">
                    <input 
                      type="checkbox" 
                      v-model="matchAllWhenSearchEmpty" 
                      class="opacity-0 w-0 h-0 peer"
                    >
                    <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                      <i v-if="!matchAllWhenSearchEmpty" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                      <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                    </span>
                  </label>
                </div>
              </div>
              
              <!-- Exact Match Toggle -->
              <div class="flex-1">
                <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium truncate">Exact Match</span>
                <div class="flex items-center h-12">
                  <label class="relative inline-block w-[60px] h-[34px]">
                    <input 
                      type="checkbox" 
                      v-model="exactMatch" 
                      class="opacity-0 w-0 h-0 peer"
                    >
                    <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[26px] before:w-[26px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[26px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                      <i v-if="!exactMatch" class="design design-circle-cross text-[#868a9b] absolute top-1/2 -translate-y-1/2 left-[8px] text-sm transition-opacity duration-100"></i>
                      <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-sm transition-opacity duration-100"></i>
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Icon Size Slider -->
          <div class="col-span-1 md:col-span-2 lg:col-span-2">
            <label for="icon-size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Size</label>
            <div class="space-y-1.5">
              <input
                id="icon-size"
                v-model="iconSize"
                type="range"
                min="3"
                max="9"
                step="0.25"
                class="w-full range-slider"
              >
              <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 gant-modern-regular items-center">
                <div class="flex items-center gap-1">
                  <i class="design design-measure2 text-gray-400 text-[20px]"></i>            
                </div>
                <span class="gant-modern-bold text-gray-900 dark:text-gray-100 text-sm">{{ iconSize }}rem</span>
                <div class="flex items-center gap-1">
                  <i class="design design-measure3 text-gray-400 text-[20px]"></i>                    
                </div>
              </div>
            </div>
          </div>

          <!-- Results Counter -->
          <div v-if="filteredIcons.length > 0" class="col-span-1 md:col-span-2">
            <span class="block text-sm font-medium text-gray-700 dark:text-gray-300 h-5 mb-2 gant-modern-medium">Count</span>
            <div class="flex items-center min-h-[3rem] bg-white dark:bg-slate-950 px-3 py-2 text-sm rounded-xl border-solid border border-gray-300 dark:border-gray-700">
              <div class="flex items-center gap-1">
                <span class="gant-modern-bold text-gray-900 dark:text-gray-100">{{ displayedIcons.length }}</span>
                <span class="text-gray-500 dark:text-gray-400 mx-1">/</span>
                <span class="gant-modern-regular text-gray-900 dark:text-gray-100">{{ filteredIcons.length }}</span>
              </div>
            </div>
          </div>

          <!-- Show Tags Toggle - NEW ELEMENT -->

        </div>
      </div>
    </div>

    <!-- Tags Filter Bar - Now conditionally rendered -->
    <div v-if="showTagsFilter" class="px-2 pb-4 -mt-4 border-b border-gray-200 dark:border-gray-800 animate-fadeIn">
      <div class="flex flex-col">
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 gant-modern-medium flex items-center gap-2">
          <i class="design design-tag text-sm"></i>
          Filter by Tags
        </label>
        <div class="flex flex-wrap gap-2">
          <template v-if="allTags.length > 0">
            <button 
              v-for="tag in allTags" 
              :key="tag"
              @click="toggleTagFilter(tag)"
              :class="[
                'px-3 py-1.5 rounded-md text-sm transition-all duration-200 gant-modern-medium',
                selectedTags.includes(tag) 
                  ? 'bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900 m'
                  : 'border border-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              ]"
            >
              {{ tag }}
              <i v-if="selectedTags.includes(tag)" 
                 class="design design-circle-remove ml-1 text-xs inline-flex items-center justify-center opacity-50"
                 aria-hidden="true"></i>
            </button>
          </template>
          <p v-else class="text-gray-500 dark:text-gray-400 italic text-sm">No tags available for this font</p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="icons grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-5">
      <div v-for="n in pageSize" :key="n" class="text-center mb-5 animate-pulse">
        <div class="icon-wrapper mx-auto">
          <!-- Icon skeleton -->
          <div class="w-16 h-16 mx-auto mb-2 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
            <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        </div>
        <!-- Content text skeleton -->
        <div class="h-4 w-20 mx-auto mt-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <!-- Class name skeleton -->
        <div class="h-8 w-full mt-2 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>

    <!-- Icons Grid with Dynamic Sizing -->
    <div v-else 
      class="icons flex flex-wrap gap-[var(--grid-gap)] w-full justify-start transition-all duration-300 ease-in-out"
      :style="{
        '--icon-base-size': `${iconSize}rem`,
        '--grid-gap': `${Math.max(0.5, iconSize * 0.2)}rem`
      }"
    >
      <div 
        v-for="icon in displayedIcons" 
        :key="icon.id" 
        class="flex flex-col flex-grow-0 flex-shrink-0 text-center rounded-md cursor-pointer group hover:bg-gray-100 dark:hover:bg-gray-800/50 transition-all duration-200 relative"
        :style="{ 
          width: `${iconSize * 2}rem`,
          padding: `${Math.max(0.5, iconSize * 0.1)}rem`
        }"
        @click="openIconModal(icon)"
      >
        <!-- Tag indicator - visible only when tags exist -->
        <template v-if="icon.tags && icon.tags.length > 0">
          <!-- Normal state: just the tag icon -->
          <div class="absolute top-1 right-1 text-gray-400 dark:text-gray-500 group-hover:opacity-0 transition-opacity duration-200">
            <i class="design design-tag-outline text-xs"></i>
          </div>
          
          <!-- Hover state: circle with count -->
          <div 
            class="absolute top-1 right-1 bg-gray-700 dark:bg-gray-600 text-white text-[10px] w-5 h-5 rounded-full flex items-center justify-center font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            :title="`${icon.tags.length} tags`"
          >
            {{ icon.tags.length }}
          </div>
        </template>
        
        <div class="flex-1 flex flex-col">
          <div class="flex-1 flex items-center justify-center my-4" 
               :style="{ height: `${iconSize}rem` }">
            <i :class="[
              initialFont,
              icon.cssClass,
              'text-gray-900 dark:text-white',
              'transition-transform duration-200 group-hover:scale-110'
            ]" 
            :style="{ fontSize: `${iconSize}rem` }"
            aria-hidden="true"></i>
          </div>
          <small class="text-gray-400 dark:text-gray-600 block overflow-hidden text-ellipsis text-[10px] sm:text-xs">
            <p class="text-xs">content: {{ icon.cssContent }}</p>
          </small>
          <div class="relative mt-2">
            <!-- Normal class name display -->
            <p 
              :class="[
                'w-full text-[10px] sm:text-xs px-2 sm:px-3 py-1.5 sm:py-2 text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-700 rounded-md transition-all duration-200 gant-modern-medium truncate whitespace-nowrap',
                copied === icon.cssClass ? 'border-green-500 dark:border-green-500 text-green-600 dark:text-green-400' : 'group-hover:border-gray-300 dark:group-hover:border-gray-600',
                'group-hover:opacity-0'
              ]"
              :title="icon.cssClass"
            >
              {{ icon.cssClass }}
            </p>
            
            <!-- Copy button overlay - add @click.stop to prevent modal opening -->
            <button 
              @click.stop="copyToClipboard(icon.cssClass)"
              :class="[
                'absolute inset-0 w-full text-[10px] sm:text-xs px-2 sm:px-3 py-1.5 sm:py-2 rounded-md transition-all duration-200 opacity-0 group-hover:opacity-100 flex items-center justify-center gap-1 sm:gap-2',
                copied === icon.cssClass 
                  ? 'bg-green-900 text-white dark:bg-green-900/90 dark:text-green-100 border border-green-500'
                  : 'bg-gray-900 text-gray-100 dark:bg-gray-800 dark:text-gray-100 border border-gray-600 dark:border-gray-700 hover:bg-gray-800 dark:hover:bg-gray-700'
              ]"
            >
              <i :class="[
                copied === icon.cssClass ? 'design design-circle-checked' : 'design design-copyclipboard',
                'text-xs sm:text-sm'
              ]"></i>
              {{ copied === icon.cssClass ? 'Copied!' : 'Copy class' }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="hasMoreIcons" id="load-more-button" class="w-full col-span-full py-6 sm:py-8 text-center">
        <button 
          @click="loadMore"
          :disabled="isLoadingMore"
          class="inline-flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-medium text-white bg-gray-950 rounded-md hover:bg-gray-700 dark:bg-gray-100 dark:text-gray-800 dark:hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          <template v-if="isLoadingMore">
            <svg class="animate-spin h-4 w-4 sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading more icons...</span>
          </template>
          <template v-else>
            <i class="claims claims-rotate text-lg sm:text-xl"></i>
            <span>Load More Icons</span>
          </template>
        </button>
      </div>
    </div>
    
    <!-- Icon Detail Modal -->
    <IconDetailModal
      :show="showIconModal"
      :icon="selectedIcon"
      :font-class="initialFont"
      :font-id="fontId"
      :initial-tags="selectedIcon?.tags || []"
      @close="closeIconModal"
      @tags-updated="handleTagsUpdated"
      @request-reload="reloadIconData"
    />
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import Fuse from 'fuse.js';
import IconDetailModal from './IconDetailModal.vue';

function debounce(fn, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), delay);
  };
}

export default {
  name: 'IconGridDetail',
  components: {
    IconDetailModal
  },
  props: {
    initialFont: {
      type: String,
      required: true
    },
    initialIcons: {
      type: Array,
      required: true
    },
    fontId: {
      type: [Number, String],
      required: false,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      isSearching: false,
      icons: [],
      displayedIcons: [],
      search: '',
      exactMatch: false,
      matchAllWhenSearchEmpty: true,
      debouncedSearch: '',
      fuse: null,
      copied: null,
      pageSize: 48,
      currentPage: 1,
      isLoadingMore: false,
      observer: null,
      searchDebounceTimeout: null,
      iconSize: 4.5,
      showIconModal: false,
      selectedIcon: null,
      currentTags: [],
      originalTags: [],
      newTag: '',
      showToast: false,
      toastMessage: '',
      toastType: 'success',
      selectedTags: [],
      showTagsFilter: true,
    }
  },
  computed: {
    allTags() {
      const tagsSet = new Set();
      this.icons.forEach(icon => {
        if (icon.tags && Array.isArray(icon.tags)) {
          icon.tags.forEach(tag => tagsSet.add(tag));
        }
      });
      return Array.from(tagsSet).sort();
    },
    filteredIcons() {
      let results = [];
      
      if (!this.debouncedSearch && !this.matchAllWhenSearchEmpty && this.selectedTags.length === 0) {
        return [];
      }

      if (this.selectedTags.length > 0) {
        results = this.icons.filter(icon => 
          icon.tags && this.selectedTags.every(tag => icon.tags.includes(tag))
        );
        
        if (this.debouncedSearch) {
          if (this.exactMatch) {
            results = results.filter(icon => 
              icon.cssClass.toLowerCase().includes(this.debouncedSearch.toLowerCase()) ||
              (icon.tags && icon.tags.some(tag => tag.toLowerCase().includes(this.debouncedSearch.toLowerCase())))
            );
          } else if (this.fuse) {
            const searchResults = this.fuse.search(this.debouncedSearch).map(result => result.item);
            results = results.filter(icon => searchResults.some(item => item.id === icon.id));
          }
        }
        
        return results;
      }

      if (!this.debouncedSearch && this.matchAllWhenSearchEmpty) {
        return this.icons;
      }

      if (this.exactMatch) {
        return this.icons.filter(icon => 
          icon.cssClass.toLowerCase().includes(this.debouncedSearch.toLowerCase()) ||
          (icon.tags && icon.tags.some(tag => tag.toLowerCase().includes(this.debouncedSearch.toLowerCase())))
        );
      }

      if (this.fuse) {
        return this.fuse.search(this.debouncedSearch).map(result => result.item);
      }

      return this.icons;
    },
    hasMoreIcons() {
      return this.displayedIcons.length < this.filteredIcons.length;
    },
    isIconEdited() {
      if (!this.selectedIcon || !this.originalTags) return false;
      
      if (this.currentTags.length !== this.originalTags.length) return true;
      
      return this.currentTags.some((tag, index) => tag !== this.originalTags[index]);
    }
  },
  watch: {
    search(value) {
      this.isSearching = true;
      this.handleSearchDebounced(value);
    },
    debouncedSearch() {
      this.displayedIcons = this.filteredIcons.slice(0, this.pageSize);
      this.isSearching = false;
    },
    icons() {
      this.initializeFuse();
      this.displayedIcons = this.filteredIcons.slice(0, this.pageSize);
    },
    selectedTags() {
      this.currentPage = 1;
      this.displayedIcons = this.filteredIcons.slice(0, this.pageSize);
    },
    showTagsFilter(newValue) {
      localStorage.setItem('showTagsFilter', JSON.stringify(newValue));
    }
  },
  created() {
    this.loading = true;
    this.icons = this.initialIcons;
    this.displayedIcons = this.icons.slice(0, this.pageSize);
    this.handleSearchDebounced = debounce((value) => {
      this.debouncedSearch = value;
    }, 300);
    
    // Get saved preference or default to true
    const savedTagsFilterPreference = localStorage.getItem('showTagsFilter');
    this.showTagsFilter = savedTagsFilterPreference !== null 
      ? JSON.parse(savedTagsFilterPreference) 
      : true;
    
    this.initializeFuse();
    this.loading = false;
  },
  mounted() {
    this.initInfiniteScroll();
  },
  unmounted() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    initInfiniteScroll() {
      if (this.observer) {
        this.observer.disconnect();
      }

      this.observer = new IntersectionObserver(
        (entries) => {
          const target = entries[0];
          if (target.isIntersecting && this.hasMoreIcons && !this.isLoadingMore) {
            this.loadMore();
          }
        },
        {
          rootMargin: '200px',
          threshold: 0.1
        }
      );

      this.$nextTick(() => {
        const loadMoreButton = document.querySelector('#load-more-button');
        if (loadMoreButton) {
          this.observer.observe(loadMoreButton);
        }
      });
    },
    async loadMore() {
      if (this.isLoadingMore) return;
      
      this.isLoadingMore = true;
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const newIcons = this.filteredIcons.slice(
        (this.currentPage) * this.pageSize,
        (this.currentPage + 1) * this.pageSize
      );
      
      if (newIcons.length > 0) {
        this.currentPage++;
        this.displayedIcons = [...this.displayedIcons, ...newIcons];
      }
      
      this.isLoadingMore = false;
    },
    async copyToClipboard(iconClass) {
      try {
        await navigator.clipboard.writeText(iconClass);
        this.copied = iconClass;
        setTimeout(() => {
          if (this.copied === iconClass) {
            this.copied = null;
          }
        }, 2000);
      } catch (err) {
        console.error('Failed to copy:', err);
      }
    },
    initializeFuse() {
      this.fuse = new Fuse(this.icons, {
        keys: [
          'cssClass', 
          'cssContent',
          'tags'
        ],
        threshold: 0.3,
        ignoreLocation: true,
        useExtendedSearch: true
      });
    },
    openIconModal(icon) {
      this.selectedIcon = { ...icon };
      this.showIconModal = true;
    },
    closeIconModal() {
      this.showIconModal = false;
      this.selectedIcon = null;
    },
    handleTagsUpdated(data) {
      const iconIndex = this.icons.findIndex(i => 
        (i.id === data.icon.id) || 
        (i.cssClass === data.icon.cssClass)
      );
      
      if (iconIndex !== -1) {
        this.icons[iconIndex].tags = [...data.tags];
      }
      
      if (this.selectedTags.length > 0) {
        this.displayedIcons = this.filteredIcons.slice(0, this.pageSize * this.currentPage);
      }
    },
    toggleTagFilter(tag) {
      if (this.selectedTags.includes(tag)) {
        this.selectedTags = [];
      } else {
        this.selectedTags = [tag];
      }
      
      this.currentPage = 1;
      this.displayedIcons = this.filteredIcons.slice(0, this.pageSize);
    },
    reloadIconData() {
      this.loading = true;
      // Emit an event to the parent component to reload the data
      this.$emit('reload-font', this.fontId);
      // After a short delay to show loading state
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
  }
}
</script>

<style>
/* Dark mode autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px rgb(55 65 81) inset !important;
  -webkit-text-fill-color: white !important;
  caret-color: white !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* Override plugins.css border-radius reset */
input[type="search"] {
  border-radius: 0.75rem !important; /* matches rounded-xl */
}

/* Minimal Range Slider */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
}

/* Track */
.range-slider::-webkit-slider-runnable-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

.range-slider::-moz-range-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

/* Thumb */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  margin-top: -7px;
  transition: all 0.15s ease;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.15s ease;
}

/* Hover state */
.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.range-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Focus state */
.range-slider:focus {
  outline: none;
}

/* Dark mode */
.dark .range-slider::-webkit-slider-runnable-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-moz-range-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-webkit-slider-thumb {
  background: #ffffff;
  border-color: #000000;
}

.dark .range-slider::-moz-range-thumb {
  background: #ffffff;
  border-color: #000000;
}

.icons {
  contain: content;
  will-change: contents;
  scroll-margin-top: 2rem;
}

.icons > div {
  contain: layout style paint;
  will-change: transform;
}

@media (max-width: 640px) {
  .icons {
    scroll-margin-top: 1rem;
  }
}

/* Animation for tags filter */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>
