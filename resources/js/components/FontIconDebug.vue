<template>
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-bold mb-4 text-gray-900 dark:text-white">Font Icon Debug</h2>
    
    <div v-if="loading" class="text-gray-600 dark:text-gray-300">Loading data...</div>
    <div v-else-if="error" class="text-red-500">{{ error }}</div>
    <div v-else>
      <div class="mb-4">
        <p class="text-gray-600 dark:text-gray-300 mb-2">Selected Font: <span class="font-semibold text-gray-900 dark:text-white">{{ selectedFont }}</span></p>
        <p class="text-gray-600 dark:text-gray-300 mb-2">Icons Loaded: <span class="font-semibold text-gray-900 dark:text-white">{{ icons.length }}</span></p>
        <p class="text-gray-600 dark:text-gray-300 mb-2">Icons with Tags: <span class="font-semibold text-gray-900 dark:text-white">{{ iconsWithTags.length }}</span></p>
        <p class="text-gray-600 dark:text-gray-300 mb-2">Total Tags: <span class="font-semibold text-gray-900 dark:text-white">{{ totalTags }}</span></p>
      </div>
      
      <details class="mb-4">
        <summary class="cursor-pointer text-blue-600 dark:text-blue-400 font-semibold">Show Font Info</summary>
        <pre class="mt-2 bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto max-h-60 text-xs">{{ JSON.stringify(fontInfo, null, 2) }}</pre>
      </details>
      
      <details>
        <summary class="cursor-pointer text-blue-600 dark:text-blue-400 font-semibold">Show First 5 Icons</summary>
        <div class="mt-2 space-y-2">
          <div 
            v-for="(icon, index) in firstFiveIcons" 
            :key="index"
            class="bg-gray-100 dark:bg-gray-900 p-4 rounded"
          >
            <div class="flex items-center gap-4 mb-2">
              <div class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <i :class="[selectedFont, icon.css_class, 'text-4xl text-gray-900 dark:text-white']"></i>
              </div>
              <div>
                <p class="font-mono text-sm text-gray-700 dark:text-gray-300">{{ icon.css_class }}</p>
                <p class="font-mono text-xs text-gray-500 dark:text-gray-400">{{ icon.css_content }}</p>
              </div>
            </div>
            <div>
              <p class="text-gray-700 dark:text-gray-300 text-sm">Tags: 
                <span v-if="icon.tags && icon.tags.length" class="font-semibold">
                  {{ icon.tags.join(', ') }}
                </span>
                <span v-else class="text-red-500">No tags found</span>
              </p>
            </div>
          </div>
        </div>
      </details>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FontIconDebug',
  props: {
    fontName: {
      type: String,
      default: 'design'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      icons: [],
      fontInfo: null
    };
  },
  computed: {
    selectedFont() {
      return this.fontName;
    },
    iconsWithTags() {
      return this.icons.filter(icon => icon.tags && icon.tags.length > 0);
    },
    totalTags() {
      return this.icons.reduce((total, icon) => {
        return total + (icon.tags && Array.isArray(icon.tags) ? icon.tags.length : 0);
      }, 0);
    },
    firstFiveIcons() {
      return this.icons.slice(0, 5);
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      this.error = null;
      
      try {
        // First try the API endpoint
        const response = await fetch(`/font-icons/${this.fontName}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch icons: ${response.status} ${response.statusText}`);
        }
        
        this.icons = await response.json();
        
        // Try to get font info
        const fontResponse = await fetch('/fonts');
        if (fontResponse.ok) {
          const fonts = await fontResponse.json();
          this.fontInfo = fonts.find(f => f.name === this.fontName || f.css_class === this.fontName);
        }
        
        console.log('Loaded icons:', this.icons.length);
        console.log('Icons with tags:', this.iconsWithTags.length);
        
      } catch (error) {
        console.error('Error fetching font data:', error);
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  }
};
</script> 