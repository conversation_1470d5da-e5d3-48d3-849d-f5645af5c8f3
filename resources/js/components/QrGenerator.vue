<template>
  <div class="qr-generator font-gant-modern">
    <!-- Two-column layout -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8">
      <!-- Left column: QR preview -->
      <div class="order-last lg:order-first">
        <div class="sticky top-4 h-full flex flex-col">
          <!-- QR Code Preview Section -->
          <div class="bg-white dark:bg-slate-950 rounded-2xl p-3 md:p-4 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden flex-grow flex flex-col">
            <div class="flex justify-between items-center mb-2 md:mb-3">
              <h3 class="text-xl md:text-3xl gant-modern-bold text-gray-900 dark:text-gray-100">QR Code Preview</h3>
              <span v-if="generated" class="text-xs text-gray-500 dark:text-gray-400">
                Scan to test
              </span>
            </div>
            
            <!-- QR Code Display -->
            <div class="flex-grow flex flex-col">
              <div 
                class="bg-white rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm mb-3 md:mb-4 flex-grow flex items-center justify-center"
                :style="{ backgroundColor: bgColor }"
              >
                <div v-if="generated" v-html="qrCodeSvg" class="qr-preview"></div>
                <div v-else class="flex items-center justify-center text-sm md:text-base text-gray-400 p-4 text-center">
                  <span>Enter content and click Generate</span>
                </div>
              </div>
              
              <!-- QR Code Info -->
              <div v-if="generated" class="w-full space-y-2 mb-3">
                <div class="text-xs md:text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 md:p-3 rounded-xl overflow-hidden">
                  <strong class="gant-modern-medium">Content:</strong> <span class="break-all">{{ content }}</span>
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                  <span class="block"><strong class="gant-modern-medium">Size:</strong> {{ size }}x{{ size }}px</span>
                  <span class="block"><strong class="gant-modern-medium">Error Correction:</strong> {{ errorCorrectionLabels[errorCorrection] }}</span>
                  <span v-if="logoPreview" class="block"><strong class="gant-modern-medium">Logo:</strong> Yes ({{ logoSize }}% size)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Right column: Options -->
      <div>
        <div class="bg-white dark:bg-slate-950 rounded-2xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
          <!-- Content Input Section -->
          <div class="mb-6 md:mb-8">
            <label for="qr-content" class="block text-xl md:text-3xl gant-modern-bold text-gray-700 dark:text-gray-300 mb-2 md:mb-3">
              URL or Text Content
            </label>
            <div class="flex">
              <input
                id="qr-content"
                v-model="content"
                type="text"
                placeholder="https://example.com"
                class="flex-grow px-3 md:px-5 py-2 md:py-3 border border-gray-300 dark:border-gray-700 rounded-l-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm md:text-base gant-modern-regular"
              />
              <button
                @click="generateQrCode"
                class="px-4 md:px-6 py-2 md:py-3 bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:hover:bg-gray-200 text-white dark:text-gray-900 rounded-r-xl transition-colors text-sm md:text-base gant-modern-medium"
              >
                <i class="design design-qr-code-outline mr-2"></i>
                Generate
              </button>
            </div>
          </div>

          <!-- Settings Section -->
          <div class="space-y-6 md:space-y-8">
            <!-- Basic Settings Section -->
            <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5">
              <h3 class="text-xl md:text-3xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-3 md:mb-5">Basic Settings</h3>
              
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
                <!-- Size -->
                <div>
                  <label for="qr-size" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Size (px)
                  </label>
                  <div class="flex items-center">
                    <input
                      id="qr-size"
                      v-model.number="size"
                      type="range"
                      min="100"
                      max="1200"
                      step="50"
                      class="range-slider w-full mr-2"
                      @input="autoGenerate"
                    />
                    <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400 w-16 text-right">{{ size }}</span>
                  </div>
                </div>

                <!-- Error Correction Level -->
                <div>
                  <label for="error-correction" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Error Correction
                  </label>
                  <select
                    id="error-correction"
                    v-model="errorCorrection"
                    class="w-full px-2 md:px-4 py-1 md:py-2 border border-gray-300 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-xs md:text-sm gant-modern-regular"
                    @change="autoGenerate"
                  >
                    <option value="L">Low (7%)</option>
                    <option value="M">Medium (15%)</option>
                    <option value="Q">Quartile (25%)</option>
                    <option value="H">High (30%)</option>
                  </select>
                </div>

                <!-- Foreground Color -->
                <div>
                  <label for="qr-color" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Foreground Color
                  </label>
                  <div class="flex">
                    <input
                      id="qr-color"
                      v-model="color"
                      type="text"
                      class="flex-grow px-2 md:px-4 py-1 md:py-2 border border-gray-300 dark:border-gray-700 rounded-l-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-xs md:text-sm gant-modern-regular"
                      @input="autoGenerate"
                    />
                    <input
                      v-model="color"
                      type="color"
                      class="h-8 md:h-10 w-8 md:w-10 border border-gray-300 dark:border-gray-700 rounded-r-xl"
                      @input="autoGenerate"
                    />
                  </div>
                </div>

                <!-- Background Color -->
                <div>
                  <label for="qr-bg-color" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Background Color
                  </label>
                  <div class="flex">
                    <input
                      id="qr-bg-color"
                      v-model="bgColor"
                      type="text"
                      class="flex-grow px-2 md:px-4 py-1 md:py-2 border border-gray-300 dark:border-gray-700 rounded-l-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-xs md:text-sm gant-modern-regular"
                      @input="autoGenerate"
                    />
                    <input
                      v-model="bgColor"
                      type="color"
                      class="h-8 md:h-10 w-8 md:w-10 border border-gray-300 dark:border-gray-700 rounded-r-xl"
                      @input="autoGenerate"
                    />
                  </div>
                </div>
              </div>

              <!-- Color Presets -->
              <div class="mt-3 md:mt-4 border-t dark:border-gray-700 pt-3 md:pt-4">
                <h4 class="text-lg md:text-3xl gant-modern-bold text-gray-700 dark:text-gray-300 mb-2">Color Presets</h4>
                <div class="flex flex-wrap gap-2">
                  <button
                    v-for="preset in colorPresets"
                    :key="preset.name"
                    @click="applyColorPreset(preset)"
                    :class="[
                      'relative flex items-center px-2 md:px-3 py-1 md:py-2 rounded-lg border transition duration-200 text-xs md:text-sm shadow-sm',
                      color === preset.fgColor && bgColor === preset.bgColor
                        ? 'border-gray-400 dark:border-gray-600 bg-white dark:bg-gray-800' 
                        : 'border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 bg-gray-50 dark:bg-gray-900/30'
                    ]"
                  >
                    <div class="flex items-center">
                      <span class="inline-block w-3 md:w-4 h-3 md:h-4 mr-1 md:mr-2 rounded-full" :style="{ backgroundColor: preset.fgColor }"></span>
                      {{ preset.name }}
                    </div>
                    <div v-if="color === preset.fgColor && bgColor === preset.bgColor" 
                      class="absolute top-1 right-1 w-2 md:w-3 h-2 md:h-3 flex items-center justify-center text-gray-700 dark:text-gray-300">
                      <i class="design design-check text-xs"></i>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <!-- Shape Styles Section -->
            <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5">
              <h3 class="text-xl md:text-3xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-3 md:mb-5">Shape Styles</h3>
              
              <!-- QR Module Style -->
              <div class="mb-4 md:mb-6">
                <label class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                  QR Module Style
                </label>
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-2 md:gap-3">
                  <div
                    v-for="style in qrModuleStyles"
                    :key="style.id"
                    @click="qrStyle = style.id; autoGenerate()"
                    :class="[
                      'relative border rounded-lg p-2 md:p-3 cursor-pointer transition duration-200 flex flex-col items-center',
                      qrStyle === style.id 
                        ? 'border-gray-400 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-sm' 
                        : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30 hover:bg-white dark:hover:bg-gray-800'
                    ]"
                  >
                    <span class="block h-8 md:h-10 w-8 md:w-10 mb-1 md:mb-2" v-html="style.icon"></span>
                    <span class="text-[10px] md:text-xs text-center gant-modern-medium">{{ style.name }}</span>
                    <div v-if="qrStyle === style.id" 
                      class="absolute top-1 right-1 w-3 md:w-4 h-3 md:h-4 flex items-center justify-center text-gray-700 dark:text-gray-300">
                      <i class="design design-check text-xs"></i>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Position Marker Style -->
              <div class="mb-4 md:mb-6">
                <label class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                  Position Marker Style
                </label>
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-2 md:gap-3">
                  <div
                    v-for="style in positionMarkerStyles"
                    :key="style.id"
                    @click="positionMarkerStyle = style.id; autoGenerate()"
                    :class="[
                      'relative border rounded-lg p-2 md:p-3 cursor-pointer transition duration-200 flex flex-col items-center',
                      positionMarkerStyle === style.id 
                        ? 'border-gray-400 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-sm' 
                        : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/30 hover:bg-white dark:hover:bg-gray-800'
                    ]"
                  >
                    <span class="block h-8 md:h-10 w-8 md:w-10 mb-1 md:mb-2" v-html="style.icon"></span>
                    <span class="text-[10px] md:text-xs text-center gant-modern-medium">{{ style.name }}</span>
                    <div v-if="positionMarkerStyle === style.id" 
                      class="absolute top-1 right-1 w-3 md:w-4 h-3 md:h-4 flex items-center justify-center text-gray-700 dark:text-gray-300">
                      <i class="design design-check text-xs"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Style Settings -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 mt-4 md:mt-6 border-t dark:border-gray-700 pt-3 md:pt-4">
                <div>
                  <label for="qr-margin" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Margin <span class="text-xs text-gray-500 dark:text-gray-400">({{ margin }} modules)</span>
                  </label>
                  <input
                    id="qr-margin"
                    v-model.number="margin"
                    type="range"
                    min="0"
                    max="10"
                    step="1"
                    class="range-slider w-full"
                    @input="autoGenerate"
                  />
                </div>
                
                <div>
                  <label for="qr-corner-radius" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">
                    Corner Radius <span class="text-xs text-gray-500 dark:text-gray-400">({{ cornerRadius }}%)</span>
                  </label>
                  <input
                    id="qr-corner-radius"
                    v-model.number="cornerRadius"
                    type="range"
                    min="0"
                    max="50"
                    step="5"
                    class="range-slider w-full"
                    @input="autoGenerate"
                  />
                </div>
              </div>
            </div>
            
            <!-- Export Options Section -->
            <div v-if="generated" class="mt-6 md:mt-8 pt-4 md:pt-6 border-t dark:border-gray-700">
              <div class="flex flex-wrap gap-3 md:gap-5">
                <button
                  @click="downloadQR('svg')"
                  class="flex-1 px-4 md:px-6 py-3 md:py-4 bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:hover:bg-gray-200 text-white dark:text-gray-900 rounded-xl transition-colors flex flex-col items-center justify-center text-sm md:text-base gant-modern-medium shadow-md"
                >
                  <i class="design design-download-svg mb-1 md:mb-2 text-2xl md:text-3xl opacity-50"></i> Download SVG
                </button>
                <button
                  @click="downloadQR('png')"
                  class="flex-1 px-4 md:px-6 py-3 md:py-4 bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:hover:bg-gray-200 text-white dark:text-gray-900 rounded-xl transition-colors flex flex-col items-center justify-center text-sm md:text-base gant-modern-medium shadow-md"
                >
                  <i class="design design-download-png mb-1 md:mb-2 text-2xl md:text-3xl opacity-50"></i> Download PNG
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, toRaw } from 'vue';

// Basic settings
const content = ref('https://example.com');
const size = ref(800);
const color = ref('#000000');
const bgColor = ref('#FFFFFF');
const generated = ref(false);
const qrCodeSvg = ref('');

// Advanced settings
const errorCorrection = ref('M');
const errorCorrectionLabels = {
  'L': 'Low (7%)',
  'M': 'Medium (15%)',
  'Q': 'Quartile (25%)',
  'H': 'High (30%)'
};
const margin = ref(4);
const cornerRadius = ref(0);
const qrStyle = ref('square');
const positionMarkerStyle = ref('square');

// Logo settings
const logoFile = ref(null);
const logoPreview = ref(null);
const logoSize = ref(15);

// Auto-generate QR code when options change
const autoUpdateDelay = ref(null);
const autoGenerate = () => {
  if (autoUpdateDelay.value) clearTimeout(autoUpdateDelay.value);
  
  autoUpdateDelay.value = setTimeout(() => {
    if (content.value) {
      generateQrCode();
    }
  }, 500);
};

// Color presets
const colorPresets = [
  { name: 'Classic', fgColor: '#000000', bgColor: '#FFFFFF' },
  { name: 'Inverse', fgColor: '#FFFFFF', bgColor: '#000000' },
  { name: 'Blue', fgColor: '#0066CC', bgColor: '#FFFFFF' },
  { name: 'Green', fgColor: '#00AA55', bgColor: '#FFFFFF' },
  { name: 'Red', fgColor: '#CC0000', bgColor: '#FFFFFF' },
  { name: 'Purple', fgColor: '#9900CC', bgColor: '#FFFFFF' },
  { name: 'Orange', fgColor: '#FF6600', bgColor: '#FFFFFF' },
];

const applyColorPreset = (preset) => {
  color.value = preset.fgColor;
  bgColor.value = preset.bgColor;
  autoGenerate();
};

// QR Module Style options
const qrModuleStyles = [
  { 
    id: 'square', 
    name: 'Square', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <rect x="2" y="2" width="4" height="4" />
      <rect x="10" y="2" width="4" height="4" />
      <rect x="18" y="2" width="4" height="4" />
      <rect x="2" y="10" width="4" height="4" />
      <rect x="10" y="10" width="4" height="4" />
      <rect x="2" y="18" width="4" height="4" />
      <rect x="10" y="18" width="4" height="4" />
      <rect x="18" y="18" width="4" height="4" />
    </svg>`
  },
  { 
    id: 'rounded', 
    name: 'Rounded', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <rect x="2" y="2" width="4" height="4" rx="1" />
      <rect x="10" y="2" width="4" height="4" rx="1" />
      <rect x="18" y="2" width="4" height="4" rx="1" />
      <rect x="2" y="10" width="4" height="4" rx="1" />
      <rect x="10" y="10" width="4" height="4" rx="1" />
      <rect x="2" y="18" width="4" height="4" rx="1" />
      <rect x="10" y="18" width="4" height="4" rx="1" />
      <rect x="18" y="18" width="4" height="4" rx="1" />
    </svg>`
  },
  { 
    id: 'dots', 
    name: 'Dots', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <circle cx="4" cy="4" r="2" />
      <circle cx="12" cy="4" r="2" />
      <circle cx="20" cy="4" r="2" />
      <circle cx="4" cy="12" r="2" />
      <circle cx="12" cy="12" r="2" />
      <circle cx="4" cy="20" r="2" />
      <circle cx="12" cy="20" r="2" />
      <circle cx="20" cy="20" r="2" />
    </svg>`
  },
  { 
    id: 'diamond', 
    name: 'Diamond', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <path d="M4,4 L6,2 L8,4 L6,6 Z" />
      <path d="M12,4 L14,2 L16,4 L14,6 Z" />
      <path d="M20,4 L22,2 L24,4 L22,6 Z" />
      <path d="M4,12 L6,10 L8,12 L6,14 Z" />
      <path d="M12,12 L14,10 L16,12 L14,14 Z" />
      <path d="M4,20 L6,18 L8,20 L6,22 Z" />
      <path d="M12,20 L14,18 L16,20 L14,22 Z" />
      <path d="M20,20 L22,18 L24,20 L22,22 Z" />
    </svg>`
  }
];

// Position Marker Style options
const positionMarkerStyles = [
  { 
    id: 'square', 
    name: 'Square', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <rect x="2" y="2" width="8" height="8" />
      <rect x="4" y="4" width="4" height="4" fill="white" />
      <rect x="5" y="5" width="2" height="2" />
    </svg>`
  },
  { 
    id: 'circle', 
    name: 'Circle', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <circle cx="6" cy="6" r="4" />
      <circle cx="6" cy="6" r="2" fill="white" />
      <circle cx="6" cy="6" r="1" />
    </svg>`
  },
  {
    id: 'rounded', 
    name: 'Rounded', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <rect x="2" y="2" width="8" height="8" rx="2" />
      <rect x="4" y="4" width="4" height="4" rx="1" fill="white" />
      <rect x="5" y="5" width="2" height="2" rx="0.5" />
    </svg>`
  },
  { 
    id: 'extra', 
    name: 'Framed', 
    icon: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="text-gray-700 dark:text-gray-300">
      <rect x="1" y="1" width="10" height="10" rx="1" />
      <rect x="3" y="3" width="6" height="6" rx="1" fill="white" />
      <rect x="4.5" y="4.5" width="3" height="3" rx="0.5" />
    </svg>`
  }
];

// Logo handling
const handleLogoUpload = (e) => {
  const file = e.target.files[0];
  if (!file) return;
  
  logoFile.value = file;
  
  const reader = new FileReader();
  reader.onload = (event) => {
    logoPreview.value = event.target.result;
    autoGenerate();
  };
  reader.readAsDataURL(file);
};

const removeLogo = () => {
  logoFile.value = null;
  logoPreview.value = null;
  autoGenerate();
};

// Generate QR code
const generateQrCode = async () => {
  if (!content.value) return;
  
  try {
    // Check if QRCode library is available
    if (!window.QRCode) {
      alert('QR Code library not loaded yet. Please try again in a moment.');
      return;
    }
    
    const options = {
      width: size.value,
      margin: margin.value,
      color: {
        dark: color.value,
        light: bgColor.value
      },
      errorCorrectionLevel: errorCorrection.value
    };
    
    // Generate QR code as SVG using window.QRCode
    const svgString = await new Promise((resolve, reject) => {
      window.QRCode.toString(content.value, {
        ...options,
        type: 'svg',
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    // Parse the SVG string
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, 'image/svg+xml');
    const svg = doc.documentElement;
    
    // Ensure we have an SVG
    if (!svg || svg.tagName !== 'svg') {
      throw new Error('Generated QR code does not contain an SVG element');
    }

    // Create a clean SVG for manipulation
    const cleanSvg = svg.cloneNode(true);
    
    // Set the proper viewBox if needed
    if (!cleanSvg.hasAttribute('viewBox')) {
      const width = parseInt(cleanSvg.getAttribute('width') || '100');
      const height = parseInt(cleanSvg.getAttribute('height') || '100');
      cleanSvg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    }
    
    // Apply QR module styling and position marker styling
    applyStyles(cleanSvg);
    
    // Add logo if present
    if (logoPreview.value) {
      addLogoToSvg(cleanSvg);
    }
    
    // Convert back to string
    qrCodeSvg.value = new XMLSerializer().serializeToString(cleanSvg);
    generated.value = true;
  } catch (error) {
    console.error('Error generating QR code:', error);
    alert('Failed to generate QR code. Please try again.');
  }
};

// Apply QR module and position marker styles
const applyStyles = (svg) => {
  if (qrStyle.value === 'square' && positionMarkerStyle.value === 'square') {
    return; // No styling needed for default square style
  }
  
  // Get all rect elements in the SVG
  const allRects = Array.from(svg.querySelectorAll('rect'));
  if (allRects.length === 0) return;
  
  // Determine module size and position markers
  const cellWidths = allRects.map(rect => parseFloat(rect.getAttribute('width') || '0'));
  const moduleSize = Math.min(...cellWidths.filter(w => w > 0));
  
  // Find all sizes of rectangles in the SVG
  const rectSizes = {};
  allRects.forEach(rect => {
    const width = parseFloat(rect.getAttribute('width') || '0');
    if (width > 0) {
      rectSizes[width] = (rectSizes[width] || 0) + 1;
    }
  });
  
  // Find position marker size - typically it's the largest rectangle that appears exactly 3 times
  // (one for each corner position marker)
  let positionMarkerSize = 0;
  for (const [size, count] of Object.entries(rectSizes)) {
    if (count <= 3 && parseFloat(size) > positionMarkerSize) {
      positionMarkerSize = parseFloat(size);
    }
  }
  
  // Extract likely position markers and QR modules
  const positionMarkers = [];
  const modules = [];
  
  // Group by positions to find position markers
  const positionGroups = {};
  
  allRects.forEach(rect => {
    const x = parseFloat(rect.getAttribute('x') || '0');
    const y = parseFloat(rect.getAttribute('y') || '0');
    const width = parseFloat(rect.getAttribute('width') || '0');
    
    // If this is a large rectangle, it might be part of a position marker
    if (width === positionMarkerSize || width >= moduleSize * 3) {
      const key = `${Math.floor(x/positionMarkerSize)},${Math.floor(y/positionMarkerSize)}`;
      if (!positionGroups[key]) {
        positionGroups[key] = [];
      }
      positionGroups[key].push(rect);
    } 
    // Otherwise, it's likely a QR module
    else if (width === moduleSize) {
      modules.push(rect);
    }
  });
  
  // Process position markers
  const positionMarkerGroups = Object.values(positionGroups)
    .filter(group => group.length >= 1);
  
  // Process the 3 corners (top-left, top-right, bottom-left)
  if (positionMarkerGroups.length > 0 && positionMarkerStyle.value !== 'square') {
    stylePositionMarkers(positionMarkerGroups, positionMarkerSize);
  }
  
  // Process QR modules
  if (modules.length > 0 && qrStyle.value !== 'square') {
    styleQrModules(modules);
  }
};

// Style QR modules
const styleQrModules = (modules) => {
  const svgNamespace = "http://www.w3.org/2000/svg";
  
  modules.forEach(module => {
    const x = parseFloat(module.getAttribute('x') || '0');
    const y = parseFloat(module.getAttribute('y') || '0');
    const width = parseFloat(module.getAttribute('width') || '0');
    const height = parseFloat(module.getAttribute('height') || '0');
    const parent = module.parentNode;
    
    if (!parent) return;
    
    // Apply different styling based on module style
    if (qrStyle.value === 'dots') {
      // Replace rectangle with circle
      const circle = document.createElementNS(svgNamespace, "circle");
      circle.setAttribute('cx', (x + width/2).toString());
      circle.setAttribute('cy', (y + height/2).toString());
      circle.setAttribute('r', (width * 0.45).toString());
      circle.setAttribute('fill', color.value);
      
      parent.replaceChild(circle, module);
    } 
    else if (qrStyle.value === 'rounded') {
      // Add rounded corners to rectangle
      const radius = Math.max(1, Math.min(width * (cornerRadius.value / 100), width/2));
      module.setAttribute('rx', radius.toString());
      module.setAttribute('ry', radius.toString());
    } 
    else if (qrStyle.value === 'diamond') {
      // Replace rectangle with diamond (rhombus)
      const diamond = document.createElementNS(svgNamespace, "polygon");
      const centerX = x + width/2;
      const centerY = y + height/2;
      const points = [
        `${centerX},${y}`, // top
        `${x + width},${centerY}`, // right
        `${centerX},${y + height}`, // bottom
        `${x},${centerY}` // left
      ].join(' ');
      
      diamond.setAttribute('points', points);
      diamond.setAttribute('fill', color.value);
      
      parent.replaceChild(diamond, module);
    }
  });
};

// Style position markers
const stylePositionMarkers = (positionMarkerGroups, markerSize) => {
  const svgNamespace = "http://www.w3.org/2000/svg";
  
  // Process each position marker group
  positionMarkerGroups.forEach(markers => {
    // Get the parent node of the first marker
    const parent = markers[0].parentNode;
    if (!parent) return;
    
    // Get the position of this marker group
    const x = parseFloat(markers[0].getAttribute('x') || '0');
    const y = parseFloat(markers[0].getAttribute('y') || '0');
    const width = markerSize; // Use the calculated position marker size
    const height = markerSize;
    
    // Create a group element for our custom marker
    const markerGroup = document.createElementNS(svgNamespace, "g");
    
    if (positionMarkerStyle.value === 'circle') {
      // Create circular position marker
      // Outer circle
      const outer = document.createElementNS(svgNamespace, "circle");
      outer.setAttribute('cx', (x + width/2).toString());
      outer.setAttribute('cy', (y + height/2).toString());
      outer.setAttribute('r', (width * 0.5).toString());
      outer.setAttribute('fill', color.value);
      
      // Middle circle (white/background)
      const middle = document.createElementNS(svgNamespace, "circle");
      middle.setAttribute('cx', (x + width/2).toString());
      middle.setAttribute('cy', (y + height/2).toString());
      middle.setAttribute('r', (width * 0.35).toString());
      middle.setAttribute('fill', bgColor.value);
      
      // Inner circle (color)
      const inner = document.createElementNS(svgNamespace, "circle");
      inner.setAttribute('cx', (x + width/2).toString());
      inner.setAttribute('cy', (y + height/2).toString());
      inner.setAttribute('r', (width * 0.2).toString());
      inner.setAttribute('fill', color.value);
      
      markerGroup.appendChild(outer);
      markerGroup.appendChild(middle);
      markerGroup.appendChild(inner);
    } 
    else if (positionMarkerStyle.value === 'rounded') {
      // Create rounded position marker
      // Outer rounded rectangle
      const outer = document.createElementNS(svgNamespace, "rect");
      outer.setAttribute('x', x.toString());
      outer.setAttribute('y', y.toString());
      outer.setAttribute('width', width.toString());
      outer.setAttribute('height', height.toString());
      outer.setAttribute('rx', (width * 0.25).toString());
      outer.setAttribute('ry', (height * 0.25).toString());
      outer.setAttribute('fill', color.value);
      
      // Middle rounded rectangle (white/background)
      const middle = document.createElementNS(svgNamespace, "rect");
      middle.setAttribute('x', (x + width * 0.15).toString());
      middle.setAttribute('y', (y + height * 0.15).toString());
      middle.setAttribute('width', (width * 0.7).toString());
      middle.setAttribute('height', (height * 0.7).toString());
      middle.setAttribute('rx', (width * 0.15).toString());
      middle.setAttribute('ry', (height * 0.15).toString());
      middle.setAttribute('fill', bgColor.value);
      
      // Inner rounded rectangle (color)
      const inner = document.createElementNS(svgNamespace, "rect");
      inner.setAttribute('x', (x + width * 0.3).toString());
      inner.setAttribute('y', (y + height * 0.3).toString());
      inner.setAttribute('width', (width * 0.4).toString());
      inner.setAttribute('height', (height * 0.4).toString());
      inner.setAttribute('rx', (width * 0.1).toString());
      inner.setAttribute('ry', (height * 0.1).toString());
      inner.setAttribute('fill', color.value);
      
      markerGroup.appendChild(outer);
      markerGroup.appendChild(middle);
      markerGroup.appendChild(inner);
    }
    else if (positionMarkerStyle.value === 'extra') {
      // Create framed position marker with extra border
      const offset = width * 0.05;
      
      // Extra outer rectangle
      const extra = document.createElementNS(svgNamespace, "rect");
      extra.setAttribute('x', (x - offset).toString());
      extra.setAttribute('y', (y - offset).toString());
      extra.setAttribute('width', (width + offset * 2).toString());
      extra.setAttribute('height', (height + offset * 2).toString());
      extra.setAttribute('rx', (width * 0.1).toString());
      extra.setAttribute('ry', (height * 0.1).toString());
      extra.setAttribute('fill', color.value);
      
      // Middle rectangle (white/background)
      const middle = document.createElementNS(svgNamespace, "rect");
      middle.setAttribute('x', (x + width * 0.15).toString());
      middle.setAttribute('y', (y + height * 0.15).toString());
      middle.setAttribute('width', (width * 0.7).toString());
      middle.setAttribute('height', (height * 0.7).toString());
      middle.setAttribute('rx', (width * 0.05).toString());
      middle.setAttribute('ry', (height * 0.05).toString());
      middle.setAttribute('fill', bgColor.value);
      
      // Inner rectangle (color)
      const inner = document.createElementNS(svgNamespace, "rect");
      inner.setAttribute('x', (x + width * 0.3).toString());
      inner.setAttribute('y', (y + height * 0.3).toString());
      inner.setAttribute('width', (width * 0.4).toString());
      inner.setAttribute('height', (height * 0.4).toString());
      inner.setAttribute('rx', (width * 0.02).toString());
      inner.setAttribute('ry', (height * 0.02).toString());
      inner.setAttribute('fill', color.value);
      
      markerGroup.appendChild(extra);
      markerGroup.appendChild(middle);
      markerGroup.appendChild(inner);
    }
    
    // Add our custom marker to the SVG and remove the original markers
    if (markerGroup.childNodes.length > 0) {
      parent.appendChild(markerGroup);
      
      // Remove the original marker elements
      markers.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
    }
  });
};

// Add logo to SVG
const addLogoToSvg = (svg) => {
  // Get SVG dimensions
  const width = parseInt(svg.getAttribute('width') || '100');
  const height = parseInt(svg.getAttribute('height') || '100');
  
  // Calculate logo size
  const logoSize = Math.min(width, height) * (logoSize.value / 100);
  
  // Create image element for logo
  const image = document.createElementNS('http://www.w3.org/2000/svg', 'image');
  image.setAttribute('href', logoPreview.value);
  image.setAttribute('width', logoSize.toString());
  image.setAttribute('height', logoSize.toString());
  image.setAttribute('x', ((width - logoSize) / 2).toString());
  image.setAttribute('y', ((height - logoSize) / 2).toString());
  
  // Add logo to SVG
  svg.appendChild(image);
};

// Download QR code
const downloadQR = async (format) => {
  if (!generated.value) return;
  
  try {
    // Create a canvas for PNG conversion if needed
    if (format === 'png') {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Set canvas size to match image
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Draw image on canvas
        context.fillStyle = bgColor.value;
        context.fillRect(0, 0, canvas.width, canvas.height);
        context.drawImage(img, 0, 0);
        
        // Create download link
        const url = canvas.toDataURL('image/png');
        downloadFile(url, `qrcode_${getTimestamp()}.png`);
      };
      
      // Create a blob URL from the SVG
      const svgBlob = new Blob([qrCodeSvg.value], { type: 'image/svg+xml' });
      img.src = URL.createObjectURL(svgBlob);
    } 
    // For SVG, download directly
    else if (format === 'svg') {
      const blob = new Blob([qrCodeSvg.value], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      downloadFile(url, `qrcode_${getTimestamp()}.svg`);
    }
  } catch (error) {
    console.error(`Error downloading QR code as ${format}:`, error);
    alert(`Failed to download QR code as ${format}. Please try again.`);
  }
};

// Helper function to download a file
const downloadFile = (url, filename) => {
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  
  // Clean up
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 100);
};

// Helper function to get timestamp for filenames
const getTimestamp = () => {
  return new Date().toISOString().replace(/[-:]/g, '').replace('T', '_').split('.')[0];
};

// Initialize with a QR code
onMounted(() => {
  // Include library via CDN if not already loaded
  if (!window.QRCode) {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/qrcode@1.5.0/build/qrcode.min.js';
    script.onload = () => {
      // Generate QR code once library is loaded
      setTimeout(() => {
        generateQrCode();
      }, 100);
    };
    document.head.appendChild(script);
  } else {
    // Generate QR code if library is already loaded
    setTimeout(() => {
      generateQrCode();
    }, 100);
  }
});

// Watch content changes to enable auto-generation
watch(content, (newValue) => {
  if (newValue) {
    autoGenerate();
  }
});
</script>

<style scoped>
.qr-generator {
  max-width: 100%;
  height: 100%;
}

.qr-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  max-height: 100vh;
}

.qr-preview svg {
  width: 100% !important;
  height: 100% !important;
  max-width: 800px;
  max-height: 800px;
  object-fit: contain;
}

/* Minimal Range Slider */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
}

/* Track */
.range-slider::-webkit-slider-runnable-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

.range-slider::-moz-range-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

/* Thumb */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  margin-top: -7px;
  transition: all 0.15s ease;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.15s ease;
}

/* Hover state */
.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.range-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Focus state */
.range-slider:focus {
  outline: none;
}

/* Dark mode */
.dark .range-slider::-webkit-slider-runnable-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-moz-range-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-webkit-slider-thumb {
  background: #ffffff;
  border-color: #000000;
}

.dark .range-slider::-moz-range-thumb {
  background: #ffffff;
  border-color: #000000;
}

/* Responsive rules */
@media (max-width: 640px) {
  .qr-preview {
    width: 100%;
    margin: 0 auto;
  }
  
  .qr-preview svg {
    max-width: 100%;
    max-height: 50vh;
  }
  
  .range-slider::-webkit-slider-thumb {
    width: 14px;
    height: 14px;
    margin-top: -6px;
  }
  
  .range-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
  }
}

/* Fix for SVG sizing within container */
:deep(svg) {
  width: 100% !important;
  height: auto !important;
  max-width: 100% !important;
}
</style> 