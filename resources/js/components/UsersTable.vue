<script setup>
import DataTable from './DataTable.vue';
import Avatar from './Avatar.vue';
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
  users: {
    type: Array,
    required: true
  },
  adminUserLogins: {
    type: Array,
    default: () => []
  },
  developerUserLogins: {
    type: Array,
    default: () => []
  },
  devUserLogins: {
    type: Array,
    default: () => []
  },
  pmUserLogins: {
    type: Array,
    default: () => []
  },
  tibiUserLogins: {
    type: Array,
    default: () => []
  }
});

const dataTable = ref(null);

// Define table columns with vertical centering for all cells
const columns = [
  { 
    key: 'todayLogin', 
    label: ' ',
    align: 'center',
    width: 'w-12',
    sortable: false,
    tdClass: 'align-middle'
  },
  { 
    key: 'avatar', 
    label: ' ',
    align: 'center',
    width: 'w-6',
    sortable: false,
    tdClass: 'p-0 align-middle'
  },
  { 
    key: 'name', 
    label: 'Meno', 
    sortable: true,
    tdClass: 'gant-modern-bold text-gray-900 dark:text-white align-middle'
  },
  { 
    key: 'surname', 
    label: 'Priezvisko', 
    sortable: true,
    tdClass: 'gant-modern-bold text-gray-900 dark:text-white align-middle'
  },
  { 
    key: 'birthday', 
    label: 'Dátum narodenia', 
    align: 'center',
    sortable: true,
    tdClass: 'align-middle'
  },
  { 
    key: 'login', 
    label: 'Login', 
    align: 'center',
    sortable: true,
    tdClass: 'font-mono gant-modern-regular text-gray-900 dark:text-white align-middle'
  },
  { 
    key: 'email', 
    label: 'Email', 
    sortable: true,
    tdClass: 'align-middle'
  },
  { 
    key: 'role', 
    label: 'Rola', 
    align: 'center',
    sortable: true,
    tdClass: 'align-middle'
  },
  { 
    key: 'projects', 
    label: 'Projekty', 
    align: 'center',
    sortable: true,
    sortKey: 'projectsCount',
    tdClass: 'align-middle'
  },
  { 
    key: 'assets', 
    label: 'Fonty', 
    align: 'center',
    sortable: true,
    sortKey: 'assetsCount',
    tdClass: 'align-middle'
  },
  { 
    key: 'created_at', 
    label: 'Registrácia', 
    align: 'center',
    sortable: true,
    hidden: true,
    tdClass: 'align-middle'
  },
  { 
    key: 'last_login_at', 
    label: 'Posledné prihlásenie', 
    align: 'center',
    sortable: true,
    hidden: true,
    tdClass: 'align-middle'
  }
];

// Navigate to user detail
function goToUserDetail(user) {
  window.location.href = `/admin/users/${user.id}`;
}

// Process users data to add computed properties
const processedUsers = computed(() => {
  return props.users.map(user => {
    // Determine if user logged in today
    const lastLoginDate = user.last_login_at ? new Date(user.last_login_at) : null;
    const today = new Date();
    const isLoggedInToday = lastLoginDate ? 
      lastLoginDate.getDate() === today.getDate() && 
      lastLoginDate.getMonth() === today.getMonth() && 
      lastLoginDate.getFullYear() === today.getFullYear() : false;
    
    // Determine user role
    let role = 'user';
    if (props.adminUserLogins.includes(user.login)) {
      role = 'admin';
    } else if (props.developerUserLogins.includes(user.login)) {
      role = 'developer';
    } else if (props.devUserLogins.includes(user.login)) {
      role = 'dev';
    } else if (props.tibiUserLogins.includes(user.login)) {
      role = 'tibi';
    } else if (props.pmUserLogins.includes(user.login)) {
      role = 'pm';
    }
    
    // Process projects data
    let projectsData = '-';
    const projectCount = user.projects?.length || 0;
    
    if (projectCount > 0) {
      let form = 'projektov';
      if (projectCount === 1) {
        form = 'projekt';
      } else if (projectCount >= 2 && projectCount <= 4) {
        form = 'projekty';
      }
      projectsData = `${projectCount} ${form}`;
    }
    
    // Process assets (fonts) data
    let assetsData = '-';
    const fontsCount = user.fonts?.length || 0;
    
    if (fontsCount > 0) {
      let form = 'fontov';
      if (fontsCount === 1) {
        form = 'font';
      } else if (fontsCount >= 2 && fontsCount <= 4) {
        form = 'fonty';
      }
      assetsData = `${fontsCount} ${form}`;
    }
    
    return {
      ...user,
      todayLogin: isLoggedInToday ? '✋' : '',
      role: role,
      projects: projectsData,
      projectsCount: projectCount,
      assets: assetsData,
      assetsCount: fontsCount
    };
  });
});

// Add this function to enhance the column headers after the component is mounted
onMounted(() => {
  setTimeout(() => {
    const headers = document.querySelectorAll('#users-table th[role="columnheader"] .sort-button');
    headers.forEach(header => {
      // Create a span element for the "Filtrovať" text
      const filterText = document.createElement('span');
      filterText.textContent = 'Filtrovať';
      filterText.className = 'ml-1 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-150';
      
      // Add the group class to the header for hover effects
      header.classList.add('group');
      
      // Append the text to the header
      header.appendChild(filterText);
    });
  }, 100); // Small delay to ensure the table is rendered
});
</script>

<template>
  <div class="hidden lg:block">
    <DataTable 
      ref="dataTable"
      :data="processedUsers" 
      :columns="columns" 
      default-sort-column="login"
      default-sort-direction="asc"
      table-id="users-table"
      @row-click="goToUserDetail"
      class="users-table-container"
      :row-class="'!border-b !border-gray-200 dark:!border-gray-700'"
    >
      <!-- Custom header template with smaller text and opacity using only Tailwind -->
      <template #header="{ column, sort }">
        <div class="flex items-center gap-2">
          <span class="text-xs text-gray-500 dark:text-gray-400 opacity-70 font-medium">{{ column.label }}</span>
          <button 
            v-if="column.sortable" 
            @click="sort(column.key)"
            class="group flex items-center gap-1 text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-150"
          >
            <i class="design design-sort text-xs"></i>
            <span class="text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-150">Filtrovať</span>
          </button>
        </div>
      </template>
      
      <!-- Avatar cell template with improved alignment -->
      <template #cell(avatar)="{ item }">
        <div class="flex items-center justify-center w-full h-full">
          <Avatar :user="item" size="sm" :ring="false" />
        </div>
      </template>
      
      <!-- Birthday cell template -->
      <template #cell(birthday)="{ value }">
        <div v-if="value" class="flex items-center justify-center text-sm">
          <i class="claims claims-calendar-outline mr-1.5 opacity-70"></i>
          {{ new Date(value).toLocaleDateString('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' }) }}
        </div>
        <span v-else class="text-gray-400 dark:text-gray-600">-</span>
      </template>
      
      <!-- Role cell template - remove avatar from here -->
      <template #cell(role)="{ item }">
        <div class="flex items-center justify-center h-full">
          <span v-if="item.role === 'admin'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
            <i class="design design-vermont-intranet-pass mr-1 opacity-70"></i> Admin
          </span>
          <span v-else-if="item.role === 'developer'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
            <i class="design design-bracket3 mr-1 opacity-70"></i> Developer
          </span>
          <span v-else-if="item.role === 'dev'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
            <i class="design design-bracket3 mr-1 opacity-70"></i> Dev
          </span>
          <span v-else-if="item.role === 'tibi'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
            <i class="design design-coffee mr-1 opacity-70"></i> Tibi
          </span>
          <span v-else-if="item.role === 'pm'" class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
            <i class="design design-race mr-1 opacity-70"></i> PM
          </span>
          <span v-else class="inline-flex items-center px-2.5 py-1 rounded-full text-xs gant-modern-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            <i class="design design-vermont-ucko mr-1 opacity-70"></i> Používateľ
          </span>
        </div>
      </template>
      
      <!-- Date formatting for created_at -->
      <template #cell(created_at)="{ value }">
        <div v-if="value" class="flex items-center justify-center text-sm">
          <i class="claims claims-calendar-outline mr-1.5 opacity-30"></i>
          {{ new Date(value).toLocaleDateString('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' }) }}
          <span class="mx-1 text-gray-300 dark:text-gray-600">|</span>
          <i class="claims claims-clock-outline mr-1.5 opacity-50"></i>
          {{ new Date(value).toLocaleTimeString('sk-SK', { hour: '2-digit', minute: '2-digit' }) }}
        </div>
        <span v-else class="text-gray-400 dark:text-gray-600">-</span>
      </template>
      
      <!-- Date formatting for last_login_at -->
      <template #cell(last_login_at)="{ value }">
        <div v-if="value" class="flex items-center justify-center text-sm">
          <i class="claims claims-clock-outline mr-1.5 opacity-50"></i>
          {{ new Date(value).toLocaleDateString('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' }) }}
          {{ new Date(value).toLocaleTimeString('sk-SK', { hour: '2-digit', minute: '2-digit' }) }}
        </div>
        <span v-else class="text-gray-400 dark:text-gray-600">-</span>
      </template>
      
      <!-- Projects cell template -->
      <template #cell(projects)="{ value, item }">
        <div v-if="value !== '-'" class="flex items-center justify-center">
          <!-- <i class="design design-service mr-1.5 opacity-70"></i> -->
          {{ value }}
        </div>
        <span v-else class="text-gray-400 dark:text-gray-600">-</span>
      </template>
      
      <!-- Assets cell template -->
      <template #cell(assets)="{ value, item }">
        <div v-if="value !== '-'" class="flex items-center justify-center">
          <i class="design design-letter mr-1.5 opacity-70"></i>
          {{ value }}
        </div>
        <span v-else class="text-gray-400 dark:text-gray-600">-</span>
      </template>
    </DataTable>
  </div>
  
  <!-- You can keep the existing mobile cards view here or create a responsive cards component -->
</template>

<style scoped>
/* Replace the existing style block with this */
:deep(table) {
  width: 100%;
  border-collapse: collapse;
}

:deep(thead tr) {
  border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

:deep(tbody tr) {
  border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

:deep(.dark thead tr),
:deep(.dark tbody tr) {
  border-bottom: 1px solid #374151 !important; /* gray-700 */
}

:deep(tbody tr:hover) {
  background-color: rgba(249, 250, 251, 0.5); /* gray-50 */
}

:deep(.dark tbody tr:hover) {
  background-color: rgba(31, 41, 55, 0.5); /* gray-800/50 */
}

:deep(tbody tr:last-child) {
  border-bottom: none !important;
}

/* Reduce cell padding for more compact layout */
:deep(th), 
:deep(td) {
  padding: 0.5rem 0.75rem;
  vertical-align: middle;
}

/* Add these styles to show "Filtrovať" text on hover */
:deep(th[role="columnheader"]) {
  position: relative;
}

:deep(th[role="columnheader"] .sort-icon) {
  display: flex;
  align-items: center;
}

:deep(th[role="columnheader"] .sort-icon::after) {
  content: "Filtrovať";
  opacity: 0;
  margin-left: 4px;
  font-size: 0.75rem;
  transition: opacity 0.15s ease;
}

:deep(th[role="columnheader"]:hover .sort-icon::after) {
  opacity: 1;
}
</style> 