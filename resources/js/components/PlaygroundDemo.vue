<template>
  <div class="h-full flex flex-col">
    <div class="mb-8">
      <p class="opacity-50 dark:text-white">
        –––––– Code handover // Playground demo component
      </p>
      <h3 class="leading-none tracking-tighter mb-10 text-black dark:text-gray-200 text-5xl lg:text-6xl xl:text-8xl gant-modern-bold">Playground</h3>
    </div>
    
    <div 
      ref="containerRef"
      class="mb-0 flex flex-col w-full flex-1 bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all duration-300"
      :style="containerHeightStyle"
    >
      <!-- Header with controls -->
      <PlaygroundHeader 
        :active-tab="activeTab" 
        @update:active-tab="setActiveTab"
      />

      <!-- Browser Controls -->
      <PlaygroundBrowserControls 
        :active-tab="activeTab"
        :active-device="activeDevice"
        :active-theme="activeTheme"
        :devices="devices"
        :themes="themes"
        :is-refreshing="isRefreshing"
        @update:active-device="setActiveDevice"
        @update:active-theme="setActiveTheme"
        @refresh="refreshPreview"
        @navigate-back="navigateBack"
        @navigate-forward="navigateForward"
      />

      <!-- Main content with smooth transitions -->
      <div class="flex-1 relative" :class="[activeDevice, { 'dark': activeTheme === 'dark' }]">
        <!-- Transition wrapper for smooth tab switching -->
        <transition
          name="fade"
          mode="out-in"
        >
          <!-- Preview Tab -->
          <PlaygroundPreview 
            v-if="activeTab === 'preview'"
            ref="previewContentRef"
            :active-device="activeDevice"
            :active-theme="activeTheme"
            :progress="progress"
            :current-status="currentStatus"
            :is-refreshing="isRefreshing"
            @dimension-change="handleDimensionChange"
            :key="'preview-' + previewRefreshKey"
          />

          <!-- Code Tab -->
          <PlaygroundCode 
            v-else-if="activeTab === 'code'"
            :demo-code="demoCode"
            @copy-code="copyCode"
            :copied="copied"
            :language="determineCodeLanguage"
            :fileName="'PlaygroundDemo.vue'"
            :key="'code'"
          />
        </transition>

        <!-- Loading Overlay -->
        <transition name="fade">
          <div v-if="isLoading" class="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-gray-100/80 dark:bg-gray-800/80 backdrop-blur-sm z-10">
            <div class="flex flex-col items-center space-y-3">
              <i class="design design-loader-4-line animate-spin text-3xl text-gray-600 dark:text-gray-400"></i>
              <span class="text-sm text-gray-600 dark:text-gray-400 gant-modern-medium">Loading...</span>
            </div>
          </div>
        </transition>
      </div>

      <!-- Notifications -->
      <PlaygroundNotifications 
        :notifications="notifications"
        @remove="removeNotification"
      />

      <!-- Floating Controls for Mobile/Small Screens -->
      <div class="lg:hidden fixed bottom-4 left-4 z-50 flex space-x-2">
        <button 
          v-for="tab in ['preview', 'code']" 
          :key="tab"
          @click="setActiveTab(tab)"
          class="w-12 h-12 flex items-center justify-center rounded-full bg-gray-800 dark:bg-gray-700 shadow-lg text-white"
          :class="{ 'bg-blue-600 dark:bg-blue-500': activeTab === tab }"
        >
          <i 
            :class="[
              'design text-lg',
              tab === 'preview' ? 'design-visible-fill' : 'design-bracket3'
            ]"
          ></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import PlaygroundHeader from './playground/PlaygroundHeader.vue'
import PlaygroundBrowserControls from './playground/PlaygroundBrowserControls.vue'
import PlaygroundPreview from './playground/PlaygroundPreview.vue'
import PlaygroundCode from './playground/PlaygroundCode.vue'
import PlaygroundNotifications from './playground/PlaygroundNotifications.vue'
import { useResizeObserver } from './playground/composables/useResizeObserver'
import { useNotifications } from './playground/composables/useNotifications'
import { useTheme } from './playground/composables/useTheme'
import { useProgress } from './playground/composables/useProgress'

// Container and sizing
const containerRef = ref(null)
const previewContentRef = ref(null)
const { width: containerWidth } = useResizeObserver(containerRef)
const { height: contentHeight } = useResizeObserver(previewContentRef)

// Default fallback height to prevent layout issues before measurements
const defaultHeight = 600

// Tabs and UI state
const activeTab = ref('preview')
const activeDevice = ref('desktop')
const isLoading = ref(false)
const copied = ref(false)
const isRefreshing = ref(false)
const previewRefreshKey = ref(0)

// Theme management
const { activeTheme } = useTheme()

// Progress simulation
const { progress, currentStatus } = useProgress()

// Browser navigation history
const navigationHistory = ref([])
const currentHistoryIndex = ref(-1)

// Navigate back in history
const navigateBack = () => {
  if (currentHistoryIndex.value > 0) {
    currentHistoryIndex.value--
    // Here you would implement actual navigation logic
    addNotification(
      'info',
      'Navigation',
      'Navigated back in browser history',
      'design-arrow-left'
    )
  }
}

// Navigate forward in history
const navigateForward = () => {
  if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
    currentHistoryIndex.value++
    // Here you would implement actual navigation logic
    addNotification(
      'info',
      'Navigation',
      'Navigated forward in browser history',
      'design-arrow-right'
    )
  }
}

// Handle dimension change event from the preview component
const handleDimensionChange = () => {
  try {
    nextTick(() => {
      // Force layout recalculation if needed
      if (activeTab.value === 'preview' && previewContentRef.value) {
        // No need to do anything special - the ResizeObserver will handle size changes
        // Just add a debug log if needed
        // console.log('Preview dimensions updated')
      }
    })
  } catch (error) {
    console.error('Error in handleDimensionChange:', error)
  }
}

// Set active tab with transition
const setActiveTab = (tab) => {
  if (activeTab.value === tab) return;
  
  isLoading.value = true;
  
  // Add a small delay for visual feedback
  setTimeout(() => {
    activeTab.value = tab;
    
    // Give time for the tab content to be mounted before removing the loading state
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false;
        
        // Force dimension recalculation when switching to preview tab
        if (tab === 'preview') {
          setTimeout(() => {
            handleDimensionChange();
          }, 100); // Small delay to ensure component is fully rendered
        }
      }, 300);
    });
  }, 150);
}

// Set active device with transition
const setActiveDevice = (device) => {
  activeDevice.value = device
  
  // Add a brief loading effect when changing devices
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 400)
}

// Set active theme
const setActiveTheme = (theme) => {
  activeTheme.value = theme
}

// Refresh the preview
const refreshPreview = () => {
  if (activeTab.value !== 'preview') {
    setActiveTab('preview')
    return
  }
  
  isRefreshing.value = true
  previewRefreshKey.value++
  
  setTimeout(() => {
    isRefreshing.value = false
  }, 600)
}

// Detect language from code
const determineCodeLanguage = computed(() => {
  // Look for script or template tags to determine language
  if (demoCode.value.includes('<script') && demoCode.value.includes('</scr' + 'ipt>')) {
    return 'vue'
  }
  if (demoCode.value.includes('<template>') && demoCode.value.includes('</template>')) {
    return 'vue'
  }
  if (demoCode.value.startsWith('<')) {
    return 'markup'
  }
  return 'javascript'
})

// Watch for device or theme changes to recalculate dimensions
watch([activeDevice, activeTheme], () => {
  if (activeTab.value === 'preview') {
    // Add a small delay to ensure the device/theme change is reflected
    setTimeout(() => {
      handleDimensionChange();
    }, 100);
  }
})

// Computed
const aspectRatioHeight = computed(() => {
  // Calculate base height from width using 16:9 ratio
  const baseHeight = containerWidth.value ? (containerWidth.value * 9) / 16 : defaultHeight
  // Ensure minimum height of 500px for better usability
  return Math.max(baseHeight, 500)
})

const containerHeight = computed(() => {
  // Base height is either aspect ratio or content height, whichever is larger
  // Add a minimum value for contentHeight to prevent NaN or 0
  const baseHeight = Math.max(aspectRatioHeight.value, contentHeight.value || defaultHeight)
  // Add padding for header controls (approximately 120px)
  return baseHeight + 120
})

// Create a style object for container height to prevent layout issues during initialization
const containerHeightStyle = computed(() => {
  return { 
    height: `${containerHeight.value || defaultHeight}px`,
    minHeight: `${defaultHeight}px` // Add minimum height for safety
  }
})

// Device and theme configurations
const devices = [
  { name: 'desktop', description: 'Desktop View (1280px)', icon: 'design-resolution_monitor' },
  { name: 'tablet', description: 'Tablet View (768px)', icon: 'design-resolution_tablet' },
  { name: 'mobile', description: 'Mobile View (375px)', icon: 'design-resolution_smartphone' }
]

const themes = [
  { name: 'light', description: 'Light Mode', icon: 'design-sunrise' },
  { name: 'dark', description: 'Dark Mode', icon: 'design-moonrise' }
]

// Ensure refs are properly initialized when tab changes
watch(activeTab, () => {
  nextTick(() => {
    // This will trigger the ResizeObserver to update
    if (containerRef.value && previewContentRef.value && activeTab.value === 'preview') {
      // Force a reflow to ensure measurements are accurate
      const width = containerRef.value.offsetWidth
      const height = previewContentRef.value.$el?.offsetHeight || 0
    }
  })
})

// Notifications system
const { notifications, addNotification, removeNotification } = useNotifications()

// Copy code functionality
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(demoCode.value)
    copied.value = true
    addNotification(
      'success', 
      'Code Copied', 
      'The code has been copied to your clipboard', 
      'design-clipboard-line'
    )
    setTimeout(() => copied.value = false, 2000)
  } catch (error) {
    addNotification(
      'error',
      'Copy Failed',
      'Failed to copy code to clipboard',
      'design-error-warning-line'
    )
  }
}

// Demo code
const demoCode = computed(() => {
  return `<template>
  <div class="card">
    <div class="card-header">
      <h3>Create project</h3>
      <p>Deploy your new project in one-click.</p>
    </div>

    <div class="card-content">
      <div class="form-group">
        <label>Project name</label>
        <div class="input-wrapper">
          <i class="design design-folder-add-line input-icon"></i>
          <input 
            v-model="form.name"
            type="text"
            placeholder="my-awesome-project"
            class="input-field input-field-with-icon"
          />
        </div>
      </div>

      <div class="form-group">
        <label>Framework</label>
        <div class="input-wrapper">
          <i class="design design-code-box-line input-icon"></i>
          <select 
            v-model="form.framework"
            class="input-field input-field-with-icon"
          >
            <option value="next">Next.js</option>
            <option value="vue">Vue</option>
            <option value="nuxt">Nuxt.js</option>
            <option value="react">React</option>
          </select>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <button @click="resetForm">
        <i class="design design-close-circle-line"></i>
        Cancel
      </button>
      <button @click="handleDeploy">
        <i class="design design-rocket-line"></i>
        Deploy
      </button>
    </div>
  </div>
</template>`
})
</script>

<style scoped>
/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Device-specific styles */
.mobile {
  max-width: 375px;
  margin: 0 auto;
}

.tablet {
  max-width: 768px;
  margin: 0 auto;
}

.desktop {
  width: 100%;
}

/* Typography settings */
:deep(*) {
  font-family: 'gant-modern-regular', sans-serif;
}

:deep(.gant-modern-regular) {
  font-family: 'gant-modern-regular', sans-serif;
}

:deep(.gant-modern-medium) {
  font-family: 'gant-modern-medium', sans-serif;
}

:deep(.gant-modern-bold) {
  font-family: 'gant-modern-bold', sans-serif;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .mobile, .tablet, .desktop {
    width: 100%;
    max-width: 100%;
  }
}
</style>
