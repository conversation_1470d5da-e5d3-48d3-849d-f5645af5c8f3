<script setup>
import DataTable from './DataTable.vue';
import { ref, computed, onMounted } from 'vue';
import Avatar from './Avatar.vue';

const props = defineProps({
  projects: {
    type: Array,
    required: true
  }
});

const dataTable = ref(null);
const csrfToken = ref('');

// Get CSRF token safely in mounted hook
onMounted(() => {
  const tokenElement = document.querySelector('meta[name="csrf-token"]');
  if (tokenElement) {
    csrfToken.value = tokenElement.getAttribute('content');
  }
  
  // Force all columns to be visible
  if (dataTable.value) {
    dataTable.value.visibleColumns = columns.map(col => col.key);
  }
});

// Define table columns
const columns = [
  { 
    key: 'name', 
    label: 'Názov', 
    sortable: true,
    tdClass: 'gant-modern-bold text-gray-900 dark:text-white',
    visible: true
  },
  { 
    key: 'description', 
    label: 'Popis', 
    sortable: true,
    tdClass: 'text-xs',
    visible: true
  },
  { 
    key: 'backend', 
    label: 'Backend', 
    sortable: true,
    tdClass: 'text-xs',
    visible: true
  },
  { 
    key: 'frontend', 
    label: 'Frontend', 
    sortable: true,
    tdClass: 'text-xs',
    visible: true
  },
  { 
    key: 'start_date', 
    label: 'Deployed', 
    sortable: true,
    tdClass: 'text-xs',
    hidden: false
  },
  { 
    key: 'end_date', 
    label: 'Upgrade', 
    sortable: true,
    tdClass: 'text-xs',
    hidden: false
  },
  { 
    key: 'status', 
    label: 'Stav', 
    align: 'center',
    sortable: true,
    visible: true
  },
  { 
    key: 'fonts', 
    label: 'Fonty', 
    sortable: false,
    visible: true
  },
  { 
    key: 'users', 
    label: 'Používatelia', 
    sortable: false,
    visible: true
  },
  { 
    key: 'actions', 
    label: 'Akcie', 
    align: 'center',
    sortable: false,
    visible: true
  }
];

// Function to confirm delete
function confirmDelete(event) {
  if (!confirm('Ste si istý, že chcete odstrániť tento projekt?')) {
    event.preventDefault();
  }
}

// Function to open edit modal
function openEditModal(project) {
  // Use the same function from the Blade file
  if (window.openEditModal) {
    window.openEditModal(
      project, 
      project.fonts.map(font => font.id), 
      project.users.map(user => user.id)
    );
  }
}

// Function to get status badge color based on status color
function getStatusBgColor(color) {
  if (!color) return 'bg-gray-100 dark:bg-gray-800';
  
  // Extract the color hex without '#'
  const hexColor = color.replace('#', '');
  
  // Return the corresponding Tailwind color class
  // This is a simplified mapping - expand as needed
  const colorMap = {
    '6366F1': 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400', // indigo
    '3B82F6': 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400',       // blue
    '10B981': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400',   // green
    '8B5CF6': 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400', // purple
    'FBBF24': 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400', // yellow
    '6B7280': 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-400',      // gray
    'EF4444': 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400',         // red
  };
  
  // Try to match the exact color
  if (colorMap[hexColor]) return colorMap[hexColor];
  
  // Default fallback
  return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-400';
}

// Function to convert hex color to rgb
function hexToRgb(hex) {
  // Remove the hash if it exists
  hex = hex ? hex.replace('#', '') : '6B7280'; // Default to gray if no color provided
  
  // Handle 3-digit hex
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  
  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `${r}, ${g}, ${b}`;
}

// Process projects to add computed properties or format dates
const processedProjects = computed(() => {
  return props.projects.map(project => {
    // Create a new object with the project data
    const processedProject = { ...project };
    
    // Ensure these properties are explicitly set and not null
    // Using strict null check to handle both null and undefined
    if (processedProject.backend === null || processedProject.backend === undefined) {
      processedProject.backend = '';
    }
    
    if (processedProject.frontend === null || processedProject.frontend === undefined) {
      processedProject.frontend = '';
    }
    
    // Ensure date properties are properly formatted
    if (processedProject.start_date) {
      // Keep the original date format for proper sorting
      processedProject.start_date = processedProject.start_date;
    }
    
    if (processedProject.end_date) {
      // Keep the original date format for proper sorting
      processedProject.end_date = processedProject.end_date;
    }
    
    return processedProject;
  });
});

// Function to get the correct icon class for a font
function getFontIconClass(fontName) {
  // Handle special cases
  const specialCases = {
    'Order Group': 'ordergroup ordergroup-thumbnail',
    'Category': 'vermont-category vermont-category-thumbnail',
    'CDB': 'cdb cdb-thumbnail',
    'Central Database': 'cdb cdb-thumbnail',
    'Eshop': 'vermont-icon vermont-icon-thumbnail'
  };
  
  // Check if it's a special case
  if (specialCases[fontName]) {
    return specialCases[fontName];
  }
  
  // Default pattern: lowercase font name + "-thumbnail"
  const lowercaseName = fontName.toLowerCase();
  return `${lowercaseName} ${lowercaseName}-thumbnail`;
}

// Function to navigate to project detail page
function navigateToProject(project) {
  // Don't navigate when clicking on action buttons
  if (event && (event.target.closest('button') || event.target.closest('form'))) {
    return;
  }
  
  if (project && project.id) {
    window.location.href = `/admin/projects/${project.id}`;
  }
}
</script>

<template>
  <div class="hidden lg:block">
    <DataTable 
      ref="dataTable"
      :data="processedProjects" 
      :columns="columns" 
      default-sort-column="name"
      default-sort-direction="asc"
      table-id="projects-table"
      class="projects-table-container"
      :row-class="'!border-b !border-gray-200 dark:!border-gray-700 cursor-pointer'"
      @row-click="(item) => navigateToProject(item)"
    >
      <!-- Custom header template to override uppercase styling -->
      <template #header="{ column, sort }">
        <div class="flex items-center normal-case" :class="{'justify-center': column.align === 'center'}">
          <!-- Sort icon before the label -->
          <template v-if="column.sortable">
            <i 
              :class="[
                'mr-1 sm:mr-1.5 text-xs sm:text-sm',
                sortColumn === column.key 
                  ? (sortDirection === 'asc' ? 'design design-arrow-up' : 'design design-arrow-down')
                  : 'design design-filter opacity-50 group-hover:opacity-100'
              ]"
            ></i>
          </template>
          <span>{{ column.label }}</span>
        </div>
      </template>
      
      <!-- Description cell with character limit -->
      <template #cell(description)="{ value }">
        <span class="text-xs">{{ value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : '-' }}</span>
      </template>
      
      <!-- Status cell with badges -->
      <template #cell(status)="{ item }">
        <span v-if="item.projectStatus" 
              class="px-3 py-1 text-xs rounded-full inline-flex items-center w-auto"
              :style="{
                backgroundColor: item.projectStatus.color,
                color: 'white',
              }">
          <i v-if="item.projectStatus.icon" :class="`${item.projectStatus.icon} mr-1.5 opacity-80`"></i>
          {{ item.projectStatus.name }}
        </span>
        <span v-else class="px-3 py-1 text-xs rounded-full bg-gray-600 text-white inline-flex items-center w-auto">
          <i class="design design-flag mr-1.5 opacity-80"></i>
          {{ item.status }}
        </span>
      </template>
      
      <!-- Backend cell -->
      <template #cell(backend)="{ item }">
        <span class="text-xs">{{ item.backend === null || item.backend === undefined || item.backend === '' ? '-' : item.backend }}</span>
      </template>
      
      <!-- Frontend cell -->
      <template #cell(frontend)="{ item }">
        <span class="text-xs">{{ item.frontend === null || item.frontend === undefined || item.frontend === '' ? '-' : item.frontend }}</span>
      </template>
      
      <!-- Fonts cell with badges -->
      <template #cell(fonts)="{ item }">
        <div class="flex flex-wrap gap-1">
          <span 
            v-for="font in item.fonts" 
            :key="font.id" 
            class="flex items-center px-2 py-1 text-sm rounded-md border border-solid border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-950 text-gray-800 dark:text-gray-300"
          >
            <i :class="[getFontIconClass(font.name), 'mr-1.5']" aria-hidden="true"></i>
            {{ font.name }}
          </span>
          <span v-if="!item.fonts || item.fonts.length === 0" class="text-gray-400 dark:text-gray-600">-</span>
        </div>
      </template>
      
      <!-- Users cell with badges -->
      <template #cell(users)="{ item }">
        <div class="flex flex-wrap gap-1">
          <span 
            v-for="user in item.users" 
            :key="user.id" 
            class="flex items-center gap-1 px-2 py-1 text-sm rounded-full border border-solid border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-950 text-gray-800 dark:text-gray-300"
          >
            <Avatar :user="user" size="sm" :ring="false" class="w-5 h-5 -ml-1 mr-1" />
            {{ user.name }} {{ user.surname }}
          </span>
          <span v-if="!item.users || item.users.length === 0" class="text-gray-400 dark:text-gray-600">-</span>
        </div>
      </template>
      
      <!-- Start date (Deployed) cell -->
      <template #cell(start_date)="{ value }">
        <span class="text-xs">{{ value ? new Date(value).toLocaleDateString('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' }) : '-' }}</span>
      </template>
      
      <!-- End date (Upgrade) cell -->
      <template #cell(end_date)="{ value }">
        <span class="text-xs">{{ value ? new Date(value).toLocaleDateString('sk-SK', { day: '2-digit', month: '2-digit', year: 'numeric' }) : '-' }}</span>
      </template>
      
      <!-- Actions cell with view, edit and delete buttons -->
      <template #cell(actions)="{ item }">
        <div class="flex space-x-2 justify-center">
          <a 
            :href="`/admin/projects/${item.id}`" 
            class="text-green-600 dark:text-green-500 hover:text-green-900 dark:hover:text-green-400"
          >
            <i class="design design-eye text-lg"></i>
          </a>
          <button 
            type="button" 
            @click.stop="openEditModal(item)"
            class="text-blue-600 dark:text-blue-500 hover:text-blue-900 dark:hover:text-blue-400"
          >
            <i class="design design-edit text-lg"></i>
          </button>
          <form :action="`/admin/projects/${item.id}`" method="POST" @submit.stop="confirmDelete">
            <input type="hidden" name="_token" :value="csrfToken">
            <input type="hidden" name="_method" value="DELETE">
            <button type="submit" class="text-red-600 dark:text-red-500 hover:text-red-900 dark:hover:text-red-400">
              <i class="design design-trash text-lg"></i>
            </button>
          </form>
        </div>
      </template>
      
      <!-- Name cell with link to detail page -->
      <template #cell(name)="{ item }">
        <a 
          :href="`/admin/projects/${item.id}`" 
          class="gant-modern-bold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors block w-full"
          @click.stop
        >
          {{ item.name }}
        </a>
      </template>
      
      <!-- Custom row template to make the entire row clickable -->
      <template #row="{ item, rowIndex }">
        <tr 
          :class="['!border-b !border-gray-200 dark:!border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50', { 'bg-gray-50 dark:bg-gray-800/30': rowIndex % 2 === 0 }]"
          @click="navigateToProject(item)"
        >
          <td v-for="column in columns" :key="column.key" :class="column.tdClass || ''" class="p-3">
            <slot :name="`cell(${column.key})`" :item="item" :value="item[column.key]">
              {{ item[column.key] }}
            </slot>
          </td>
        </tr>
      </template>
    </DataTable>
  </div>
</template>

<style scoped>
/* Table styles */
:deep(table) {
  width: 100%;
  border-collapse: collapse;
}

:deep(thead tr) {
  border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

:deep(tbody tr) {
  border-bottom: 1px solid #e5e7eb !important; /* gray-200 */
}

:deep(.dark thead tr),
:deep(.dark tbody tr) {
  border-bottom: 1px solid #374151 !important; /* gray-700 */
}

:deep(tbody tr:hover) {
  background-color: rgba(249, 250, 251, 0.5); /* gray-50 */
}

:deep(.dark tbody tr:hover) {
  background-color: rgba(31, 41, 55, 0.5); /* gray-800/50 */
}

:deep(tbody tr:last-child) {
  border-bottom: none !important;
}

:deep(th), 
:deep(td) {
  padding: 0.75rem 1rem;
}

/* Override uppercase styling in table headers */
:deep(thead th) {
  text-transform: none !important;
}

/* Additional style to ensure normal case for header text */
:deep(.normal-case) {
  text-transform: none !important;
}
</style> 