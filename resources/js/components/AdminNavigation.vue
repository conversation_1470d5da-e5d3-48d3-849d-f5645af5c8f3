<template>
  <!-- Admin Navigation Tabs - Segmented Control Style -->
  <div class="mb-8">
    <!-- Responsive heading with smaller text on mobile -->
    <h2 class="text-3xl md:text-5xl lg:text-6xl xl:text-8xl gant-modern-bold leading-tight md:leading-none tracking-tighter mb-4 md:mb-12 dark:text-gray-200">
      Admin
    </h2>

    <!-- Mobile dropdown menu -->
    <div class="block md:hidden mb-4">
      <select 
        v-model="selectedTab" 
        @change="navigateTo"
        class="w-full p-2 rounded-lg bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
      >
        <option 
          v-for="(tab, index) in tabs" 
          :key="index" 
          :value="tab.route"
        >
          {{ tab.label }}
          {{ tab.badgeCount > 0 ? `(${tab.badgeCount})` : '' }}
        </option>
      </select>
    </div>

    <!-- Desktop horizontal tabs -->
    <div class="hidden md:inline-flex p-1 rounded-lg bg-gray-100 dark:bg-gray-800/70 shadow-sm flex-wrap gap-1">
      <a 
        v-for="(tab, index) in tabs" 
        :key="index"
        :href="tab.route" 
        :class="[
          'relative flex items-center px-3 lg:px-5 py-2 text-sm lg:text-base rounded-lg transition-all duration-200',
          isActive(tab.routeName) 
            ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-white gant-modern-bold' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 gant-modern-regular'
        ]"
      >
        <i :class="[
          'mr-2',
          tab.icon,
          isActive(tab.routeName) 
            ? 'text-gray-900 dark:text-gray-200' 
            : 'text-gray-400 dark:text-gray-300'
        ]"></i>
        <span>{{ tab.label }}</span>
        <span 
          v-if="tab.badgeCount > 0" 
          class="ml-2 text-xs gant-modern-medium px-2 py-0.5 rounded-full"
          :class="tab.badgeClass"
        >{{ tab.badgeCount }}</span>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminNavigation',
  props: {
    currentRoute: {
      type: String,
      required: true
    },
    needsUpdateCount: {
      type: Number,
      default: 0
    },
    newUsersCount: {
      type: Number,
      default: 0
    },
    activityCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      selectedTab: '',  // New data property for mobile dropdown
      routes: {
        'admin.index': '/admin',
        'admin.users': '/admin/users',
        'admin.projects': '/admin/projects',
        'admin.typography': '/admin/typography',
        'admin.assets': '/admin/assets',
        'admin.tools': '/admin/tools',
        'admin.code': '/admin/code',
        'admin.animations': '/admin/animations',
        'admin.icons': '/admin/icons',
        'admin.tags': '/admin/tags',
        'admin.activities': '/admin/activities',
        'admin.changelog': '/admin/changelog',
        'admin.whiteboard-statuses.index': '/admin/whiteboard-statuses',
        'admin.morph': '#'
      }
    };
  },
  computed: {
    tabs() {
      return [
        {
          label: 'Main',
          route: this.getRoute('admin.index'),
          routeName: 'admin.index',
          icon: 'design design-mn',
          badgeCount: this.needsUpdateCount || 0,
          badgeClass: 'bg-red-600 text-white'
        },
        {
          label: 'Icons',
          route: this.getRoute('admin.icons'),
          routeName: 'admin.icons',
          icon: 'design  design-component2',
          badgeCount: this.needsUpdateCount || 0,
          badgeClass: 'bg-red-600 text-white'
        },
        {
          label: 'Users',
          route: this.getRoute('admin.users'),
          routeName: 'admin.users',
          icon: 'design design-vermont-ucko',
          badgeCount: this.newUsersCount || 0,
          badgeClass: 'bg-red-600 text-white'
        },
        {
          label: 'Projects',
          route: this.getRoute('admin.projects'),
          routeName: 'admin.projects',
          icon: 'design design-service',
          badgeCount: 0,
          badgeClass: 'bg-red-600 text-white'
        },
        {
          label: 'Whiteboard',
          route: this.routes['admin.whiteboard-statuses.index'] || '/admin/whiteboard-statuses',
          routeName: 'admin.whiteboard-statuses.index',
          icon: 'design design-resolution_monitor',
          badgeCount: 0,
        },
        {
          label: 'Changelog',
          route: this.getRoute('admin.changelog'),
          routeName: 'admin.changelog',
          icon: 'wms wms-browse-history-outline',
          badgeCount: 0,
          badgeClass: 'bg-red-600 text-white'
        },
        // {
        //   label: 'Typo',
        //   route: this.getRoute('admin.typography'),
        //   routeName: 'admin.typography',
        //   icon: 'design design-letter',
        //   badgeCount: 0,
        //   badgeClass: 'bg-red-600 text-white'
        // },
        // {
        //   label: 'Assets',
        //   route: this.getRoute('admin.assets'),
        //   routeName: 'admin.assets',
        //   icon: 'design design-download-file-outline',
        //   badgeCount: 0,
        //   badgeClass: 'bg-red-600 text-white'
        // },
        // {
        //   label: 'Tools',
        //   route: this.getRoute('admin.tools'),
        //   routeName: 'admin.tools',
        //   icon: 'design design-svg-stuff',
        //   badgeCount: 0,
        //   badgeClass: 'bg-red-600 text-white'
        // },  
        // {
        //   label: 'Code',
        //   route: this.getRoute('admin.code'),
        //   routeName: 'admin.code',
        //   icon: 'design design-bracket2',
        //   badgeCount: 0,
        //   badgeClass: 'bg-red-600 text-white'
        // },
        // {
        //   label: 'Anim',
        //   route: this.getRoute('admin.animations'),
        //   routeName: 'admin.animations',
        //   icon: 'design design-transparency',
        //   badgeCount: 0,
        //   badgeClass: 'bg-red-600 text-white'
        // },
        {
          label: 'History',
          route: this.getRoute('admin.activities'),
          routeName: 'admin.activities',
          icon: 'wms wms-browse-history-outline',
          badgeCount: 0,
          badgeClass: 'bg-red-600 text-white'
        }
      ];
    }
  },
  mounted() {
    // Update routes if window.route is available
    if (window.route) {
      try {
        this.routes['admin.index'] = window.route('admin.index');
        this.routes['admin.users'] = window.route('admin.users');
        this.routes['admin.projects'] = window.route('admin.projects');
        this.routes['admin.changelog'] = window.route('admin.changelog');
        this.routes['admin.typography'] = window.route('admin.typography');
        this.routes['admin.assets'] = window.route('admin.assets');
        this.routes['admin.tools'] = window.route('admin.tools');
        this.routes['admin.code'] = window.route('admin.code');
        this.routes['admin.animations'] = window.route('admin.animations');
        this.routes['admin.icons'] = window.route('admin.icons');
        this.routes['admin.activities'] = window.route('admin.activities');
        this.routes['admin.whiteboard-statuses.index'] = window.route('admin.whiteboard-statuses.index');
      } catch (e) {
        console.error('Error getting routes:', e);
      }
    }

    // Set initial selected tab based on current route
    const currentTab = this.tabs.find(tab => this.isActive(tab.routeName));
    if (currentTab) {
      this.selectedTab = currentTab.route;
    }
  },
  methods: {
    isActive(routeName) {
      return this.currentRoute === routeName;
    },
    getRoute(name) {
      // First try to use window.route, fallback to our predefined routes
      if (window.route) {
        try {
          return window.route(name);
        } catch (e) {
          console.error(`Error getting route ${name}:`, e);
        }
      }
      return this.routes[name] || '#';
    },
    // New method to handle mobile navigation
    navigateTo() {
      if (this.selectedTab) {
        window.location.href = this.selectedTab;
      }
    }
  }
}
</script> 
