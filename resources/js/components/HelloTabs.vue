<template>
  <div class="relative z-[50] pointer-events-auto w-full" tabindex="0" @keydown="handleKeydown" ref="tabsContainer">
    <!-- Segmented Tab Controls -->
    <div class="flex justify-center w-full mb-6">
      <div class="inline-flex p-0.5 gap-1 backdrop-blur-lg bg-white/30 dark:bg-gray-950/30 rounded-xl ring-1 ring-gray-200/50 dark:ring-gray-700/50 z-[50] gant-modern-regular">
        <button
          @click="setSelected('commands')"
          :class="tabClasses('commands')"
          ref="commandsTab"
        >
         <i class="design design-command-pallete mr-2 opacity-60"></i>Search
        </button>
        <button
          @click="setSelected('bookmarks')"
          :class="tabClasses('bookmarks')"
          ref="bookmarksTab"
        >
          <i class="design design-bookmark mr-2 opacity-60"></i>Bookmarks
        </button>
      </div>
    </div>

    <!-- Tab Panels -->
    <div class="relative z-40 pointer-events-auto w-full">
      <div v-if="selected === 'commands'" class="mx-auto max-w-3xl">
        <command-palette :is-admin="isAdmin" autofocus />
      </div>
      <div v-else class="w-full">
        <bookmarks />
      </div>
    </div>
  </div>
</template>

<script>
import CommandPalette from './CommandPalette.vue';
import Bookmarks from './Bookmarks.vue';

export default {
  name: 'HelloTabs',
  components: { CommandPalette, Bookmarks },
  props: {
    isAdmin: { type: Boolean, default: false }
  },
  data() {
    return { 
      selected: this.getSavedTab() || 'commands', 
      tabs: ['commands', 'bookmarks']
    };
  },
  methods: {
    tabClasses(tab) {
      const base = 'px-4 py-2 rounded-lg focus:outline-none transition-colors duration-200 ';
      const active = 'bg-white dark:bg-slate-950 text-slate-900 dark:text-slate-100 gant-modern-bold';
      const inactive = 'text-slate-200 dark:text-slate-800 hover:bg-white/50 dark:hover:bg-slate-800/50';
      return tab === this.selected ? `${base} ${active}` : `${base} ${inactive}`;
    },
    setSelected(tab) {
      this.selected = tab;
      this.saveTabPreference(tab);
    },
    getSavedTab() {
      try {
        return localStorage.getItem('vermont_design_selected_tab');
      } catch (e) {
        console.error('Failed to read from localStorage:', e);
        return null;
      }
    },
    saveTabPreference(tab) {
      try {
        localStorage.setItem('vermont_design_selected_tab', tab);
      } catch (e) {
        console.error('Failed to save to localStorage:', e);
      }
    },
    handleKeydown(event) {
      // Handle left/right arrow keys
      if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
        event.preventDefault();
        
        const currentIndex = this.tabs.indexOf(this.selected);
        let newIndex;
        
        if (event.key === 'ArrowLeft') {
          // Left arrow - move to previous tab, wrap to end if at first
          newIndex = currentIndex === 0 ? this.tabs.length - 1 : currentIndex - 1;
        } else {
          // Right arrow - move to next tab, wrap to beginning if at last
          newIndex = currentIndex === this.tabs.length - 1 ? 0 : currentIndex + 1;
        }
        
        this.setSelected(this.tabs[newIndex]);
        
        // Focus the tab button for better accessibility
        this.$nextTick(() => {
          const tabRefs = {
            'commands': this.$refs.commandsTab,
            'bookmarks': this.$refs.bookmarksTab
          };
          
          if (tabRefs[this.selected]) {
            tabRefs[this.selected].focus();
          }
        });
      }
    }
  },
  mounted() {
    // Focus the container to enable keyboard navigation
    this.$refs.tabsContainer.focus();
  }
};
</script>

<style scoped>
/* Focus outline for the container but only when keyboard navigating */

div[tabindex="0"]:focus:not(:focus-visible) {
  outline: none;
}

button:focus {
  outline: none;
}
</style> 