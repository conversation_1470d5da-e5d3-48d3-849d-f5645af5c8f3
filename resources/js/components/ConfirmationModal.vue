<template>
  <div class="fixed inset-0 bg-black dark:bg-slate-950 bg-opacity-50 dark:bg-opacity-80 backdrop-blur-md flex items-center justify-center p-4 z-50">
    <div class="bg-white dark:bg-slate-950 rounded-lg shadow-xl max-w-md w-full">
      <div class="px-6 py-4 border-b dark:border-slate-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Confirm Deletion
        </h3>
      </div>
      
      <div class="px-6 py-4">
        <p class="text-gray-700 dark:text-gray-300">
          Are you sure you want to delete the card "{{ card.headline }}"? This action cannot be undone.
        </p>
      </div>
      
      <div class="px-6 py-4 border-t dark:border-slate-700 flex justify-end space-x-3">
        <button 
          @click="$emit('close')" 
          class="px-4 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700 focus:outline-none"
        >
          Cancel
        </button>
        <button 
          @click="$emit('confirm')" 
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none"
        >
          Delete
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    card: {
      type: Object,
      required: true
    }
  }
}
</script> 