<template>
  <form :action="route" method="POST" class="space-y-4 w-full">
    <input type="hidden" name="_token" :value="csrf">

    <!-- Login -->
    <div class="w-full">
      <label for="login" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ loginLabel }}</label>
      <div class="mt-1.5 input-wrapper">
        <span class="input-icon">
          <i class="design design-vermont-ucko text-gray-400 dark:text-gray-500"></i>
        </span>
        <input 
          id="login" 
          ref="loginInput"
          v-model="loginValue"
          class="block w-full bg-transparent py-3 rounded-md text-gray-900 dark:text-white border border-solid border-gray-200 dark:border-gray-700 placeholder-gray-500 focus:ring-2 focus:ring-gray-900 dark:focus:ring-gray-200 sm:text-sm input-field input-field-with-icon "
          type="text" 
          name="login" 
          placeholder="uXXXX"
          required 
          autocomplete="username" 
        >
      </div>
    </div>

    <!-- Password -->
    <div class="w-full">
      <label for="password" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ passwordLabel }}</label>
      <div class="mt-1.5 input-wrapper">
        <span class="input-icon">
          <i class="design design-vermont-intranet-pass text-gray-400 dark:text-gray-500"></i>
        </span>
        <input 
          id="password" 
          v-model="passwordValue"
          class="block w-full bg-transparent py-3 rounded-md text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-gray-900 dark:focus:ring-gray-200 sm:text-sm input-field input-field-with-icon border border-solid border-gray-400 dark:border-gray-700"
          type="password"
          name="password"
          placeholder="••••••••"
          required 
          autocomplete="current-password" 
        >
      </div>
    </div>

    <!-- Remember Me -->
    <div class="flex items-center justify-end gap-2">
      <label for="remember_me" class="text-sm text-gray-700 dark:text-gray-300">
        {{ rememberLabel }}
      </label>
      <label class="relative inline-block w-9 h-5">
        <input 
          id="remember_me" 
          type="checkbox"
          name="remember"
          v-model="rememberMe"
          class="sr-only peer"
        >
        <div class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-700 rounded-full transition-all duration-300 peer-checked:bg-[#0D1220] dark:peer-checked:bg-white before:content-[''] before:absolute before:h-4 before:w-4 before:left-0.5 before:bottom-0.5 before:bg-white before:rounded-full before:transition-all before:duration-300 peer-checked:before:translate-x-4 dark:peer-checked:before:bg-[#313131]"></div>
        <i class="claims claims-okay absolute text-[10px] top-1/2 -translate-y-1/2 left-[21px] opacity-0 transition-opacity duration-300 text-gray-900 dark:text-white peer-checked:opacity-100 z-10"></i>
      </label>

    </div>

    <div class="mt-12 py-8 w-full">
      <button 
        type="submit" 
        :disabled="!isFormValid"
        :class="[
          'w-full py-2.5 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2',
          isFormValid 
            ? 'bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100'
            : 'bg-transparent border text-gray-300 dark:text-gray-600 border-gray-200 dark:border-gray-700 cursor-not-allowed'
        ]"
      >
        <span>{{ signInLabel }}</span>
        <i class="design design-arrow-right text-lg"></i>
      </button>
    </div>

    <div v-if="hasPasswordRequest" class="relative">
      <a class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" :href="passwordRequestRoute">
        {{ forgotPasswordLabel }}
      </a>
    </div>
  </form>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  route: String,
  csrf: String,
  oldLogin: String,
  loginLabel: String,
  passwordLabel: String,
  rememberLabel: String,
  signInLabel: String,
  forgotPasswordLabel: String,
  hasPasswordRequest: Boolean,
  passwordRequestRoute: String
})

const loginInput = ref(null)
const loginValue = ref(props.oldLogin || '')
const passwordValue = ref('')
const rememberMe = ref(false)

const isFormValid = computed(() => {
  return loginValue.value.trim() !== '' && passwordValue.value.trim() !== ''
})

onMounted(() => {
  loginInput.value?.focus()
})
</script>
