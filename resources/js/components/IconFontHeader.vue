<template>
  <div class="font-gant-modern mb-8">
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center">
      <div>
        <p class="opacity-50 dark:text-white">
                        {{ subheadline }}
                    </p>
        <h2 class="text-5xl lg:text-6xl gant-modern-bold dark:text-white">{{ title }}</h2>
        <div class=" text-gray-500 dark:text-gray-400">
          <i class="design design-commit opacity-50 me-1"></i>Last update: <span class="gant-modern-bold text-gray-900 dark:text-white me-6">{{ lastUpdate }}</span> <i class="design design-component opacity-50 me-1"></i>Icons count: <span class="gant-modern-bold text-gray-900 dark:text-white">{{ iconsCount }}</span>
        </div>
      </div>
      <div class="mt-4 lg:mt-0 flex flex-wrap gap-2">
        <button @click="downloadFont" :disabled="downloadSuccess" :class="['inline-block px-4 py-3 rounded-lg gant-modern-bold text-sm transition-all duration-200 disabled:opacity-75', downloadSuccess ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : 'text-gray-900 bg-gray-100 hover:bg-gray-200 dark:bg-gray-900 dark:text-gray-200 dark:hover:bg-gray-800']">
          <i :class="['design', downloadSuccess ? 'design-circle-checked text-green-500' : 'design-download-file text-gray-400', 'mr-1 transition-all duration-200']" aria-hidden="true"></i>
          <span :class="{ 'opacity-0': downloadSuccess, 'opacity-100': !downloadSuccess, 'transition-opacity duration-200': true }">Download Font</span>
          <span :class="{ 'opacity-100': downloadSuccess, 'opacity-0': !downloadSuccess, 'transition-opacity duration-200 absolute': true }">Downloaded!</span>
        </button>
        <button @click="copyScss" :disabled="copySuccess" :class="['inline-block px-4 py-3 rounded-lg gant-modern-bold text-sm transition-all duration-200 disabled:opacity-75', copySuccess ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : 'text-gray-900 bg-gray-100 hover:bg-gray-200 dark:bg-gray-900 dark:text-gray-200 dark:hover:bg-gray-800']">
          <i :class="['design', copySuccess ? 'design-circle-checked text-green-500' : 'design-copyclipboard text-gray-400', 'mr-1 transition-all duration-200']" aria-hidden="true"></i>
          <span :class="{ 'opacity-0': copySuccess, 'opacity-100': !copySuccess, 'transition-opacity duration-200': true }">Copy SCSS</span>
          <span :class="{ 'opacity-100': copySuccess, 'opacity-0': !copySuccess, 'transition-opacity duration-200 absolute': true }">Copied!</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.scale-110 {
  transform: scale(1.9);
}

button {
  position: relative;
}

button span {
  display: inline-block;
}

button span[class*="absolute"] {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
</style>

<script>
import axios from 'axios'

export default {
  name: 'IconFontHeader',
  data() {
    return {
      copySuccess: false,
      downloadSuccess: false,
      localScssContent: null,
      isLoadingScss: false
    };
  },
  props: {
    title: {
      type: String,
      required: true
    },
    fontName: {
      type: String,
      required: false,
      default: ''
    },
    fontClass: {
      type: String,
      required: false,
      default: ''
    },
    fontId: {
      type: String,
      required: false,
      default: ''
    },
    lastUpdate: {
      type: String,
      required: true
    },
    iconsCount: {
      type: [String, Number],
      required: true,
      default: '0'
    },
    downloadFontUrl: {
      type: String,
      required: true
    },
    indexLink: {
      type: String,
      required: true
    },
    webfontLink: {
      type: String,
      required: true
    },
    scssContent: {
      type: String,
      required: false,
      default: ''
    },
    hash: {
      type: String,
      required: false,
      default: ''
    },
    subheadline: {
      type: String,
      required: false,
      default: ''
    }
  },
  computed: {
    processedScssContent() {
      return this.scssContent || this.localScssContent || '';
    }
  },
  methods: {
    downloadFont() {
      window.location.href = this.downloadFontUrl;
      this.downloadSuccess = true;
      setTimeout(() => {
        this.downloadSuccess = false;
      }, 2000);
    },
    async copyScss() {
      try {
        if (!this.processedScssContent && !this.localScssContent) {
          await this.loadScssContent();
        }
        
        const contentToCopy = this.processedScssContent || '';
        await navigator.clipboard.writeText(contentToCopy);
        this.copySuccess = true;
        setTimeout(() => {
          this.copySuccess = false;
        }, 2000);
      } catch (err) {
        console.error('Failed to copy SCSS:', err);
      }
    },
    async loadScssContent() {
      if (this.isLoadingScss) return;
      
      this.isLoadingScss = true;
      try {
        // Use fontName for the API call if available, otherwise fall back to title
        const fontIdentifier = this.fontName || this.title;
        const response = await fetch(`/api/scss/${fontIdentifier.toLowerCase()}`);
        if (!response.ok) {
          // Try with fontClass if the first attempt fails
          if (this.fontClass) {
            const secondAttempt = await fetch(`/api/scss/${this.fontClass.toLowerCase()}`);
            if (secondAttempt.ok) {
              this.localScssContent = await secondAttempt.text();
              this.isLoadingScss = false;
              return;
            }
          }
          throw new Error('Failed to load SCSS content');
        }
        this.localScssContent = await response.text();
      } catch (err) {
        console.error('Failed to load SCSS content:', err);
        this.localScssContent = '// Error loading SCSS content';
      } finally {
        this.isLoadingScss = false;
      }
    },
    async updateViewCount() {
      if (!this.fontId) {
        console.warn('Font ID is not provided');
        return;
      }
      
      try {
        await axios.post(`/fonts/${this.fontId}/view`);
      } catch (error) {
        // Silently handle auth errors as view count is not critical
        if (error.response?.status !== 401) {
          console.error('Failed to update view count:', error);
        }
      }
    }
  },
  created() {
    // If scss content isn't provided as a prop, load it from the API
    if (!this.scssContent) {
      this.loadScssContent();
    }
  },
  mounted() {
    if (this.fontId) {
      this.updateViewCount();
    }
  }
}
</script>
