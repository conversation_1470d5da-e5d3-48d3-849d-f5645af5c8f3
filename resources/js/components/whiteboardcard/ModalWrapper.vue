<template>
  <transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 scale-95"
    enter-to-class="opacity-100 scale-100"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 scale-100" 
    leave-to-class="opacity-0 scale-95"
  >
    <div class="fixed inset-0 bg-white dark:bg-slate-950 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-md flex items-center justify-center p-6 z-50">
      <div class="bg-white dark:bg-slate-950 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-in-out">
        <slot></slot>
      </div>
    </div>
  </transition>
</template>

<script setup>
// This component is a simple wrapper for the modal with transition effects
</script> 