<template>
  <div class="flex items-center justify-between px-6 py-5 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-900 z-10">
    <!-- Left side with title -->
    <div class="flex items-center">
      <i class="design design-card-edit mr-2 text-gray-400 dark:text-gray-500"></i>
      <h2 class="text-xl font-medium text-gray-900 dark:text-white">{{ title }}</h2>
    </div>
    
    <!-- Close button -->
    <button
      type="button"
      @click="$emit('close')"
      class="p-1.5 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
      aria-label="Close modal"
    >
      <i class="design design-x h-5 w-5"></i>
    </button>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  }
});

defineEmits(['close']);
</script> 