<template>
  <div class="px-6 py-4 border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-800">
    <div class="flex flex-col sm:flex-row items-start sm:items-center">
      <div class="inline-flex p-1 rounded-lg bg-gray-100 dark:bg-gray-800 shadow-sm">
        <button 
          v-for="tab in tabs"
          :key="tab.id"
          @click="$emit('change-tab', tab.id)" 
          :class="[
            'flex items-center px-4 py-2 text-sm rounded-md transition-all duration-200',
            activeTab === tab.id 
              ? 'bg-white dark:bg-slate-700 shadow-sm text-gray-900 dark:text-white font-medium' 
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
          ]"
          :title="`${tab.name} (Alt+${tab.shortcutKey})`"
        >
          <i :class="[tab.icon, 'mr-2']"></i>
          <span>{{ tab.name }}</span>
          <span class="ml-2 text-xs text-gray-400 dark:text-gray-500">{{ tab.shortcutKey }}</span>
        </button>
      </div>
      
      <!-- Tab Indicators (Mobile) -->
      <div class="flex items-center space-x-1 mt-2 sm:hidden">
        <span 
          v-for="tab in tabs" 
          :key="tab.id"
          class="w-2 h-2 rounded-full transition-all"
          :class="activeTab === tab.id ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'"
        ></span>
      </div>
      
      <!-- Keyboard Shortcuts Help -->
      <div class="ml-auto mt-3 sm:mt-0 text-xs text-gray-500 dark:text-gray-400 hidden sm:flex items-center">
        <button @click="toggleHelp" class="mr-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors" :class="{ 'bg-gray-100 dark:bg-slate-700': showHelp }">
          <i class="design design-help-circle"></i>
        </button>
        
        <div v-if="showHelp" class="flex items-center space-x-4">
          <div>
            <span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Alt+1,2,3</span> 
            Switch Tabs
          </div>
          <div>
            <span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Ctrl+←→</span> 
            Navigate Tabs
          </div>
          <div>
            <span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">Ctrl+Enter</span> 
            Save
          </div>
          <div>
            <span class="bg-gray-200 dark:bg-gray-700 rounded px-1.5 py-0.5 mr-1">?</span> 
            All Shortcuts
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['change-tab']);

const showHelp = ref(false);

const toggleHelp = () => {
  showHelp.value = !showHelp.value;
};

const tabs = [
  {
    id: 'details',
    name: 'Details',
    icon: 'design design-file-text-fill',
    shortcutKey: '1'
  },
  {
    id: 'links',
    name: 'Links',
    icon: 'design design-link2',
    shortcutKey: '2'
  },
  {
    id: 'assignments',
    name: 'Assignments',
    icon: 'design design-user-multiple',
    shortcutKey: '3'
  }
];
</script> 