<template>
  <!-- Navigation Tabs - Segmented Control Style -->
  <div class="flex items-center p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
    <div class="inline-flex p-1 rounded-lg bg-gray-100 dark:bg-gray-700 shadow-sm">
      <button 
        v-for="tab in tabs"
        :key="tab.id"
        @click="$emit('change-tab', tab.id)" 
        :class="[
          'flex items-center px-3 py-2 text-sm rounded-md transition-all duration-200',
          activeTab === tab.id 
            ? 'bg-white dark:bg-gray-600 shadow-sm text-gray-900 dark:text-white font-medium' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300'
        ]"
      >
        <i :class="[tab.icon, 'mr-1.5']"></i>
        <span>{{ tab.name }}</span>
      </button>
    </div>
    
    <!-- View/Edit mode toggle -->
    <div class="ml-auto">
      <view-mode-toggle 
        :active-mode="viewMode"
        @change="$emit('change-view-mode', $event)"
      />
    </div>
  </div>
</template>

<script setup>
// No imports needed since defineProps and defineEmits are compiler macros
import ViewModeToggle from './ViewModeToggle.vue';

const props = defineProps({
  activeTab: {
    type: String,
    required: true
  },
  viewMode: {
    type: String,
    default: 'view'
  }
});

defineEmits(['change-tab', 'change-view-mode']);

const tabs = [
  {
    id: 'details',
    name: 'Details',
    icon: 'design design-file-text-fill'
  },
  {
    id: 'links',
    name: 'Links & Media',
    icon: 'design design-link'
  },
  {
    id: 'assignments',
    name: 'Assignments',
    icon: 'design design-users'
  }
];
</script> 