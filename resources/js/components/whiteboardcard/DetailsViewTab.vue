<template>
  <div class="space-y-6">
    <!-- Headline -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">{{ card.headline }}</h3>
      
      <!-- Status Badge -->
      <div class="flex items-center mb-4">
        <span 
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="statusColorClasses"
        >
          {{ statusName }}
        </span>
      </div>
      
      <!-- Description if available -->
      <div v-if="card.description" class="prose dark:prose-invert prose-sm max-w-none mt-2" v-html="formattedDescription"></div>
    </div>
    
    <!-- Project Information -->
    <div v-if="card.project" class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Project</h4>
      <div class="flex items-center">
        <div 
          class="w-8 h-8 rounded-md bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden mr-2"
          v-if="card.project.logo"
        >
          <img :src="card.project.logo" :alt="card.project.name" class="w-full h-full object-cover">
        </div>
        <div v-else class="w-8 h-8 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 flex items-center justify-center mr-2">
          <i class="design design-briefcase"></i>
        </div>
        <span class="text-gray-900 dark:text-white">{{ card.project.name }}</span>
      </div>
    </div>
    
    <!-- Date Information -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <div class="grid grid-cols-2 gap-4">
        <!-- Card Date -->
        <div>
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</h4>
          <div class="flex items-center">
            <i class="design design-calendar mr-1.5 text-gray-400 dark:text-gray-500"></i>
            <span class="text-gray-900 dark:text-white">{{ formatDate(card.card_date) }}</span>
          </div>
        </div>
        
        <!-- Deadline if available -->
        <div v-if="card.deadline">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Deadline</h4>
          <div class="flex items-center">
            <i class="design design-clock-fill mr-1.5" 
              :class="isDeadlinePast ? 'text-red-500' : 'text-gray-400 dark:text-gray-500'"></i>
            <span :class="isDeadlinePast ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-white'">
              {{ formatDate(card.deadline) }}
              <span v-if="isDeadlinePast" class="text-xs ml-1">(Past due)</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  card: {
    type: Object,
    required: true
  },
  statusOptions: {
    type: Array,
    default: () => []
  },
  getStatusColor: {
    type: Function,
    default: () => 'gray'
  }
});

// Format date to display in a user-friendly way
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Calculate if deadline is in the past
const isDeadlinePast = computed(() => {
  if (!props.card.deadline) return false;
  
  const deadline = new Date(props.card.deadline);
  const today = new Date();
  return deadline < today;
});

// Get status name from the status option
const statusName = computed(() => {
  const status = props.statusOptions.find(s => s.slug === props.card.status);
  return status ? status.name : 'Unknown';
});

// Get status color classes based on status
const statusColorClasses = computed(() => {
  const color = props.getStatusColor(props.card.status);
  return {
    'bg-green-100 text-green-800': color === 'green',
    'bg-yellow-100 text-yellow-800': color === 'yellow',
    'bg-red-100 text-red-800': color === 'red',
    'bg-blue-100 text-blue-800': color === 'blue',
    'bg-purple-100 text-purple-800': color === 'purple',
    'bg-gray-100 text-gray-800': color === 'gray'
  };
});

// Format description for HTML display
const formattedDescription = computed(() => {
  if (!props.card.description) return '';
  return props.card.description.replace(/\n/g, '<br>');
});
</script> 