<template>
  <div class="space-y-6">
    <!-- Main Link -->
    <div v-if="card.link" class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Main Link</h4>
      <div class="flex items-center">
        <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3 overflow-hidden">
          <img 
            v-if="card.image && card.image !== 'link.svg'"
            :src="`/img/category/${card.image}`" 
            :alt="getLinkDomain(card.link)"
            class="w-full h-full object-contain"
            @error="handleImageError"
          >
          <i v-else class="design design-link2 text-gray-500 dark:text-gray-400 text-xl"></i>
        </div>
        <div class="flex-1">
          <a :href="card.link" 
             :target="card.link_target || '_blank'" 
             class="text-blue-600 dark:text-blue-400 hover:underline break-all block"
          >
            {{ formatLink(card.link) }}
          </a>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ getLinkDomain(card.link) }}
          </span>
        </div>
        <a :href="card.link" 
           :target="card.link_target || '_blank'"
           class="ml-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-md"
           title="Open link"
        >
          <i class="design design-external-link"></i>
        </a>
      </div>
    </div>
    
    <!-- Additional Links -->
    <div v-if="card.additional_links && card.additional_links.length > 0" class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Additional Links</h4>
      <div class="space-y-3">
        <div 
          v-for="(link, index) in card.additional_links" 
          :key="index"
          class="flex items-center"
        >
          <div class="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3 overflow-hidden">
            <img 
              v-if="link.image"
              :src="`/img/category/${link.image}`" 
              :alt="getLinkDomain(link.url)"
              class="w-full h-full object-contain"
              @error="handleImageError"
            >
            <i v-else class="design design-link2 text-gray-500 dark:text-gray-400"></i>
          </div>
          
          <div class="flex-1">
            <a :href="link.url" 
               :target="link.target || '_blank'" 
               class="text-blue-600 dark:text-blue-400 hover:underline break-all block"
            >
              {{ link.title || formatLink(link.url) }}
            </a>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ getLinkDomain(link.url) }}
            </span>
          </div>
          
          <a :href="link.url" 
             :target="link.target || '_blank'"
             class="ml-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-md"
             title="Open link"
          >
            <i class="design design-external-link"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  card: {
    type: Object,
    required: true
  }
});

// Format link to display in a user-friendly way
const formatLink = (url) => {
  if (!url) return '';
  
  // Remove protocol
  let formatted = url.replace(/^https?:\/\//, '');
  // Remove trailing slash
  formatted = formatted.replace(/\/$/, '');
  
  return formatted;
};

// Extract domain from URL
const getLinkDomain = (url) => {
  if (!url) return '';
  
  try {
    const domain = new URL(url).hostname;
    return domain;
  } catch (e) {
    // Handle invalid URLs
    return url;
  }
};

// Handle image loading errors
const handleImageError = (e) => {
  // Replace with default icon
  e.target.style.display = 'none';
  const iconElement = document.createElement('i');
  iconElement.className = 'design design-link2 text-gray-500 dark:text-gray-400';
  e.target.parentNode.appendChild(iconElement);
};
</script> 