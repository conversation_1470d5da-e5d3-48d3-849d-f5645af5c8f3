<template>
  <div class="space-y-6">
    <!-- Assigned Users -->
    <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <label class="text-sm gant-modern-medium text-gray-900 dark:text-white flex items-center">
          <i class="design design-user-multiple mr-2 opacity-70"></i>
          Assigned Users
        </label>
        <span class="text-xs text-gray-500 dark:text-gray-400 px-2 py-0.5 bg-gray-100 dark:bg-slate-800 rounded-full">
          {{ selectedUserIds.length }} selected
        </span>
      </div>
      
      <!-- Search Users -->
      <div class="relative mb-2">
        <input
          type="text"
          v-model="userSearch"
          class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white text-sm"
          placeholder="Search users..."
        />
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="design design-search-fill text-gray-400 dark:text-gray-500"></i>
        </div>
      </div>
      
      <!-- Select All / Clear All Controls -->
      <div class="flex justify-end mb-2 space-x-2">
        <button 
          type="button"
          @click="selectAllUsers"
          class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Select All
        </button>
        <span class="text-gray-300 dark:text-gray-600">|</span>
        <button 
          type="button"
          @click="clearAllUsers"
          class="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
        >
          Clear All
        </button>
      </div>
      
      <!-- Users Selection - Grid Layout -->
      <div class="border border-gray-300 dark:border-slate-700 rounded-md shadow-sm p-3 max-h-80 overflow-y-auto bg-white dark:bg-slate-800">
        <div v-if="filteredUsers.length === 0" class="text-gray-500 dark:text-gray-400 text-sm text-center py-4">
          No users found
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
          <div 
            v-for="user in filteredUsers" 
            :key="user.id"
            @click="toggleUserSelection(user.id)" 
            class="flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid rounded-lg p-2 relative cursor-pointer transition-all duration-200 min-h-[90px]"
            :class="selectedUserIds.includes(user.id) 
              ? 'border-slate-800 dark:border-slate-400 bg-gray-50 dark:bg-slate-800/30' 
              : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-950/50'"
          >
            <div class="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mb-1 border border-gray-300 dark:border-gray-600">
              <span class="text-sm gant-modern-medium text-gray-800 dark:text-gray-300">{{ getUserInitials(user.name) }}</span>
            </div>
            <span class="mt-1 text-xs gant-modern-medium text-gray-700 dark:text-gray-300 text-center truncate w-full">{{ user.name }}</span>
            <span class="text-xs gant-modern-regular text-gray-500 dark:text-gray-400 truncate w-full text-center">{{ user.email }}</span>
            
            <!-- Selection indicator (single checkmark) -->
            <div 
              v-if="selectedUserIds.includes(user.id)" 
              class="absolute top-1 right-1 bg-slate-800 text-white dark:bg-slate-600 rounded-full p-0.5"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Associated Fonts -->
    <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4">
      <div class="flex items-center justify-between mb-3">
        <label class="text-sm gant-modern-medium text-gray-900 dark:text-white flex items-center">
          <i class="design design-format-list-checks mr-2 opacity-70"></i> 
          Associated Fonts
        </label>
        <span class="text-xs text-gray-500 dark:text-gray-400 px-2 py-0.5 bg-gray-100 dark:bg-slate-800 rounded-full">
          {{ selectedFontIds.length }} selected
        </span>
      </div>
      
      <!-- Select All / Clear All Controls -->
      <div class="flex justify-end mb-2 space-x-2">
        <button 
          type="button"
          @click="selectAllFonts"
          class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Select All
        </button>
        <span class="text-gray-300 dark:text-gray-600">|</span>
        <button 
          type="button"
          @click="clearAllFonts"
          class="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
        >
          Clear All
        </button>
      </div>
      
      <!-- Fonts Selection - Grid Layout -->
      <div class="border border-gray-300 dark:border-slate-700 rounded-md shadow-sm p-3 max-h-80 overflow-y-auto bg-white dark:bg-slate-800">
        <div v-if="fonts.length === 0" class="text-gray-500 dark:text-gray-400 text-sm text-center py-4">
          No fonts found
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
          <div 
            v-for="font in fonts" 
            :key="font.id"
            @click="toggleFontSelection(font.id)" 
            class="flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid rounded-lg p-2 relative cursor-pointer transition-all duration-200 min-h-[90px]"
            :class="selectedFontIds.includes(font.id) 
              ? 'border-slate-800 dark:border-slate-400 bg-gray-50 dark:bg-slate-800/30' 
              : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-950/50'"
          >
            <i :class="[getFontIconClass(font.name), 'text-3xl text-gray-900 dark:text-white mb-1']"></i>
            <span class="text-xs gant-modern-medium text-gray-700 dark:text-gray-300 text-center truncate w-full">{{ font.name }}</span>
            
            <!-- Selection indicator (single checkmark) -->
            <div 
              v-if="selectedFontIds.includes(font.id)" 
              class="absolute top-1 right-1 bg-slate-800 text-white dark:bg-slate-600 rounded-full p-0.5"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  users: {
    type: Array,
    required: true
  },
  fonts: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

// Local state
const userSearch = ref('');
const selectedUserIds = ref([]);
const selectedFontIds = ref([]);

// Initialize selectedUserIds and selectedFontIds from modelValue
selectedUserIds.value = props.modelValue.user_ids || [];
selectedFontIds.value = props.modelValue.font_ids || [];

// Filtered users based on search
const filteredUsers = computed(() => {
  if (!userSearch.value) return props.users;
  
  const search = userSearch.value.toLowerCase();
  return props.users.filter(user => 
    user.name.toLowerCase().includes(search) || 
    user.email.toLowerCase().includes(search)
  );
});

// Update v-model when selections change
const updateModel = () => {
  emit('update:modelValue', {
    ...props.modelValue,
    user_ids: selectedUserIds.value,
    font_ids: selectedFontIds.value
  });
};

// User selection methods
const selectAllUsers = () => {
  selectedUserIds.value = props.users.map(user => Number(user.id));
  updateModel();
};

const clearAllUsers = () => {
  selectedUserIds.value = [];
  updateModel();
};

const toggleUserSelection = (userId) => {
  if (selectedUserIds.value.includes(userId)) {
    selectedUserIds.value = selectedUserIds.value.filter(id => id !== userId);
  } else {
    selectedUserIds.value.push(userId);
  }
  updateModel();
};

// Font selection methods
const selectAllFonts = () => {
  selectedFontIds.value = props.fonts.map(font => Number(font.id));
  updateModel();
};

const clearAllFonts = () => {
  selectedFontIds.value = [];
  updateModel();
};

const toggleFontSelection = (fontId) => {
  if (selectedFontIds.value.includes(fontId)) {
    selectedFontIds.value = selectedFontIds.value.filter(id => id !== fontId);
  } else {
    selectedFontIds.value.push(fontId);
  }
  updateModel();
};

// Helper methods
const getUserInitials = (name) => {
  const initials = name.split(' ').map(word => word[0]).join('');
  return initials.toUpperCase();
};

const getFontIconClass = (fontName) => {
  // Convert the font name to lowercase for case-insensitive matching
  const name = fontName.toLowerCase();
  
  // Handle specific exceptions as provided
  if (name.includes('central database') || name.includes('cdb')) return 'cdb cdb-thumbnail';
  if (name.includes('eshop')) return 'vermont-icon vermont-icon-thumbnail';
  if (name.includes('category')) return 'vermont-category vermont-category-thumbnail';
  if (name.includes('order') || name.includes('og')) return 'ordergroup ordergroup-thumbnail';
  
  // For all other fonts, extract base name and use standard pattern
  let baseName = name;
  
  // Extract the base font name before any spaces or special characters
  if (name.includes(' ')) {
    baseName = name.split(' ')[0];
  }
  
  // Clean up any remaining special characters
  baseName = baseName.replace(/[^a-z0-9]/g, '');
  
  // Return the appropriate class with the thumbnail suffix
  return `${baseName} ${baseName}-thumbnail`;
};
</script> 