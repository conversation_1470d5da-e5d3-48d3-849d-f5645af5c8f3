<template>
  <div class="space-y-6">
    <!-- Main Link Section -->
    <div class="mb-6 border border-gray-200 dark:border-slate-700 rounded-lg p-4 bg-white dark:bg-slate-800/30">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
        <i class="design design-link2 mr-1.5 opacity-70"></i>
        Main Link
      </h4>
      
      <div class="space-y-3">
        <!-- Main Link URL with Icon -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="design design-globe text-gray-400 dark:text-gray-500"></i>
          </div>
          <input 
            id="link"
            v-model="mainLink"
            type="text"
            class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white"
            placeholder="example.com"
            @blur="formatLink"
          />
          <div class="flex items-center mt-2 justify-between">
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Enter URL without http:// — it will be added automatically
            </p>
            
            <!-- Preview Link Button -->
            <a 
              v-if="modelValue.link"
              :href="modelValue.link" 
              target="_blank" 
              rel="noopener noreferrer"
              class="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-2"
            >
              <i class="design design-external-link mr-1"></i>
              Preview
            </a>
          </div>
        </div>
        
        <!-- Link Target Toggle -->
        <div class="flex items-center mt-3 p-3 border border-gray-100 dark:border-slate-700 rounded-md bg-gray-50 dark:bg-slate-800/30">
          <i class="design design-layout-right text-gray-400 dark:text-gray-500 mr-2"></i>
          <span class="text-sm text-gray-700 dark:text-gray-300">Open link in new tab</span>
          <button 
            type="button"
            @click="toggleLinkTarget"
            class="relative inline-flex items-center h-5 rounded-full w-9 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-800 dark:focus:ring-offset-slate-950 ml-auto"
            :class="modelValue.link_target === '_blank' ? 'bg-slate-900 dark:bg-slate-700' : 'bg-gray-200 dark:bg-gray-700'"
          >
            <span 
              class="inline-block w-3.5 h-3.5 transform bg-white rounded-full transition-transform"
              :class="modelValue.link_target === '_blank' ? 'translate-x-5' : 'translate-x-0.5'"
            ></span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Additional Links Section -->
    <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4 bg-white dark:bg-slate-800/30">
      <div class="flex justify-between items-center mb-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
          <i class="design design-link-2-fill mr-1.5 opacity-70"></i>
          Additional Links
        </h4>
        <button 
          type="button" 
          @click="addLink" 
          class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-slate-950 hover:bg-slate-800 dark:bg-slate-700 dark:hover:bg-slate-600 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Add Link
        </button>
      </div>
      
      <div v-if="modelValue.additional_links.length === 0" class="text-sm text-gray-500 dark:text-gray-400 italic text-center py-5 border border-dashed border-gray-200 dark:border-slate-700 rounded">
        No additional links added yet
      </div>
      
      <!-- Sortable Links List -->
      <div class="space-y-3">
        <div 
          v-for="(link, index) in modelValue.additional_links" 
          :key="index"
          class="p-3 border border-gray-100 dark:border-slate-800 rounded-md bg-gray-50 dark:bg-slate-800/30 transition-all hover:shadow-sm"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <img 
                :src="'/img/logos/' + (link.image || 'link.svg')"
                :alt="link.title || 'Link icon'" 
                class="h-5 w-5 object-contain mr-2"
              />
              <h5 class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ link.title || 'Untitled Link' }}</h5>
            </div>
            
            <div class="flex items-center space-x-2">
              <!-- Preview link -->
              <a 
                v-if="link.url"
                :href="link.url" 
                target="_blank" 
                rel="noopener noreferrer"
                class="p-1.5 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-full transition-colors"
                title="Preview link"
              >
                <i class="design design-external-link h-4 w-4"></i>
              </a>
              
              <!-- Handle for drag-and-drop (future functionality) -->
              <button 
                type="button" 
                class="p-1.5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full cursor-move transition-colors"
                title="Drag to reorder"
              >
                <i class="design design-drag-vertical-fill h-4 w-4"></i>
              </button>
              
              <!-- Delete button -->
              <button 
                type="button" 
                @click="removeLink(index)" 
                class="p-1.5 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-full transition-colors"
                title="Remove link"
              >
                <i class="design design-trash h-4 w-4"></i>
              </button>
            </div>
          </div>
          
          <div class="grid grid-cols-12 gap-2">
            <!-- Link Title -->
            <div class="col-span-12 sm:col-span-4">
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Title</label>
              <input 
                v-model="link.title"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white text-sm"
                placeholder="Link title"
              />
            </div>
            
            <!-- Link URL -->
            <div class="col-span-12 sm:col-span-5">
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">URL</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="design design-link text-gray-400 dark:text-gray-500"></i>
                </div>
                <input 
                  v-model="link.url"
                  type="text"
                  required
                  class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white text-sm"
                  placeholder="example.com"
                  @blur="formatAdditionalLink(link)"
                />
              </div>
            </div>
            
            <!-- Link Icon -->
            <div class="col-span-12 sm:col-span-3">
              <label class="block text-xs text-gray-500 dark:text-gray-400 mb-1">Icon</label>
              <select
                v-model="link.image"
                class="w-full px-3 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white text-sm"
              >
                <option v-for="logo in logoIcons" :key="logo.path" :value="logo.path">
                  {{ logo.name }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  logoIcons: {
    type: Array,
    required: true
  },
  fontMap: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

// Computed property for two-way binding on link
const mainLink = computed({
  get: () => props.modelValue.link,
  set: (value) => {
    emit('update:modelValue', {
      ...props.modelValue,
      link: value
    });
  }
});

const formatLink = () => {
  if (!props.modelValue.link) return;
  
  let url = props.modelValue.link.trim();
  
  // Skip if it's already properly formatted
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return;
  }
  
  // Add https:// prefix
  emit('update:modelValue', {
    ...props.modelValue,
    link: 'https://' + url.replace(/^(www\.|http:\/\/|https:\/\/)/, '')
  });
};

const formatAdditionalLink = (link) => {
  if (!link.url) return;
  
  let url = link.url.trim();
  
  // Skip if it's already properly formatted
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return;
  }
  
  // Add https:// prefix
  link.url = 'https://' + url.replace(/^(www\.|http:\/\/|https:\/\/)/, '');
  
  // We need to update the whole formData to trigger reactivity
  emit('update:modelValue', {
    ...props.modelValue
  });
};

const toggleLinkTarget = () => {
  emit('update:modelValue', {
    ...props.modelValue,
    link_target: props.modelValue.link_target === '_blank' ? '_self' : '_blank'
  });
};

const addLink = () => {
  const updatedLinks = [
    ...props.modelValue.additional_links,
    {
      title: '',
      url: '',
      image: 'link.svg'  // Default to link.svg in the logos folder
    }
  ];
  
  emit('update:modelValue', {
    ...props.modelValue,
    additional_links: updatedLinks
  });
};

const removeLink = (index) => {
  const updatedLinks = [...props.modelValue.additional_links];
  updatedLinks.splice(index, 1);
  
  emit('update:modelValue', {
    ...props.modelValue,
    additional_links: updatedLinks
  });
};
</script> 