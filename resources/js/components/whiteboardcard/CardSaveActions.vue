<template>
  <div class="flex items-center space-x-3">
    <button
      type="button"
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      @click="$emit('save')"
    >
      Save
    </button>
    <button
      v-if="showCloseButton"
      type="button"
      class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      @click="$emit('close')"
    >
      Close
    </button>
    <button 
      v-if="showDeleteButton"
      type="button"
      class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
      @click="$emit('delete')"
    >
      Delete
    </button>
  </div>
</template>

<script>
export default {
  props: {
    showCloseButton: {
      type: Boolean,
      default: true
    },
    showDeleteButton: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['save', 'close', 'delete']
}
</script> 