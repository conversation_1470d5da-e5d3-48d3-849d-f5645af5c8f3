<template>
  <div class="flex rounded-md shadow-sm">
    <button
      v-for="mode in viewModes"
      :key="mode.value"
      type="button"
      :class="[
        'px-3 py-1.5 text-sm font-medium',
        'focus:outline-none focus:ring-1 focus:ring-blue-500',
        activeMode === mode.value 
          ? 'bg-blue-600 text-white' 
          : 'bg-white text-gray-700 hover:bg-gray-50',
        mode.value === viewModes[0].value ? 'rounded-l-md' : '',
        mode.value === viewModes[viewModes.length - 1].value ? 'rounded-r-md' : '',
        'border border-gray-300',
        mode.value !== viewModes[0].value ? '-ml-px' : '',
      ]"
      @click="$emit('change', mode.value)"
    >
      {{ mode.label }}
    </button>
  </div>
</template>

<script>
export default {
  props: {
    activeMode: {
      type: String,
      required: true
    },
    viewModes: {
      type: Array,
      default: () => [
        { label: 'View', value: 'view' },
        { label: 'Edit', value: 'edit' }
      ]
    }
  },
  
  emits: ['change']
}
</script> 