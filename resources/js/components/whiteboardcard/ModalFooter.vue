<template>
  <div class="flex items-center justify-between p-4 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800/60 backdrop-blur-sm sticky bottom-0">
    <!-- Left side - tab navigation -->
    <div class="flex items-center space-x-3">
      <button 
        v-if="activeTab !== 'details'"
        @click="$emit('previous-tab')" 
        type="button" 
        class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
      >
        <i class="design design-arrow-left mr-1.5"></i>
        Previous
      </button>
      
      <button 
        v-if="activeTab !== 'assignments'"
        @click="$emit('next-tab')" 
        type="button" 
        class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors"
      >
        Next
        <i class="design design-arrow-right ml-1.5"></i>
      </button>
      
      <!-- Tab indicator -->
      <div class="hidden md:flex items-center space-x-1 ml-2 text-xs text-gray-500 dark:text-gray-400">
        <span 
          v-for="(tab, index) in ['details', 'links', 'assignments']" 
          :key="tab"
          class="w-2 h-2 rounded-full transition-all"
          :class="activeTab === tab ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'"
        ></span>
        <span class="ml-1">{{ activeTabIndex + 1 }}/3</span>
      </div>
    </div>

    <!-- Right side - action buttons -->
    <div class="flex items-center space-x-3">
      <!-- Delete button (only in edit mode) -->
      <button
        v-if="isEditing"
        type="button"
        @click="confirmDelete"
        class="inline-flex items-center px-3 py-2 border border-transparent text-sm rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors"
      >
        <i class="design design-trash mr-1.5"></i>
        Delete
      </button>
      
      <!-- Cancel button -->
      <button
        type="button"
        @click="$emit('close')"
        class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-700 hover:bg-gray-50 dark:hover:bg-slate-600 transition-colors"
      >
        Cancel
        <span class="ml-1.5 text-xs text-gray-400 dark:text-gray-500">(Esc)</span>
      </button>
      
      <!-- Save button with validity indicator -->
      <button
        type="button"
        @click="$emit('save')"
        :disabled="!isValid"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm rounded-md text-white transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
        :class="isValid ? 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600' : 'bg-gray-400 dark:bg-slate-600'"
      >
        <i v-if="isValid" class="design design-check mr-1.5"></i>
        <i v-else class="design design-alert-triangle mr-1.5"></i>
        {{ saveButtonText }}
        <span v-if="isValid" class="ml-1.5 text-xs text-blue-200 dark:text-blue-300">(Ctrl+Enter)</span>
      </button>
    </div>
    
    <!-- Delete confirmation modal -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Delete Card</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-5">Are you sure you want to delete this card? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
          <button 
            @click="showDeleteConfirm = false" 
            class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-slate-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700">
            Cancel
          </button>
          <button 
            @click="confirmDeleteAction" 
            class="px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 rounded-md">
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  isEditing: {
    type: Boolean,
    default: false
  },
  isValid: {
    type: Boolean,
    default: true
  },
  activeTab: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['save', 'close', 'delete', 'previous-tab', 'next-tab']);

// State for delete confirmation
const showDeleteConfirm = ref(false);

// Computed properties
const saveButtonText = computed(() => {
  return props.isEditing ? 'Update Card' : 'Create Card';
});

const activeTabIndex = computed(() => {
  if (props.activeTab === 'details') return 0;
  if (props.activeTab === 'links') return 1;
  if (props.activeTab === 'assignments') return 2;
  return 0;
});

// Methods
const confirmDelete = () => {
  showDeleteConfirm.value = true;
};

const confirmDeleteAction = () => {
  showDeleteConfirm.value = false;
  emit('delete');
};
</script> 