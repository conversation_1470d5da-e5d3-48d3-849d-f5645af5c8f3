<template>
  <div class="space-y-6">
    <!-- Assigned Users -->
    <div v-if="hasAssignedUsers" class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Assigned Users</h4>
      <div class="space-y-3">
        <div 
          v-for="user in card.assignedUsers" 
          :key="user.id"
          class="flex items-center"
        >
          <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden mr-3">
            <img 
              v-if="user.avatar"
              :src="user.avatar" 
              :alt="user.name"
              class="w-full h-full object-cover"
            >
            <span v-else class="text-sm font-medium text-gray-600 dark:text-gray-300">
              {{ getUserInitials(user.name) }}
            </span>
          </div>
          <div>
            <span class="text-gray-900 dark:text-white">{{ user.name }}</span>
            <span v-if="user.email" class="text-xs text-gray-500 dark:text-gray-400 block">{{ user.email }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm text-gray-500 dark:text-gray-400 text-sm italic">
      No users assigned to this card.
    </div>
    
    <!-- Associated Fonts -->
    <div v-if="hasFonts" class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Typography</h4>
      <div class="space-y-3">
        <div 
          v-for="font in card.fonts" 
          :key="font.id"
          class="flex items-center"
        >
          <div class="w-8 h-8 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3 overflow-hidden">
            <i class="design design-type text-gray-500 dark:text-gray-400"></i>
          </div>
          <div>
            <span class="text-gray-900 dark:text-white">{{ font.name }}</span>
            <span v-if="font.family" class="text-xs text-gray-500 dark:text-gray-400 block">{{ font.family }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm text-gray-500 dark:text-gray-400 text-sm italic">
      No typography associated with this card.
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  card: {
    type: Object,
    required: true
  }
});

// Check if card has assigned users
const hasAssignedUsers = computed(() => {
  return props.card.assignedUsers && props.card.assignedUsers.length > 0;
});

// Check if card has fonts
const hasFonts = computed(() => {
  return props.card.fonts && props.card.fonts.length > 0;
});

// Extract initials from user name
const getUserInitials = (name) => {
  if (!name) return '?';
  
  const nameParts = name.split(' ');
  if (nameParts.length === 1) {
    return nameParts[0].substring(0, 2).toUpperCase();
  }
  
  return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
};
</script> 