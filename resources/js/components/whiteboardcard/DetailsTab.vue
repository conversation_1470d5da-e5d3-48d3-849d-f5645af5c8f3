<template>
  <div class="space-y-6">
    <!-- Logo Image Selection with Improved Layout -->
    <div class="border border-gray-200 dark:border-slate-700 rounded-lg p-4 mb-6 bg-white dark:bg-slate-800/30">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
        <i class="design design-image mr-1.5 opacity-70"></i>
        Card Image
      </h4>
      
      <!-- Search Filter for Logos -->
      <div class="relative mb-3">
        <input
          type="text"
          v-model="logoSearch"
          class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white text-sm"
          placeholder="Search logos..."
        />
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="design design-search-fill text-gray-400 dark:text-gray-500"></i>
        </div>
      </div>
      
      <!-- Logo Grid with Filtered Results -->
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 max-h-60 overflow-y-auto p-1 rounded-md">
        <div 
          v-for="logo in filteredLogos" 
          :key="logo.path" 
          @click="selectLogo(logo.path)"
          class="relative border-2 rounded-md p-2 cursor-pointer transition-all hover:bg-gray-50 dark:hover:bg-slate-700/50"
          :class="modelValue.image === logo.path ? 'border-slate-800 dark:border-slate-400 bg-gray-50 dark:bg-slate-700/50' : 'border-gray-200 dark:border-slate-700'"
        >
          <img 
            :src="'/img/' + logo.path" 
            :alt="logo.name" 
            class="h-14 w-full object-contain"
          >
          <div class="mt-1 text-xs font-medium text-center text-gray-700 dark:text-gray-300 truncate">
            {{ logo.name }}
          </div>
          <div 
            v-if="modelValue.image === logo.path" 
            class="absolute -top-2 -right-2 bg-slate-800 text-white dark:bg-slate-600 rounded-full p-1 shadow-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Card Information Section -->
    <div class="bg-white dark:bg-slate-800/30 border border-gray-200 dark:border-slate-700 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
        <i class="design design-file-text-fill mr-1.5 opacity-70"></i>
        Card Information
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Left column -->
        <div class="space-y-5">
          <!-- Headline -->
          <div>
            <label for="headline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <span>Headline</span> <span class="text-red-500 ml-1">*</span>
              <span class="ml-auto text-xs text-gray-400">Required</span>
            </label>
            <input 
              id="headline"
              v-model="modelValue.headline"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white"
              placeholder="Card headline"
            />
          </div>
          
          <!-- Project with Improved Select -->
          <div>
            <label for="project" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <span>Project</span>
              <span class="ml-auto text-xs text-gray-400">Optional</span>
            </label>
            <div class="relative">
              <select
                id="project"
                v-model="modelValue.project_id"
                class="appearance-none w-full px-3 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white pr-10"
              >
                <option value="">None</option>
                <option v-for="project in projects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <i class="design design-chevron-down text-gray-400"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Right column -->
        <div class="space-y-5">
          <!-- Date with Calendar Icon -->
          <div>
            <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <span>Date</span> <span class="text-red-500 ml-1">*</span>
              <span class="ml-auto text-xs text-gray-400">Required</span>
            </label>
            <div class="relative">
              <input 
                id="date"
                v-model="modelValue.card_date"
                type="date"
                required
                class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="design design-calendar text-gray-400 dark:text-gray-500"></i>
              </div>
            </div>
          </div>
          
          <!-- Deadline with Calendar Icon -->
          <div>
            <label for="deadline" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
              <span>Deadline</span>
              <span class="ml-auto text-xs text-gray-400">Optional</span>
            </label>
            <div class="relative">
              <input 
                id="deadline"
                v-model="modelValue.deadline"
                type="date"
                class="w-full px-3 py-2 pl-9 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="design design-calendar-event text-gray-400 dark:text-gray-500"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Description (full width) with Character Count -->
      <div class="mt-6">
        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
          <span>Description</span>
          <span class="ml-auto text-xs text-gray-400">{{ descriptionLength }}/500 characters</span>
        </label>
        <textarea 
          id="description"
          v-model="modelValue.description"
          rows="6"
          maxlength="500"
          class="w-full px-3 py-2 border border-gray-300 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-slate-800 focus:border-slate-800 dark:bg-slate-950 dark:text-white"
          placeholder="Provide a description for this card..."
        ></textarea>
      </div>
    </div>
    
    <!-- Status Selection Section -->
    <div class="bg-white dark:bg-slate-800/30 border border-gray-200 dark:border-slate-700 rounded-lg p-4">
      <div class="w-full">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
          <i class="design design-col-visible mr-1.5 opacity-70"></i>
          Card Status
        </label>
        
        <!-- Status Selection - Grid Layout with Improved Visuals -->
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mb-3">
          <button
            v-for="status in statusOptions" 
            :key="status.slug"
            type="button"
            @click="updateStatus(status.slug)"
            :data-slug="status.slug"
            class="flex items-center justify-between px-3 py-2.5 border border-solid rounded-md cursor-pointer transition-all duration-200"
            :class="modelValue.status === status.slug 
              ? 'border-slate-800 dark:border-slate-400 bg-gray-50 dark:bg-slate-800/30 shadow-sm' 
              : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-950/50'"
          >
            <div class="flex items-center">
              <span class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: status.color }"></span>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ status.name }}</span>
            </div>
            <div 
              v-if="modelValue.status === status.slug" 
              class="bg-slate-800 text-white dark:bg-slate-600 rounded-full p-0.5"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </button>
        </div>
        
        <p class="text-xs text-gray-500 dark:text-gray-400">
          This determines the card's visibility and status in the system. Statuses are pulled directly from the database and can be managed in the admin panel.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  categoryLogos: {
    type: Array,
    required: true
  },
  projects: {
    type: Array,
    required: true
  },
  statusOptions: {
    type: Array,
    required: true
  },
  defaultStatusSlug: {
    type: String,
    required: true
  },
  getStatusColor: {
    type: Function,
    required: true
  },
  showAdvanced: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

// Local state for logo search
const logoSearch = ref('');

// Computed properties
const filteredLogos = computed(() => {
  if (!logoSearch.value) return props.categoryLogos;
  
  const search = logoSearch.value.toLowerCase();
  return props.categoryLogos.filter(logo => 
    logo.name.toLowerCase().includes(search) || 
    logo.path.toLowerCase().includes(search)
  );
});

const descriptionLength = computed(() => {
  return props.modelValue.description?.length || 0;
});

// Methods
const selectLogo = (path) => {
  // Update the model value using the v-model pattern
  emit('update:modelValue', {
    ...props.modelValue,
    image: path
  });
};

const updateStatus = (status) => {
  // Set is_active based on whether this is the default status
  const is_active = status === props.defaultStatusSlug;
  
  emit('update:modelValue', {
    ...props.modelValue,
    status: status,
    is_active: is_active
  });
};
</script> 