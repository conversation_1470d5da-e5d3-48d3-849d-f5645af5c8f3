<template>
  <div class="relative z-50 pointer-events-auto w-full" v-cloak>
    <!-- Modal positioned above the grid -->
    <div v-if="editingBookmark" class="relative z-[9999] mx-auto mb-6 max-w-3xl">
      <div class=" rounded-lg overflow-hidden">
        <div class="bg-white/80 dark:bg-slate-950/80 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 backdrop-blur-lg">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-2xl leading-6 gant-modern-bold text-gray-900 dark:text-gray-100" id="modal-title">
                <i class="design design-bookmark mr-1 opacity-50"></i>
                {{ isNewBookmark ? 'Add Bookmark' : 'Edit Bookmark' }}
                <i class="design design-edit ml-1 opacity-50"></i>
              </h3>
              <div class="my-4">
                <edit-card 
                  :bookmark="editingBookmark" 
                  @update="updateBookmarkData" 
                  @cancel="cancelEdit"
                  @delete="confirmDelete"
                />
              </div>
            </div>
          </div>
          <button 
            type="button" 
            class="mt-3 w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-transparent text-base gant-modern-regular text-gray-700 hover:bg-gray-50 focus:outline-none dark:text-gray-200 dark:border-slate-500 dark:hover:bg-slate-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm gant-modern-bold"
            @click="cancelEdit"
          >
            <i class="design design-close mr-2"></i>
            Cancel
          </button>
          <button 
            type="button" 
            class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-slate-900 text-base gant-modern-bold text-white hover:bg-slate-100 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm gant-modern-bold dark:bg-slate-100 dark:text-slate-900 dark:hover:bg-slate-200"
            @click="saveBookmark"
          >
            <i class="design design-save mr-2"></i>
            Save
          </button>

        </div>
      </div>
    </div>

    <!-- Grid of Bookmarks -->
    <div class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4">
      <div v-for="bookmark in bookmarks" :key="bookmark.id ?? bookmark.__tempId" class="h-full">
        <div class="relative z-50 backdrop-blur-lg bg-white/40 dark:bg-gray-950/40 rounded-xl p-4 h-full pointer-events-auto transition-all duration-200 ">
          <ViewCard
            :bookmark="bookmark"
            @edit="() => startEdit(bookmark)"
          />
        </div>
      </div>
      <button @click="addBookmark" class="flex flex-col items-center justify-center bg-transparent rounded-xl border border-dashed border-gray-200 dark:border-gray-800 p-4 h-full min-h-[160px] pointer-events-auto transition-all duration-200 ">
        <i class="design design-plus text-3xl mb-2 text-slate-300 dark:text-slate-700"></i>
        <span class="text-sm gant-modern-regular">Add new</span>
      </button>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import ViewCard from './ViewCard.vue';
import EditCard from './EditCard.vue';

export default {
  name: 'Bookmarks',
  components: { ViewCard, EditCard },
  data() {
    return {
      bookmarks: [],
      original: [],
      editingBookmark: null,
      currentData: null,
    };
  },
  computed: {
    isNewBookmark() {
      return this.editingBookmark && !this.editingBookmark.id && !this.editingBookmark.__tempId;
    }
  },
  methods: {
    fetch() {
      axios.get('/api/bookmarks').then(res => {
        this.bookmarks = res.data.map(b => ({ ...b }));
        this.original = res.data.map(b => ({ ...b }));
      });
    },
    addBookmark() {
      const tempId = `temp-${Date.now()}`;
      const newBookmark = { id: null, title: '', icon_class: '', links: [], __tempId: tempId };
      this.bookmarks.unshift(newBookmark);
      this.original.unshift(null);
      this.editingBookmark = newBookmark;
    },
    startEdit(bookmark) {
      const idx = this.bookmarks.findIndex(b => (b.id ?? b.__tempId) === (bookmark.id ?? bookmark.__tempId));
      if (idx !== -1) {
        // Create a deep copy to avoid direct mutation
        this.editingBookmark = JSON.parse(JSON.stringify(this.bookmarks[idx]));
        
        // Scroll to top to show the modal
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    },
    cancelEdit() {
      if (!this.editingBookmark) return;
      
      // If it's a new bookmark, remove it from the list
      if (this.editingBookmark.id == null && this.editingBookmark.__tempId) {
        const idx = this.bookmarks.findIndex(b => b.__tempId === this.editingBookmark.__tempId);
        if (idx !== -1) {
          this.bookmarks.splice(idx, 1);
          this.original.splice(idx, 1);
        }
      }
      
      this.editingBookmark = null;
      this.currentData = null;
    },
    updateBookmarkData(data) {
      this.currentData = data;
    },
    saveBookmark() {
      if (!this.currentData) return;
      
      const { bookmark, links } = this.currentData;
      
      // Client-side validation: ensure title is provided
      if (!bookmark.title || !bookmark.title.trim()) {
        alert('Please provide a title for the bookmark.');
        return;
      }
      // Prefix missing scheme on link URLs
      links.forEach(l => {
        if (l.url && !/^https?:\/\//i.test(l.url)) {
          l.url = 'https://' + l.url;
        }
      });
      // Skip links without both label and URL to avoid validation errors
      const validLinks = links.filter(l => l.label.trim() !== '' && l.url.trim() !== '');
      const isNew = bookmark.id == null;
      const idx = this.bookmarks.findIndex(b => (b.id ?? b.__tempId) === (bookmark.id ?? bookmark.__tempId));
      if (isNew) {
        axios.post('/api/bookmarks', { title: bookmark.title, icon_class: bookmark.icon_class })
          .then(res => {
            const newId = res.data.id;
            const promises = validLinks.map(l => axios.post('/api/bookmark-links', { bookmark_id: newId, label: l.label, url: l.url }));
            if (promises.length) {
              Promise.all(promises).then(() => this.fetch());
            } else {
              this.fetch();
            }
          })
          .catch(err => console.error('Error creating bookmark:', err.response?.data || err));
      } else {
        axios.put(`/api/bookmarks/${bookmark.id}`, { title: bookmark.title, icon_class: bookmark.icon_class })
          .then(() => {
            const postLinks = [];
            // update existing or add new valid links
            links.forEach(l => {
              if (l.id) {
                postLinks.push(axios.put(`/api/bookmark-links/${l.id}`, { label: l.label, url: l.url }));
              } else if (l.label.trim() !== '' && l.url.trim() !== '') {
                postLinks.push(axios.post('/api/bookmark-links', { bookmark_id: bookmark.id, label: l.label, url: l.url }));
              }
            });
            // delete removed links
            const original = this.original[idx]?.links || [];
            const existing = original.map(l => l.id);
            const kept = links.filter(l => l.id).map(l => l.id);
            existing.filter(id => !kept.includes(id)).forEach(id => postLinks.push(axios.delete(`/api/bookmark-links/${id}`)));
            if (postLinks.length) {
              Promise.all(postLinks).then(() => this.fetch());
            } else {
              this.fetch();
            }
          })
          .catch(err => console.error('Error updating bookmark:', err.response?.data || err));
      }
      this.editingBookmark = null;
      this.currentData = null;
    },
    confirmDelete() {
      if (confirm('Are you sure you want to delete this bookmark?')) {
        this.deleteBookmark(this.editingBookmark);
      }
    },
    deleteBookmark(bookmark) {
      if (bookmark.id) {
        axios.delete(`/api/bookmarks/${bookmark.id}`).then(() => this.fetch());
      } else {
        // For new bookmarks that haven't been saved yet
        const idx = this.bookmarks.findIndex(b => b.__tempId === bookmark.__tempId);
        if (idx !== -1) {
          this.bookmarks.splice(idx, 1);
          this.original.splice(idx, 1);
        }
      }
      this.editingBookmark = null;
      this.currentData = null;
    }
  },
  mounted() {
    this.fetch();
  }
};
</script>

<style scoped>
[v-cloak] { display: none; }

@media (min-width: 480px) and (max-width: 639px) {
  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style> 