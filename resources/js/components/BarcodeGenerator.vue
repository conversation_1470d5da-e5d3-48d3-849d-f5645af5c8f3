<template>
  <div class="barcode-generator font-gant-modern">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-8">
      <!-- Left column: Preview -->
      <div class="order-last lg:order-first">
        <div class="sticky top-4 h-full flex flex-col">
          <div class="bg-white dark:bg-slate-950 rounded-2xl p-3 md:p-4 shadow-sm border border-gray-100 dark:border-gray-800 flex flex-col flex-grow">
            <h3 class="text-xl md:text-3xl gant-modern-bold text-gray-900 dark:text-gray-100 mb-3">Barcode Preview</h3>
            <!-- Preview Area -->
            <div class="bg-white dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700 shadow-inner flex items-center justify-center p-4 flex-grow">
              <div v-if="previewCode" class="w-full h-full flex flex-col items-center justify-center">
                <div class="barcode-svg-container w-full max-w-full">
                  <div v-html="generateSvg(previewCode)"></div>
                </div>
                <div v-if="mode === 'multiple' && codes.length > 1" class="text-center text-xs text-gray-500 dark:text-gray-400 mt-2">(Previewing first of {{ codes.length }} barcodes)</div>
              </div>
              <div v-else class="text-center w-full">
                <div v-if="inputValue" class="flex flex-col items-center justify-center">
                  <div class="mb-2 text-sm text-gray-600 dark:text-gray-400">Generating...</div>
                  <div class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="h-full bg-gray-900 dark:bg-gray-400 animate-progress"></div>
                  </div>
                </div>
                <div v-else class="text-sm text-gray-400">
                  Enter data to see preview
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right column: Input & Settings -->
      <div>
        <div class="bg-white dark:bg-slate-950 rounded-2xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
          <!-- Input Section -->
          <div class="mb-6 md:mb-8">
            <label class="block text-xl md:text-3xl gant-modern-bold text-gray-700 dark:text-gray-300 mb-2 md:mb-3">
              Barcode Data
            </label>
            <div class="flex items-center mb-3 gap-4 text-sm md:text-base">
              <!-- Top controls row -->
              <div class="w-full flex flex-col md:flex-row md:flex-wrap gap-4 mb-4">
                <!-- Mode Switch -->
                <div class="flex items-center justify-between md:justify-start md:flex-1">
                  <label for="barcode-mode" class="text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 md:mr-3">Mode:</label>
                  <div class="flex items-center h-8">
                    <label class="relative inline-block w-[60px] h-[28px]">
                      <input 
                        type="checkbox" 
                        id="barcode-mode"
                        v-model="isBatchMode" 
                        class="opacity-0 w-0 h-0 peer"
                      >
                      <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[20px] before:w-[20px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[32px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                        <i v-if="!isBatchMode" class="design design-single absolute top-1/2 -translate-y-1/2 left-[8px] text-xs text-gray-500"></i>
                        <i v-else class="design design-copy absolute top-1/2 -translate-y-1/2 right-[8px] text-xs text-gray-200"></i>
                      </span>
                    </label>
                    <span class="ml-2 text-xs text-gray-600 dark:text-gray-400">{{ isBatchMode ? 'Multiple' : 'Single' }}</span>
                  </div>
                </div>

                <!-- Type -->
                <div class="flex items-center justify-between md:justify-start md:flex-1">
                  <label for="barcode-type" class="text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 md:mr-3">Type:</label>
                  <button 
                    @click="toggleTypeSelector" 
                    class="select select-bordered select-sm md:select-md flex-grow max-w-[180px] text-xs md:text-sm gant-modern-regular font-normal h-8 md:h-10 flex items-center px-3"
                  >
                    <span>{{ getBarcodeTypeName(type) }}</span>
                    <i class="design design-chevron-down ml-auto"></i>
                  </button>
                </div>

                <!-- Show Value Toggle -->
                <div class="flex items-center justify-between md:justify-start md:flex-1">
                  <label for="showValue" class="text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 md:mr-3">Show Value:</label>
                  <div class="flex items-center h-8">
                    <label class="relative inline-block w-[60px] h-[28px]">
                      <input 
                        type="checkbox" 
                        id="showValue"
                        v-model="showValue" 
                        class="opacity-0 w-0 h-0 peer"
                      >
                      <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[20px] before:w-[20px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[32px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                        <i v-if="!showValue" class="design design-circle-cross text-gray-500 absolute top-1/2 -translate-y-1/2 left-[8px] text-xs"></i>
                        <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-xs"></i>
                      </span>
                    </label>
                  </div>
                </div>
                
                <!-- Show Margin Toggle -->
                <div class="flex items-center justify-between md:justify-start md:flex-1">
                  <label for="showMargin" class="text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 md:mr-3">Show Margin:</label>
                  <div class="flex items-center h-8">
                    <label class="relative inline-block w-[60px] h-[28px]">
                      <input 
                        type="checkbox" 
                        id="showMargin"
                        v-model="showMargin" 
                        class="opacity-0 w-0 h-0 peer"
                      >
                      <span class="absolute cursor-pointer inset-0 bg-gray-100 dark:bg-gray-800 transition-all duration-400 rounded-[34px] before:content-[''] before:absolute before:h-[20px] before:w-[20px] before:left-[4px] before:bottom-[4px] before:bg-white dark:before:bg-gray-600 before:transition-all before:duration-400 before:rounded-full peer-checked:bg-gray-900 dark:peer-checked:bg-gray-700 peer-checked:before:transform peer-checked:before:translate-x-[32px] peer-checked:before:bg-white dark:peer-checked:before:bg-gray-300">
                        <i v-if="!showMargin" class="design design-circle-cross text-gray-500 absolute top-1/2 -translate-y-1/2 left-[8px] text-xs"></i>
                        <i v-else class="design design-circle-check text-gray-200 absolute top-1/2 -translate-y-1/2 right-[8px] text-xs"></i>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Barcode Type Selector (Collapsible) -->
            <transition name="fade">
              <div v-if="showTypeSelector" class="mb-4 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden">
                <div class="pt-3 pb-2 px-4">
                  <div class="flex items-center gap-3 mb-1">
                    <h3 class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400">Select Barcode Type</h3>
                  </div>
                </div>
                
                <!-- GS1 & Retail Group -->
                <div class="px-4 pb-2">
                  <h4 class="text-xs text-gray-500 dark:text-gray-400 mb-2">GS1 & Retail</h4>
                  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    <div 
                      v-for="option in barcodeOptions.retail" 
                      :key="option.value"
                      @click="selectBarcodeType(option.value)" 
                      class="relative flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-900 rounded-lg p-3 hover:border-gray-300 dark:hover:border-slate-700 transition-colors duration-200 cursor-pointer"
                      :class="{ 'border-primary dark:border-primary': type === option.value }"
                    >
                      <i v-if="type === option.value" class="design design-check absolute top-2 right-2 text-primary"></i>
                      <span class="text-md mb-1 gant-modern-bold text-slate-900 dark:text-slate-200 text-center leading-none">{{ option.label }}</span>
                      <span class="text-xs gant-modern-regular text-slate-600 dark:text-slate-400">{{ option.description || '&nbsp;' }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- Logistics Group -->
                <div class="px-4 pb-2 pt-1">
                  <h4 class="text-xs text-gray-500 dark:text-gray-400 mb-2">Logistics & Supply Chain</h4>
                  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    <div 
                      v-for="option in barcodeOptions.logistics" 
                      :key="option.value"
                      @click="selectBarcodeType(option.value)" 
                      class="relative flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-900 rounded-lg p-3 hover:border-gray-300 dark:hover:border-slate-700 transition-colors duration-200 cursor-pointer"
                      :class="{ 'border-primary dark:border-primary': type === option.value }"
                    >
                      <i v-if="type === option.value" class="design design-check absolute top-2 right-2 text-primary"></i>
                      <span class="text-md mb-1 gant-modern-bold text-slate-900 dark:text-slate-200 text-center leading-none">{{ option.label }}</span>
                      <span class="text-xs gant-modern-regular text-slate-600 dark:text-slate-400">{{ option.description || '&nbsp;' }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- Industrial & Generic -->
                <div class="px-4 pb-2 pt-1">
                  <h4 class="text-xs text-gray-500 dark:text-gray-400 mb-2">Industrial & General Purpose</h4>
                  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    <div 
                      v-for="option in barcodeOptions.industrial" 
                      :key="option.value"
                      @click="selectBarcodeType(option.value)" 
                      class="relative flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-900 rounded-lg p-3 hover:border-gray-300 dark:hover:border-slate-700 transition-colors duration-200 cursor-pointer"
                      :class="{ 'border-primary dark:border-primary': type === option.value }"
                    >
                      <i v-if="type === option.value" class="design design-check absolute top-2 right-2 text-primary"></i>
                      <span class="text-md mb-1 gant-modern-bold text-slate-900 dark:text-slate-200 text-center leading-none">{{ option.label }}</span>
                      <span class="text-xs gant-modern-regular text-slate-600 dark:text-slate-400">{{ option.description || '&nbsp;' }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- Specialized -->
                <div class="px-4 pb-4 pt-1">
                  <h4 class="text-xs text-gray-500 dark:text-gray-400 mb-2">Specialized</h4>
                  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    <div 
                      v-for="option in barcodeOptions.specialized" 
                      :key="option.value"
                      @click="selectBarcodeType(option.value)" 
                      class="relative flex flex-col items-center justify-center bg-white dark:bg-slate-950 border border-solid border-gray-200 dark:border-slate-900 rounded-lg p-3 hover:border-gray-300 dark:hover:border-slate-700 transition-colors duration-200 cursor-pointer"
                      :class="{ 'border-primary dark:border-primary': type === option.value }"
                    >
                      <i v-if="type === option.value" class="design design-check absolute top-2 right-2 text-primary"></i>
                      <span class="text-md mb-1 gant-modern-bold text-slate-900 dark:text-slate-200 text-center leading-none">{{ option.label }}</span>
                      <span class="text-xs gant-modern-regular text-slate-600 dark:text-slate-400">{{ option.description || '&nbsp;' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </transition>
            <input
              type="text"
              v-model="inputValue"
              :placeholder="mode === 'multiple' ? 'e.g., 1234567, 9876543' : 'e.g., 123456789012'"
              class="w-full px-3 md:px-4 py-2 md:py-2.5 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary focus:border-primary outline-none text-sm md:text-sm gant-modern-regular"
            />
          </div>

          <!-- Settings Section -->
          <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5 mb-6 md:mb-8">
            <h3 class="text-lg md:text-2xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4 md:mb-6">Settings</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
              <!-- Width Slider -->
              <div class="col-span-1 sm:col-span-2">
                <label for="barcode-width" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">Width (px)</label>
                <div class="flex items-center">
                  <input 
                    id="barcode-width" 
                    type="range" 
                    v-model.number="width" 
                    min="100" 
                    max="500" 
                    step="10" 
                    class="w-full range-slider mr-3 md:mr-4" 
                  />
                  <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400 w-10 text-right">{{ width }}</span>
                </div>
              </div>
              <!-- Height Slider -->
              <div class="col-span-1 sm:col-span-2">
                <label for="barcode-height" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">Height (px)</label>
                <div class="flex items-center">
                  <input 
                    id="barcode-height" 
                    type="range" 
                    v-model.number="height" 
                    min="30" 
                    max="200" 
                    step="5" 
                    class="w-full range-slider mr-3 md:mr-4" 
                  />
                  <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400 w-10 text-right">{{ height }}</span>
                </div>
              </div>
              <!-- Font Size Slider -->
              <div class="col-span-1 sm:col-span-2">
                <label for="font-size" class="block text-xs md:text-sm gant-modern-medium text-gray-700 dark:text-gray-300 mb-1 md:mb-2">Font Size</label>
                <div class="flex items-center">
                  <input 
                    id="font-size" 
                    type="range" 
                    v-model.number="fontSize" 
                    min="8" 
                    max="32" 
                    step="1" 
                    :disabled="!showValue"
                    class="w-full range-slider mr-3 md:mr-4" 
                    :class="{ 'opacity-50 cursor-not-allowed': !showValue }"
                  />
                  <span class="text-xs md:text-sm text-gray-600 dark:text-gray-400 w-10 text-right">{{ fontSize }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Export Section -->
          <div class="border dark:border-gray-700 rounded-xl p-3 md:p-5">
            <h3 class="text-lg md:text-2xl gant-modern-bold text-gray-800 dark:text-gray-200 mb-4 md:mb-6">Export</h3>
             <div v-if="codes.length > 0 && codes[0]">
                <div v-if="mode === 'single'" class="flex gap-3 md:gap-5">
                   <button @click="download(codes[0], 0, 'svg')" class="px-8 py-3 rounded-lg bg-slate-900 dark:bg-slate-700 text-white font-medium hover:bg-slate-800 dark:hover:bg-slate-600 transition-colors">
                     <i class="design design-download-svg text-lg md:text-xl"></i> Download SVG
                   </button>
                   <button @click="download(codes[0], 0, 'png')" class="px-8 py-3 rounded-lg bg-slate-900 dark:bg-slate-700 text-white font-medium hover:bg-slate-800 dark:hover:bg-slate-600 transition-colors">
                     <i class="design design-download-png text-lg md:text-xl"></i> Download PNG
                   </button>
                </div>
                <div v-else> <!-- Multiple Mode -->
                    <div class="flex flex-col gap-2 max-h-48 overflow-y-auto mb-3 pr-2">
                       <div v-for="(code, idx) in codes" :key="idx" class="flex justify-between items-center border border-solid border-gray-300 dark:border-gray-700 p-2 md:p-3 rounded-lg">
                        
                           <span class="text-sm md:text-base truncate mr-2 font-mono dark:text-gray-300"> <i class="design design-barcode text-lg md:text-xl opacity-50 me-3"></i>{{ code || '-' }}</span>
                           <div class="flex gap-2 md:gap-3">
                               <button @click="download(code, idx, 'svg')" class="btn btn-xs md:btn-sm btn-ghost dark:text-gray-400 dark:hover:bg-gray-700" title="Download SVG"><i class="design design-download-svg me-1 opacity-50"></i>Download svg</button>
                               <button @click="download(code, idx, 'png')" class="btn btn-xs md:btn-sm btn-ghost dark:text-gray-400 dark:hover:bg-gray-700" title="Download PNG"><i class="design design-download-png me-1 opacity-50"></i>Download png</button>
                           </div>
                       </div>
                    </div>
                </div>
             </div>
             <div v-else class="text-center text-sm text-gray-500 dark:text-gray-400">
                 Generate a barcode to enable export options.
             </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const isBatchMode = ref(false);
const mode = computed(() => isBatchMode.value ? 'multiple' : 'single');
const inputValue = ref('');
const type = ref('EAN13');
const width = ref(200);
const height = ref(100);
const fontSize = ref(14);
const showValue = ref(true);
const showMargin = ref(true);
const showTypeSelector = ref(false);

// Barcode type definitions and grouping
const barcodeOptions = {
  retail: [
    { value: 'EAN13', label: 'EAN-13', description: 'Global retail' },
    { value: 'EAN8', label: 'EAN-8', description: 'Small products' },
    { value: 'ISBN13', label: 'ISBN-13', description: 'Books' },
    { value: 'GTIN', label: 'GTIN', description: 'Trade items' }
  ],
  logistics: [
    { value: 'SSCC', label: 'SSCC-18', description: 'Shipping units' },
    { value: 'ITF14', label: 'ITF-14', description: 'Packaging' },
    { value: 'GS1_128', label: 'GS1-128', description: 'Logistics' }
  ],
  industrial: [
    { value: 'CODE128', label: 'Code 128', description: 'High density' },
    { value: 'CODE39', label: 'Code 39', description: 'Industry standard' },
    { value: 'CODE39_FULL_ASCII', label: 'Code 39 Full', description: 'ASCII support' },
    { value: 'CODE93', label: 'Code 93', description: 'Compact format' },
    { value: 'CODE11', label: 'Code 11', description: 'Telecommunications' },
    { value: 'CODABAR', label: 'Codabar', description: 'Libraries/Blood banks' },
    { value: 'ITF', label: 'Code 2of5', description: 'Numeric-only' },
    { value: 'MSI', label: 'MSI', description: 'Inventory' }
  ],
  specialized: [
    { value: 'PHARMACODE', label: 'Pharmacode', description: 'Pharmaceutical' },
    { value: 'TELEPEN_ALPHA', label: 'Telepen Alpha', description: 'Libraries' }
  ]
};

// Helper functions for the type selector
function toggleTypeSelector() {
  showTypeSelector.value = !showTypeSelector.value;
}

function selectBarcodeType(value) {
  type.value = value;
  showTypeSelector.value = false; // Close after selection
}

function getBarcodeTypeName(typeValue) {
  // Search through all option groups to find the matching label
  for (const group in barcodeOptions) {
    const option = barcodeOptions[group].find(opt => opt.value === typeValue);
    if (option) return option.label;
  }
  return typeValue; // Fallback to the raw value if not found
}

// Code39 Data
const code39Patterns = {
  '0': 'n n n w w n w n n', '1': 'w n n w n n n n w', '2': 'n n w w n n n n w',
  '3': 'w n w w n n n n n', '4': 'n n n w w n n n w', '5': 'w n n w w n n n n',
  '6': 'n n w w w n n n n', '7': 'n n n w n n w n w', '8': 'w n n w n n w n n',
  '9': 'n n w w n n w n n', 'A': 'w n n n n w n n w', 'B': 'n n w n n w n n w',
  'C': 'w n w n n w n n n', 'D': 'n n n n w w n n w', 'E': 'w n n n w w n n n',
  'F': 'n n w n w w n n n', 'G': 'n n n n n w w n w', 'H': 'w n n n n w w n n',
  'I': 'n n w n n w w n n', 'J': 'n n n n w w w n n', 'K': 'w n n n n n n w w',
  'L': 'n n w n n n n w w', 'M': 'w n w n n n n w n', 'N': 'n n n n w n n w w',
  'O': 'w n n n w n n w n', 'P': 'n n w n w n n w n', 'Q': 'n n n n n n w w w',
  'R': 'w n n n n n w w n', 'S': 'n n w n n n w w n', 'T': 'n n n n w n w w n',
  'U': 'w w n n n n n n w', 'V': 'n w w n n n n n w', 'W': 'w w w n n n n n n',
  'X': 'n w n n w n n n w', 'Y': 'w w n n w n n n n', 'Z': 'n w w n w n n n n',
  '-': 'n w n n n n w n w', '.': 'w w n n n n w n n', ' ': 'n w w n n n w n n',
  '$': 'n w n w n w n n n', '/': 'n w n w n n n w n', '+': 'n w n n n w n w n',
  '%': 'n n n w n w n w n', '*': 'n w n n w n w n n' // Start/Stop Character
};

// Code128 Data
const code128Patterns = {
  // Maps ASCII char code to Code128 value and bar pattern
  // Subset B includes ASCII 32-127
  32: { value: 0, pattern: '212222' }, 33: { value: 1, pattern: '222122' }, 34: { value: 2, pattern: '222221' }, 35: { value: 3, pattern: '121223' }, 36: { value: 4, pattern: '121322' }, 37: { value: 5, pattern: '131222' }, 38: { value: 6, pattern: '122213' }, 39: { value: 7, pattern: '122312' }, 40: { value: 8, pattern: '132212' }, 41: { value: 9, pattern: '221213' }, 42: { value: 10, pattern: '221312' }, 43: { value: 11, pattern: '231212' }, 44: { value: 12, pattern: '112232' }, 45: { value: 13, pattern: '122132' }, 46: { value: 14, pattern: '122231' }, 47: { value: 15, pattern: '113222' }, 48: { value: 16, pattern: '123122' }, 49: { value: 17, pattern: '123221' }, 50: { value: 18, pattern: '223112' }, 51: { value: 19, pattern: '223211' }, 52: { value: 20, pattern: '211232' }, 53: { value: 21, pattern: '211331' }, 54: { value: 22, pattern: '213113' }, 55: { value: 23, pattern: '213311' }, 56: { value: 24, pattern: '213131' }, 57: { value: 25, pattern: '311213' }, 58: { value: 26, pattern: '311312' }, 59: { value: 27, pattern: '331112' }, 60: { value: 28, pattern: '312113' }, 61: { value: 29, pattern: '312311' }, 62: { value: 30, pattern: '332111' }, 63: { value: 31, pattern: '314111' }, 64: { value: 32, pattern: '221411' }, 65: { value: 33, pattern: '431111' }, 66: { value: 34, pattern: '111224' }, 67: { value: 35, pattern: '111422' }, 68: { value: 36, pattern: '121124' }, 69: { value: 37, pattern: '121421' }, 70: { value: 38, pattern: '141122' }, 71: { value: 39, pattern: '141221' }, 72: { value: 40, pattern: '112214' }, 73: { value: 41, pattern: '112412' }, 74: { value: 42, pattern: '122114' }, 75: { value: 43, pattern: '122411' }, 76: { value: 44, pattern: '142112' }, 77: { value: 45, pattern: '142211' }, 78: { value: 46, pattern: '241211' }, 79: { value: 47, pattern: '221114' }, 80: { value: 48, pattern: '413111' }, 81: { value: 49, pattern: '241112' }, 82: { value: 50, pattern: '134111' }, 83: { value: 51, pattern: '111242' }, 84: { value: 52, pattern: '121142' }, 85: { value: 53, pattern: '121241' }, 86: { value: 54, pattern: '114212' }, 87: { value: 55, pattern: '124112' }, 88: { value: 56, pattern: '124211' }, 89: { value: 57, pattern: '411212' }, 90: { value: 58, pattern: '421112' }, 91: { value: 59, pattern: '421211' }, 92: { value: 60, pattern: '212141' }, 93: { value: 61, pattern: '214121' }, 94: { value: 62, pattern: '412121' }, 95: { value: 63, pattern: '111143' }, 96: { value: 64, pattern: '111341' }, 97: { value: 65, pattern: '131141' }, 98: { value: 66, pattern: '114113' }, 99: { value: 67, pattern: '114311' }, 100: { value: 68, pattern: '411113' }, 101: { value: 69, pattern: '411311' }, 102: { value: 70, pattern: '113141' }, 103: { value: 71, pattern: '114131' }, 104: { value: 72, pattern: '311141' }, 105: { value: 73, pattern: '411131' }, 106: { value: 74, pattern: '211412' }, 107: { value: 75, pattern: '211214' }, 108: { value: 76, pattern: '211232' }, 109: { value: 77, pattern: '2331112' }, // Check this one, seems different length
  110: { value: 78, pattern: '211412' }, 111: { value: 79, pattern: '211214' }, 112: { value: 80, pattern: '211232' }, 113: { value: 81, pattern: '233111' }, 114: { value: 82, pattern: '211412' }, 115: { value: 83, pattern: '211214' }, 116: { value: 84, pattern: '211232' }, 117: { value: 85, pattern: '233111' }, 118: { value: 86, pattern: '211412' }, 119: { value: 87, pattern: '211214' }, 120: { value: 88, pattern: '211232' }, 121: { value: 89, pattern: '233111' }, 122: { value: 90, pattern: '211412' }, 123: { value: 91, pattern: '211214' }, 124: { value: 92, pattern: '211232' }, 125: { value: 93, pattern: '233111' }, 126: { value: 94, pattern: '211412' }, 127: { value: 95, pattern: '211214' },
  // Special characters (not directly mapped to ASCII but have values)
  START_B: { value: 104, pattern: '211214' },
  STOP: { value: 106, pattern: '2331112' } // Note: Stop pattern is 7 modules
};
// Reverse map for getting pattern from value (needed for checksum)
const code128ValueToPattern = Object.values(code128Patterns).reduce((acc, curr) => {
  if (curr.value !== undefined) acc[curr.value] = curr.pattern;
  return acc;
}, {});

const codes = computed(() => {
  const raw = mode.value === 'multiple'
    ? inputValue.value.split(',').map(s => s.trim()).filter(Boolean)
    : [inputValue.value.trim()].filter(Boolean); // Ensure single mode also filters empty input
  return raw;
});

const eanPatterns = {
  L: ['0001101','0011001','0010011','0111101','0100011','0110001','0101111','0111011','0110111','0001011'],
  G: ['0100111','0110011','0011011','0100001','0011101','0111001','0000101','0010001','0001001','0010111'],
  R: ['1110010','1100110','1101100','1000010','1011100','1001110','1010000','1000100','1001000','1110100']
};
const eanStructure = {
  EAN13: ['L','L','L','L','L','L','G','G','G','G','G','G'],
  EAN8:  ['L','L','L','L']
};

function calculateEANCheck(raw) {
  if (!/^\d+$/.test(raw)) return NaN; // Return NaN if not numeric
  let sum = 0;
  for (let i = 0; i < raw.length; i++) {
    sum += parseInt(raw[i], 10) * ((raw.length - i) % 2 === 1 ? 3 : 1); // EAN standard calculation
  }
  const mod = sum % 10;
  return mod === 0 ? 0 : 10 - mod;
}

function getFullCodeValue(code) {
  if (!code) return '';
  const currentType = type.value;
  const rawLen = currentType === 'EAN13' ? 12 : (currentType === 'EAN8' ? 7 : code.length);
  const clean = (code.match(/\d/g) || []).join('').slice(0, rawLen); // Take only required length

  if ((currentType === 'EAN13' || currentType === 'EAN8') && clean.length === rawLen) {
    const check = calculateEANCheck(clean);
    return clean + check;
  }
  return code; // Return original for Code128 or invalid EAN
}

function renderEAN(code) {
  const currentType = type.value; // Capture current type
  const cfg = eanStructure[currentType];
  if (!cfg) return `<svg xmlns="http://www.w3.org/2000/svg" width="${width.value}" height="${height.value}" viewBox="0 0 ${width.value} ${height.value}">
    <text x="${width.value/2}" y="${height.value/2}" font-size="${fontSize.value * 0.7}" fill="#333" text-anchor="middle">Invalid EAN type</text>
    <rect x="${width.value * 0.3}" y="${height.value/2 + 10}" width="${width.value * 0.4}" height="2" rx="1" fill="#ddd" />
  </svg>`; // Handle if type somehow changes

  const total = currentType === 'EAN13' ? 13 : 8;
  const rawLen = total - 1;
  const clean = (code.match(/\d/g) || []).join('').slice(0, rawLen); // Ensure correct length

  if (clean.length !== rawLen) {
    // Calculate progress percentage based on length
    const progressPercent = (clean.length / rawLen) * 100;
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${width.value}" height="${height.value}" viewBox="0 0 ${width.value} ${height.value}">
      <text x="${width.value/2}" y="${height.value/2}" font-size="${fontSize.value * 0.7}" fill="#333" text-anchor="middle">${clean.length}/${rawLen} digits</text>
      <rect x="${width.value * 0.3}" y="${height.value/2 + 10}" width="${width.value * 0.4}" height="2" rx="1" fill="#ddd" />
      <rect x="${width.value * 0.3}" y="${height.value/2 + 10}" width="${width.value * 0.4 * progressPercent / 100}" height="2" rx="1" fill="#666" />
    </svg>`;
  }

  const check = calculateEANCheck(clean);
  if (isNaN(check)) {
     return `<svg xmlns="http://www.w3.org/2000/svg" width="${width.value}" height="${height.value}" viewBox="0 0 ${width.value} ${height.value}">
      <text x="${width.value/2}" y="${height.value/2}" font-size="${fontSize.value * 0.7}" fill="#333" text-anchor="middle">Numbers only</text>
      <rect x="${width.value * 0.3}" y="${height.value/2 + 10}" width="${width.value * 0.4}" height="2" rx="1" fill="#ddd" />
     </svg>`;
  }

  const full = clean + check;
  let pattern = '101'; // Start guard

  if (currentType === 'EAN13') {
    const firstDigit = parseInt(full[0], 10); // Determine parity pattern from first digit (not needed for EAN8)
    const structure = eanStructure.EAN13; // Use fixed EAN13 structure for now
     for (let i = 0; i < 6; i++) { // First 6 digits use L or G based on structure
        const digit = parseInt(full[i], 10);
        pattern += eanPatterns[structure[i]][digit];
     }
     pattern += '01010'; // Center guard
     for (let i = 6; i < 12; i++) { // Last 6 digits use R
        const digit = parseInt(full[i+1], 10); // EAN-13 uses digits 1-12 for encoding bars (index 0 is parity)
        pattern += eanPatterns.R[digit];
     }
  } else { // EAN8
     for (let i = 0; i < 4; i++) pattern += eanPatterns.L[parseInt(full[i], 10)];
     pattern += '01010'; // Center guard
     for (let i = 4; i < 8; i++) pattern += eanPatterns.R[parseInt(full[i], 10)];
  }

  pattern += '101'; // End guard

  const calculatedWidth = width.value; // Use reactive width
  const calculatedHeight = height.value; // Use reactive height
  const moduleW = calculatedWidth / pattern.length;
  const barH = calculatedHeight;
  let x = 0;
  const bars = [];

  for (const bit of pattern) {
    if (bit === '1') {
      bars.push(`<rect x="${x.toFixed(2)}" y="0" width="${moduleW.toFixed(2)}" height="${barH}" fill="black"/>`);
    }
    x += moduleW;
  }

  // Add quiet zone (margin) if showMargin is true
  const marginSize = showMargin.value ? moduleW * 10 : 0; // Standard quiet zone is ~10 modules wide total
  const svgWidth = calculatedWidth + (showMargin.value ? marginSize : 0);
  const svgHeight = barH + (showValue.value ? fontSize.value + 5 : 0); // Add space for text + padding

  let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="${showMargin.value ? -marginSize/2 : 0} 0 ${svgWidth} ${svgHeight}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
  svg += `<g shape-rendering="crispEdges" transform="translate(${showMargin.value ? marginSize/2 : 0}, 0)">${bars.join('')}</g>`;

  if (showValue.value) {
     const textY = barH + fontSize.value * 1.2; // Position text below bars
     let textElements = '';
     const fontAttrs = `font-size="${fontSize.value}" text-anchor="middle" font-family="sans-serif" fill="black"`;

     if (currentType === 'EAN13') {
         // Calculate x positions for the 3 text segments
         const xFirst = (showMargin.value ? marginSize/2 : 0) - moduleW * 4; // Position before start guard
         const xLeft = (showMargin.value ? marginSize/2 : 0) + moduleW * (3 + (6*7)/2); // Center of left 6 digits
         const xRight = (showMargin.value ? marginSize/2 : 0) + moduleW * (3 + 6*7 + 5 + (6*7)/2); // Center of right 6 digits

         textElements += `<text x="${xFirst.toFixed(2)}" y="${textY}" ${fontAttrs}>${full[0]}</text>`; // First digit
         textElements += `<text x="${xLeft.toFixed(2)}" y="${textY}" ${fontAttrs}>${full.substring(1, 7)}</text>`; // Digits 1-6
         textElements += `<text x="${xRight.toFixed(2)}" y="${textY}" ${fontAttrs}>${full.substring(7)}</text>`; // Digits 7-12
     } else { // EAN8
         // Calculate x positions for the 2 text segments
         const xLeft = (showMargin.value ? marginSize/2 : 0) + moduleW * (3 + (4*7)/2); // Center of left 4 digits
         const xRight = (showMargin.value ? marginSize/2 : 0) + moduleW * (3 + 4*7 + 5 + (4*7)/2); // Center of right 4 digits

         textElements += `<text x="${xLeft.toFixed(2)}" y="${textY}" ${fontAttrs}>${full.substring(0,4)}</text>`; // Digits 0-3
         textElements += `<text x="${xRight.toFixed(2)}" y="${textY}" ${fontAttrs}>${full.substring(4)}</text>`; // Digits 4-7
     }
     svg += textElements;
  }
  svg += `</svg>`;
  return svg;
}

// Stub for Code128
function renderCode128(code) {
  const calculatedWidth = width.value;
  const calculatedHeight = height.value;
  let errorMessage = '';

  // 1. Validate input for Subset B (ASCII 32-127)
  const dataValues = [];
  for (let i = 0; i < code.length; i++) {
    const charCode = code.charCodeAt(i);
    if (charCode < 32 || charCode > 127) {
      errorMessage = `Invalid character '${code[i]}' for Code128 Subset B.`;
      break;
    }
    dataValues.push(code128Patterns[charCode].value);
  }

  if (errorMessage) {
    const svgHeightErr = calculatedHeight + (showValue.value ? fontSize.value + 5 : 0);
    let svgErr = `<svg xmlns="http://www.w3.org/2000/svg" width="${calculatedWidth}" height="${svgHeightErr}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
    svgErr += `<text x="${calculatedWidth/2}" y="${calculatedHeight/2}" fill="#333" font-size="${fontSize.value * 0.7}" text-anchor="middle">Invalid format</text>`;
    svgErr += `<rect x="${calculatedWidth * 0.3}" y="${calculatedHeight/2 + 10}" width="${calculatedWidth * 0.4}" height="2" rx="1" fill="#ddd" />`;
    if (showValue.value) {
      svgErr += `<text x="${calculatedWidth/2}" y="${calculatedHeight + fontSize.value}" text-anchor="middle" font-size="${fontSize.value}" fill="black" font-family="sans-serif">${code}</text>`;
    }
    svgErr += `</svg>`;
    return svgErr;
  }

  // 2. Calculate Checksum
  let checksumWeightSum = code128Patterns.START_B.value; // Start with Start B value
  dataValues.forEach((value, index) => {
    checksumWeightSum += value * (index + 1);
  });
  const checksumValue = checksumWeightSum % 103;

  // 3. Build the full sequence of patterns
  let fullPatternString = code128Patterns.START_B.pattern;
  dataValues.forEach(value => {
    fullPatternString += code128ValueToPattern[value];
  });
  fullPatternString += code128ValueToPattern[checksumValue];
  fullPatternString += code128Patterns.STOP.pattern;

  // 4. Generate binary bar pattern (1=bar, 0=space)
  let binaryPattern = '';
  for (const patternChar of fullPatternString) {
    const width = parseInt(patternChar, 10);
    const isBar = binaryPattern.length % 2 === 0; // Start with a bar
    binaryPattern += (isBar ? '1' : '0').repeat(width);
  }

  // 5. Draw SVG
  const totalModules = binaryPattern.length;
  const moduleW = calculatedWidth / totalModules;
  const barH = calculatedHeight;
  let x = 0;
  const bars = [];

  for (const bit of binaryPattern) {
    if (bit === '1') {
      bars.push(`<rect x="${x.toFixed(2)}" y="0" width="${moduleW.toFixed(2)}" height="${barH}" fill="black"/>`);
    }
    x += moduleW;
  }

  // Add quiet zone (margin)
  const marginSize = showMargin.value ? moduleW * 10 : 0; // ~10 modules quiet zone
  const svgWidth = calculatedWidth + (showMargin.value ? marginSize * 2 : 0); // Margin on both sides
  const svgHeight = barH + (showValue.value ? fontSize.value + 5 : 0);

  let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="${showMargin.value ? -marginSize : 0} 0 ${svgWidth} ${svgHeight}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
  svg += `<g shape-rendering="crispEdges" transform="translate(${showMargin.value ? marginSize : 0}, 0)">${bars.join('')}</g>`;

  if (showValue.value) {
    // Simple centered text for Code128
    const textY = barH + fontSize.value * 1.2;
    svg += `<text x="${svgWidth / 2}" y="${textY}" text-anchor="middle" font-size="${fontSize.value}" fill="black" font-family="sans-serif">${code}</text>`;
  }
  svg += `</svg>`;
  return svg;
}

// --- Code 39 Renderer ---
function renderCode39(code) {
  const calculatedWidth = width.value;
  const calculatedHeight = height.value;
  let errorMessage = '';

  // Validate input
  const upperCode = code.toUpperCase(); // Code 39 is case-insensitive by definition
  for (let i = 0; i < upperCode.length; i++) {
    if (!code39Patterns[upperCode[i]]) {
      errorMessage = `Invalid character '${code[i]}' for Code 39.`;
      break;
    }
  }

  if (errorMessage) {
    const svgHeightErr = calculatedHeight + (showValue.value ? fontSize.value + 5 : 0);
    let svgErr = `<svg xmlns="http://www.w3.org/2000/svg" width="${calculatedWidth}" height="${svgHeightErr}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
    svgErr += `<text x="${calculatedWidth/2}" y="${calculatedHeight/2}" fill="#333" font-size="${fontSize.value * 0.7}" text-anchor="middle">Invalid format</text>`;
    svgErr += `<rect x="${calculatedWidth * 0.3}" y="${calculatedHeight/2 + 10}" width="${calculatedWidth * 0.4}" height="2" rx="1" fill="#ddd" />`;
    if (showValue.value) {
      svgErr += `<text x="${calculatedWidth/2}" y="${calculatedHeight + fontSize.value}" text-anchor="middle" font-size="${fontSize.value}" fill="black" font-family="sans-serif">${code}</text>`;
    }
    svgErr += `</svg>`;
    return svgErr;
  }

  // Add start/stop characters
  const fullCode = '*' + upperCode + '*';

  // Generate bar/space pattern
  const bars = [];
  let totalNarrowModules = 0;
  let totalWideModules = 0;
  const narrowBar = 1; // Relative width
  const wideBar = 3;   // Common ratio for Code 39 (can be adjusted)
  const narrowSpace = 1; // Always 1

  for (const char of fullCode) {
      const pattern = code39Patterns[char].split(' ');
      // 9 elements: 5 bars, 4 spaces
      for (let i = 0; i < 9; i++) {
          const isBar = i % 2 === 0;
          const isWide = pattern[i] === 'w';
          const moduleWidth = isBar ? (isWide ? wideBar : narrowBar) : narrowSpace;

          if (isBar) {
              if (isWide) totalWideModules += wideBar; else totalNarrowModules += narrowBar;
              bars.push({ type: 'bar', width: moduleWidth });
          } else {
              // Inter-character space is always narrow
              totalNarrowModules += narrowSpace;
              bars.push({ type: 'space', width: moduleWidth });
          }
      }
       // Add inter-character gap (narrow space) except for the last character
      if (char !== fullCode[fullCode.length - 1]) {
          totalNarrowModules += narrowSpace;
          bars.push({ type: 'space', width: narrowSpace });
      }
  }

  const totalRelativeWidth = totalNarrowModules + totalWideModules;
  const moduleW = calculatedWidth / totalRelativeWidth;
  const barH = calculatedHeight;
  let x = 0;
  const svgBars = [];

  for (const bar of bars) {
      const currentWidth = bar.width * moduleW;
      if (bar.type === 'bar') {
          svgBars.push(`<rect x="${x.toFixed(2)}" y="0" width="${currentWidth.toFixed(2)}" height="${barH}" fill="black"/>`);
      }
      x += currentWidth;
  }

  // Add quiet zone (margin)
  const marginSize = showMargin.value ? moduleW * 10 : 0; // ~10 narrow modules quiet zone
  const svgWidth = calculatedWidth + (showMargin.value ? marginSize * 2 : 0);
  const svgHeight = barH + (showValue.value ? fontSize.value + 5 : 0);

  let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="${showMargin.value ? -marginSize : 0} 0 ${svgWidth} ${svgHeight}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
  svg += `<g shape-rendering="crispEdges" transform="translate(${showMargin.value ? marginSize : 0}, 0)">${svgBars.join('')}</g>`;

  if (showValue.value) {
      const textY = barH + fontSize.value * 1.2;
      // Code 39 usually displays text with start/stop characters if desired, or just data
      const displayText = code; // Or use fullCode to include asterisks
      svg += `<text x="${svgWidth / 2}" y="${textY}" text-anchor="middle" font-size="${fontSize.value}" fill="black" font-family="sans-serif">${displayText}</text>`;
  }
  svg += `</svg>`;
  return svg;
}

// --- Placeholder for unimplemented types ---
function renderNotImplemented(typeName) {
    const calculatedWidth = width.value;
    const calculatedHeight = height.value;
    const svgHeight = calculatedHeight + (showValue.value ? fontSize.value + 5 : 0);
    let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${calculatedWidth}" height="${svgHeight}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
    svg += `<text x="${calculatedWidth / 2}" y="${calculatedHeight / 2}" font-size="${fontSize.value * 0.7}" fill="#666" text-anchor="middle">${typeName} not implemented</text>`;
    svg += `<rect x="${calculatedWidth * 0.1}" y="${calculatedHeight * 0.1}" width="${calculatedWidth * 0.8}" height="${calculatedHeight * 0.8}" fill="none" stroke="#ccc" stroke-dasharray="5,5" />`;
    if (showValue.value && inputValue.value) {
        svg += `<text x="${calculatedWidth / 2}" y="${calculatedHeight + fontSize.value}" text-anchor="middle" font-size="${fontSize.value}" fill="#aaa" font-family="sans-serif">${inputValue.value}</text>`;
    }
    svg += `</svg>`;
    return svg;
}

// --- SSCC Validation and Rendering ---
function validateSSCC(code) {
  // SSCC must be numeric and exactly 18 digits
  if (!/^\d{18}$/.test(code)) {
    return {
      valid: false,
      errorMessage: `SSCC must be exactly 18 digits, got ${code.length} digits.`
    };
  }
  
  // Check if the check digit is valid (last digit)
  const digits = code.split('').map(d => parseInt(d, 10));
  const checkDigit = digits.pop(); // Remove and get the last digit
  
  // Calculate checksum: multiply odd positions by 3, sum all, mod 10, subtract from 10, mod 10
  let sum = 0;
  digits.forEach((digit, idx) => {
    sum += digit * (idx % 2 === 0 ? 3 : 1);
  });
  const calculatedCheck = (10 - (sum % 10)) % 10;
  
  if (calculatedCheck !== checkDigit) {
    return {
      valid: false,
      errorMessage: `Invalid check digit. Expected ${calculatedCheck}, got ${checkDigit}.`
    };
  }
  
  return { valid: true };
}

// Render SSCC as GS1-128 (Code 128 with specific structure)
function renderSSCC(code) {
  // Validate the input
  const validation = validateSSCC(code);
  if (!validation.valid) {
    const calculatedWidth = width.value;
    const calculatedHeight = height.value;
    const svgHeight = calculatedHeight + (showValue.value ? fontSize.value + 5 : 0);
    let svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${calculatedWidth}" height="${svgHeight}" style="background-color: ${showMargin.value ? 'white' : 'transparent'};">`;
    svg += `<text x="${calculatedWidth / 2}" y="${calculatedHeight / 2}" font-size="${fontSize.value * 0.7}" fill="#666" text-anchor="middle">${validation.errorMessage}</text>`;
    svg += `<rect x="${calculatedWidth * 0.1}" y="${calculatedHeight * 0.1}" width="${calculatedWidth * 0.8}" height="${calculatedHeight * 0.8}" fill="none" stroke="#ccc" stroke-dasharray="5,5" />`;
    if (showValue.value && inputValue.value) {
      svg += `<text x="${calculatedWidth / 2}" y="${calculatedHeight + fontSize.value}" text-anchor="middle" font-size="${fontSize.value}" fill="#aaa" font-family="sans-serif">${inputValue.value}</text>`;
    }
    svg += `</svg>`;
    return svg;
  }
  
  // For SSCC, we need to encode the full AI format (00) + 18 digits
  // AI (00) + SSCC
  const gs1Code = "(00)" + code;
  
  // Use the Code 128 renderer with the GS1-formatted code
  // This is a simplified approach - a full implementation would handle GS1 FNC1 characters properly
  return renderCode128(gs1Code);
}

// --- ITF-14 Validation and Rendering ---
function validateITF14(code) {
  // ITF-14 must be numeric and exactly 14 digits
  if (!/^\d{14}$/.test(code)) {
    return {
      valid: false,
      errorMessage: `ITF-14 must be exactly 14 digits, got ${code.length} digits.`
    };
  }
  
  // Check if the check digit is valid (last digit)
  const digits = code.substring(0, 13).split('').map(d => parseInt(d, 10));
  const checkDigit = parseInt(code[13], 10);
  
  // Calculate checksum: multiply odd positions by 3, sum all, mod 10, subtract from 10, mod 10
  let sum = 0;
  digits.forEach((digit, idx) => {
    sum += digit * (idx % 2 === 0 ? 1 : 3); // ITF-14 uses different weighting than SSCC
  });
  const calculatedCheck = (10 - (sum % 10)) % 10;
  
  if (calculatedCheck !== checkDigit) {
    return {
      valid: false,
      errorMessage: `Invalid check digit. Expected ${calculatedCheck}, got ${checkDigit}.`
    };
  }
  
  return { valid: true };
}

// Render ITF-14 as a placeholder for now
function renderITF14(code) {
  // For now, just render a placeholder until we implement the actual ITF renderer
  return renderNotImplemented('ITF-14 (Coming Soon)');
}

// --- GS1-128 rendering ---
function renderGS1_128(code) {
  // GS1-128 is a specialized application of Code 128
  // For now, provide a basic implementation that adds the FNC1 character at the beginning
  return renderNotImplemented('GS1-128 (Coming Soon)');
}

// --- GTIN rendering ---
function renderGTIN(code) {
  // GTIN can be GTIN-8, GTIN-12, GTIN-13, or GTIN-14
  // We can detect the format based on length and use the appropriate renderer
  const length = code.replace(/\D/g, '').length;
  
  if (length === 8) {
    return renderEAN(code); // Use EAN-8 renderer
  } else if (length === 13) {
    return renderEAN(code); // Use EAN-13 renderer
  } else if (length === 14) {
    return renderITF14(code); // Use ITF-14 renderer
  } else {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="${width.value}" height="${height.value}">
      <text x="${width.value/2}" y="${height.value/2}" font-size="${fontSize.value * 0.7}" text-anchor="middle" fill="#666">
        Invalid GTIN format. Must be 8, 13, or 14 digits.
      </text>
    </svg>`;
  }
}

function generateSvg(code) {
  if (!code) return '';
  switch (type.value) {
    case 'EAN13':
    case 'ISBN13': // Treat ISBN13 as EAN13 for rendering
    case 'EAN8':
      return renderEAN(code);
    case 'CODE128':
      return renderCode128(code);
    case 'CODE39':
      return renderCode39(code);
    case 'SSCC':
      return renderSSCC(code);
    case 'ITF14':
      return renderITF14(code);
    case 'GS1_128':
      return renderGS1_128(code);
    case 'GTIN':
      return renderGTIN(code);
    case 'CODE39_FULL_ASCII':
      return renderNotImplemented('Code-39 Full ASCII');
    case 'CODE93':
      return renderNotImplemented('Code-93');
    case 'CODE11':
        return renderNotImplemented('Code-11');
    case 'CODABAR':
        return renderNotImplemented('Codabar');
    case 'ITF':
        return renderNotImplemented('Code-2of5 Interleaved');
    case 'MSI':
        return renderNotImplemented('MSI');
    case 'PHARMACODE':
        return renderNotImplemented('Pharmacode');
    case 'TELEPEN_ALPHA':
        return renderNotImplemented('Telepen Alpha');
    default:
      // Fallback for any unexpected type
      return `<svg xmlns="http://www.w3.org/2000/svg" width="${width.value}" height="${height.value}"><text x="10" y="20" fill="red">Unknown Type: ${type.value}</text></svg>`;
  }
}

async function download(code, idx, format) { // Added format parameter
  if (!code) return;
  const svgString = generateSvg(code);
  const filename = `${type.value}_${code || 'barcode'}.${format}`;
  const a = document.createElement('a');
  a.style.display = 'none';
  document.body.appendChild(a);

  if (format === 'svg') {
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  } else if (format === 'png') {
    const img = new Image();
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    img.onload = () => {
      const canvas = document.createElement('canvas');
      const svgElement = new DOMParser().parseFromString(svgString, 'image/svg+xml').documentElement;
      canvas.width = parseFloat(svgElement.getAttribute('width') || width.value);
      canvas.height = parseFloat(svgElement.getAttribute('height') || height.value);
      const ctx = canvas.getContext('2d');

      if (ctx) {
        if (!showMargin.value) {
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        canvas.toBlob(blob => {
          if (blob) {
            const pngUrl = URL.createObjectURL(blob);
            a.href = pngUrl;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(pngUrl);
          }
          URL.revokeObjectURL(url);
        }, 'image/png');
      } else {
        URL.revokeObjectURL(url);
      }
    };
    img.onerror = () => {
        console.error("Failed to load SVG image for PNG conversion.");
        URL.revokeObjectURL(url);
    };
    img.src = url;
  }
  document.body.removeChild(a);
}

const previewCode = computed(() => {
  // Only preview the first valid code
  return codes.value.length > 0 ? codes.value[0] : null;
});

</script>

<style scoped>
/* Ensure SVG scales nicely within its container */
.barcode-svg-container :deep(svg) {
  display: block;
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* Progress bar animation */
@keyframes progress {
  0% { width: 0; }
  50% { width: 60%; }
  100% { width: 100%; }
}

.animate-progress {
  animation: progress 1.5s ease-in-out infinite;
  width: 0;
  background-color: #333;
  border-radius: 1px;
}

/* Add styles for range sliders like in IconGrid.vue */
.range-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
}

/* Track */
.range-slider::-webkit-slider-runnable-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

.range-slider::-moz-range-track {
  height: 2px;
  background: rgb(209 213 219); /* gray-300 */
  border: none;
}

/* Thumb */
.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  margin-top: -7px;
  transition: all 0.15s ease;
}

.range-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #000000;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.15s ease;
}

/* Hover state */
.range-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.range-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* Focus state */
.range-slider:focus {
  outline: none;
}

/* Dark mode */
.dark .range-slider::-webkit-slider-runnable-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-moz-range-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark .range-slider::-webkit-slider-thumb {
  background: #ffffff;
  border-color: #000000;
}

.dark .range-slider::-moz-range-thumb {
  background: #ffffff;
  border-color: #000000;
}

/* Style toggle */
.toggle {
  border: 0;
  --tglbg: #d1d5db; /* gray-300 */
  background-color: #d1d5db;
}
.dark .toggle {
   --tglbg: #4b5563; /* gray-600 */
   background-color: #4b5563;
}

.toggle:checked {
   --tglbg: var(--fallback-p,oklch(var(--p)/1));
   background-color: var(--fallback-p,oklch(var(--p)/1));
   border-color: var(--fallback-p,oklch(var(--p)/1));
}
.toggle:checked:hover {
   --tglbg: var(--fallback-p,oklch(var(--p)/1));
   background-color: var(--fallback-p,oklch(var(--p)/1));
   border-color: var(--fallback-p,oklch(var(--p)/1));
}
.toggle:disabled {
   opacity: 0.5;
   cursor: not-allowed;
}

/* Transition for the type selector */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 