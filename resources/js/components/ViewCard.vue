<template>
  <div class="flex flex-col gap-2 relative group h-full">
    <!-- Edit button in top-right corner, visible on hover -->
    <button 
      @click="$emit('edit', bookmark)" 
      class="absolute top-1 right-1 text-blue-500 hover:text-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 p-1"
    >
      <i class="design design-edit"></i>
    </button>
    
    <div class="flex items-center mb-2">
      <i :class="bookmark.icon_class" class="text-3xl text-gray-700 dark:text-gray-300 mr-3"></i>
      <h3 class="text-lg gant-modern-bold dark:text-gray-200 line-clamp-1">{{ bookmark.title }}</h3>
    </div>
    
    <div class="flex-grow flex flex-wrap gap-2 content-start">
      <a
        v-for="link in bookmark.links"
        :key="link.id"
        :href="link.url"
        target="_blank"
        class="px-2 py-1 bg-transparent text-sm rounded hover:bg-slate-950 dark:hover:bg-white border border-solid border-gray-200 dark:border-gray-800 transition-colors duration-200 gant-modern-regular hover:gant-modern-bold hover:text-white"
      >
        {{ link.label }}
      </a>
      <div v-if="bookmark.links.length === 0" class="text-slate-500 dark:text-slate-400 text-sm italic">
        No links added
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ViewCard',
  props: {
    bookmark: {
      type: Object,
      required: true
    }
  }
};
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 