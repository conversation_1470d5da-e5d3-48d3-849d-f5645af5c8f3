<template>
  <div class="ordergroup-image-container flex flex-col lg:flex-row w-full gap-4 sm:gap-6">
    <!-- Left Column - Preview Area -->
    <div class="preview-area w-full lg:w-1/2 bg-white dark:bg-slate-950 border border-gray-100 dark:border-gray-800 rounded-xl p-4 sm:p-6 overflow-hidden transition-all duration-300 hover:shadow-lg">
      <div class="preview-header mb-4 sm:mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div>
          <h3 class="text-lg sm:text-xl gant-modern-bold tracking-tighter dark:text-gray-200 mb-1">Preview</h3>
          <p class="text-xs text-gray-500 dark:text-gray-400">{{ selectedImages.length }} images selected</p>
        </div>
        <div class="controls flex items-center gap-2">
          <button 
            @click="combineSelectedImages" 
            :disabled="selectedImages.length === 0"
            :class="{
              'opacity-50 cursor-not-allowed': selectedImages.length === 0,
              'hover:scale-105 active:scale-95': selectedImages.length > 0
            }"
            class="btn-primary px-4 py-2 rounded-lg bg-slate-950 dark:bg-white text-white dark:text-black border-2 border-transparent font-medium transition-all duration-300 shadow-md text-sm flex items-center gap-2"
          >
            <i class="design design-combine-outline text-lg"></i>
            <span class="gant-modern-bold">Combine Images</span>
          </button>
          <button 
            v-if="combinedImageUrl" 
            @click="downloadCombinedImage" 
            class="btn-download px-4 py-2 rounded-lg bg-green-600 text-white border-2 border-transparent font-medium transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-md text-sm flex items-center gap-2"
          >
            <i class="design design-download-outline text-lg"></i>
            <span class="gant-modern-bold">Download</span>
          </button>
        </div>
      </div>
      <div 
        class="preview-content flex items-center justify-center bg-gray-50 dark:bg-gray-900 rounded-lg overflow-auto transition-all duration-300 h-[350px] sm:h-[450px] lg:h-[500px]"
        :class="{'animate-pulse': isProcessing}"
      >
        <div v-if="combinedImageUrl" class="relative w-full h-full flex items-center justify-center p-4">
          <img 
            :src="combinedImageUrl" 
            alt="Combined Image" 
            class="max-w-full max-h-full object-contain rounded-lg shadow-sm transition-all duration-300 hover:scale-[1.02]" 
          />
        </div>
        <div v-else-if="isProcessing" class="text-center p-6">
          <i class="design design-loading-outline text-5xl text-gray-300 dark:text-gray-600 mb-3 block animate-spin"></i>
          <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-sm">
            Processing images...
          </p>
        </div>
        <div v-else class="text-center p-6">
          <i class="design design-browse-gallery-outline text-5xl text-gray-300 dark:text-gray-600 mb-3 block"></i>
          <p class="text-gray-500 dark:text-gray-400 gant-modern-regular text-sm">
            Select images from the folders on the right and click "Combine Images" to generate a preview
          </p>
        </div>
      </div>
    </div>

    <!-- Right Column - Image Folders Accordion -->
    <div class="image-folders w-full lg:w-1/2 bg-white dark:bg-slate-950 border border-gray-100 dark:border-gray-800 rounded-xl p-4 sm:p-6 overflow-auto max-h-[500px] lg:max-h-[calc(100vh-12rem)] transition-all duration-300 hover:shadow-lg [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:h-1.5 [&::-webkit-scrollbar-track]:bg-black/5 [&::-webkit-scrollbar-track]:rounded-lg [&::-webkit-scrollbar-thumb]:bg-black/20 [&::-webkit-scrollbar-thumb]:rounded-lg">
      <div class="folders-header mb-4 sm:mb-6">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg sm:text-xl gant-modern-bold tracking-tighter dark:text-gray-200">Image Folders</h3>
          <span class="text-xs text-gray-500 dark:text-gray-400">{{ totalImages }} images available</span>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 gant-modern-regular">
          Select images from different folders to combine
        </p>
      </div>

      <!-- Settings Panel -->
      <div class="settings-panel mb-4 sm:mb-6 border border-gray-100 dark:border-gray-800 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/50 transition-all duration-300">
        <h4 class="text-sm font-semibold mb-3 dark:text-gray-300 flex items-center gap-2">
          <i class="design design-settings-outline text-lg"></i>
          Output Settings
        </h4>
        <div class="grid grid-cols-1 gap-3">
          <div>
            <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1.5">Output Filename</label>
            <div class="relative">
              <input 
                v-model="outputFilename" 
                type="text" 
                class="w-full px-3 py-2 bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-200"
                placeholder="combined-image"
              >
              <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">.jpg</span>
            </div>
          </div>
          <div class="flex gap-4">
            <div class="w-1/2">
              <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1.5">Width (px)</label>
              <input 
                v-model="outputWidth" 
                type="number" 
                min="1"
                class="w-full px-3 py-2 bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-200"
              >
            </div>
            <div class="w-1/2">
              <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1.5">Height (px)</label>
              <input 
                v-model="outputHeight" 
                type="number" 
                min="1"
                class="w-full px-3 py-2 bg-white dark:bg-slate-950 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm transition-all duration-200"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Image Folders Accordion -->
      <div class="accordion-container space-y-2">
        <div 
          v-for="(folder, index) in folders" 
          :key="index" 
          class="accordion-item border border-gray-100 dark:border-gray-800 rounded-lg overflow-hidden transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-700"
        >
          <div 
            @click="toggleFolder(index)" 
            class="accordion-header flex justify-between items-center p-3 cursor-pointer bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            <div class="flex items-center gap-2">
              <i :class="[
                'design',
                folder.name === 'Icons' ? 'design-image-multiple-outline' :
                folder.name === 'Illustrations' ? 'design-brush-outline' :
                'design-logo-outline',
                'text-lg'
              ]"></i>
              <h4 class="text-sm font-semibold dark:text-gray-300">
                {{ folder.name }}
                <span class="text-xs text-gray-500 dark:text-gray-400 ml-2">
                  ({{ folder.images.filter(img => img.selected).length }}/{{ folder.images.length }})
                </span>
              </h4>
            </div>
            <span class="transform transition-transform duration-200" :class="{'rotate-180': folder.isOpen}">
              <i class="design design-arrow-down text-lg"></i>
            </span>
          </div>
          <div 
            v-show="folder.isOpen" 
            class="accordion-content p-3 bg-white dark:bg-slate-950 transition-all duration-300 max-h-[300px] overflow-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:h-1.5 [&::-webkit-scrollbar-track]:bg-black/5 [&::-webkit-scrollbar-track]:rounded-lg [&::-webkit-scrollbar-thumb]:bg-black/20 [&::-webkit-scrollbar-thumb]:rounded-lg" 
          >
            <div v-if="folder.loading" class="py-6 text-center">
              <i class="design design-loading-outline animate-spin text-2xl text-gray-400 dark:text-gray-600 mb-2"></i>
              <p class="text-sm text-gray-500 dark:text-gray-400">Loading images...</p>
            </div>
            <div v-else-if="!folder.images.length" class="py-6 text-center">
              <i class="design design-folder-remove-outline text-2xl text-gray-400 dark:text-gray-600 mb-2"></i>
              <p class="text-sm text-gray-500 dark:text-gray-400">No images found in this folder</p>
            </div>
            <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-3 xl:grid-cols-4 gap-3">
              <div 
                v-for="(image, imageIndex) in folder.images" 
                :key="imageIndex" 
                @click="toggleSelectImage(folder.id, imageIndex)"
                class="group cursor-pointer rounded-lg transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                <div 
                  class="relative aspect-square border-2 rounded-lg overflow-hidden transition-all duration-200"
                  :class="{
                    'border-blue-500 dark:border-blue-400 shadow-md': image.selected,
                    'border-gray-200 dark:border-gray-700': !image.selected
                  }"
                >
                  <div class="absolute inset-0 bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-2">
                    <img :src="image.url" :alt="image.name" class="max-w-full max-h-full object-contain" />
                  </div>
                  <div 
                    v-if="image.selected" 
                    class="absolute inset-0 bg-blue-500/10 dark:bg-blue-400/10 flex items-center justify-center"
                  >
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg">
                      <i class="design design-check text-sm"></i>
                    </div>
                  </div>
                </div>
                <p class="mt-1.5 text-2xs text-center truncate dark:text-gray-300">{{ image.name }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';

// State
const folders = reactive([
  { 
    id: 1, 
    name: 'Icons', 
    path: 'icons', 
    isOpen: false,
    loading: false,
    images: []
  },
  { 
    id: 2, 
    name: 'Illustrations', 
    path: 'illustrations', 
    isOpen: false,
    loading: false,
    images: []
  },
  { 
    id: 3, 
    name: 'Logos', 
    path: 'logos', 
    isOpen: false,
    loading: false,
    images: []
  }
]);

const combinedImageUrl = ref(null);
const outputFilename = ref('combined-image');
const outputWidth = ref(800);
const outputHeight = ref(600);
const isProcessing = ref(false);

// Computed properties
const totalImages = computed(() => {
  return folders.reduce((total, folder) => total + folder.images.length, 0);
});

const selectedImages = computed(() => {
  const selected = [];
  folders.forEach(folder => {
    folder.images.forEach(image => {
      if (image.selected) {
        selected.push({
          name: image.name,
          content: image.content,
          folderName: folder.name
        });
      }
    });
  });
  return selected;
});

// Toggle folder open/closed
const toggleFolder = (index) => {
  folders[index].isOpen = !folders[index].isOpen;
  
  // Load images if this is the first time opening the folder
  if (folders[index].isOpen && folders[index].images.length === 0) {
    loadImagesFromFolder(folders[index]);
  }
};

// Load images from a folder
const loadImagesFromFolder = async (folder) => {
  folder.loading = true;
  
  try {
    // In a real implementation, this would be an API call to get images from the server
    const response = await fetch(`/api/images/${folder.path}`);
    if (!response.ok) throw new Error('Failed to load images');
    
    const data = await response.json();
    
    folder.images = data.map(image => ({
      name: image.name,
      url: image.url,
      content: image.content,
      selected: false
    }));
  } catch (error) {
    console.error('Error loading images:', error);
    // Add some mock data for demonstration purposes
    await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay
    folder.images = Array.from({ length: 9 }, (_, i) => ({
      name: `${folder.name.toLowerCase()}-${i + 1}.jpg`,
      url: `/img/demo/${folder.path}/${i + 1}.jpg`,
      content: `<img src="/img/demo/${folder.path}/${i + 1}.jpg" alt="${folder.name} ${i + 1}" />`,
      selected: false
    }));
  } finally {
    folder.loading = false;
  }
};

// Toggle selection of an image
const toggleSelectImage = (folderId, imageIndex) => {
  const folder = folders.find(f => f.id === folderId);
  if (folder) {
    folder.images[imageIndex].selected = !folder.images[imageIndex].selected;
  }
};

// Combine selected images
const combineSelectedImages = async () => {
  const selected = selectedImages.value;
  
  if (selected.length === 0) {
    alert('Please select at least one image to combine');
    return;
  }
  
  isProcessing.value = true;
  combinedImageUrl.value = null;
  
  try {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // In a real implementation, this would be an API call to combine images on the server
    const combinedImage = `<img src="/img/demo/combined-image.jpg" alt="Combined Image" />`;
    combinedImageUrl.value = 'data:image/jpeg;base64,' + btoa(combinedImage);
  } finally {
    isProcessing.value = false;
  }
};

// Download the combined image
const downloadCombinedImage = () => {
  if (!combinedImageUrl.value) return;
  
  const link = document.createElement('a');
  link.href = combinedImageUrl.value;
  link.download = `${outputFilename.value || 'combined-image'}.jpg`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Initialize component
onMounted(() => {
  // Open the first folder by default
  if (folders.length > 0) {
    toggleFolder(0);
  }
});
</script>

<style scoped>
.accordion-content {
  overflow: auto;
  transition: all 0.3s ease-out;
}

/* Custom scrollbar styling */
.accordion-content::-webkit-scrollbar,
.image-folders::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.accordion-content::-webkit-scrollbar-track,
.image-folders::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.accordion-content::-webkit-scrollbar-thumb,
.image-folders::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.dark .accordion-content::-webkit-scrollbar-track,
.dark .image-folders::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark .accordion-content::-webkit-scrollbar-thumb,
.dark .image-folders::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .preview-content {
    height: 350px;
  }
  
  .image-folders {
    max-height: 500px;
  }
}

@media (min-width: 1024px) {
  .preview-content {
    height: 500px;
  }
  
  .image-folders {
    max-height: calc(100vh - 12rem);
  }
}

/* Transitions */
.image-item {
  transition: all 0.2s ease-out;
}

.image-item:hover {
  transform: scale(1.05);
}

.btn-primary,
.btn-download {
  transition: all 0.2s ease-out;
}

.btn-primary:hover:not(:disabled),
.btn-download:hover {
  transform: scale(1.05);
}

.preview-area,
.image-folders {
  transition: all 0.3s ease-out;
}
</style> 