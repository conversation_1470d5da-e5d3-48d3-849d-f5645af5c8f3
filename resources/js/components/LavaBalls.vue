<template>
  <div class="lavaballs">
    <svg class="lavasvg" viewBox="0 0 1000 1000" preserveAspectRatio="xMidYMid slice">
      <defs>
        <filter id="gooify" width="400%" x="-10%" height="400%" y="-150%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="15" result="blur" />
          <feColorMatrix
            in="blur"
            mode="matrix"
            values="1 0 0 0 0
                   0 1 0 0 0
                   0 0 1 0 0
                   0 0 0 25 -10"
          />
        </filter>

        <linearGradient id="lavaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#ff1900" />
          <stop offset="100%" stop-color="#eec80c" />
        </linearGradient>
      </defs>

      <g filter="url(#gooify)">
        <circle
          class="blobb glow"
          fill="url(#lavaGradient)"
          :cx="circle1.x"
          :cy="circle1.y"
          r="100"
        />

        <circle
          class="blobb glow"
          fill="url(#lavaGradient)"
          :cx="circle2.x"
          :cy="circle2.y"
          r="80"
        />

        <circle
          class="blobb glow"
          fill="url(#lavaGradient)"
          :cx="circle3.x"
          :cy="circle3.y"
          r="90"
        />
        <circle
          class="blobb glow"
          fill="url(#lavaGradient)"
          :cx="circle4.x"
          :cy="circle4.y"
          r="95"
        />
        <circle
          class="blobb glow"
          fill="url(#lavaGradient)"
          :cx="circle5.x"
          :cy="circle5.y"
          r="85"
        />
      </g>
    </svg>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const circle1 = ref({ x: 500, y: 500 });
const circle2 = ref({ x: 500, y: 500 });
const circle3 = ref({ x: 500, y: 500 });
const circle4 = ref({ x: 500, y: 500 });
const circle5 = ref({ x: 500, y: 500 });

let animationFrame;

const animate = () => {
  const time = Date.now() * 0.0002; // Keep the slow animation speed
  
  circle1.value = {
    x: 500 + Math.sin(time * 0.3) * 200,
    y: 500 + Math.cos(time * 0.2) * 200
  };
  
  circle2.value = {
    x: 500 + Math.sin(time * 0.4) * 150,
    y: 500 + Math.cos(time * 0.3) * 150
  };
  
  circle3.value = {
    x: 500 + Math.sin(time * 0.2) * 175,
    y: 500 + Math.cos(time * 0.4) * 175
  };
  
  circle4.value = {
    x: 500 + Math.sin(time * 0.3) * 190,
    y: 500 + Math.cos(time * 0.2) * 190
  };
  
  circle5.value = {
    x: 500 + Math.sin(time * 0.25) * 160,
    y: 500 + Math.cos(time * 0.35) * 160
  };
  
  animationFrame = requestAnimationFrame(animate);
};

onMounted(() => {
  animate();
});

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
  }
});
</script>

<style scoped>
.lavaballs {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.lavasvg {
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0;
  overflow: visible;
}

.blobb {
  fill: url(#lavaGradient);
  transition: all 0.3s ease;
  mix-blend-mode: soft-light;
  opacity: 0.6;
}

.glow {
  filter: url(#glowing);
}
</style>
