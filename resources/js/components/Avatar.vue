<script setup>
import { computed } from 'vue'

// Import all user images so Vite processes them
const images = import.meta.glob([
    '../../img/users/*.jpeg',
    '../../img/users/*.jpg',
    '../../img/users/*.png'
], { eager: true })

const props = defineProps({
    user: {
        type: Object,
        required: true
    },
    size: {
        type: String,
        default: 'md'
    },
    ring: {
        type: Boolean,
        default: true
    }
})

const sizes = {
    sm: 'w-10 h-10',
    md: 'w-20 h-20',
    lg: 'w-32 h-32'
}

const defaultAvatar = '/img/default-avatar.png'

// Modified to properly handle the avatar_url property from the HasAvatar trait
const avatarUrl = computed(() => {
    // Try to access avatar_url directly or return default
    if (props.user?.avatar_url) {
        return props.user.avatar_url;
    }
    
    // If there's no avatar_url but there's a login, try to generate the path
    if (props.user?.login) {
        const loginBasedPath = `/img/users/${props.user.login}.jpeg`;
        // Check if it's in our Vite-processed images
        const imageKey = Object.keys(images).find(path => 
            path.includes(`/${props.user.login}.jpeg`) || 
            path.includes(`/${props.user.login}.jpg`) || 
            path.includes(`/${props.user.login}.png`)
        );
        
        if (imageKey) {
            // Return the processed image URL from Vite
            return images[imageKey].default;
        }
        
        // Try the login-based path directly
        return loginBasedPath;
    }
    
    // Fallback to default
    return defaultAvatar;
})
</script>

<template>
    <div
        :class="[
            sizes[size],
            'bg-gray-950 dark:bg-slate-900 rounded-full flex items-center justify-center overflow-hidden',
            { 'ring-2 ring-gray-200 dark:ring-gray-700': ring }
        ]"
    >
        <img 
            :src="avatarUrl"
            :alt="`${user.name || user.login || 'User'}'s avatar`"
            class="w-full h-full object-cover"
            @error="$event.target.src = defaultAvatar"
            loading="lazy"
        >
    </div>
</template> 