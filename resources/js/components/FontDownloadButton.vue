<template>
  <button
    @click="handleDownload"
    class="bg-transparent flex-none text-black border border-gray-200 dark:text-gray-200 dark:border-gray-600 gant-modern-bold py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
  >
    <a class="flex items-center gap-2">
      <i :class="['design', `design-${type}`, 'opacity-25']"></i>
      <span>.{{ type }}</span>
    </a>
  </button>
</template>

<script>
export default {
  name: 'FontDownloadButton',
  
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['scss', 'woff2'].includes(value)
    },
    fontFamily: {
      type: String,
      required: true
    },
    currentSettings: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      fontFiles: {
        'Gant-Modern': {
          woff2: {
            300: 'GantModernV2-Light.woff2',
            500: 'GantModernV2-Regular.woff2',
            700: 'GantModernV2-Medium.woff2',
            900: 'GantModernV2-Bold.woff2',
            '300i': 'GantModernV2-LightItalic.woff2',
            '500i': 'GantModernV2-RegularItalic.woff2',
            '700i': 'GantModernV2-MediumItalic.woff2',
            '900i': 'GantModernV2-BoldItalic.woff2'
          },
          scss: `@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-Regular.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-Medium.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-Bold.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-LightItalic.woff2') format('woff2');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-RegularItalic.woff2') format('woff2');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-MediumItalic.woff2') format('woff2');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Gant-Modern';
  src: url('../fonts-typeface/GantModernV2-BoldItalic.woff2') format('woff2');
  font-weight: 900;
  font-style: italic;
}`
        },
        'Gant-Serif': {
          woff2: {
            900: 'GantSerif-MediumCondensed.woff2'
          },
          scss: `@font-face {
  font-family: 'Gant-Serif';
  src: url('../fonts-typeface/GantSerif-MediumCondensed.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
}`
        },
        'nunito-sans': {
          woff2: {
            variable: 'nunito-sans.woff2'
          },
          scss: `@font-face {
  font-family: 'nunito-sans';
  src: url('../fonts-typeface/nunito-sans.woff2') format('woff2-variations');
  font-weight: 200 1000;
  font-stretch: 75% 125%;
  font-style: normal;
}`
        }
      }
    }
  },

  methods: {
    async handleDownload() {
      const fontData = this.fontFiles[this.fontFamily];
      if (!fontData) return;

      if (this.type === 'scss') {
        this.downloadScss(fontData.scss);
      } else if (this.type === 'woff2') {
        await this.downloadWoff2(fontData.woff2);
      }
    },

    downloadScss(scssContent) {
      const blob = new Blob([scssContent], { type: 'text/scss' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.fontFamily.toLowerCase()}.scss`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },

    async downloadWoff2(woff2Files) {
      if (!woff2Files) return;

      // For variable fonts, download the single file
      if (woff2Files.variable) {
        const response = await fetch(`/fonts-typeface/${woff2Files.variable}`);
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = woff2Files.variable;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        return;
      }

      // For static fonts, determine which file to download based on current settings
      const weight = this.currentSettings.weight || 400;
      const isItalic = this.currentSettings.italic;
      
      // Find the closest weight
      const weights = Object.keys(woff2Files).filter(w => !w.includes('i')).map(Number);
      const closestWeight = weights.reduce((prev, curr) => {
        return Math.abs(curr - weight) < Math.abs(prev - weight) ? curr : prev;
      });

      const fileKey = isItalic ? `${closestWeight}i` : closestWeight;
      const fileName = woff2Files[fileKey];

      if (fileName) {
        const response = await fetch(`/fonts-typeface/${fileName}`);
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    }
  }
}
</script>
