<template>
  <div class="max-w-4xl mx-auto mb-2 sm:mb-6" v-if="showHeader">
    <p class="opacity-50 dark:text-white">{{ subheadline }}</p>
    <h2 class="text-5xl lg:text-6xl gant-modern-bold dark:text-white">{{ title }}</h2>
    <div class="text-gray-500 dark:text-gray-400">
      {{ excerpt }}
    </div>
  </div>
  <div class="terminal-container max-w-4xl mx-auto rounded-lg overflow-hidden shadow-2xl mb-20">
    <div class="terminal-header bg-gray-800 p-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 rounded-full bg-red-500"></div>
          <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div class="w-3 h-3 rounded-full bg-green-500"></div>
          <span class="ml-4 text-gray-400 text-sm">{{ filename }}</span>
        </div>
        <div class="flex items-center space-x-4">
          <a v-if="downloadUrl" 
             :href="downloadUrl" 
             :download="filename" 
             class="text-gray-400 hover:text-gray-200 text-sm focus:outline-none flex items-center space-x-2">
            <i class="design design-download-file text-xl"></i>
            <span>Download file</span>
          </a>
          <button @click="copyCode" 
                  class="text-gray-400 hover:text-gray-200 text-sm focus:outline-none flex items-center space-x-2"
                  :class="{ 'text-green-400': copied }">
            <i class="design design-copyclipboard-scss text-xl"></i>
            <span>{{ copied ? 'Copied!' : 'Copy to clipboard' }}</span>
          </button>
          <div class="flex items-center space-x-2">
            <button v-if="isAnimating" 
                    @click="skipAnimation" 
                    class="text-gray-400 hover:text-gray-200 text-sm focus:outline-none flex items-center space-x-2">
              <i class="wms wms-play-and-pause-button text-xl"></i>
              <span>Skip</span>
            </button>
            <button v-if="isAnimating" 
                    @click="togglePause" 
                    class="text-gray-400 hover:text-gray-200 text-sm focus:outline-none flex items-center space-x-2">
              <i class="wms text-xl" :class="isPaused ? 'wms-play-start' : 'wms-prerusit2'"></i>
              <span>{{ isPaused ? 'Resume' : 'Pause' }}</span>
            </button>
            <button v-if="!isAnimating"
                    @click="toggleExpand" 
                    class="text-gray-400 hover:text-gray-200 text-sm focus:outline-none flex items-center space-x-2">
              <span class="flex items-center">
                <i class="design ml-2 me-2 text-xl" :class="isExpanded ? 'design-hidden-fill' : 'design-visible-fill'"></i>
                {{ isExpanded ? 'Show Less' : 'Show More' }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="terminal-body bg-gray-900 p-4 font-mono text-sm">
      <div ref="codeWrapper" 
           class="transition-all duration-500 ease-in-out relative overflow-hidden"
           :class="{ 'max-h-64': !isExpanded, 'max-h-[100000px]': isExpanded }">
        <div class="relative">
          <pre class="text-gray-100 whitespace-pre-wrap leading-relaxed"><code :class="'language-' + language" ref="codeElement" v-html="initialCodeDisplay"></code></pre>
          <span v-if="isAnimating && !isPaused" class="typing-cursor"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue'
import { useClipboard, useTimeoutFn } from '@vueuse/core'
import Prism from 'prismjs'
import 'prismjs/components/prism-scss'

// Props
const props = defineProps({
  code: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  downloadUrl: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'scss'
  },
  title: {
    type: String,
    default: 'Classes'
  },
  subheadline: {
    type: String,
    default: ''
  },
  lastUpdate: {
    type: String,
    default: ''
  },
  iconsCount: {
    type: [Number, String],
    default: 0
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  excerpt: {
    type: String,
    default: ''
  }
})

// Refs
const isExpanded = ref(false)
const isAnimating = ref(false)
const isPaused = ref(false)
const codeWrapper = ref(null)
const codeElement = ref(null)
const copied = ref(false)
const currentText = ref('')
const shouldContinue = ref(true)

// Composables
const { copy } = useClipboard()

// Computed
const initialCodeDisplay = computed(() => {
  if (isExpanded.value) {
    return Prism.highlight(
      currentText.value || props.code,
      Prism.languages[props.language] || Prism.languages.plaintext,
      props.language
    )
  }
  
  const lines = props.code.split('\n')
  const initialText = lines.slice(0, 10).join('\n')
  return Prism.highlight(
    initialText,
    Prism.languages[props.language] || Prism.languages.plaintext,
    props.language
  )
})

// Methods
const copyCode = async () => {
  await copy(props.code)
  copied.value = true
  useTimeoutFn(() => {
    copied.value = false
  }, 2000)
}

const sleep = (ms) => new Promise((resolve, reject) => {
  if (!shouldContinue.value) {
    reject(new Error('Animation cancelled'))
    return
  }
  
  let timeoutId
  const checkPause = () => {
    if (isPaused.value) {
      clearTimeout(timeoutId)
      const wait = () => {
        if (isPaused.value && shouldContinue.value) {
          requestAnimationFrame(wait)
        } else if (!shouldContinue.value) {
          reject(new Error('Animation cancelled'))
        } else {
          timeoutId = setTimeout(resolve, ms)
        }
      }
      requestAnimationFrame(wait)
    } else {
      timeoutId = setTimeout(resolve, ms)
    }
  }
  checkPause()
})

const skipAnimation = () => {
  shouldContinue.value = false
  isPaused.value = false
  isAnimating.value = false
  currentText.value = props.code
  if (codeElement.value) {
    codeElement.value.innerHTML = Prism.highlight(
      currentText.value,
      Prism.languages[props.language] || Prism.languages.plaintext,
      props.language
    )
  }
}

const togglePause = () => {
  isPaused.value = !isPaused.value
}

const typeText = async () => {
  try {
    shouldContinue.value = true
    const lines = props.code.split('\n')
    currentText.value = lines.slice(0, 10).join('\n')
    await nextTick()

    const remainingLines = lines.slice(20)
    
    for (const line of remainingLines) {
      if (!shouldContinue.value) break
      
      currentText.value += '\n'
      for (const char of line) {
        if (!shouldContinue.value) break
        
        currentText.value += char
        if (codeElement.value) {
          codeElement.value.innerHTML = Prism.highlight(
            currentText.value,
            Prism.languages[props.language] || Prism.languages.plaintext,
            props.language
          )
        }
        await sleep(Math.random() * 6 + 2)
      }
      await sleep(50)
    }
  } catch (error) {
    if (error.message !== 'Animation cancelled') {
      console.error('Error during typing animation:', error)
    }
    if (isExpanded.value) {
      currentText.value = props.code
    }
  } finally {
    shouldContinue.value = true
    isPaused.value = false
    isAnimating.value = false
  }
}

const toggleExpand = async () => {
  if (isAnimating.value) return
  
  try {
    if (!isExpanded.value) {
      isExpanded.value = true
      await nextTick()
      isAnimating.value = true
      await typeText()
    } else {
      shouldContinue.value = false
      isPaused.value = false
      isAnimating.value = false
      currentText.value = ''
      isExpanded.value = false
      await nextTick()
      if (codeWrapper.value) {
        codeWrapper.value.scrollIntoView({ behavior: 'smooth' })
      }
    }
  } catch (error) {
    console.error('Error toggling expand:', error)
    isAnimating.value = false
    currentText.value = isExpanded.value ? props.code : ''
  }
}

// Lifecycle
onMounted(() => {
  if (typeof Prism !== 'undefined') {
    Prism.highlightAll()
  }
})

// Reset animation when code changes
watch(() => props.code, () => {
  if (!isExpanded.value) {
    shouldContinue.value = false
    isPaused.value = false
    isAnimating.value = false
    currentText.value = ''
  }
})
</script>

<style scoped>
.terminal-body :deep(pre) {
  font-size: 14px;
  line-height: 1.6;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  margin: 0;
  padding: 0;
  overflow-x: auto;
  background-color: transparent !important;
}

.terminal-body :deep(code) {
  background-color: transparent !important;
  padding: 0 !important;
}

.max-h-64 {
  max-height: 16rem;
  overflow: hidden;
}

.max-h-\[100000px\] {
  max-height: 100000px;
}

@keyframes blink {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #ffffff;
  margin-left: 2px;
  vertical-align: middle;
  animation: blink 0.8s step-end infinite;
}

@media (max-width: 640px) {
  .terminal-header {
    padding: 1rem;
  }
  
  .terminal-body {
    padding: 0.75rem;
  }
  
  .terminal-body :deep(pre) {
    font-size: 12px;
  }
}

/* Prism.js Theme Overrides */
:deep(.token.comment),
:deep(.token.prolog),
:deep(.token.doctype),
:deep(.token.cdata) {
  color: #6272A4;
}

:deep(.token.punctuation) {
  color: #F8F8F2;
}

:deep(.token.property),
:deep(.token.tag),
:deep(.token.constant),
:deep(.token.symbol) {
  color: #8BE9FD;
}

:deep(.token.boolean),
:deep(.token.number) {
  color: #BD93F9;
}

:deep(.token.selector),
:deep(.token.attr-name),
:deep(.token.string),
:deep(.token.char),
:deep(.token.builtin) {
  color: #F1FA8C;
}

:deep(.token.operator),
:deep(.token.entity),
:deep(.token.url),
:deep(.language-css .token.string),
:deep(.style .token.string),
:deep(.token.variable) {
  color: #FF79C6;
}

:deep(.token.atrule),
:deep(.token.attr-value),
:deep(.token.function) {
  color: #50FA7B;
}

:deep(.token.keyword) {
  color: #FF79C6;
}

:deep(.token.regex),
:deep(.token.important) {
  color: #FFB86C;
}
</style>
