<script setup>
import { ref, reactive } from 'vue'

const props = defineProps({
  userData: {
    type: Object,
    required: true
  },
  csrfToken: {
    type: String,
    required: true
  },
  updateUrl: {
    type: String,
    required: true
  }
})

const user = ref(props.userData)
const loading = ref(false)
const error = ref(null)
const success = ref(null)
const isEditing = ref(false)

const form = reactive({
  email: props.userData.email || ''
})

const toggleEmailEdit = (show) => {
  isEditing.value = show
  if (!show) {
    form.email = user.value.email
    error.value = null
  }
}

const updateProfile = async () => {
  try {
    loading.value = true
    error.value = null
    
    const formData = new FormData()
    formData.append('email', form.email)
    formData.append('name', user.value.name)
    formData.append('surname', user.value.surname || '')
    
    const response = await fetch(props.updateUrl, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-TOKEN': props.csrfToken,
        'Accept': 'application/json'
      },
      credentials: 'same-origin'
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Chyba pri aktualizácii profilu')
    }

    const data = await response.json()
    
    success.value = 'Email bol úspešne aktualizovaný'
    user.value = data.user
    isEditing.value = false
    
    setTimeout(() => {
      success.value = null
    }, 3000)
  } catch (e) {
    error.value = e.message
    console.error('Error updating profile:', e)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="p-4 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-5 bg-red-50/50 dark:bg-red-900/10" role="alert">
      <i class="design design-circle-remove mr-2 text-lg"></i>
      <span>{{ error }}</span>
    </div>

    <!-- Success Message -->
    <div v-if="success" class="p-4 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-5 bg-green-50/50 dark:bg-green-900/10" role="alert">
      <i class="design design-circle-checked mr-2 text-lg"></i>
      <span>{{ success }}</span>
    </div>

    <!-- Email Section -->
    <div class="group">
      <dt class="text-sm gant-modern-medium text-gray-500 dark:text-gray-400 mb-1">Email</dt>
      <dd class="text-gray-900 dark:text-white gant-modern-bold text-2xl flex items-center gap-3">
        <div v-if="!isEditing" class="flex items-center gap-3">
          <span v-if="user.email">{{ user.email }}</span>
          <span v-else class="text-gray-400 dark:text-gray-600">-</span>
          <button 
            type="button" 
            @click="toggleEmailEdit(true)"
            class="text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <i class="claims claims-edit-text-outline text-base"></i>
          </button>
        </div>
        
        <form v-else @submit.prevent="updateProfile" class="w-full flex items-center">
          <div class="flex-grow relative">
            <input 
              type="email" 
              v-model="form.email"
              class="!bg-white dark:!bg-gray-800 flex h-10 rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-300 dark:focus:ring-gray-600 dark:focus:ring-offset-gray-800 gant-modern-medium w-full" 
              placeholder="Zadajte email"
              :disabled="loading"
            >
          </div>
          <div class="flex items-center ml-2">
            <button 
              type="button"
              @click="toggleEmailEdit(false)" 
              class="p-3 py-2 text-sm text-gray-800 dark:text-gray-200 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800"
            >
              <i class="claims claims-close text-base"></i> Zrušiť
            </button>
            <button 
              type="submit" 
              :disabled="loading"
              class="px-3 py-2 ml-2 text-white text-sm bg-gray-800 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <i v-if="!loading" class="claims claims-save text-base"></i> 
              <i v-else class="claims claims-loading text-base animate-spin"></i>
              Uložiť
            </button>

          </div>
        </form>
      </dd>
    </div>
  </div>
</template> 