<script setup>
import { ref } from 'vue'

const props = defineProps({
  initialPage: {
    type: String,
    default: 'projectassets'
  },
  updateUrl: {
    type: String,
    required: true
  },
  csrfToken: {
    type: String,
    required: true
  }
})

const defaultPage = ref(props.initialPage)
const loading = ref(false)
const success = ref(null)
const error = ref(null)

const updatePage = async (value) => {
  try {
    loading.value = true
    error.value = null
    
    const response = await fetch(props.updateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': props.csrfToken,
        'Accept': 'application/json'
      },
      body: JSON.stringify({ default_page: value })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Chyba pri aktualizácii predvolenej stránky')
    }
    
    success.value = 'Predvolená stránka bola úspešne aktualizovaná'
    
    setTimeout(() => {
      success.value = null
    }, 3000)
  } catch (e) {
    error.value = e.message
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center mb-2">
      <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 dark:border-white"></div>
    </div>

    <!-- Error Message -->
    <div v-if="error" 
         class="p-3 mb-3 text-sm gant-modern-regular text-red-700 dark:text-red-400 flex items-center border-l-4 border-red-500 dark:border-red-600 pl-4 bg-red-50/50 dark:bg-red-900/10 rounded-r" 
         role="alert">
      <i class="design design-circle-remove mr-2 text-lg"></i>
      <span>{{ error }}</span>
    </div>

    <!-- Success Message -->
    <div v-if="success" 
         class="p-3 mb-3 text-sm gant-modern-regular text-green-700 dark:text-green-400 flex items-center border-l-4 border-green-500 dark:border-green-600 pl-4 bg-green-50/50 dark:bg-green-900/10 rounded-r" 
         role="alert">
      <i class="design design-circle-checked mr-2 text-lg"></i>
      <span>{{ success }}</span>
    </div>

    <div class="relative">
      <select 
        id="default-page-selector"
        v-model="defaultPage"
        @change="updatePage($event.target.value)"
        class="appearance-none bg-white dark:bg-slate-900 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-2.5 pr-10 text-sm gant-modern-medium text-gray-800 dark:text-gray-200 w-full max-w-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition-colors"
      >
        <option value="projectassets">Project Assets</option>
        <option value="profile">Profil</option>
        <option value="tools">Tools</option>
      </select>
      <div class="absolute right-3 top-[60%] transform -translate-y-1/2 pointer-events-none">
        <i class="design design-chevron-down text-gray-400 dark:text-gray-600"></i>
      </div>
      <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
        Vyberte si stránku, ktorá sa zobrazí po prihlásení
      </p>
    </div>
  </div>
</template> 