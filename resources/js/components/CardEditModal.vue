<template>
  <div v-if="show" class="fixed inset-0 z-[9999] overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity backdrop-blur-sm" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="relative bg-white dark:bg-slate-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full max-w-lg mx-4">
        <div class="bg-white dark:bg-slate-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 gant-modern-bold text-gray-900 dark:text-gray-100" id="modal-title">
                {{ isNewBookmark ? 'Add Bookmark' : 'Edit Bookmark' }}
              </h3>
              <div class="mt-4">
                <edit-card 
                  :bookmark="bookmark" 
                  @update="updateBookmarkData" 
                  @cancel="$emit('close')" 
                />
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-slate-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button 
            type="button" 
            class="w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm gant-modern-bold"
            @click="saveBookmark"
          >
            <i class="design design-save mr-2"></i>
            Save
          </button>
          <button 
            type="button" 
            class="mt-3 w-full inline-flex justify-center items-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none dark:bg-slate-600 dark:text-gray-200 dark:border-slate-500 dark:hover:bg-slate-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm gant-modern-bold"
            @click="$emit('close')"
          >
            <i class="design design-close mr-2"></i>
            Cancel
          </button>
          <button 
            v-if="!isNewBookmark"
            type="button" 
            class="mt-3 w-full inline-flex justify-center items-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm gant-modern-bold"
            @click="confirmDelete"
          >
            <i class="design design-trash mr-2"></i>
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EditCard from './EditCard.vue';

export default {
  name: 'CardEditModal',
  components: { EditCard },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    bookmark: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currentData: null
    };
  },
  computed: {
    isNewBookmark() {
      return !this.bookmark.id && !this.bookmark.__tempId;
    }
  },
  methods: {
    updateBookmarkData(data) {
      this.currentData = data;
    },
    saveBookmark() {
      if (this.currentData) {
        this.$emit('save', this.currentData);
      }
    },
    confirmDelete() {
      if (confirm('Are you sure you want to delete this bookmark?')) {
        this.$emit('delete', this.bookmark);
      }
    }
  },
  emits: ['close', 'save', 'delete']
};
</script>

<style scoped>
/* Force higher stacking context */
div[aria-modal="true"] {
  position: relative;
  z-index: 99999;
}
</style> 