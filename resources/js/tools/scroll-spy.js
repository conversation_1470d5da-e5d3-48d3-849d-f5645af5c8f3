export function initScrollSpy() {
    // Check if this is a documentation page
    if (!document.querySelector('[data-page="tool-documentation"]')) {
        return; // Exit if not on a documentation page
    }

    setTimeout(() => {
        const sections = document.querySelectorAll('[data-section]');
        const navLinks = document.querySelectorAll('[data-nav-link]');

        if (!sections.length || !navLinks.length) {
            return; // Silently exit if elements aren't found
        }

        // Map to track relationships between subsections and their parents
        const subsectionParents = {
            'normal-fill': 'crop',
            'crop-to-fit': 'crop',
            'generative-expand': 'crop'
        };

        // Group of related fill method subsections that should be highlighted together
        const relatedSubsections = ['normal-fill', 'crop-to-fit', 'generative-expand'];

        function updateActiveLink() {
            let currentSectionId = '';
            let isSubsection = false;
            let isFillMethodSection = false;
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                
                if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                    currentSectionId = section.getAttribute('data-section');
                    // Check if this is a subsection
                    isSubsection = subsectionParents.hasOwnProperty(currentSectionId);
                    
                    // Check if this is any of the fill method sections
                    isFillMethodSection = relatedSubsections.includes(currentSectionId);
                }
            });

            navLinks.forEach(link => {
                // Remove all active classes
                link.classList.remove('text-gray-900', 'dark:text-gray-100', 'gant-modern-bold');
                link.classList.add('text-gray-600', 'dark:text-gray-400', 'gant-modern-regular');
                
                const linkId = link.getAttribute('data-nav-link');
                
                // Activate the current section link
                if (linkId === currentSectionId) {
                    // Add active classes including font weight
                    link.classList.remove('text-gray-600', 'dark:text-gray-400', 'gant-modern-regular');
                    link.classList.add('text-gray-900', 'dark:text-gray-100', 'gant-modern-bold');
                }
                
                // Also activate the parent section if we're in a subsection
                if (isSubsection && linkId === subsectionParents[currentSectionId]) {
                    link.classList.remove('text-gray-600', 'dark:text-gray-400', 'gant-modern-regular');
                    link.classList.add('text-gray-900', 'dark:text-gray-100', 'gant-modern-bold');
                }
                
                // If we're in any fill method section, highlight all three subsection links
                if (isFillMethodSection && relatedSubsections.includes(linkId)) {
                    link.classList.remove('text-gray-600', 'dark:text-gray-400', 'gant-modern-regular');
                    link.classList.add('text-gray-900', 'dark:text-gray-100', 'gant-modern-bold');
                }
            });
        }

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('data-nav-link');
                const targetSection = document.querySelector(`[data-section="${targetId}"]`);
                
                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });

        window.addEventListener('scroll', updateActiveLink);
        updateActiveLink(); // Initial check
    }, 100);
}

// Initialize when the DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initScrollSpy);
} else {
    initScrollSpy();
} 