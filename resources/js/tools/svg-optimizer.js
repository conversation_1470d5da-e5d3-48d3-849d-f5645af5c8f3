import { createApp } from 'vue';
import SvgOptimizer from '../components/tools/SvgOptimizer.vue';

// Create a new Vue app instance for the SVG Optimizer tool
const app = createApp({});

// Register the component globally for this app instance
app.component('svg-optimizer', SvgOptimizer);

// Mount the app to the designated element in the Blade file
app.mount('#svg-optimizer-app');

console.log('SVG Optimizer Vue app mounted.'); 