// Create stylesheet utility
class StyleSheet {
    constructor() {
        this.style = document.createElement('style');
        this.style.setAttribute('type', 'text/css');
        document.head.appendChild(this.style);
    }
    
    setRule(rule) {
        if (!rule) {
            this.style.innerHTML = '';
            return;
        }
        this.style.innerHTML = rule;
    }
}

/**
 * Pathformer
 * SVG converter to paths
 */
class Pathformer {
    constructor(element) {
        // Element selection
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (!element) {
            throw new Error('Pathformer [constructor]: "element" parameter is required');
        }
        
        // Only SVG
        if (element.constructor.name === 'SVGSVGElement') {
            this.el = element;
        } else {
            throw new Error('Pathformer [constructor]: "element" parameter must be a SVG element');
        }
        
        // Start
        this.scan(element);
    }
    
    scan(svg) {
        const elements = Array.from(svg.querySelectorAll('circle, rect, line, ellipse, polyline, polygon'));
        
        for (let element of elements) {
            const path = this[`convert${element.tagName.toUpperCase()}`](element);
            element.parentNode.replaceChild(path, element);
        }
    }
    
    convertPOLYLINE(element) {
        const points = element.getAttribute('points');
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        
        if (points) {
            let newPoints = points.trim().replace(/\s+/g, ',').replace(/,,/g, ',');
            path.setAttribute('d', `M${newPoints}`);
        }
        
        this.copyAttributes(element, path);
        return path;
    }
    
    convertPOLYGON(element) {
        const path = this.convertPOLYLINE(element);
        path.setAttribute('d', `${path.getAttribute('d')}z`);
        return path;
    }
    
    convertCIRCLE(element) {
        const cx = parseFloat(element.getAttribute('cx') || 0);
        const cy = parseFloat(element.getAttribute('cy') || 0);
        const r = parseFloat(element.getAttribute('r') || 0);
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', `M${cx},${cy-r}a${r},${r},0,1,0,0,${2*r}a${r},${r},0,1,0,0,-${2*r}`);
        
        this.copyAttributes(element, path);
        return path;
    }
    
    convertELLIPSE(element) {
        const cx = parseFloat(element.getAttribute('cx') || 0);
        const cy = parseFloat(element.getAttribute('cy') || 0);
        const rx = parseFloat(element.getAttribute('rx') || 0);
        const ry = parseFloat(element.getAttribute('ry') || 0);
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', `M${cx},${cy-ry}a${rx},${ry},0,1,0,0,${2*ry}a${rx},${ry},0,1,0,0,-${2*ry}`);
        
        this.copyAttributes(element, path);
        return path;
    }
    
    convertLINE(element) {
        const x1 = element.getAttribute('x1') || 0;
        const y1 = element.getAttribute('y1') || 0;
        const x2 = element.getAttribute('x2') || 0;
        const y2 = element.getAttribute('y2') || 0;
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', `M${x1},${y1}L${x2},${y2}`);
        
        this.copyAttributes(element, path);
        return path;
    }
    
    convertRECT(element) {
        const x = parseFloat(element.getAttribute('x') || 0);
        const y = parseFloat(element.getAttribute('y') || 0);
        const width = parseFloat(element.getAttribute('width') || 0);
        const height = parseFloat(element.getAttribute('height') || 0);
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', `M${x},${y}h${width}v${height}h-${width}z`);
        
        this.copyAttributes(element, path);
        return path;
    }
    
    copyAttributes(element, destination) {
        Array.from(element.attributes).forEach(attribute => {
            if (attribute.name !== 'd' && attribute.name !== 'points') {
                destination.setAttribute(attribute.name, attribute.value);
            }
        });
    }
}

/**
 * Vivus
 * SVG animation
 */
class Vivus {
    constructor(element, options) {
        // Element selection
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (!element) {
            throw new Error('Vivus [constructor]: "element" parameter is required');
        }
        
        // Default options
        const DEFAULT_OPTIONS = {
            type: 'delayed',
            duration: 3000,
            delay: null,
            start: 'autostart',
            pathTimingFunction: 'linear',
            loop: false,
            loopStart: 0,
            loopEnd: 1000,
            loopTransition: 100
        };
        
        this.options = Object.assign({}, DEFAULT_OPTIONS, options || {});
        this.el = element;
        this.paths = [];
        this.id = Date.now();
        
        // Initialize
        this.scan();
        this.process();
    }
    
    scan() {
        // Ensure it's always a path-based SVG
        if (this.el.tagName.toLowerCase() !== 'svg') {
            throw new Error('Vivus [scan]: "element" parameter must be a SVG element');
        }
        
        // Get all paths
        const paths = Array.from(this.el.querySelectorAll('path'));
        
        // Store path data
        this.paths = paths.map(path => {
            return {
                el: path,
                length: Math.ceil(path.getTotalLength())
            };
        });
        
        this.totalLength = this.paths.reduce((total, path) => total + path.length, 0);
    }
    
    process() {
        // Prepare CSS
        const rule = this.getCSSRules();
        this.styleSheet = new StyleSheet();
        this.styleSheet.setRule(rule);
        
        // Add classes
        this.paths.forEach((path, index) => {
            path.el.classList.add(`path-${this.id}-${index}`);
        });
        
        // Auto-start animation
        if (this.options.start === 'autostart') {
            this.render();
        }
    }
    
    getCSSRules() {
        if (!this.paths.length) {
            return '';
        }
        
        let timePoint = 0;
        let rules = '';
        const state = {
            duration: this.options.duration,
            type: this.options.type,
            delay: this.options.delay !== null ? this.options.delay : this.options.duration / 3,
            pathTimingFunction: this.options.pathTimingFunction
        };
        
        // Log animation settings
        console.log("Animation settings:", {
            type: state.type,
            duration: state.duration,
            loop: this.options.loop,
            loopStart: this.options.loopStart,
            loopEnd: this.options.loopEnd,
            loopTransition: this.options.loopTransition
        });
        
        this.paths.forEach((path, index) => {
            const length = path.length;
            // FIX: Calculate pathDelay correctly based on animation type
            const pathDelay = state.type === 'delayed' ? state.delay * index : 
                             state.type === 'oneByOne' ? state.duration * index : 0;
            
            // When looping, we don't want to add forwards as it prevents proper resetting
            const forwardsValue = ' forwards';
            
            // Basic config - primary animation
            if (this.options.loop) {
                // For looping animations, we need a different approach
                rules += `
                    .path-${this.id}-${index} {
                        stroke-dasharray: ${length};
                        stroke-dashoffset: ${length};
                        animation: path${this.id}-${index} ${state.duration}ms ${state.pathTimingFunction} ${pathDelay}ms infinite;
                    }
                `;
            } else {
                // For non-looping animations
                rules += `
                    .path-${this.id}-${index} {
                        stroke-dasharray: ${length};
                        stroke-dashoffset: ${length};
                        animation: path${this.id}-${index} ${state.duration}ms ${state.pathTimingFunction} ${pathDelay}ms${forwardsValue};
                    }
                `;
            }
            
            // Animation keyframes for the drawing
            rules += `
                @keyframes path${this.id}-${index} {
                    0% { stroke-dashoffset: ${length}; }
                    100% { stroke-dashoffset: 0; }
                }
            `;
            
            // For loop with fade effect
            if (this.options.loop) {
                const loopStart = this.options.loopStart;
                const loopEnd = this.options.loopEnd;
                const fadeTime = this.options.loopTransition;
                
                // Calculate the total animation cycle time including delay
                const totalDrawTime = state.duration;
                const totalAnimTime = totalDrawTime + loopStart + loopEnd + fadeTime * 2;
                
                // For delayed start, we need to account for the delay in the fade animation
                const fadeInTime = (pathDelay / totalAnimTime) * 100;
                const stayVisibleStartTime = fadeInTime;
                const stayVisibleEndTime = ((pathDelay + totalDrawTime + loopStart) / totalAnimTime) * 100;
                const fadeOutStartTime = stayVisibleEndTime;
                const fadeOutEndTime = ((pathDelay + totalDrawTime + loopStart + fadeTime) / totalAnimTime) * 100;
                const stayHiddenStartTime = fadeOutEndTime;
                const stayHiddenEndTime = ((pathDelay + totalDrawTime + loopStart + fadeTime + loopEnd) / totalAnimTime) * 100;
                const fadeInStartTime = stayHiddenEndTime;
                const fadeInEndTime = ((pathDelay + totalDrawTime + loopStart + fadeTime + loopEnd + fadeTime) / totalAnimTime) * 100;
                
                // Separate opacity animation for the loop effect
                rules += `
                    .path-${this.id}-${index} {
                        animation: 
                            path${this.id}-${index} ${totalDrawTime}ms ${state.pathTimingFunction} ${pathDelay}ms infinite,
                            pathOpacity${this.id}-${index} ${totalAnimTime}ms linear 0ms infinite;
                    }
                    
                    @keyframes pathOpacity${this.id}-${index} {
                        0% { stroke-opacity: 0; }
                        ${fadeInTime.toFixed(2)}% { stroke-opacity: 1; }
                        ${stayVisibleEndTime.toFixed(2)}% { stroke-opacity: 1; }
                        ${fadeOutEndTime.toFixed(2)}% { stroke-opacity: 0; }
                        ${stayHiddenEndTime.toFixed(2)}% { stroke-opacity: 0; }
                        ${fadeInEndTime.toFixed(2)}% { stroke-opacity: 1; }
                        100% { stroke-opacity: 0; }
                    }
                `;
            }
        });
        
        return rules;
    }
    
    render() {
        this.styleSheet.setRule(this.getCSSRules());
    }
    
    reset() {
        this.styleSheet.setRule('');
        
        // Remove classes
        this.paths.forEach((path, index) => {
            path.el.classList.remove(`path-${this.id}-${index}`);
        });
    }
    
    start() {
        this.reset();
        this.render();
    }
    
    getCSS() {
        return this.getCSSRules();
    }
}

/**
 * DraggablePanel
 * Makes a panel draggable within its container
 */
class DraggablePanel {
    constructor(element, handleSelector = '.drag-handle', options = {}) {
        this.element = element;
        this.handle = element.querySelector(handleSelector);
        this.isDragging = false;
        this.initialX = 0;
        this.initialY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.xOffset = 0;
        this.yOffset = 0;
        this.containerRect = null;
        this.panelRect = null;
        this.options = Object.assign({
            savePosition: true,
            storageKey: 'svgAnimationPanelPosition',
            defaultPosition: { x: 0, y: 0 },
            snapThreshold: 20,
            snapEdges: true
        }, options);
        
        this.collapsed = false;
        this.collapseButton = document.getElementById('collapsePanel');
        this.resetButton = document.getElementById('resetPosition');
        
        this.bindEvents();
        this.loadPosition();
    }
    
    bindEvents() {
        if (!this.handle) return;
        
        this.handle.addEventListener('mousedown', this.dragStart.bind(this));
        document.addEventListener('mousemove', this.drag.bind(this));
        document.addEventListener('mouseup', this.dragEnd.bind(this));
        
        // Touch events for mobile
        this.handle.addEventListener('touchstart', this.dragStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.drag.bind(this), { passive: false });
        document.addEventListener('touchend', this.dragEnd.bind(this));
        
        // Update container dimensions on resize
        window.addEventListener('resize', this.updateContainerRect.bind(this));
        
        // Collapse panel functionality
        if (this.collapseButton) {
            this.collapseButton.addEventListener('click', this.toggleCollapse.bind(this));
        }
        
        // Reset position functionality
        if (this.resetButton) {
            this.resetButton.addEventListener('click', this.resetPosition.bind(this));
        }
        
        // Initial setup
        this.updateContainerRect();
    }
    
    updateContainerRect() {
        // Get the container (parent of the panel)
        const container = this.element.parentElement;
        this.containerRect = container.getBoundingClientRect();
        this.panelRect = this.element.getBoundingClientRect();
        
        // Add different padding values: p-6 (24px) for top and left, p-12 (48px) for bottom and right
        this.containerRect = {
            ...this.containerRect,
            width: this.containerRect.width - 72, // 24px left + 48px right
            height: this.containerRect.height - 72, // 24px top + 48px bottom
            left: this.containerRect.left + 24, // p-6 padding
            top: this.containerRect.top + 24, // p-6 padding
            right: this.containerRect.right - 48, // p-12 padding
            bottom: this.containerRect.bottom - 48 // p-12 padding
        };
    }
    
    dragStart(e) {
        e.preventDefault();
        
        // Only allow dragging on desktop or when not in mobile view
        if (window.innerWidth < 768) return;
        
        this.updateContainerRect();
        
        if (e.type === 'touchstart') {
            this.initialX = e.touches[0].clientX - this.xOffset;
            this.initialY = e.touches[0].clientY - this.yOffset;
        } else {
            this.initialX = e.clientX - this.xOffset;
            this.initialY = e.clientY - this.yOffset;
        }
        
        if (e.target === this.handle || this.handle.contains(e.target)) {
            this.isDragging = true;
            this.element.classList.add('dragging');
            document.body.style.cursor = 'grabbing';
        }
    }
    
    drag(e) {
        if (!this.isDragging) return;
        e.preventDefault();
        
        let clientX, clientY;
        
        if (e.type === 'touchmove') {
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else {
            clientX = e.clientX;
            clientY = e.clientY;
        }
        
        this.currentX = clientX - this.initialX;
        this.currentY = clientY - this.initialY;
        
        // Constrain to container boundaries with padding
        const maxX = this.containerRect.width - this.panelRect.width + 48; // Add right padding offset
        const maxY = this.containerRect.height - this.panelRect.height + 48; // Add bottom padding offset
        
        // Apply minimum padding: p-6 (24px) for top and left, p-12 (48px) for bottom and right
        this.currentX = Math.max(24, Math.min(this.currentX, maxX));
        this.currentY = Math.max(24, Math.min(this.currentY, maxY));
        
        // Snap to edges if close enough
        if (this.options.snapEdges) {
            // Snap to left edge (with p-6 padding)
            if (this.currentX - 24 < this.options.snapThreshold) {
                this.currentX = 24;
            }
            
            // Snap to right edge (with p-12 padding)
            if (maxX - this.currentX < this.options.snapThreshold) {
                this.currentX = maxX;
            }
            
            // Snap to top edge (with p-6 padding)
            if (this.currentY - 24 < this.options.snapThreshold) {
                this.currentY = 24;
            }
            
            // Snap to bottom edge (with p-12 padding)
            if (maxY - this.currentY < this.options.snapThreshold) {
                this.currentY = maxY;
            }
        }
        
        this.xOffset = this.currentX;
        this.yOffset = this.currentY;
        
        this.setTranslate(this.currentX, this.currentY);
    }
    
    dragEnd() {
        if (!this.isDragging) return;
        
        this.initialX = this.currentX;
        this.initialY = this.currentY;
        
        this.isDragging = false;
        this.element.classList.remove('dragging');
        document.body.style.cursor = '';
        
        // Save position
        if (this.options.savePosition) {
            this.savePosition();
        }
    }
    
    setTranslate(xPos, yPos) {
        this.element.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
    }
    
    savePosition() {
        if (window.localStorage && this.options.savePosition) {
            const position = {
                x: this.currentX,
                y: this.currentY
            };
            localStorage.setItem(this.options.storageKey, JSON.stringify(position));
        }
    }
    
    loadPosition() {
        if (window.localStorage && this.options.savePosition) {
            try {
                const savedPosition = localStorage.getItem(this.options.storageKey);
                if (savedPosition) {
                    const position = JSON.parse(savedPosition);
                    
                    // Ensure position respects padding boundaries: p-6 (24px) for top and left
                    this.currentX = Math.max(24, position.x); // Minimum 24px from left
                    this.currentY = Math.max(24, position.y); // Minimum 24px from top
                    this.xOffset = this.currentX;
                    this.yOffset = this.currentY;
                    this.setTranslate(this.currentX, this.currentY);
                } else {
                    // Use default position with p-6 (24px) padding
                    this.currentX = this.options.defaultPosition.x + 24;
                    this.currentY = this.options.defaultPosition.y + 24;
                    this.xOffset = this.currentX;
                    this.yOffset = this.currentY;
                    this.setTranslate(this.currentX, this.currentY);
                }
            } catch (e) {
                console.error('Error loading panel position', e);
                this.resetPosition();
            }
        }
    }
    
    resetPosition() {
        // Reset to default position (top left with p-6 padding)
        this.currentX = this.options.defaultPosition.x + 24; // Add p-6 padding
        this.currentY = this.options.defaultPosition.y + 24; // Add p-6 padding
        this.xOffset = this.currentX;
        this.yOffset = this.currentY;
        this.setTranslate(this.currentX, this.currentY);
        
        if (this.options.savePosition) {
            localStorage.removeItem(this.options.storageKey);
        }
    }
    
    toggleCollapse() {
        this.collapsed = !this.collapsed;
        
        // Get all content except the header
        const header = this.element.querySelector('.sidebar-head');
        const allContent = Array.from(this.element.children).filter(el => el !== header);
        
        if (this.collapsed) {
            // Collapse panel - hide all content except header
            allContent.forEach(el => el.style.display = 'none');
            this.element.classList.add('collapsed');
            this.collapseButton.querySelector('i').classList.remove('design-arrow-up');
            this.collapseButton.querySelector('i').classList.add('design-arrow-down');
            this.collapseButton.setAttribute('title', 'Expand Panel');
            
            // Add bottom border to header when collapsed
            header.classList.add('border-b', 'pb-0');
        } else {
            // Expand panel - show all content
            allContent.forEach(el => el.style.display = '');
            this.element.classList.remove('collapsed');
            this.collapseButton.querySelector('i').classList.remove('design-arrow-down');
            this.collapseButton.querySelector('i').classList.add('design-arrow-up');
            this.collapseButton.setAttribute('title', 'Collapse Panel');
            
            // Remove bottom border from header when expanded
            header.classList.remove('border-b', 'pb-0');
        }
        
        // Update container dimensions after collapse/expand
        setTimeout(() => {
            this.updateContainerRect();
        }, 300);
    }
}

/**
 * ViewerController
 * Manages the drag and drop and display
 */
class ViewerController {
    constructor(container) {
        this.container = container;
        this.svgTag = null;
        this.initialSVG = null;
        this.dropzone = document.querySelector('.dropzone');
        this.fileInput = document.querySelector('#fileInput');
        this.introBox = document.querySelector('.introbox');
        this.svgWrapper = null;
        this.currentSize = 50; // Default size percentage
        
        this.bindEvents();
    }
    
    bindEvents() {
        // Handle drag over events for visual feedback
        this.dropzone.addEventListener('dragover', e => {
            e.preventDefault();
            e.stopPropagation();
            this.dropzone.classList.add('border-indigo-500', 'bg-indigo-50', 'dark:bg-indigo-900', 'dark:bg-opacity-10');
        });
        
        // Handle drag leave events to reset visual feedback
        this.dropzone.addEventListener('dragleave', e => {
            e.preventDefault();
            e.stopPropagation();
            this.dropzone.classList.remove('border-indigo-500', 'bg-indigo-50', 'dark:bg-indigo-900', 'dark:bg-opacity-10');
        });
        
        // Handle drop events
        this.dropzone.addEventListener('drop', e => {
            e.preventDefault();
            e.stopPropagation();
            this.dropzone.classList.remove('border-indigo-500', 'bg-indigo-50', 'dark:bg-indigo-900', 'dark:bg-opacity-10');
            
            if (e.dataTransfer.files.length) {
                this.handleFile(e.dataTransfer.files[0]);
            }
        });
        
        // Handle click to select file
        this.dropzone.addEventListener('click', () => {
            this.fileInput.click();
        });
        
        // Handle file selection
        this.fileInput.addEventListener('change', e => {
            if (e.target.files.length) {
                this.handleFile(e.target.files[0]);
            }
        });
    }
    
    handleFile(file) {
        // Check if file is SVG
        if (file.type !== 'image/svg+xml') {
            this.showMessage('Please drop an SVG file (.svg)', 'error');
            return;
        }
        
        this.readFile(file);
    }
    
    readFile(file) {
        const reader = new FileReader();
        
        reader.onload = e => {
            // Store initial SVG content
            this.initialSVG = e.target.result;
            
            // Hide only the dropzone content instead of the entire introbox
            const dropzoneContent = document.querySelector('.dropzone-content');
            if (dropzoneContent) {
                dropzoneContent.style.display = 'none';
            }
            
            // Create SVG wrapper if it doesn't exist
            if (!this.svgWrapper) {
                this.svgWrapper = document.createElement('div');
                this.svgWrapper.className = 'svg-wrapper';
                this.container.appendChild(this.svgWrapper);
            } else {
                // Clear existing content
                this.svgWrapper.innerHTML = '';
            }
            
            // Insert SVG content
            this.svgWrapper.innerHTML = this.initialSVG;
            
            // Store reference to SVG element
            this.svgTag = this.svgWrapper.querySelector('svg');
            
            // Make SVG responsive
            if (this.svgTag) {
                this.makeResponsive(this.svgTag);
                // Show success message
                this.showMessage('SVG loaded successfully! Use the options panel to customize animation.', 'success');
            }
        };
        
        reader.onerror = () => {
            this.showMessage('Error reading the SVG file. Please try again.', 'error');
        };
        
        reader.readAsText(file);
    }
    
    makeResponsive(svgElement) {
        // Get original viewBox or create one from width/height
        let viewBox = svgElement.getAttribute('viewBox');
        if (!viewBox) {
            const width = svgElement.getAttribute('width') || svgElement.width.baseVal.value;
            const height = svgElement.getAttribute('height') || svgElement.height.baseVal.value;
            if (width && height) {
                viewBox = `0 0 ${width} ${height}`;
                svgElement.setAttribute('viewBox', viewBox);
            }
        }
        
        // Remove fixed width/height
        svgElement.removeAttribute('width');
        svgElement.removeAttribute('height');
        
        // Add responsive attributes using current size
        svgElement.style.width = `${this.currentSize}%`;
        svgElement.style.height = `${this.currentSize}%`;
        svgElement.style.maxWidth = `${this.currentSize}%`;
        svgElement.style.maxHeight = `${this.currentSize}vh`;
        svgElement.style.position = 'absolute';
        svgElement.style.left = '50%';
        svgElement.style.top = '50%';
        svgElement.style.transform = 'translate(-50%, -50%)';
        svgElement.style.fill = 'none';
        svgElement.style.background = 'transparent';
        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        
        // Ensure no fill is applied to paths
        const paths = svgElement.querySelectorAll('path');
        paths.forEach(path => {
            if (!path.hasAttribute('fill')) {
                path.setAttribute('fill', 'none');
            }
        });
    }
    
    reset() {
        if (this.svgWrapper && this.initialSVG) {
            this.svgWrapper.innerHTML = this.initialSVG;
            this.svgTag = this.svgWrapper.querySelector('svg');
            
            // Ensure SVG maintains responsive sizing
            if (this.svgTag) {
                this.makeResponsive(this.svgTag);
            }
            
            // Keep the dropzone border and pattern visible
            const dropzoneContent = document.querySelector('.dropzone-content');
            if (dropzoneContent) {
                dropzoneContent.style.display = 'none';
            }
        }
    }
    
    getCurrentSVG() {
        if (!this.svgWrapper) return null;
        return this.svgWrapper.innerHTML;
    }
    
    showMessage(message, type = 'error') {
        const alertBox = document.querySelector('.alert-message');
        alertBox.querySelector('.message-text').textContent = message;
        alertBox.classList.remove('hidden');
        
        // Reset classes
        alertBox.classList.remove('bg-red-100', 'border-red-400', 'text-red-700', 'bg-green-100', 'border-green-400', 'text-green-700');
        
        // Apply appropriate styling
        if (type === 'error') {
            alertBox.classList.add('bg-red-100', 'border-red-400', 'text-red-700');
        } else {
            alertBox.classList.add('bg-green-100', 'border-green-400', 'text-green-700');
        }
        
        // Auto-hide after delay
        setTimeout(() => {
            alertBox.classList.add('hidden');
        }, type === 'error' ? 5000 : 3000);
    }
    
    resizeSvg(sizePercent) {
        // Update size value display
        const sizeValueElement = document.getElementById('svgSizeValue');
        if (sizeValueElement) {
            sizeValueElement.textContent = `${sizePercent}%`;
        }
        
        // Store the new size
        this.currentSize = parseInt(sizePercent, 10);
        
        // Apply to SVG if it exists
        if (this.svgTag) {
            this.svgTag.style.width = `${this.currentSize}%`;
            this.svgTag.style.height = `${this.currentSize}%`;
            this.svgTag.style.maxWidth = `${this.currentSize}%`;
            this.svgTag.style.maxHeight = `${this.currentSize}vh`;
        }
    }
}

/**
 * OptionController
 * Manages form options and controls
 */
class OptionController {
    constructor(container, viewerCtrl) {
        this.container = container;
        this.viewerCtrl = viewerCtrl;
        this.form = container.querySelector('form');
        this.loopToggle = this.form.querySelector('input[name="loop"]');
        this.loopPanel = this.form.querySelector('.control-loop-panel');
        this.manualTriggerPanel = this.form.querySelector('.manual-trigger-class-panel');
        this.delayPanel = this.form.querySelector('.delay-panel');
        this.typeDescriptions = {
            delayed: this.form.querySelector('.delayed-description'),
            async: this.form.querySelector('.async-description'),
            oneByOne: this.form.querySelector('.oneByOne-description')
        };
        
        // Initialize form state
        this.updateForm();
        
        // Enable download button after first update
        const drawButton = document.getElementById('drawButton');
        const downloadButton = document.getElementById('downloadButton');
        
        if (drawButton) {
            drawButton.addEventListener('click', () => {
                this.draw();
                if (downloadButton && downloadButton.hasAttribute('disabled')) {
                    downloadButton.removeAttribute('disabled');
                }
            });
        }
        
        if (downloadButton) {
            downloadButton.addEventListener('click', () => {
                this.download();
            });
        }
        
        // Add event listener for animation type change
        const typeSelect = this.form.querySelector('select[name="type"]');
        if (typeSelect) {
            typeSelect.addEventListener('change', (e) => {
                this.updateTypeDescription(e.target.value);
            });
        }
        
        // Initialize type description
        const initialType = this.form.querySelector('select[name="type"]')?.value;
        if (initialType) {
            this.updateTypeDescription(initialType);
        }
    }
    
    updateTypeDescription(type) {
        // Hide all descriptions
        Object.values(this.typeDescriptions).forEach(desc => {
            if (desc) desc.classList.add('hidden');
        });
        
        // Show selected description
        if (this.typeDescriptions[type]) {
            this.typeDescriptions[type].classList.remove('hidden');
        }
    }
    
    updateForm() {
        // Handle manual trigger class panel visibility
        const startOption = this.form.querySelector('input[name="start"]:checked').value;
        if (this.manualTriggerPanel) {
            this.manualTriggerPanel.classList.toggle('hidden', startOption !== 'manual');
        }
        
        // Handle delay panel visibility
        const animationType = this.form.querySelector('select[name="type"]').value;
        if (this.delayPanel) {
            this.delayPanel.classList.toggle('hidden', animationType !== 'delayed');
        }
        
        // Handle loop panel visibility
        const loopChecked = this.loopToggle.checked;
        if (this.loopPanel) {
            this.loopPanel.classList.toggle('hidden', !loopChecked);
        }
        
        // Update loop toggle status
        const loopStatus = document.getElementById('loopToggleStatus');
        if (loopStatus) {
            loopStatus.textContent = loopChecked ? "On" : "Off";
        }
    }
    
    getOptions() {
        return {
            type: this.form.querySelector('select[name="type"]')?.value || 'delayed',
            duration: parseInt(this.form.querySelector('input[name="duration"]')?.value || 3000, 10),
            delay: this.form.querySelector('input[name="delay"]')?.value ? parseInt(this.form.querySelector('input[name="delay"]')?.value, 10) : null,
            start: this.form.querySelector('input[name="start"]:checked')?.value || 'autostart',
            triggerClass: this.form.querySelector('input[name="triggerClass"]')?.value || 'start',
            pathTimingFunction: this.form.querySelector('select[name="pathTimingFunction"]')?.value || 'linear',
            loop: this.form.querySelector('input[name="loop"]')?.checked || false,
            loopStart: parseInt(this.form.querySelector('input[name="loopStart"]')?.value || 800, 10),
            loopEnd: parseInt(this.form.querySelector('input[name="loopEnd"]')?.value || 3000, 10),
            loopTransition: parseInt(this.form.querySelector('input[name="loopTransition"]')?.value || 400, 10)
        };
    }
    
    draw() {
        // Reset viewer
        this.viewerCtrl.reset();
        const svgTag = this.viewerCtrl.svgTag;
        
        if (!svgTag) {
            this.showMessage('Please drop an SVG first', 'error');
            return;
        }
        
        try {
            // Store original styles
            const originalStyles = {
                width: svgTag.style.width,
                height: svgTag.style.height,
                maxWidth: svgTag.style.maxWidth,
                maxHeight: svgTag.style.maxHeight,
                position: svgTag.style.position,
                left: svgTag.style.left,
                top: svgTag.style.top,
                transform: svgTag.style.transform,
                fill: svgTag.style.fill,
                background: svgTag.style.background,
                preserveAspectRatio: svgTag.getAttribute('preserveAspectRatio')
            };
            
            // Convert all shapes to paths
            this.pathformer = new Pathformer(svgTag);
            
            // Apply animation
            const options = this.getOptions();
            
            // Debug options
            console.log("Animation options:", options);
            
            this.vivus = new Vivus(svgTag, options);
            
            // Restore all original styles
            Object.assign(svgTag.style, originalStyles);
            svgTag.setAttribute('preserveAspectRatio', originalStyles.preserveAspectRatio);
            
            // Apply current size from ViewerController
            if (this.viewerCtrl.currentSize) {
                svgTag.style.width = `${this.viewerCtrl.currentSize}%`;
                svgTag.style.height = `${this.viewerCtrl.currentSize}%`;
                svgTag.style.maxWidth = `${this.viewerCtrl.currentSize}%`;
                svgTag.style.maxHeight = `${this.viewerCtrl.currentSize}vh`;
            }
            
            // Ensure no fill is applied to paths after animation
            const paths = svgTag.querySelectorAll('path');
            paths.forEach(path => {
                if (!path.hasAttribute('fill')) {
                    path.setAttribute('fill', 'none');
                }
            });
            
            // For manual trigger
            if (options.start === 'manual') {
                svgTag.classList.add('start-ready');
                svgTag.addEventListener('click', () => {
                    svgTag.classList.toggle(options.triggerClass);
                });
            }
            
            // Show success message
            this.showMessage('Animation updated successfully!', 'success');
        } catch (error) {
            console.error(error);
            this.showMessage(error.message, 'error');
        }
    }
    
    download() {
        if (!this.vivus) {
            this.showMessage('Please process your SVG first with the Update button', 'error');
            return;
        }
        
        try {
            // Get current SVG content
            let svgContent = this.viewerCtrl.getCurrentSVG();
            
            // Get CSS animation rules
            const cssRules = this.vivus.getCSS();
            
            // Insert CSS in SVG - FIXED: properly insert style tag
            // Instead of inserting right after the SVG tag, insert before the closing SVG tag
            svgContent = svgContent.replace('</svg>', `<style>${cssRules}</style></svg>`);
            
            // Create download link
            const blob = new Blob([svgContent], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'animated_svg_' + Date.now() + '.svg';
            link.click();
            
            URL.revokeObjectURL(url);
            
            // Show success message
            this.showMessage('SVG downloaded successfully!', 'success');
        } catch (error) {
            console.error(error);
            this.showMessage(error.message, 'error');
        }
    }
    
    showMessage(message, type = 'error') {
        const alertBox = document.querySelector('.alert-message');
        alertBox.querySelector('.message-text').textContent = message;
        alertBox.classList.remove('hidden');
        
        // Reset classes
        alertBox.classList.remove('bg-red-100', 'border-red-400', 'text-red-700', 'bg-green-100', 'border-green-400', 'text-green-700');
        
        // Apply appropriate styling
        if (type === 'error') {
            alertBox.classList.add('bg-red-100', 'border-red-400', 'text-red-700');
        } else {
            alertBox.classList.add('bg-green-100', 'border-green-400', 'text-green-700');
        }
        
        // Auto-hide after delay
        setTimeout(() => {
            alertBox.classList.add('hidden');
        }, type === 'error' ? 5000 : 3000);
    }
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        // Initialize controllers
        const viewerCtrl = new ViewerController(document.querySelector('.viewer'));
        const optionCtrl = new OptionController(document.querySelector('.sidebar'), viewerCtrl);
        
        // Initialize draggable panel with options
        const draggablePanel = new DraggablePanel(document.querySelector('.draggable-panel'), '.drag-handle', {
            savePosition: true,
            defaultPosition: { x: 0, y: 0 },
            snapThreshold: 20
        });
        
        // Make controllers globally accessible
        window.viewerCtrl = viewerCtrl;
        window.optionCtrl = optionCtrl;
        window.draggablePanel = draggablePanel;
        
        // Error handling
        const alertBox = document.querySelector('.alert-message');
        if (alertBox) {
            window.onerror = function(msg) {
                alertBox.querySelector('.message-text').textContent = msg;
                alertBox.classList.remove('hidden');
                alertBox.classList.remove('bg-green-100', 'border-green-400', 'text-green-700');
                alertBox.classList.add('bg-red-100', 'border-red-400', 'text-red-700');
                
                setTimeout(() => {
                    alertBox.classList.add('hidden');
                }, 8000);
            };
        }
        
        // Initialize range sliders to match their number inputs
        const rangeSliders = document.querySelectorAll('input[type="range"]');
        rangeSliders.forEach(slider => {
            const targetInput = document.querySelector(`input[name="${slider.name.replace('_range', '')}"]`);
            if (targetInput) {
                slider.value = targetInput.value;
            }
        });
        
        // Toggle options panel on mobile
        const toggleButton = document.getElementById('toggleOptions');
        const optionsPanel = document.querySelector('.sidebar');
        
        if (toggleButton && optionsPanel) {
            toggleButton.addEventListener('click', function() {
                optionsPanel.classList.toggle('hidden');
                
                // Reset position when toggling on mobile
                if (window.innerWidth < 768) {
                    optionsPanel.style.transform = '';
                    if (!optionsPanel.classList.contains('hidden')) {
                        optionsPanel.style.position = 'fixed';
                        optionsPanel.style.top = '24px'; // p-6 padding
                        optionsPanel.style.left = '24px'; // p-6 padding
                        optionsPanel.style.right = '48px'; // p-12 padding
                        optionsPanel.style.bottom = '48px';
                        optionsPanel.style.width = 'auto';
                    }
                }
            });
        }
        
        // Tab switching functionality
        const tabInputs = document.querySelectorAll('input[name="tab"]');
        const tabContents = [
            document.getElementById('tab-paths'),
            document.getElementById('tab-fill'),
            document.getElementById('tab-morph')
        ];
        
        tabInputs.forEach((input, index) => {
            input.addEventListener('change', () => {
                // Hide all tab contents
                tabContents.forEach(content => {
                    if (content) content.classList.add('hidden');
                });
                // Show the selected tab content
                if (tabContents[index]) tabContents[index].classList.remove('hidden');
            });
        });
    } catch (error) {
        console.error('Error initializing SVG animation:', error);
    }
});