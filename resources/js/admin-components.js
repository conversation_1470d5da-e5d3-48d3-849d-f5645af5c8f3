// Admin components with lazy loading
import { defineAsyncComponent } from 'vue';
import DatabaseManager from './components/admin/DatabaseManager.vue'

export default (app) => {
    // These components are already registered in app.js, so we don't need to register them again
    // Only register components here that aren't already in app.js
    
    // Other admin-specific components can be registered here if needed
    // app.component('some-admin-component', defineAsyncComponent(() => import('./components/admin/SomeAdminComponent.vue')));

    app.component('database-manager', DatabaseManager)
} 