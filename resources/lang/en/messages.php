<?php

return [
    // General
    'welcome' => 'Welcome',
    'home' => 'Home',
    'about' => 'About',
    'contact' => 'Contact',
    'language' => 'Language',
    'switch_language' => 'Switch to Slovak',
    'hello' => 'Hello,',
    'changelog' => 'Changelog',
    
    // Navigation
    'search_icons' => 'Search icons',
    'assets' => 'Projects assets',
    'font_family' => 'Font Family',
    'classes' => 'SCSS Classes',
    'playground' => 'Playground',
    'administration' => 'Admin',
    'search_icon' => 'Search icon',
    'icon_fonts' => 'Icon fonts',
    'font_families' => 'Font Families',
    'svg' => 'SVG Icons',
    'lottie' => 'Lottie Animations',
    'animations' => 'Animations',
    'tools' => 'Tools',
    'tools_description' => 'Plugins, scripts and tools for applications',
    'code' => 'Code',
    'code_description' => 'Development resources and tools',

    // Back navigation
    'back_to' => 'Back to',
    'previous_page' => 'Previous Page',
    'dashboard' => 'Dashboard',
    'icon_search' => 'Icon Search',
    
    // Common actions
    'back' => 'Back',
    'logout' => 'Logout',
    'old_website' => 'Old website',
    
    // Authentication
    'login_credentials' => 'Enter your credentials to sign in to your account',
    'login' => 'Vermont Učko',
    'password' => 'Intranet Password',
    'remember_me' => 'Remember me',
    'sign_in' => 'Sign In',
    'forgot_password' => 'Forgot your password?',
    'or' => 'Or',
    
    // Project assets page
    'preview' => 'Preview',
    'cloud' => 'Cloud',
    'project_assets' => 'Projects assets',
    'assets_description' => '–––––– Here you can find all the icons you have downloaded. You can delete any icon you want, as well as update the content of existing icons.',
    
    // Typography page
    'typography' => 'TYPOGRAPHY',
    'font_families_title' => 'Font Families:',
    'vermont_main_fontface' => 'VERMONT MAIN FONTFACE',
    'vermont_accent_fontface' => 'VERMONT ACCENT FONTFACE',
    'gant_modern' => 'Gant Modern V2 CE',
    'gant_serif' => 'Gant Serif V2 CE',
    
    // Greetings
    'hello' => 'Ahoj',
    'welcome_back' => 'Sevas',
    'nice_to_see_you' => 'Čauko',
    'good_to_have_you' => 'Good to have you',
    'hey_there' => 'Hey there',
    'good_morning' => 'Good morning',
    'good_afternoon' => 'Good afternoon',
    'good_evening' => 'Good evening',
    'good_night' => 'Good night',
    
    // Special user greetings
    'special_hello_u1111' => 'Greetings boss',
    'welcome_back_boss' => 'Welcome back boss',
    'chief_in_the_house' => 'Chief in the house',
    
    // User 4431 greetings
    'hello_designer' => 'Hello creative mind',
    'welcome_design_master' => 'Welcome back design master',
    'design_wizard' => 'The design wizard has arrived',

    // Language Switcher
    'click_to_change' => 'Click to change language',
    'click_to_confirm' => 'Click again to confirm',
    'switch_to_slovak' => 'Switch to Slovak',
    'switch_to_english' => 'Switch to English',

    // Theme Toggle
    'toggle_light_mode' => 'Switch to light mode',
    'toggle_dark_mode' => 'Switch to dark mode',
    'theme_toggle' => 'Toggle theme',
    'theme_light' => 'Light theme',
    'theme_dark' => 'Dark theme',
    'theme_system' => 'System theme',
    'theme_toggle_aria' => 'Toggle between light and dark theme',

    // Cookie Modal
    'cookie_dear_user' => 'Dear Windows user',
    'cookie_warning' => 'Warning: Prolonged exposure to poorly designed user interfaces has been linked to increased stress levels, eye strain, and sudden urges to throw your device out the window. Our research shows that 9 out of 10 developers experience elevated blood pressure when encountering non-responsive buttons and confusing navigation menus.',
    'cookie_symptoms_link' => 'Learn about the symptoms',
    'cookie_risks' => '¯\\_(ツ)_/¯ By continuing to use this interface, you acknowledge the risks of UI-induced frustration and accept that random popup modals may appear at any time. Side effects may include excessive sighing, keyboard mashing, and spontaneous facepalming. For your safety, we recommend taking frequent breaks and keeping stress balls nearby.',
    'cookie_therapy_link' => 'UI Therapy Center',
    'cookie_support_link' => 'Emergency Design Support',
    'cookie_accept_all' => 'Accept All',
    'cookie_manage_settings' => 'Manage Settings',
    'cookie_close' => 'Close',

    // Icon Grid
    'icon_webfonts' => 'Vermont webfonts',
    'icon_find_my' => 'Find My Icon',
    'icon_search_for' => 'Search for',
    'icon_search_placeholder' => 'Start typing to search icons...',
    'icon_project' => 'Project',
    'icon_all_icons' => 'All Icons',
    'icon_preview_size' => 'Preview size',
    'icon_exact_match' => 'Exact Match',
    'icon_match_all_empty' => 'Match all when empty',
    'icon_default' => 'Default',
    'icon_copy_class' => 'Copy class',
    'icon_copied' => 'Copied!',
    'icon_loading_more' => 'Loading more icons...',
    'icon_load_more' => 'Load More Icons',
    'icon_matching_icons' => 'matching icons displayed',
    'icon_of' => 'of',

    // Login Form
    'login_label' => 'Username',
    'login_placeholder' => 'uXXXX',
    'password_label' => 'Password',
    'password_placeholder' => '••••••••',
    'remember_me' => 'Remember me',
    'sign_in' => 'Sign in',
    'forgot_password' => 'Forgot your password?',

    // Font Download Button
    'download_scss' => 'Download SCSS',
    'download_woff2' => 'Download WOFF2',
    'download_font' => 'Download Font',
    'download_failed' => 'Download failed',
    'file_type_scss' => '.scss',
    'file_type_woff2' => '.woff2',

    // Typed Greeting
    'greeting_hello' => 'Hello',
    'greeting_hi' => 'Hi',
    'greeting_welcome' => 'Welcome',
    'greeting_good_morning' => 'Good morning',
    'greeting_good_afternoon' => 'Good afternoon',
    'greeting_good_evening' => 'Good evening',
    'waving_hand' => 'Waving hand',

    // Playground Demo
    'code_handover' => '–––––– Code handover // Non fuctional component still in progress but working on it',
    'preview_tab' => 'Preview',
    'code_tab' => 'Code',
    'preview_tooltip' => 'View the live preview of your component',
    'code_tooltip' => 'View and copy the source code',
    'device_mobile' => 'Mobile view',
    'device_tablet' => 'Tablet view',
    'device_desktop' => 'Desktop view',
    'windows_logo_alt' => 'Windows 95 Logo',
    'please_wait' => 'Please wait...',
    'windows_operations' => 'Windows is performing some important operations',
    'complete' => 'complete',
    'loading' => 'Loading...',
    'copy_code' => 'Copy code',
    'code_copied' => 'Copied!',
    'initializing' => 'Initializing...',
    'checking_system' => 'Checking system configuration...',
    'loading_components' => 'Loading necessary components...',
    'preparing_installation' => 'Preparing installation...',
    'almost_there' => 'Almost there...',
    'few_more_moments' => 'Just a few more moments...',

    // Command Palette
    'command_default_placeholder' => 'What can I help you with?',
    'command_search_placeholder' => 'Search for icons, fonts, or assets...',
    'command_lets_go' => 'Let\'s go >>',
    'command_find_icons' => 'Find some class names of Icons',
    'command_update_assets' => 'Update/upload assets in my Project',
    'command_typography' => 'Typography and Font Families',
    'command_variables' => 'Variables & SCSS Vermont Classes',
    'command_playground' => 'Playground, handover & inspiration',
    'command_navigate' => 'to navigate',
    'command_select' => 'to select',

    // Tools page translations
    'last_modification' => 'Last modification:',
    'download_jsx_script' => 'Download .JSX script',
    'edition_only' => '* 2023 edition only',
    'tool_eshop_resizer' => 'EshopResizer',
    'tool_insta_resizer' => 'InstaResizer',
    'photoshop_version' => 'Photoshop v',
    'eshop_description' => 'Photoshop automation script for batch processing and resizing e-commerce images.',
    'insta_description' => 'Photoshop automation script for batch processing and resizing Instagram images.',
    
    // Documentation pages
    'adobe_photoshop' => 'Adobe Photoshop',
    'documentation' => 'Documentation',
    'installation' => 'Installation',
    'how_to_use' => 'How to Use',
    'troubleshooting' => 'Troubleshooting',
    'download_modal_title' => 'Download Script',
    'download_confirm' => 'Confirm Download',
    'download_cancel' => 'Cancel',
    'script_version' => 'Script Version',
    'compatible_with' => 'Compatible with',
    'photoshop_versions' => 'Photoshop versions',
    'script_details' => 'Script Details',
    'developer' => 'Developer',
    'last_updated' => 'Last Updated',
    'file_size' => 'File Size',
    'license' => 'License',
    'terms_of_use' => 'Terms of Use',
    'internal_use' => 'For internal Vermont use only',
    'support_contact' => 'Support Contact',
    
    // Tool functionality and messages
    'download_started' => 'Download started...',
    'download_complete' => 'Download complete',
    'download_error' => 'Error downloading file',
    'installation_steps' => 'Installation Steps',
    'step' => 'Step',
    'of' => 'of',
    'next_step' => 'Next Step',
    'previous_step' => 'Previous Step',
    'finish' => 'Finish',
    'important_note' => 'Important Note',
    'warning' => 'Warning',
    'info' => 'Information',
    'success' => 'Success',
    'error' => 'Error',
    'tip' => 'Tip',
    'read_more' => 'Read more',
    'show_less' => 'Show less',

    // Code page translations
    'scss_classes' => 'SCSS Classes',
    'scss_classes_subtitle' => 'Explore Vermont SCSS classes',
    'scss_classes_description' => 'Browse and search through all available SCSS utility classes for consistent styling across Vermont projects.',
    
    'playground_title' => 'Playground',
    'playground_subtitle' => 'Interactive code environment',
    'playground_description' => 'Experiment with code components and see real-time previews in this interactive development environment.',

    // Admin Dashboard
    'admin_dashboard' => 'Dashboard',
    'admin_system_stats' => 'System statistics and information overview',
    'admin_refresh_data' => 'Refresh data',
    'admin_users' => 'Users',
    'admin_new_badge' => 'New',
    'admin_view_all' => 'View all',
    'admin_total_users' => 'Total users count',
    'admin_no_new_colleagues' => 'No new colleagues :/',
    'admin_logged_in_today' => 'logged in today',
    'admin_logged_in_today_plural' => 'logged in today',

    // Icons Management
    'admin_icons' => 'Icons',
    'admin_manage_icons' => 'Manage icons',
    'admin_total_icons' => 'Total icons in system',
    'admin_fonts' => 'fonts',
    'admin_all_up_to_date' => 'All up to date',
    'admin_fonts_need_update' => 'font needs update|fonts need update',

    // Recent Activity
    'admin_recent_activity' => 'Recent Activity',
    'admin_view_all_activities' => 'View all',
    'admin_icon_update' => 'Icon Update',
    'admin_last_icon_update' => 'Last icon update',
    'admin_new_users' => 'New Users',
    'admin_new_user_count' => 'new user|new users',
    'admin_today' => 'Today',
    'admin_fonts_update_needed' => 'Fonts Need Update',
    'admin_check_icons_section' => 'Check Icons section',
    'admin_no_recent_activity' => 'No recent activity',

    // Admin Icons Management
    'admin_icon_management' => 'Správa ikon',
    'admin_icon_management_desc' => 'Správa a aktualizácia ikon v systéme',
    'admin_icon_count' => 'Celkový počet ikon',
    'admin_update_needed' => 'Potrebná aktualizácia',
    'admin_up_to_date' => 'Aktuálne',
    'admin_last_update' => 'Posledná aktualizácia',
    'admin_download_font' => 'Stiahnuť font',
    'admin_update_icons' => 'Aktualizovať ikony',
    'admin_delete_font' => 'Vymazať font',

    // Admin Activities
    'admin_activity' => 'Aktivita',
    'admin_activity_desc' => 'Prehľad aktivity používateľov a systému',
    'admin_activity_timeline' => 'Časová os aktivity',
    'admin_filter' => 'Filtrovať',
    'admin_yesterday' => 'Včera',
    'admin_user_login' => 'Prihlásenie používateľa',
    'admin_user_login_desc' => 'Používateľ :user sa prihlásil do systému',
    'admin_icon_update_activity' => 'Aktualizácia ikon',
    'admin_icon_update_desc' => 'Aktualizácia ikon vo fonte :font',
    'admin_at_time' => 'o :time',

    // Admin User Detail
    'admin_back_to_users' => 'Späť na používateľov',
    'admin_user_detail' => 'Detail používateľa',
    'admin_user_detail_desc' => 'Detailné informácie o používateľovi',
    'admin_profile' => 'Profil',
    'admin_admin_badge' => 'Admin',
    'admin_new_badge' => 'Nový',
    'admin_user_id' => 'ID používateľa',
    'admin_name' => 'Meno',
    'admin_email' => 'Email',
    'admin_role' => 'Rola',
    'admin_role_admin' => 'Administrátor',
    'admin_role_user' => 'Používateľ',
    'admin_status' => 'Status',
    'admin_status_active' => 'Aktívny',
    'admin_status_inactive' => 'Neaktívny',
    'admin_created_at' => 'Vytvorené',
    'admin_last_login' => 'Posledné prihlásenie',
    'admin_never' => 'Nikdy',

    // Common Admin Actions
    'admin_refresh_data' => 'Obnoviť údaje',

    // Admin Typography Page
    'admin_typography_title' => 'Typografia',
    'admin_typography_desc' => 'Správa písiem a typografických nastavení',

    // Admin Tools Page
    'admin_tools_title' => 'Nástroje',
    'admin_tools_desc' => 'Správa systémových nástrojov',

    // Admin Blog Page
    'admin_blog_title' => 'Blog',
    'admin_blog_desc' => 'Správa blogových príspevkov',

    // Admin Assets Page
    'admin_assets_title' => 'Assety',
    'admin_assets_desc' => 'Správa digitálnych assetov',

    // Admin Animations Page
    'admin_animations_title' => 'Animácie',
    'admin_animations_desc' => 'Správa animačných súborov',

    // Admin Code Page
    'admin_code_title' => 'Kód',
    'admin_code_desc' => 'Správa kódu a dokumentácia',

    'activity_tab' => 'Activity',
    'activity_desc' => 'Recent activity in the system',
    'whiteboard' => 'Whiteboard',
    'whiteboard_description' => 'To do List - Current tasks and projects',
];
