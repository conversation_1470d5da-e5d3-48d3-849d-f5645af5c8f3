<?php

return [
    // General
    'welcome' => 'Vitajte',
    'home' => 'Domov',
    'about' => 'O nás',
    'contact' => 'Kontakt',
    'language' => 'Jaz<PERSON>',
    'switch_language' => 'Prepnúť na angličtinu',
    'hello' => 'Ahoj,',
    
    // Navigation
    'search_icons' => 'Hľadať ikony',
    'icons' => 'Ikony',
    'assets' => 'Zdroje projektov',
    'font_family' => 'Písmo',
    'classes' => 'Triedy',
    'playground' => 'Playground',
    'administration' => 'Admin',
    'search_icon' => 'Hľadať ikonu',
    'icon_fonts' => 'Ikony písma',
    'font_families' => 'Rodiny písma',
    'svg' => 'SVG Ikony',
    'lottie' => 'Lottie Animácie',
    'animations' => 'Animácie',
    'tools' => 'Nástroje',
    'tools_description' => 'Nástroje pre Vermont',
    'code' => 'Kód',
    'code_description' => 'Vývojárske zdroje a nástroje',
    
    // Back navigation
    'back_to' => 'Späť na',
    'previous_page' => 'Predchádzajúca stránka',
    'dashboard' => 'Dashboard',
    'icon_search' => 'Vyhľadávanie ikon',
    
    // Common actions
    'back' => 'Späť',
    'logout' => 'Odhlásiť sa',
    'old_website' => 'Stará webstránka',
    
    // Authentication
    'login_credentials' => 'Zadajte svoje prihlasovacie údaje',
    'login' => 'Login (uXXXX)',
    'password' => 'Heslo',
    'remember_me' => 'Zapamätať si ma',
    'sign_in' => 'Prihlásiť sa',
    'forgot_password' => 'Zabudli ste heslo?',
    'or' => 'Alebo',
    
    // Project assets page
    'preview' => 'Náhľad',
    'cloud' => 'Cloud',
    'project_assets' => 'Zdroje projektov',
    'assets_description' => 'Toto je miesto, kde nájdete všetky ikony, ktoré ste si stiahli. Môžete vymazať akúkoľvek ikonu, ak ste ju chceli, ako aj aktualizovať obsah existujúcich ikon.',
    
    // Typography page
    'typography' => 'TYPOGRAFIA',
    'font_families_title' => 'Rodiny písma:',
    'vermont_main_fontface' => 'HLAVNÉ PÍSMO VERMONT',
    'vermont_accent_fontface' => 'DOPLNKOVÉ PÍSMO VERMONT',
    'gant_modern' => 'Gant Modern V2 CE',
    'gant_serif' => 'Gant Serif V2 CE',

    // Greetings
    'hello' => 'Ahoj',
    'welcome_back' => 'Sevas',
    'nice_to_see_you' => 'Čauko',
    'good_to_have_you' => 'Radi ťa vidíme',
    'hey_there' => 'Nazdar',
    'good_morning' => 'Dobré ráno',
    'good_afternoon' => 'Dobrý deň',
    'good_evening' => 'Dobrý večer',
    'good_night' => 'Dobrú noc',
    
    // Special user greetings
    'special_hello_u1111' => 'Zdravím šéfe',
    'welcome_back_boss' => 'Vitaj späť šéfe',
    'chief_in_the_house' => 'Šéf v dome',
    
    // User 4431 greetings
    'hello_designer' => 'Ahoj kreatívna duša',
    'welcome_design_master' => 'Vitaj späť majster dizajnu',
    'design_wizard' => 'Dizajnový čarodejník je tu',

    // Language Switcher
    'click_to_change' => 'Kliknite pre zmenu jazyka',
    'click_to_confirm' => 'Kliknite znova pre potvrdenie',
    'switch_to_slovak' => 'Prepnúť na slovenčinu',
    'switch_to_english' => 'Prepnúť na angličtinu',

    // Theme Toggle
    'toggle_light_mode' => 'Prepnúť na svetlý režim',
    'toggle_dark_mode' => 'Prepnúť na tmavý režim',
    'theme_toggle' => 'Prepnúť tému',
    'theme_light' => 'Svetlá téma',
    'theme_dark' => 'Tmavá téma',
    'theme_system' => 'Systémová téma',
    'theme_toggle_aria' => 'Prepnúť medzi svetlou a tmavou témou',

    // Cookie Modal
    'cookie_dear_user' => 'Vážený používateľ Windows',
    'cookie_warning' => 'Upozornenie: Dlhodobé vystavenie zle navrhnutým používateľským rozhraniam bolo spojené so zvýšenou úrovňou stresu, únavou očí a náhlymi potrebami vyhodiť vaše zariadenie z okna. Náš výskum ukazuje, že 9 z 10 vývojárov zažíva zvýšený krvný tlak pri stretnutí s nereagujúcimi tlačidlami a mätúcou navigáciou.',
    'cookie_symptoms_link' => 'Dozvedieť sa o príznakoch',
    'cookie_risks' => '¯\\_(ツ)_/¯ Pokračovaním v používaní tohto rozhrania beriete na vedomie riziká frustrácie spôsobenej UI a súhlasíte s tým, že sa môžu kedykoľvek objaviť náhodné vyskakovacie okná. Vedľajšie účinky môžu zahŕňať nadmerné vzdychanie, búchanie do klávesnice a spontánne facepalming. Pre vašu bezpečnosť odporúčame časté prestávky a mať poruke antistresové loptičky.',
    'cookie_therapy_link' => 'UI Terapeutické centrum',
    'cookie_support_link' => 'Núdzová podpora dizajnu',
    'cookie_accept_all' => 'Prijať všetko',
    'cookie_manage_settings' => 'Spravovať nastavenia',
    'cookie_close' => 'Zavrieť',

    // Icon Grid
    'icon_webfonts' => 'Vermont webfonty',
    'icon_find_my' => 'Nájsť moju ikonu',
    'icon_search_for' => 'Hľadať',
    'icon_search_placeholder' => 'Začnite písať pre vyhľadávanie ikon...',
    'icon_project' => 'Projekt',
    'icon_all_icons' => 'Všetky ikony',
    'icon_preview_size' => 'Veľkosť náhľadu',
    'icon_exact_match' => 'Presná zhoda',
    'icon_match_all_empty' => 'Zobraziť všetko pri prázdnom vyhľadávaní',
    'icon_default' => 'Predvolené',
    'icon_copy_class' => 'Kopírovať triedu',
    'icon_copied' => 'Skopírované!',
    'icon_loading_more' => 'Načítavam ďalšie ikony...',
    'icon_load_more' => 'Načítať ďalšie ikony',
    'icon_matching_icons' => 'zodpovedajúcich ikon zobrazených',
    'icon_of' => 'z',

    // Login Form
    'login_label' => 'Prihlasovacie meno',
    'login_placeholder' => 'uXXXX',
    'password_label' => 'Heslo',
    'password_placeholder' => '••••••••',
    'remember_me' => 'Zapamätať si ma',
    'sign_in' => 'Prihlásiť sa',
    'forgot_password' => 'Zabudli ste heslo?',

    // Font Download Button
    'download_scss' => 'Stiahnuť SCSS',
    'download_woff2' => 'Stiahnuť WOFF2',
    'download_font' => 'Stiahnuť písmo',
    'download_failed' => 'Sťahovanie zlyhalo',
    'file_type_scss' => '.scss',
    'file_type_woff2' => '.woff2',

    // Typed Greeting
    'greeting_hello' => 'Ahoj',
    'greeting_hi' => 'Čau',
    'greeting_welcome' => 'Vitaj',
    'greeting_good_morning' => 'Dobré ráno',
    'greeting_good_afternoon' => 'Dobrý deň',
    'greeting_good_evening' => 'Dobrý večer',
    'waving_hand' => 'Mávajúca ruka',

    // Playground Demo
    'code_handover' => '–––––– Odovzdanie kódu // Nefunkčný komponent stále vo vývoji, ale pracujeme na ňom',
    'preview_tab' => 'Náhľad',
    'code_tab' => 'Kód',
    'preview_tooltip' => 'Zobraziť živý náhľad komponentu',
    'code_tooltip' => 'Zobraziť a kopírovať zdrojový kód',
    'device_mobile' => 'Mobilné zobrazenie',
    'device_tablet' => 'Tabletové zobrazenie',
    'device_desktop' => 'Desktop zobrazenie',
    'windows_logo_alt' => 'Windows 95 Logo',
    'please_wait' => 'Prosím čakajte...',
    'windows_operations' => 'Windows vykonáva dôležité operácie',
    'complete' => 'dokončené',
    'loading' => 'Načítavam...',
    'copy_code' => 'Kopírovať kód',
    'code_copied' => 'Skopírované!',
    'initializing' => 'Inicializácia...',
    'checking_system' => 'Kontrola konfigurácie systému...',
    'loading_components' => 'Načítavanie potrebných komponentov...',
    'preparing_installation' => 'Príprava inštalácie...',
    'almost_there' => 'Už sme skoro tam...',
    'few_more_moments' => 'Ešte chvíľku...',

    // Command Palette
    'command_default_placeholder' => 'S čím vám môžem pomôcť?',
    'command_search_placeholder' => 'Hľadať ikony, písma alebo assety...',
    'command_lets_go' => 'Poďme na to >>',
    'command_find_icons' => 'Nájsť názvy tried ikon',
    'command_update_assets' => 'Aktualizovať/nahrať assety v mojom projekte',
    'command_typography' => 'Typografia a rodiny písiem',
    'command_variables' => 'Premenné a SCSS Vermont triedy',
    'command_playground' => 'Playground, odovzdanie a inšpirácia',
    'command_navigate' => 'na navigáciu',
    'command_select' => 'na výber',
    
    // Tools page translations
    'last_modification' => 'Posledná úprava:',
    'download_jsx_script' => 'Stiahnuť .JSX skript',
    'edition_only' => '* len edícia 2023',
    'tool_eshop_resizer' => 'EshopResizer',
    'tool_insta_resizer' => 'InstaResizer',
    'photoshop_version' => 'Photoshop v',
    'eshop_description' => 'Automatizačný skript pre Photoshop na hromadné spracovanie a zmenu veľkosti obrázkov e-shopu.',
    'insta_description' => 'Automatizačný skript pre Photoshop na hromadné spracovanie a zmenu veľkosti obrázkov pre Instagram.',
    
    // Documentation pages
    'adobe_photoshop' => 'Adobe Photoshop',
    'documentation' => 'Dokumentácia',
    'installation' => 'Inštalácia',
    'how_to_use' => 'Ako používať',
    'troubleshooting' => 'Riešenie problémov',
    'download_modal_title' => 'Stiahnuť skript',
    'download_confirm' => 'Potvrdiť stiahnutie',
    'download_cancel' => 'Zrušiť',
    'script_version' => 'Verzia skriptu',
    'compatible_with' => 'Kompatibilné s',
    'photoshop_versions' => 'verziami Photoshopu',
    'script_details' => 'Detaily skriptu',
    'developer' => 'Vývojár',
    'last_updated' => 'Posledná aktualizácia',
    'file_size' => 'Veľkosť súboru',
    'license' => 'Licencia',
    'terms_of_use' => 'Podmienky použitia',
    'internal_use' => 'Len pre interné použitie Vermont',
    'support_contact' => 'Kontakt na podporu',
    
    // Tool functionality and messages
    'download_started' => 'Sťahovanie začalo...',
    'download_complete' => 'Sťahovanie dokončené',
    'download_error' => 'Chyba pri sťahovaní súboru',
    'installation_steps' => 'Kroky inštalácie',
    'step' => 'Krok',
    'of' => 'z',
    'next_step' => 'Ďalší krok',
    'previous_step' => 'Predchádzajúci krok',
    'finish' => 'Dokončiť',
    'important_note' => 'Dôležitá poznámka',
    'warning' => 'Upozornenie',
    'info' => 'Informácia',
    'success' => 'Úspech',
    'error' => 'Chyba',
    'tip' => 'Tip',
    'read_more' => 'Čítať viac',
    'show_less' => 'Zobraziť menej',

    // Code page translations
    'scss_classes' => 'SCSS Triedy',
    'scss_classes_subtitle' => 'Preskúmajte Vermont SCSS triedy',
    'scss_classes_description' => 'Prehliadajte a vyhľadávajte všetky dostupné SCSS utility triedy pre konzistentný štýl naprieč Vermont projektami.',
    
    'playground_title' => 'Playground',
    'playground_subtitle' => 'Interaktívne vývojové prostredie',
    'playground_description' => 'Experimentujte s kódovými komponentmi a sledujte náhľady v reálnom čase v tomto interaktívnom vývojovom prostredí.',

    // Admin Users List
    'admin_users_title' => 'Users',
    'admin_users_desc' => 'System users management',
    'admin_users_list' => 'Users List',
    'admin_filter' => 'Filter',

    // Users Table Headers
    'admin_table_id' => 'ID',
    'admin_table_name' => 'Name',
    'admin_table_email' => 'Email',
    'admin_table_role' => 'Role',
    'admin_table_status' => 'Status',
    'admin_table_created' => 'Created',
    'admin_table_last_login' => 'Last Login',
    'admin_table_actions' => 'Actions',

    // User Actions
    'admin_view_details' => 'View Details',
    'admin_edit_user' => 'Edit User',
    'admin_delete_user' => 'Delete User',

    // Admin Typography
    'admin_typography_title' => 'Typography',
    'admin_typography_desc' => 'Font management and typography settings',

    // Admin Tools
    'admin_tools_title' => 'Tools',
    'admin_tools_desc' => 'System tools and utilities',

    // Admin Code
    'admin_code_title' => 'Code',
    'admin_code_desc' => 'Code management and documentation',

    // Admin Blog
    'admin_blog_title' => 'Blog',
    'admin_blog_desc' => 'Blog posts management',

    // Admin Assets
    'admin_assets_title' => 'Assets',
    'admin_assets_desc' => 'Digital assets management',

    // Admin Animations
    'admin_animations_title' => 'Animations',
    'admin_animations_desc' => 'Animation files management',

    // Admin SVG Animations
    'admin_svg_animations_title' => 'SVG Animations',
    'admin_svg_animations_desc' => 'SVG animation files management',

    // New additions
    'activity_tab' => 'Aktivita',
    'activity_desc' => 'Nedávna aktivita v systéme',
    'whiteboard' => 'Tabuľa',
    'whiteboard_description' => 'Spravujte a zdieľajte dôležité karty a zdroje',
];
