@use 'sass:map';
@use './cdb';
@use './claims';
@use './design';
@use './wms';
@use './eshop';
@use './matrix';
@use './retail';
@use './tos';
@use './og';
@use './category';
@use './hrms';
@use './transporthub';
@use './nunito-sans';
@use './gant-modernv2';
@use './gant-serif';
@use './gant-serif-italic';



// Font configurations
$font-configs: (
  'cdb': (family: 'cdb', weight: 400),
  'claims': (family: 'claims', weight: 400),
  'design': (family: 'design', weight: 400),
  'wms': (family: 'wms', weight: 400),
  'vermont-icon': (family: 'vermont-icon', weight: 400),
  'matrix': (family: 'matrix', weight: 400),
  'retail': (family: 'retail', weight: 400),
  'tos': (family: 'tos', weight: 400),
  'og': (family: 'ordergroup', weight: 400),
  'hrms': (family: 'hrms', weight: 400),
  'transporthub': (family: 'transporthub', weight: 400),
  'category': (family: 'vermont-category', weight: 400),
  'nunito-sans': (family: 'nunito-sans', weight: (200, 1000)),
  'Gant-Modern-bold': (family: 'Gant-Modern-bold', weight: 900, file: 'Bold'),
  'Gant-Modern-medium': (family: 'Gant-Modern-medium', weight: 700, file: 'Medium'),
  'Gant-Modern-regular': (family: 'Gant-Modern-regular', weight: 500, file: 'Regular'),
  'Gant-Modern-light': (family: 'Gant-Modern-light', weight: 300, file: 'Light'),
  'Gant-Serif': (family: 'Gant-Serif', weight: 900)
);

// Font face declarations
@each $name, $config in $font-configs {
  @font-face {
    font-family: map.get($config, 'family');
    @if $name == 'nunito-sans' {
      src: url("../fonts-typeface/#{$name}.woff2") format('woff2-variations');
    } @else if $name == 'Gant-Modern-bold' or $name == 'Gant-Modern-medium' or $name == 'Gant-Modern-regular' or $name == 'Gant-Modern-light' {
      src: url("/resources/fonts-typeface/GantModernV2/GantModernV2-#{map.get($config, 'file')}.woff2") format('woff2');
    } @else if $name == 'Gant-Serif' {
      src: url("/resources/fonts-typeface/gant-serif/GantSerif-Bold.woff2") format('woff2');
    } @else {
      src: url("/resources/fonts-icons/#{$name}.woff2") format('woff2');
    }
    font-weight: map.get($config, 'weight');
    font-style: normal;
    font-display: swap;
  }
}

// Variables for use in other files
:root {
  @each $name, $config in $font-configs {
    --font-family-#{$name}: #{map.get($config, 'family')};
    --font-weight-#{$name}: #{map.get($config, 'weight')};
  }
}
