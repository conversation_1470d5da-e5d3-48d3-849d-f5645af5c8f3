:root {
  --radio-color-default: #fff;
  --radio-color-checked-1: #ADB5BD;
  --radio-color-checked-2: #0D6EFD;
}

.custom-radios {
  input[type="radio"] {
    display: none;

    + label {
      color: #333;

      span {
        display: inline-block;
        width: 2.5rem;      
        height: 2.5rem;      
        margin: -0.0625rem 0.25rem 0 0; 
        vertical-align: middle;
        cursor: pointer;
        border-radius: 50%;
        border: 0.125rem solid #DEE2E6; 
        background: var(--radio-color-default) center no-repeat;
        text-align: center;
        line-height: 2.75rem;  
        img {
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }
    }

    @for $i from 1 through 4 {
      &#color-#{$i} + label span {
        background-color: var(--radio-color-default);
      }
    }

    &:checked + label span img {
      opacity: 1;
    }

    &:checked#color-1 + label span {
      background-color: var(--radio-color-checked-1);
      border: 0.125rem solid var(--radio-color-checked-1);  // 2px to rem
    }

    @for $i from 2 through 4 {
      &:checked#color-#{$i} + label span {
        background-color: var(--radio-color-checked-2);
        border: 0.125rem solid var(--radio-color-checked-2);  // 2px to rem
      }
    }
  }
}