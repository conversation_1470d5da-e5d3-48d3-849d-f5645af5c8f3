@font-face {
  font-family: 'Gant-Serif';
  src: url("../fonts-typeface/GantSerif-MediumCondensed.woff2") format('woff2');
  font-weight: 500;
  display: swap;
}

:root {
  --gant-serif-medium-condensed-aalt: "aalt" off;
  --gant-serif-medium-condensed-ordn: "ordn" off;
  --gant-serif-medium-condensed-sups: "sups" off;
}

.gant-serif-medium-condensed-aalt {
  --gant-serif-medium-condensed-aalt: "aalt" on;
}

.gant-serif-medium-condensed-ordn {
  --gant-serif-medium-condensed-ordn: "ordn" on;

  @supports (font-variant-numeric: ordinal) {
    --gant-serif-medium-condensed-ordn: "ordn" on;
    font-variant-numeric: ordinal;
  }
}

.gant-serif-medium-condensed-sups {
  --gant-serif-medium-condensed-sups: "sups" on;

  @supports (font-variant-position: super) {
    --gant-serif-medium-condensed-sups: "sups" on;
    font-variant-position: super;
  }
}

.gant-serif-medium-condensed-aalt,
.gant-serif-medium-condensed-ordn,
.gant-serif-medium-condensed-sups {
  font-feature-settings: var(--gant-serif-medium-condensed-aalt), var(--gant-serif-medium-condensed-ordn), var(--gant-serif-medium-condensed-sups);
}

.gant-serif {
  font-family: 'Gant-Serif' !important;
}
.gant-serif{font-family: 'Gant-Serif'!important;}
