$wms-404: "\ea01";
$wms-a1: "\ea02";
$wms-a2: "\ea03";
$wms-a3: "\ea04";
$wms-a4: "\ea05";
$wms-a5: "\ea06";
$wms-acceleration: "\ea07";
$wms-acceleration-outline: "\ea08";
$wms-accordion: "\ea09";
$wms-accordion-down: "\ea0a";
$wms-accordion-up: "\ea0b";
$wms-add: "\ea0c";
$wms-add-outline: "\ea0d";
$wms-add-solo: "\ea0e";
$wms-add_klt: "\ea0f";
$wms-again: "\ea10";
$wms-again2: "\ea11";
$wms-alert: "\ea12";
$wms-alert-outline: "\ea13";
$wms-alert2: "\ea14";
$wms-alert3: "\ea15";
$wms-alert4: "\ea16";
$wms-alert5: "\ea17";
$wms-arrow: "\ea18";
$wms-arrow-down: "\ea19";
$wms-arrow-left: "\ea1a";
$wms-arrow-outline: "\ea1b";
$wms-arrow-right: "\ea1c";
$wms-arrow-up: "\ea1d";
$wms-arrow2: "\ea1e";
$wms-arrow3: "\ea1f";
$wms-arrow4: "\ea20";
$wms-arrow5: "\ea21";
$wms-arrow6: "\ea22";
$wms-arrow7: "\ea23";
$wms-article: "\ea24";
$wms-article-check: "\ea25";
$wms-article-check-outline: "\ea26";
$wms-article-count: "\ea27";
$wms-article-count-num: "\ea28";
$wms-article-dameged: "\ea29";
$wms-article-dameged-outline: "\ea2a";
$wms-article-detai: "\ea2b";
$wms-article-detail-outline: "\ea2c";
$wms-article-dirty: "\ea2d";
$wms-article-dirty-outline: "\ea2e";
$wms-article-etiketa: "\ea2f";
$wms-article-info: "\ea30";
$wms-article-info-outline: "\ea31";
$wms-article-mandatory: "\ea32";
$wms-article-mandatory-outline: "\ea33";
$wms-article-missing: "\ea34";
$wms-article-missing-outline: "\ea35";
$wms-article-nalokacii: "\ea36";
$wms-article-nalokacii-outline: "\ea37";
$wms-article-objednavka-outline: "\ea38";
$wms-article-okay: "\ea39";
$wms-article-okay-outline: "\ea3a";
$wms-article-outline: "\ea3b";
$wms-article-outline-etiketa: "\ea3c";
$wms-article-packing-remove: "\ea3d";
$wms-article-packing-remove-outline: "\ea3e";
$wms-article-photo: "\ea3f";
$wms-article-photo-outline: "\ea40";
$wms-article-pick: "\ea41";
$wms-article-pick-outline: "\ea42";
$wms-article-ready: "\ea43";
$wms-article-ready-outline: "\ea44";
$wms-article-scanned: "\ea45";
$wms-article-scanned-outline: "\ea46";
$wms-article-stop: "\ea47";
$wms-article-stop-outline: "\ea48";
$wms-article-surplus: "\ea49";
$wms-article-surplus-outline: "\ea4a";
$wms-article-waiting: "\ea4b";
$wms-article-waiting-outline: "\ea4c";
$wms-articles-count-filled: "\ea4d";
$wms-articles-count-filled-preparing: "\ea4e";
$wms-articles-count-filled-ready: "\ea4f";
$wms-articles-count-preparing: "\ea50";
$wms-articles-count-ready: "\ea51";
$wms-articles-packing-remove: "\ea52";
$wms-articles-packing-remove-outline: "\ea53";
$wms-articles-pick-filled: "\ea54";
$wms-articles-pick-outline: "\ea55";
$wms-articles-swap: "\ea56";
$wms-articles-swap-outline: "\ea57";
$wms-articles-waiting-filled: "\ea58";
$wms-articles-waiting-outline: "\ea59";
$wms-as: "\ea5a";
$wms-as-active: "\ea5b";
$wms-as-carousel: "\ea5c";
$wms-as-conveyor: "\ea5d";
$wms-as-outline: "\ea5e";
$wms-as-requested_by_matus: "\ea5f";
$wms-asign-task: "\ea60";
$wms-asign-task-circle: "\ea61";
$wms-audit: "\ea62";
$wms-audit-add: "\ea63";
$wms-audit-add-outline: "\ea64";
$wms-audit-check: "\ea65";
$wms-audit-check-outline: "\ea66";
$wms-audit-diference: "\ea67";
$wms-audit-diference-outline: "\ea68";
$wms-audit-locked: "\ea69";
$wms-audit-locked-outline: "\ea6a";
$wms-audit-lokation: "\ea6b";
$wms-audit-lokation-outline: "\ea6c";
$wms-audit-minus: "\ea6d";
$wms-audit-minus-outline: "\ea6e";
$wms-audit-okay: "\ea6f";
$wms-audit-okay-outline: "\ea70";
$wms-audit-outline: "\ea71";
$wms-audit-problem: "\ea72";
$wms-audit-problem-outline: "\ea73";
$wms-audit-submit-changes: "\ea74";
$wms-audit-submit-changes-outline-1: "\ea75";
$wms-audit-wait: "\ea76";
$wms-audit-wait-outline: "\ea77";
$wms-audit-wrong: "\ea78";
$wms-audit-wrong-outline: "\ea79";
$wms-autostore: "\ea7a";
$wms-autostore-pickovanie: "\ea7b";
$wms-autostore-pickovanie-outline: "\ea7c";
$wms-autostore_expedicia: "\ea7d";
$wms-autostore_prijem: "\ea7e";
$wms-autostore_prijem_vbin: "\ea7f";
$wms-avizovany: "\ea80";
$wms-b2b_default: "\ea81";
$wms-b2b_default-outline: "\ea82";
$wms-b2b_gift: "\ea83";
$wms-b2b_gift-outline: "\ea84";
$wms-b2b_move: "\ea85";
$wms-b2b_move-outline: "\ea86";
$wms-b2b_movein: "\ea87";
$wms-b2b_movein-outline: "\ea88";
$wms-b2b_ready: "\ea89";
$wms-b2b_ready-outline: "\ea8a";
$wms-b2b_setup: "\ea8b";
$wms-b2b_setup-outline: "\ea8c";
$wms-b2b_skladnik: "\ea8d";
$wms-b2b_skladnik-outline: "\ea8e";
$wms-b2b_stop: "\ea8f";
$wms-b2b_stop-outline: "\ea90";
$wms-b2b_triedenie: "\ea91";
$wms-b2b_triedenie-outline: "\ea92";
$wms-b2b_visible: "\ea93";
$wms-b2b_visible-outline: "\ea94";
$wms-b2b_wait: "\ea95";
$wms-b2b_wait-outline: "\ea96";
$wms-b2c-material: "\ea97";
$wms-b2c_default: "\ea98";
$wms-b2c_default-outline: "\ea99";
$wms-b2c_gift: "\ea9a";
$wms-b2c_gift-outline: "\ea9b";
$wms-b2c_move: "\ea9c";
$wms-b2c_move-outline: "\ea9d";
$wms-b2c_movein: "\ea9e";
$wms-b2c_movein-outline: "\ea9f";
$wms-b2c_ready: "\eaa0";
$wms-b2c_ready-outline: "\eaa1";
$wms-b2c_skladnik: "\eaa2";
$wms-b2c_skladnik-outline: "\eaa3";
$wms-b2c_stop: "\eaa4";
$wms-b2c_stop-outline: "\eaa5";
$wms-b2c_triedenie: "\eaa6";
$wms-b2c_triedenie-outline: "\eaa7";
$wms-b2c_visible: "\eaa8";
$wms-b2c_visible-outline: "\eaa9";
$wms-b2c_wait: "\eaaa";
$wms-b2c_wait-outline: "\eaab";
$wms-balenie: "\eaac";
$wms-balenie-artikel: "\eaad";
$wms-balenie-artikel-filled: "\eaae";
$wms-balenie-in: "\eaaf";
$wms-balenie-move: "\eab0";
$wms-balenie-ready: "\eab1";
$wms-balenie-stop: "\eab2";
$wms-balenie-wait: "\eab3";
$wms-balenie_kompletizacia: "\eab4";
$wms-bin-closed: "\eab5";
$wms-bin-empty: "\eab6";
$wms-bin-error: "\eab7";
$wms-bin-half-20: "\eab8";
$wms-bin-half-22: "\eab9";
$wms-bin-half-blue: "\eaba";
$wms-bin-half-closed: "\eabb";
$wms-bin-half-open: "\eabc";
$wms-bin-half-port: "\eabd";
$wms-bin-half-yellow: "\eabe";
$wms-bin-history: "\eabf";
$wms-bin-m-half-blue: "\eac0";
$wms-bin-m-half-yellow: "\eac1";
$wms-bin-m-quarter-blue: "\eac2";
$wms-bin-m-quarter-green: "\eac3";
$wms-bin-m-quarter-red: "\eac4";
$wms-bin-m-quarter-yellow: "\eac5";
$wms-bin-m-whole: "\eac6";
$wms-bin-open: "\eac7";
$wms-bin-port: "\eac8";
$wms-bin-problem: "\eac9";
$wms-bin-quarter-40: "\eaca";
$wms-bin-quarter-41: "\eacb";
$wms-bin-quarter-42: "\eacc";
$wms-bin-quarter-43: "\eacd";
$wms-bin-quarter-44: "\eace";
$wms-bin-quarter-blue: "\eacf";
$wms-bin-quarter-closed: "\ead0";
$wms-bin-quarter-green: "\ead1";
$wms-bin-quarter-open: "\ead2";
$wms-bin-quarter-port: "\ead3";
$wms-bin-quarter-red: "\ead4";
$wms-bin-quarter-yellow: "\ead5";
$wms-bin-settings: "\ead6";
$wms-bin-whole: "\ead7";
$wms-bin-whole-10: "\ead8";
$wms-bin-whole-closed: "\ead9";
$wms-bin-whole-open: "\eada";
$wms-bin-whole-port: "\eadb";
$wms-bin_add-outline: "\eadc";
$wms-bin_artikle-outline: "\eadd";
$wms-bin_checked: "\eade";
$wms-bin_empty-outline: "\eadf";
$wms-bin_full: "\eae0";
$wms-bin_full-problem: "\eae1";
$wms-bin_half: "\eae2";
$wms-bin_half-problem: "\eae3";
$wms-bin_hold-outline: "\eae4";
$wms-bin_inventory-outline: "\eae5";
$wms-bin_okay-outline: "\eae6";
$wms-bin_outline: "\eae7";
$wms-bin_quarter: "\eae8";
$wms-bin_quarter-problem: "\eae9";
$wms-bin_remove-outline: "\eaea";
$wms-bin_striketrought-outline: "\eaeb";
$wms-bin_uncategorised: "\eaec";
$wms-bin_unchecked: "\eaed";
$wms-bin_waiting-outline: "\eaee";
$wms-box: "\eaef";
$wms-box-add: "\eaf0";
$wms-box-crazydays: "\eaf1";
$wms-box-down: "\eaf2";
$wms-box-info: "\eaf3";
$wms-box-location: "\eaf4";
$wms-box-okay: "\eaf5";
$wms-box-scan: "\eaf6";
$wms-box-stop: "\eaf7";
$wms-box-wait: "\eaf8";
$wms-box-warning: "\eaf9";
$wms-box-wishlist: "\eafa";
$wms-box_open: "\eafb";
$wms-box_open-add: "\eafc";
$wms-box_open-crazydays: "\eafd";
$wms-box_open-down: "\eafe";
$wms-box_open-info: "\eaff";
$wms-box_open-location: "\eb00";
$wms-box_open-okay: "\eb01";
$wms-box_open-scan: "\eb02";
$wms-box_open-stop: "\eb03";
$wms-box_open-wait: "\eb04";
$wms-box_open-warning: "\eb05";
$wms-box_open-wishlist: "\eb06";
$wms-browse-history: "\eb07";
$wms-browse-history-outline: "\eb08";
$wms-cakanie-hodinky: "\eb09";
$wms-camera: "\eb0a";
$wms-carouselport-autostore: "\eb0b";
$wms-carouselport-autostore-closed: "\eb0c";
$wms-carouselport-autostore-open: "\eb0d";
$wms-carouselport-autostore-open-outline: "\eb0e";
$wms-carouselport-autostore-outline: "\eb0f";
$wms-carouselport-autostore-outline-closed: "\eb10";
$wms-carton: "\eb11";
$wms-carton-claims-damaged: "\eb12";
$wms-carton-claims-dirty: "\eb13";
$wms-carton-count: "\eb14";
$wms-carton-damaged: "\eb15";
$wms-carton-empty: "\eb16";
$wms-carton-info: "\eb17";
$wms-carton-missing: "\eb18";
$wms-carton-objednavka: "\eb19";
$wms-carton-okay: "\eb1a";
$wms-carton-prijem: "\eb1b";
$wms-carton-scan: "\eb1c";
$wms-carton-surplus: "\eb1d";
$wms-cas: "\eb1e";
$wms-center: "\eb1f";
$wms-center-city: "\eb20";
$wms-center-city-outline: "\eb21";
$wms-center-outline: "\eb22";
$wms-change: "\eb23";
$wms-checked: "\eb24";
$wms-checked-outline: "\eb25";
$wms-chevron-arr-down: "\eb26";
$wms-chevron-arr-left: "\eb27";
$wms-chevron-arr-right: "\eb28";
$wms-chevron-arr-up: "\eb29";
$wms-chevron-arrow-down: "\eb2a";
$wms-chevron-arrow-left: "\eb2b";
$wms-chevron-arrow-right: "\eb2c";
$wms-chevron-arrow-up: "\eb2d";
$wms-chevron-down: "\eb2e";
$wms-chevron-left: "\eb2f";
$wms-chevron-right: "\eb30";
$wms-chevron-time: "\eb31";
$wms-chevron-time-outline: "\eb32";
$wms-chevron-up: "\eb33";
$wms-choice: "\eb34";
$wms-choice-outline: "\eb35";
$wms-clock: "\eb36";
$wms-clock-outline: "\eb37";
$wms-close: "\eb38";
$wms-collapse-transporthub-double: "\eb39";
$wms-collapse-transporthub-double-outline: "\eb3a";
$wms-collapse-transporthub-single: "\eb3b";
$wms-collapse-transporthub-single-outline: "\eb3c";
$wms-color-palette: "\eb3d";
$wms-color-palette-outline: "\eb3e";
$wms-crazy-days: "\eb3f";
$wms-crazy-days-bomb: "\eb40";
$wms-crossroad-accounting-groups: "\eb41";
$wms-crossroad-analytics-graph1: "\eb42";
$wms-crossroad-analytics-graph2: "\eb43";
$wms-crossroad-analytics-graph3: "\eb44";
$wms-crossroad-analytics-graph4: "\eb45";
$wms-crossroad-aviza-global: "\eb46";
$wms-crossroad-brand: "\eb47";
$wms-crossroad-buildings: "\eb48";
$wms-crossroad-clothing-sizes: "\eb49";
$wms-crossroad-colours: "\eb4a";
$wms-crossroad-colours-logistic: "\eb4b";
$wms-crossroad-documents: "\eb4c";
$wms-crossroad-genders: "\eb4d";
$wms-crossroad-goods-movement: "\eb4e";
$wms-crossroad-group4: "\eb4f";
$wms-crossroad-internal-transfer: "\eb50";
$wms-crossroad-inventory: "\eb51";
$wms-crossroad-inventory-diference: "\eb52";
$wms-crossroad-inventory-global: "\eb53";
$wms-crossroad-inventory-group: "\eb54";
$wms-crossroad-inventory-position: "\eb55";
$wms-crossroad-labels-krt: "\eb56";
$wms-crossroad-labels-pallete: "\eb57";
$wms-crossroad-logs: "\eb58";
$wms-crossroad-maingroup: "\eb59";
$wms-crossroad-measure-units: "\eb5a";
$wms-crossroad-module: "\eb5b";
$wms-crossroad-order-group: "\eb5c";
$wms-crossroad-packign-messages: "\eb5d";
$wms-crossroad-porter-mode: "\eb5e";
$wms-crossroad-porter-reservation: "\eb5f";
$wms-crossroad-prijemky-global: "\eb60";
$wms-crossroad-printedlabels: "\eb61";
$wms-crossroad-product: "\eb62";
$wms-crossroad-reklamacia-presun: "\eb63";
$wms-crossroad-seasons: "\eb64";
$wms-crossroad-subject-adress: "\eb65";
$wms-crossroad-subjects: "\eb66";
$wms-crossroad-task: "\eb67";
$wms-crossroad-temperature: "\eb68";
$wms-crossroad-tickets: "\eb69";
$wms-crossroad-trademark: "\eb6a";
$wms-crossroad-vydajky-global: "\eb6b";
$wms-crossroad-vydajky-global-1: "\eb6c";
$wms-crossroad-workstation-logs: "\eb6d";
$wms-crossroad-zvozove-global: "\eb6e";
$wms-dameged-good2: "\eb6f";
$wms-dameged-good3: "\eb70";
$wms-dameged-goods: "\eb71";
$wms-dash-b2b: "\eb72";
$wms-dash-b2c1: "\eb73";
$wms-dash-b2cm: "\eb74";
$wms-dash-boxpackage: "\eb75";
$wms-dash-boxpackage-outline: "\eb76";
$wms-dash-cons: "\eb77";
$wms-dash-count: "\eb78";
$wms-dash-high: "\eb79";
$wms-dash-inv: "\eb7a";
$wms-dash-navyse: "\eb7b";
$wms-dash-navyse-outline: "\eb7c";
$wms-dash-poskodene: "\eb7d";
$wms-dash-poskodene-outline: "\eb7e";
$wms-dash-recinv: "\eb7f";
$wms-dash-ret: "\eb80";
$wms-dash-spinave: "\eb81";
$wms-dash-spinave-outline: "\eb82";
$wms-dash-svc: "\eb83";
$wms-dashboard: "\eb84";
$wms-dashboard-1: "\eb85";
$wms-dashboard-home: "\eb86";
$wms-dashboard-return_home: "\eb87";
$wms-dashboard2: "\eb88";
$wms-dashboard_ine: "\eb89";
$wms-dashboard_ine-active: "\eb8a";
$wms-dashboard_konsolidacia: "\eb8b";
$wms-dashboard_konsolidacia-active: "\eb8c";
$wms-dashboard_prijem: "\eb8d";
$wms-dashboard_prijem-active: "\eb8e";
$wms-dashboard_vydaj: "\eb8f";
$wms-dashboard_vydaj-active: "\eb90";
$wms-datum: "\eb91";
$wms-delete: "\eb92";
$wms-delivery-cart: "\eb93";
$wms-delivery-cart-add: "\eb94";
$wms-delivery-cart-add-outline: "\eb95";
$wms-delivery-cart-info: "\eb96";
$wms-delivery-cart-info-outline: "\eb97";
$wms-delivery-cart-outline: "\eb98";
$wms-delivery-cart-remove: "\eb99";
$wms-delivery-cart-remove-outline: "\eb9a";
$wms-delivery-cart-skladnik: "\eb9b";
$wms-delivery-cart-skladnik-outline: "\eb9c";
$wms-delivery-cart-stop: "\eb9d";
$wms-delivery-cart-stop-outline: "\eb9e";
$wms-delivery-cart-transfer: "\eb9f";
$wms-delivery-cart-transfer-outline: "\eba0";
$wms-delivery-cart-warning: "\eba1";
$wms-delivery-cart-warning-outline: "\eba2";
$wms-depth: "\eba3";
$wms-depth2: "\eba4";
$wms-deselectall: "\eba5";
$wms-device-pda: "\eba6";
$wms-device-zebra-printers: "\eba7";
$wms-diagram-arrow-outline: "\eba8";
$wms-diagram-line-outline: "\eba9";
$wms-diagram-point: "\ebaa";
$wms-diagram-point-danger: "\ebab";
$wms-diagram-point-danger-outline: "\ebac";
$wms-diagram-point-outline: "\ebad";
$wms-diagram-point-time-outline: "\ebae";
$wms-diagram-point-time-outline-1: "\ebaf";
$wms-diagram-point-warning: "\ebb0";
$wms-diagram-point-warning-outline: "\ebb1";
$wms-diagram-time-danger: "\ebb2";
$wms-diagram-time-danger-1: "\ebb3";
$wms-diference: "\ebb4";
$wms-diference-circle: "\ebb5";
$wms-diference-okay: "\ebb6";
$wms-diference-outline: "\ebb7";
$wms-diference-position: "\ebb8";
$wms-digitalsignature: "\ebb9";
$wms-digitalsignature-outline: "\ebba";
$wms-digitalsignature2: "\ebbb";
$wms-digitalsignature3: "\ebbc";
$wms-dispatch: "\ebbd";
$wms-dispatch2: "\ebbe";
$wms-document: "\ebbf";
$wms-document-missing: "\ebc0";
$wms-document-missing-outline: "\ebc1";
$wms-document-okay-outline: "\ebc2";
$wms-document-outline: "\ebc3";
$wms-document-pdf: "\ebc4";
$wms-documents-outline: "\ebc5";
$wms-dodavka: "\ebc6";
$wms-done-task: "\ebc7";
$wms-done-task-circle: "\ebc8";
$wms-doprava-dpd: "\ebc9";
$wms-doprava-interna: "\ebca";
$wms-doprava-neurcena: "\ebcb";
$wms-doprava-odlozena-paleta: "\ebcc";
$wms-doprava-packeta: "\ebcd";
$wms-doprava-ppl: "\ebce";
$wms-dopravca-gls: "\ebcf";
$wms-dopravca-gls-arrow: "\ebd0";
$wms-dopravca-gls-circle: "\ebd1";
$wms-dopravca-neznamy: "\ebd2";
$wms-dopravca-neznamy-outline: "\ebd3";
$wms-dopravca-unknow: "\ebd4";
$wms-dopravnik_segment: "\ebd5";
$wms-drag-gesture: "\ebd6";
$wms-drag-horizontal: "\ebd7";
$wms-drag-left: "\ebd8";
$wms-drag-left-outline: "\ebd9";
$wms-drag-right: "\ebda";
$wms-drag-right-outline: "\ebdb";
$wms-drag-vertical: "\ebdc";
$wms-ean: "\ebdd";
$wms-ean2: "\ebde";
$wms-edit: "\ebdf";
$wms-edit-outline: "\ebe0";
$wms-empty: "\ebe1";
$wms-empty-1: "\ebe2";
$wms-empty-square: "\ebe3";
$wms-enter: "\ebe4";
$wms-enter2: "\ebe5";
$wms-eso: "\ebe6";
$wms-eso9: "\ebe7";
$wms-etikety-count: "\ebe8";
$wms-etikety-count-num: "\ebe9";
$wms-etikety-count-num-outline: "\ebea";
$wms-etikety-count-outline: "\ebeb";
$wms-expand-transporthub-double: "\ebec";
$wms-expand-transporthub-double-outline: "\ebed";
$wms-expand-transporthub-single: "\ebee";
$wms-expand-transporthub-single-outline: "\ebef";
$wms-expedicia: "\ebf0";
$wms-export: "\ebf1";
$wms-filter: "\ebf2";
$wms-forcast: "\ebf3";
$wms-foteni-vazenie: "\ebf4";
$wms-fotenie: "\ebf5";
$wms-fotografie: "\ebf6";
$wms-fotografie-outline: "\ebf7";
$wms-free: "\ebf8";
$wms-gear-setup: "\ebf9";
$wms-gear-setup-outline: "\ebfa";
$wms-gift: "\ebfb";
$wms-gift-outline: "\ebfc";
$wms-goods: "\ebfd";
$wms-goods-full: "\ebfe";
$wms-goods-id: "\ebff";
$wms-goods-id-outline: "\ec00";
$wms-goods-none: "\ec01";
$wms-height: "\ec02";
$wms-height2: "\ec03";
$wms-help: "\ec04";
$wms-help-outline: "\ec05";
$wms-hidden: "\ec06";
$wms-hidden-outline: "\ec07";
$wms-highpriority: "\ec08";
$wms-in-progress: "\ec09";
$wms-info: "\ec0a";
$wms-info-filled: "\ec0b";
$wms-input_: "\ec0c";
$wms-input_article: "\ec0d";
$wms-input_artikel: "\ec0e";
$wms-input_camera: "\ec0f";
$wms-input_carton: "\ec10";
$wms-input_carton2: "\ec11";
$wms-input_cm: "\ec12";
$wms-input_cm-height: "\ec13";
$wms-input_cm-height-square: "\ec14";
$wms-input_cm-lenght: "\ec15";
$wms-input_cm-lenght-square: "\ec16";
$wms-input_cm-square: "\ec17";
$wms-input_cm-width: "\ec18";
$wms-input_cm-width-square: "\ec19";
$wms-input_ean: "\ec1a";
$wms-input_g: "\ec1b";
$wms-input_g-square: "\ec1c";
$wms-input_image: "\ec1d";
$wms-input_kg: "\ec1e";
$wms-input_kg-square: "\ec1f";
$wms-input_klt: "\ec20";
$wms-input_num: "\ec21";
$wms-input_paleta: "\ec22";
$wms-input_pieces: "\ec23";
$wms-input_pieces-square: "\ec24";
$wms-input_qr: "\ec25";
$wms-input_stender: "\ec26";
$wms-input_type: "\ec27";
$wms-input_umiestnenie: "\ec28";
$wms-input_weight: "\ec29";
$wms-input_weight-g: "\ec2a";
$wms-input_weight-g2: "\ec2b";
$wms-input_weight-kg: "\ec2c";
$wms-input_weight-kg2: "\ec2d";
$wms-input_weight2: "\ec2e";
$wms-internadoprava: "\ec2f";
$wms-inventura: "\ec30";
$wms-inventura-outline: "\ec31";
$wms-kamera_sklad_wifi: "\ec32";
$wms-kamera_sklad_zebra: "\ec33";
$wms-kamion: "\ec34";
$wms-karantena: "\ec35";
$wms-karantena-outline: "\ec36";
$wms-klec: "\ec37";
$wms-klec-1: "\ec38";
$wms-klec-1-mandatory: "\ec39";
$wms-klec-2: "\ec3a";
$wms-klec-2-mandatory: "\ec3b";
$wms-klec-3: "\ec3c";
$wms-klec-3-mandatory: "\ec3d";
$wms-klec-4: "\ec3e";
$wms-klec-4-mandatory: "\ec3f";
$wms-klec-5: "\ec40";
$wms-klec-5-mandatory: "\ec41";
$wms-klec-ouline: "\ec42";
$wms-klt: "\ec43";
$wms-klt-1: "\ec44";
$wms-klt-add: "\ec45";
$wms-klt-artikle: "\ec46";
$wms-klt-artikle-filled: "\ec47";
$wms-klt-artikle-filled-outline: "\ec48";
$wms-klt-artikle-outline: "\ec49";
$wms-klt-done: "\ec4a";
$wms-klt-empty: "\ec4b";
$wms-klt-empty-1: "\ec4c";
$wms-klt-mandatory: "\ec4d";
$wms-klt-mandatory-outline: "\ec4e";
$wms-klt-outline: "\ec4f";
$wms-klt-packing-ready: "\ec50";
$wms-klt-packing-ready-mandatory: "\ec51";
$wms-klt-packing-ready-mandatory-outline: "\ec52";
$wms-klt-packing-ready-outline: "\ec53";
$wms-klt-packing-waiting: "\ec54";
$wms-klt-packing-waiting-outline: "\ec55";
$wms-klt-put: "\ec56";
$wms-klt-putin: "\ec57";
$wms-klt-remove: "\ec58";
$wms-klt-scan: "\ec59";
$wms-klt-spustit: "\ec5a";
$wms-klt-transfer: "\ec5b";
$wms-klt-zdvihnut: "\ec5c";
$wms-konsolidacia: "\ec5d";
$wms-laravel-pulse: "\ec5e";
$wms-ldopravnik_segment-outline: "\ec5f";
$wms-logistoc-nophoto: "\ec60";
$wms-logistoc-nophoto-outline: "\ec61";
$wms-logistoc-photo: "\ec62";
$wms-logistoc-photo-outline: "\ec63";
$wms-logo-dpd: "\ec64";
$wms-logo-packeta: "\ec65";
$wms-logo-ppl: "\ec66";
$wms-logo-zasilkovna: "\ec67";
$wms-logotype: "\ec68";
$wms-logout-user: "\ec69";
$wms-lokacia-autostore: "\ec6a";
$wms-lokacia-paletovy: "\ec6b";
$wms-magic_wand: "\ec6c";
$wms-material: "\ec6d";
$wms-material-label: "\ec6e";
$wms-material-label-outline: "\ec6f";
$wms-material-mandatory: "\ec70";
$wms-material-mandatory-outline: "\ec71";
$wms-material-outline: "\ec72";
$wms-matus_markusek_logo_v1: "\ec73";
$wms-menu_option: "\ec74";
$wms-menu_option-desktop: "\ec75";
$wms-menu_option-logout: "\ec76";
$wms-menu_option-tablet: "\ec77";
$wms-menu_option2: "\ec78";
$wms-menu_option3: "\ec79";
$wms-menu_option4: "\ec7a";
$wms-meranie: "\ec7b";
$wms-minus-solo: "\ec7c";
$wms-mn: "\ec7d";
$wms-modules: "\ec7e";
$wms-monitor-autostore: "\ec7f";
$wms-monitor-jednopolozkove: "\ec80";
$wms-monitor-paletovysklad: "\ec81";
$wms-monitor-viacpolozkove: "\ec82";
$wms-moon: "\ec83";
$wms-naceste: "\ec84";
$wms-nadriadeny: "\ec85";
$wms-nadriadeny-outline: "\ec86";
$wms-nahlad: "\ec87";
$wms-nakladka-cesta: "\ec88";
$wms-name1: "\ec89";
$wms-name1-outline: "\ec8a";
$wms-neavizovany: "\ec8b";
$wms-new-edit-text: "\ec8c";
$wms-new-edit-text-outline: "\ec8d";
$wms-no-photo: "\ec8e";
$wms-notification-seen: "\ec8f";
$wms-notification-unseen: "\ec90";
$wms-notify: "\ec91";
$wms-notify-outline: "\ec92";
$wms-objem: "\ec93";
$wms-objem-liter: "\ec94";
$wms-objem-outline: "\ec95";
$wms-odhlasit: "\ec96";
$wms-okay: "\ec97";
$wms-other: "\ec98";
$wms-packing-circle_dis-close_b2c: "\ec99";
$wms-packing-circle_dis-hold_b2b: "\ec9a";
$wms-packing-circle_dis_b2b: "\ec9b";
$wms-packing-circle_dis_b2b-add: "\ec9c";
$wms-packing-circle_dis_b2b-add-outline: "\ec9d";
$wms-packing-circle_dis_b2b-close: "\ec9e";
$wms-packing-circle_dis_b2b-close-outline: "\ec9f";
$wms-packing-circle_dis_b2b-danger: "\eca0";
$wms-packing-circle_dis_b2b-danger-outline: "\eca1";
$wms-packing-circle_dis_b2b-down: "\eca2";
$wms-packing-circle_dis_b2b-down-outline: "\eca3";
$wms-packing-circle_dis_b2b-down2: "\eca4";
$wms-packing-circle_dis_b2b-down2-outline: "\eca5";
$wms-packing-circle_dis_b2b-down3: "\eca6";
$wms-packing-circle_dis_b2b-down3-outline: "\eca7";
$wms-packing-circle_dis_b2b-hold-outline: "\eca8";
$wms-packing-circle_dis_b2b-info: "\eca9";
$wms-packing-circle_dis_b2b-info-outline: "\ecaa";
$wms-packing-circle_dis_b2b-move: "\ecab";
$wms-packing-circle_dis_b2b-move-outline: "\ecac";
$wms-packing-circle_dis_b2b-okay: "\ecad";
$wms-packing-circle_dis_b2b-okay-outline: "\ecae";
$wms-packing-circle_dis_b2b-okay-workstation-outline: "\ecaf";
$wms-packing-circle_dis_b2b-okayworkstation: "\ecb0";
$wms-packing-circle_dis_b2b-outline: "\ecb1";
$wms-packing-circle_dis_b2b-ready: "\ecb2";
$wms-packing-circle_dis_b2b-ready-outline: "\ecb3";
$wms-packing-circle_dis_b2b-return: "\ecb4";
$wms-packing-circle_dis_b2b-return-outline: "\ecb5";
$wms-packing-circle_dis_b2b-right: "\ecb6";
$wms-packing-circle_dis_b2b-right-outline: "\ecb7";
$wms-packing-circle_dis_b2b-right2: "\ecb8";
$wms-packing-circle_dis_b2b-right2-outline: "\ecb9";
$wms-packing-circle_dis_b2b-right3: "\ecba";
$wms-packing-circle_dis_b2b-right3-outline: "\ecbb";
$wms-packing-circle_dis_b2b-skladnik: "\ecbc";
$wms-packing-circle_dis_b2b-skladnik-outline: "\ecbd";
$wms-packing-circle_dis_b2b-stop: "\ecbe";
$wms-packing-circle_dis_b2b-stop-outline: "\ecbf";
$wms-packing-circle_dis_b2b-wait: "\ecc0";
$wms-packing-circle_dis_b2b-wait-outline: "\ecc1";
$wms-packing-circle_dis_b2b-wait-workstation: "\ecc2";
$wms-packing-circle_dis_b2b-wait_glass: "\ecc3";
$wms-packing-circle_dis_b2b-wait_glass-outline: "\ecc4";
$wms-packing-circle_dis_b2b-wait_glassworkstation: "\ecc5";
$wms-packing-circle_dis_b2b-wait_glassworkstation-outline: "\ecc6";
$wms-packing-circle_dis_b2b-waitworkstation-outline: "\ecc7";
$wms-packing-circle_dis_b2b-warning: "\ecc8";
$wms-packing-circle_dis_b2b-warning-outline: "\ecc9";
$wms-packing-circle_dis_b2b-workstation: "\ecca";
$wms-packing-circle_dis_b2b-workstation-outline: "\eccb";
$wms-packing-circle_dis_b2c: "\eccc";
$wms-packing-circle_dis_b2c-add: "\eccd";
$wms-packing-circle_dis_b2c-add-outline: "\ecce";
$wms-packing-circle_dis_b2c-close-outline: "\eccf";
$wms-packing-circle_dis_b2c-danger: "\ecd0";
$wms-packing-circle_dis_b2c-danger-outline: "\ecd1";
$wms-packing-circle_dis_b2c-down: "\ecd2";
$wms-packing-circle_dis_b2c-down-outline: "\ecd3";
$wms-packing-circle_dis_b2c-down2: "\ecd4";
$wms-packing-circle_dis_b2c-down2-outline: "\ecd5";
$wms-packing-circle_dis_b2c-down3: "\ecd6";
$wms-packing-circle_dis_b2c-down3-outline: "\ecd7";
$wms-packing-circle_dis_b2c-hold: "\ecd8";
$wms-packing-circle_dis_b2c-hold-outline: "\ecd9";
$wms-packing-circle_dis_b2c-info: "\ecda";
$wms-packing-circle_dis_b2c-info-outline: "\ecdb";
$wms-packing-circle_dis_b2c-move: "\ecdc";
$wms-packing-circle_dis_b2c-move-outline: "\ecdd";
$wms-packing-circle_dis_b2c-okay: "\ecde";
$wms-packing-circle_dis_b2c-okay-outline: "\ecdf";
$wms-packing-circle_dis_b2c-okay-workstation-outline: "\ece0";
$wms-packing-circle_dis_b2c-okayworkstation: "\ece1";
$wms-packing-circle_dis_b2c-outline: "\ece2";
$wms-packing-circle_dis_b2c-ready: "\ece3";
$wms-packing-circle_dis_b2c-ready-outline: "\ece4";
$wms-packing-circle_dis_b2c-return: "\ece5";
$wms-packing-circle_dis_b2c-return-outline: "\ece6";
$wms-packing-circle_dis_b2c-right: "\ece7";
$wms-packing-circle_dis_b2c-right-outline: "\ece8";
$wms-packing-circle_dis_b2c-right2: "\ece9";
$wms-packing-circle_dis_b2c-right2-outline: "\ecea";
$wms-packing-circle_dis_b2c-right3: "\eceb";
$wms-packing-circle_dis_b2c-right3-outline: "\ecec";
$wms-packing-circle_dis_b2c-skladnik: "\eced";
$wms-packing-circle_dis_b2c-skladnik-outline: "\ecee";
$wms-packing-circle_dis_b2c-stop: "\ecef";
$wms-packing-circle_dis_b2c-stop-outline: "\ecf0";
$wms-packing-circle_dis_b2c-wait: "\ecf1";
$wms-packing-circle_dis_b2c-wait-outline: "\ecf2";
$wms-packing-circle_dis_b2c-wait-workstation: "\ecf3";
$wms-packing-circle_dis_b2c-wait-workstation-outline: "\ecf4";
$wms-packing-circle_dis_b2c-wait_glass: "\ecf5";
$wms-packing-circle_dis_b2c-wait_glass-outline: "\ecf6";
$wms-packing-circle_dis_b2c-wait_glassworkstation: "\ecf7";
$wms-packing-circle_dis_b2c-wait_glassworkstation-outline: "\ecf8";
$wms-packing-circle_dis_b2c-warning: "\ecf9";
$wms-packing-circle_dis_b2c-warning-outline: "\ecfa";
$wms-packing-circle_dis_b2c-workstation: "\ecfb";
$wms-packing-circle_dis_b2c-workstation-outline: "\ecfc";
$wms-packing-circle_dispatch-okay_b2b: "\ecfd";
$wms-packing-circle_dispatch-ready_b2b: "\ecfe";
$wms-packing-circle_dispatch-right_b2b: "\ecff";
$wms-packing-circle_dispatch_b2b: "\ed00";
$wms-packing-circle_dispatch_b2b-add: "\ed01";
$wms-packing-circle_dispatch_b2b-add-outline: "\ed02";
$wms-packing-circle_dispatch_b2b-close: "\ed03";
$wms-packing-circle_dispatch_b2b-close-outline: "\ed04";
$wms-packing-circle_dispatch_b2b-danger: "\ed05";
$wms-packing-circle_dispatch_b2b-danger-outline: "\ed06";
$wms-packing-circle_dispatch_b2b-down: "\ed07";
$wms-packing-circle_dispatch_b2b-down-outline: "\ed08";
$wms-packing-circle_dispatch_b2b-down2: "\ed09";
$wms-packing-circle_dispatch_b2b-down2-outline: "\ed0a";
$wms-packing-circle_dispatch_b2b-down3: "\ed0b";
$wms-packing-circle_dispatch_b2b-down3-outline: "\ed0c";
$wms-packing-circle_dispatch_b2b-hold: "\ed0d";
$wms-packing-circle_dispatch_b2b-hold-outline: "\ed0e";
$wms-packing-circle_dispatch_b2b-info: "\ed0f";
$wms-packing-circle_dispatch_b2b-info-outline: "\ed10";
$wms-packing-circle_dispatch_b2b-move: "\ed11";
$wms-packing-circle_dispatch_b2b-move-outline: "\ed12";
$wms-packing-circle_dispatch_b2b-okay-outline: "\ed13";
$wms-packing-circle_dispatch_b2b-outline: "\ed14";
$wms-packing-circle_dispatch_b2b-ready-outline: "\ed15";
$wms-packing-circle_dispatch_b2b-return: "\ed16";
$wms-packing-circle_dispatch_b2b-return-outline: "\ed17";
$wms-packing-circle_dispatch_b2b-right-outline: "\ed18";
$wms-packing-circle_dispatch_b2b-right2: "\ed19";
$wms-packing-circle_dispatch_b2b-right2-outline: "\ed1a";
$wms-packing-circle_dispatch_b2b-right3: "\ed1b";
$wms-packing-circle_dispatch_b2b-right3-outline: "\ed1c";
$wms-packing-circle_dispatch_b2b-skladnik: "\ed1d";
$wms-packing-circle_dispatch_b2b-skladnik-outline: "\ed1e";
$wms-packing-circle_dispatch_b2b-stop: "\ed1f";
$wms-packing-circle_dispatch_b2b-stop-outline: "\ed20";
$wms-packing-circle_dispatch_b2b-wait: "\ed21";
$wms-packing-circle_dispatch_b2b-wait-1: "\ed22";
$wms-packing-circle_dispatch_b2b-wait-outline: "\ed23";
$wms-packing-circle_dispatch_b2b-wait-outline-1: "\ed24";
$wms-packing-circle_dispatch_b2b-warning: "\ed25";
$wms-packing-circle_dispatch_b2b-warning-outline: "\ed26";
$wms-packing-circle_dispatch_b2b-workstation: "\ed27";
$wms-packing-circle_dispatch_b2b-workstation-outline: "\ed28";
$wms-packing-circle_dispatch_b2c: "\ed29";
$wms-packing-circle_dispatch_b2c-add: "\ed2a";
$wms-packing-circle_dispatch_b2c-add-outline: "\ed2b";
$wms-packing-circle_dispatch_b2c-close: "\ed2c";
$wms-packing-circle_dispatch_b2c-close-outline: "\ed2d";
$wms-packing-circle_dispatch_b2c-danger: "\ed2e";
$wms-packing-circle_dispatch_b2c-danger-outline: "\ed2f";
$wms-packing-circle_dispatch_b2c-down: "\ed30";
$wms-packing-circle_dispatch_b2c-down-outline: "\ed31";
$wms-packing-circle_dispatch_b2c-down2: "\ed32";
$wms-packing-circle_dispatch_b2c-down2-outline: "\ed33";
$wms-packing-circle_dispatch_b2c-down3: "\ed34";
$wms-packing-circle_dispatch_b2c-down3-outline: "\ed35";
$wms-packing-circle_dispatch_b2c-hold: "\ed36";
$wms-packing-circle_dispatch_b2c-hold-outline: "\ed37";
$wms-packing-circle_dispatch_b2c-info: "\ed38";
$wms-packing-circle_dispatch_b2c-info-outline: "\ed39";
$wms-packing-circle_dispatch_b2c-move: "\ed3a";
$wms-packing-circle_dispatch_b2c-move-outline: "\ed3b";
$wms-packing-circle_dispatch_b2c-okay: "\ed3c";
$wms-packing-circle_dispatch_b2c-okay-outline: "\ed3d";
$wms-packing-circle_dispatch_b2c-outline: "\ed3e";
$wms-packing-circle_dispatch_b2c-ready: "\ed3f";
$wms-packing-circle_dispatch_b2c-ready-outline: "\ed40";
$wms-packing-circle_dispatch_b2c-return: "\ed41";
$wms-packing-circle_dispatch_b2c-return-outline: "\ed42";
$wms-packing-circle_dispatch_b2c-right: "\ed43";
$wms-packing-circle_dispatch_b2c-right-outline: "\ed44";
$wms-packing-circle_dispatch_b2c-right2: "\ed45";
$wms-packing-circle_dispatch_b2c-right2-outline: "\ed46";
$wms-packing-circle_dispatch_b2c-right3: "\ed47";
$wms-packing-circle_dispatch_b2c-right3-outline: "\ed48";
$wms-packing-circle_dispatch_b2c-skladnik: "\ed49";
$wms-packing-circle_dispatch_b2c-skladnik-outline: "\ed4a";
$wms-packing-circle_dispatch_b2c-stop: "\ed4b";
$wms-packing-circle_dispatch_b2c-stop-outline: "\ed4c";
$wms-packing-circle_dispatch_b2c-wait: "\ed4d";
$wms-packing-circle_dispatch_b2c-wait-outline: "\ed4e";
$wms-packing-circle_dispatch_b2c-wait_glass: "\ed4f";
$wms-packing-circle_dispatch_b2c-wait_glass-outline: "\ed50";
$wms-packing-circle_dispatch_b2c-warning: "\ed51";
$wms-packing-circle_dispatch_b2c-warning-outline: "\ed52";
$wms-packing-circle_dispatch_b2c-workstation: "\ed53";
$wms-packing-circle_dispatch_b2c-workstation-outline: "\ed54";
$wms-packing_circle_dis_b2b-right-workstation-outline: "\ed55";
$wms-packing_circle_dis_b2c-right-workstation-outline: "\ed56";
$wms-packing_dis-close_b2c: "\ed57";
$wms-packing_dis-hold_b2b: "\ed58";
$wms-packing_dis_b2b: "\ed59";
$wms-packing_dis_b2b-add: "\ed5a";
$wms-packing_dis_b2b-add-outline: "\ed5b";
$wms-packing_dis_b2b-close: "\ed5c";
$wms-packing_dis_b2b-close-outline: "\ed5d";
$wms-packing_dis_b2b-danger: "\ed5e";
$wms-packing_dis_b2b-danger-outline: "\ed5f";
$wms-packing_dis_b2b-down: "\ed60";
$wms-packing_dis_b2b-down-outline: "\ed61";
$wms-packing_dis_b2b-down2: "\ed62";
$wms-packing_dis_b2b-down2-outline: "\ed63";
$wms-packing_dis_b2b-down3: "\ed64";
$wms-packing_dis_b2b-down3-outline: "\ed65";
$wms-packing_dis_b2b-hold-outline: "\ed66";
$wms-packing_dis_b2b-info: "\ed67";
$wms-packing_dis_b2b-info-outline: "\ed68";
$wms-packing_dis_b2b-move: "\ed69";
$wms-packing_dis_b2b-move-outline: "\ed6a";
$wms-packing_dis_b2b-okay: "\ed6b";
$wms-packing_dis_b2b-okay-outline: "\ed6c";
$wms-packing_dis_b2b-okayworkstation: "\ed6d";
$wms-packing_dis_b2b-okayworkstation-outline: "\ed6e";
$wms-packing_dis_b2b-outline: "\ed6f";
$wms-packing_dis_b2b-ready: "\ed70";
$wms-packing_dis_b2b-ready-outline: "\ed71";
$wms-packing_dis_b2b-return: "\ed72";
$wms-packing_dis_b2b-return-outline: "\ed73";
$wms-packing_dis_b2b-right: "\ed74";
$wms-packing_dis_b2b-right-outline: "\ed75";
$wms-packing_dis_b2b-right-workstation-outline: "\ed76";
$wms-packing_dis_b2b-right2: "\ed77";
$wms-packing_dis_b2b-right2-outline: "\ed78";
$wms-packing_dis_b2b-right3: "\ed79";
$wms-packing_dis_b2b-right3-outline: "\ed7a";
$wms-packing_dis_b2b-skladnik: "\ed7b";
$wms-packing_dis_b2b-skladnik-outline: "\ed7c";
$wms-packing_dis_b2b-stop: "\ed7d";
$wms-packing_dis_b2b-stop-outline: "\ed7e";
$wms-packing_dis_b2b-wait: "\ed7f";
$wms-packing_dis_b2b-wait-outline: "\ed80";
$wms-packing_dis_b2b-wait_glass: "\ed81";
$wms-packing_dis_b2b-wait_glass-outline: "\ed82";
$wms-packing_dis_b2b-wait_glassworkstation: "\ed83";
$wms-packing_dis_b2b-wait_glassworkstation-outline: "\ed84";
$wms-packing_dis_b2b-waitworkstation: "\ed85";
$wms-packing_dis_b2b-waitworkstation-outline: "\ed86";
$wms-packing_dis_b2b-warning: "\ed87";
$wms-packing_dis_b2b-warning-outline: "\ed88";
$wms-packing_dis_b2b-workstation: "\ed89";
$wms-packing_dis_b2b-workstation-outline: "\ed8a";
$wms-packing_dis_b2c: "\ed8b";
$wms-packing_dis_b2c-add: "\ed8c";
$wms-packing_dis_b2c-add-outline: "\ed8d";
$wms-packing_dis_b2c-close-outline: "\ed8e";
$wms-packing_dis_b2c-danger: "\ed8f";
$wms-packing_dis_b2c-danger-outline: "\ed90";
$wms-packing_dis_b2c-down: "\ed91";
$wms-packing_dis_b2c-down-outline: "\ed92";
$wms-packing_dis_b2c-down2: "\ed93";
$wms-packing_dis_b2c-down2-outline: "\ed94";
$wms-packing_dis_b2c-down3: "\ed95";
$wms-packing_dis_b2c-down3-outline: "\ed96";
$wms-packing_dis_b2c-hold: "\ed97";
$wms-packing_dis_b2c-hold-outline: "\ed98";
$wms-packing_dis_b2c-info: "\ed99";
$wms-packing_dis_b2c-info-outline: "\ed9a";
$wms-packing_dis_b2c-move: "\ed9b";
$wms-packing_dis_b2c-move-outline: "\ed9c";
$wms-packing_dis_b2c-okay: "\ed9d";
$wms-packing_dis_b2c-okay-outline: "\ed9e";
$wms-packing_dis_b2c-okayworkstation: "\ed9f";
$wms-packing_dis_b2c-okayworkstation-outline: "\eda0";
$wms-packing_dis_b2c-outline: "\eda1";
$wms-packing_dis_b2c-ready: "\eda2";
$wms-packing_dis_b2c-ready-outline: "\eda3";
$wms-packing_dis_b2c-return: "\eda4";
$wms-packing_dis_b2c-return-outline: "\eda5";
$wms-packing_dis_b2c-right: "\eda6";
$wms-packing_dis_b2c-right-outline: "\eda7";
$wms-packing_dis_b2c-right-workstation-outline: "\eda8";
$wms-packing_dis_b2c-right2: "\eda9";
$wms-packing_dis_b2c-right2-outline: "\edaa";
$wms-packing_dis_b2c-right3: "\edab";
$wms-packing_dis_b2c-right3-outline: "\edac";
$wms-packing_dis_b2c-skladnik: "\edad";
$wms-packing_dis_b2c-skladnik-outline: "\edae";
$wms-packing_dis_b2c-stop: "\edaf";
$wms-packing_dis_b2c-stop-outline: "\edb0";
$wms-packing_dis_b2c-wait: "\edb1";
$wms-packing_dis_b2c-wait-outline: "\edb2";
$wms-packing_dis_b2c-wait_glass: "\edb3";
$wms-packing_dis_b2c-wait_glass-outline: "\edb4";
$wms-packing_dis_b2c-wait_glassworkstation: "\edb5";
$wms-packing_dis_b2c-wait_glassworkstation-outline: "\edb6";
$wms-packing_dis_b2c-waitworkstation: "\edb7";
$wms-packing_dis_b2c-waitworkstation-outline: "\edb8";
$wms-packing_dis_b2c-warning: "\edb9";
$wms-packing_dis_b2c-warning-outline: "\edba";
$wms-packing_dis_b2c-workstation: "\edbb";
$wms-packing_dis_b2c-workstation-outline: "\edbc";
$wms-packing_dispatch-okay_b2b: "\edbd";
$wms-packing_dispatch-ready_b2b: "\edbe";
$wms-packing_dispatch-right_b2b: "\edbf";
$wms-packing_dispatch_b2b: "\edc0";
$wms-packing_dispatch_b2b-add: "\edc1";
$wms-packing_dispatch_b2b-add-outline: "\edc2";
$wms-packing_dispatch_b2b-close: "\edc3";
$wms-packing_dispatch_b2b-close-outline: "\edc4";
$wms-packing_dispatch_b2b-danger: "\edc5";
$wms-packing_dispatch_b2b-danger-outline: "\edc6";
$wms-packing_dispatch_b2b-down: "\edc7";
$wms-packing_dispatch_b2b-down-outline: "\edc8";
$wms-packing_dispatch_b2b-down2: "\edc9";
$wms-packing_dispatch_b2b-down2-outline: "\edca";
$wms-packing_dispatch_b2b-down3: "\edcb";
$wms-packing_dispatch_b2b-down3-outline: "\edcc";
$wms-packing_dispatch_b2b-hold: "\edcd";
$wms-packing_dispatch_b2b-hold-outline: "\edce";
$wms-packing_dispatch_b2b-info: "\edcf";
$wms-packing_dispatch_b2b-info-outline: "\edd0";
$wms-packing_dispatch_b2b-move: "\edd1";
$wms-packing_dispatch_b2b-move-outline: "\edd2";
$wms-packing_dispatch_b2b-okay-outline: "\edd3";
$wms-packing_dispatch_b2b-outline: "\edd4";
$wms-packing_dispatch_b2b-ready-outline: "\edd5";
$wms-packing_dispatch_b2b-return: "\edd6";
$wms-packing_dispatch_b2b-return-outline: "\edd7";
$wms-packing_dispatch_b2b-right-outline: "\edd8";
$wms-packing_dispatch_b2b-right2: "\edd9";
$wms-packing_dispatch_b2b-right2-outline: "\edda";
$wms-packing_dispatch_b2b-right3: "\eddb";
$wms-packing_dispatch_b2b-right3-outline: "\eddc";
$wms-packing_dispatch_b2b-skladnik: "\eddd";
$wms-packing_dispatch_b2b-skladnik-outline: "\edde";
$wms-packing_dispatch_b2b-stop: "\eddf";
$wms-packing_dispatch_b2b-stop-outline: "\ede0";
$wms-packing_dispatch_b2b-wait: "\ede1";
$wms-packing_dispatch_b2b-wait-outline: "\ede2";
$wms-packing_dispatch_b2b-wait_glass: "\ede3";
$wms-packing_dispatch_b2b-wait_glass-outline: "\ede4";
$wms-packing_dispatch_b2b-warning: "\ede5";
$wms-packing_dispatch_b2b-warning-outline: "\ede6";
$wms-packing_dispatch_b2b-workstation: "\ede7";
$wms-packing_dispatch_b2b-workstation-outline: "\ede8";
$wms-packing_dispatch_b2c: "\ede9";
$wms-packing_dispatch_b2c-add: "\edea";
$wms-packing_dispatch_b2c-add-outline: "\edeb";
$wms-packing_dispatch_b2c-close: "\edec";
$wms-packing_dispatch_b2c-close-outline: "\eded";
$wms-packing_dispatch_b2c-danger: "\edee";
$wms-packing_dispatch_b2c-danger-outline: "\edef";
$wms-packing_dispatch_b2c-down: "\edf0";
$wms-packing_dispatch_b2c-down-outline: "\edf1";
$wms-packing_dispatch_b2c-down2: "\edf2";
$wms-packing_dispatch_b2c-down2-outline: "\edf3";
$wms-packing_dispatch_b2c-down3: "\edf4";
$wms-packing_dispatch_b2c-down3-outline: "\edf5";
$wms-packing_dispatch_b2c-hold: "\edf6";
$wms-packing_dispatch_b2c-hold-outline: "\edf7";
$wms-packing_dispatch_b2c-info: "\edf8";
$wms-packing_dispatch_b2c-info-outline: "\edf9";
$wms-packing_dispatch_b2c-move: "\edfa";
$wms-packing_dispatch_b2c-move-outline: "\edfb";
$wms-packing_dispatch_b2c-okay: "\edfc";
$wms-packing_dispatch_b2c-okay-outline: "\edfd";
$wms-packing_dispatch_b2c-outline: "\edfe";
$wms-packing_dispatch_b2c-ready: "\edff";
$wms-packing_dispatch_b2c-ready-outline: "\ee00";
$wms-packing_dispatch_b2c-return: "\ee01";
$wms-packing_dispatch_b2c-return-outline: "\ee02";
$wms-packing_dispatch_b2c-right: "\ee03";
$wms-packing_dispatch_b2c-right-outline: "\ee04";
$wms-packing_dispatch_b2c-right2: "\ee05";
$wms-packing_dispatch_b2c-right2-outline: "\ee06";
$wms-packing_dispatch_b2c-right3: "\ee07";
$wms-packing_dispatch_b2c-right3-outline: "\ee08";
$wms-packing_dispatch_b2c-skladnik: "\ee09";
$wms-packing_dispatch_b2c-skladnik-outline: "\ee0a";
$wms-packing_dispatch_b2c-stop: "\ee0b";
$wms-packing_dispatch_b2c-stop-outline: "\ee0c";
$wms-packing_dispatch_b2c-wait: "\ee0d";
$wms-packing_dispatch_b2c-wait-outline: "\ee0e";
$wms-packing_dispatch_b2c-wait_glass: "\ee0f";
$wms-packing_dispatch_b2c-wait_glass-outline: "\ee10";
$wms-packing_dispatch_b2c-warning: "\ee11";
$wms-packing_dispatch_b2c-warning-outline: "\ee12";
$wms-packing_dispatch_b2c-workstation: "\ee13";
$wms-packing_dispatch_b2c-workstation-outline: "\ee14";
$wms-paletovy: "\ee15";
$wms-paletovy-kam: "\ee16";
$wms-paletovy-odkial: "\ee17";
$wms-paletovy-outline: "\ee18";
$wms-paletovy-outline2: "\ee19";
$wms-paletovy-scan: "\ee1a";
$wms-paletovy-transfer: "\ee1b";
$wms-paletovy-zmena: "\ee1c";
$wms-paletovy2: "\ee1d";
$wms-paletovy2-1: "\ee1e";
$wms-paletovy2-outline: "\ee1f";
$wms-paletovy_pozicia: "\ee20";
$wms-paletovy_pozicia_plna: "\ee21";
$wms-paletovy_pozicia_plocha: "\ee22";
$wms-paletovy_pozicia_prazdna: "\ee23";
$wms-paletovy_pozicia_rezervovana: "\ee24";
$wms-paletovy_pozicia_vyuzita: "\ee25";
$wms-paletovy_pozicia_vyuzita-25: "\ee26";
$wms-paletovy_pozicia_vyuzita-50: "\ee27";
$wms-paletovy_pozicia_vyuzita-75: "\ee28";
$wms-paletovy_sklad: "\ee29";
$wms-paletovy_sklad-lokacia: "\ee2a";
$wms-paletovy_sklad-lokacia-outline: "\ee2b";
$wms-paletovy_sklad-pickovanie: "\ee2c";
$wms-paletovy_sklad-pickovanie-outline: "\ee2d";
$wms-paletovy_sklad_expedicia: "\ee2e";
$wms-paletovy_sklad_prijem: "\ee2f";
$wms-pallete: "\ee30";
$wms-pallete-damaged: "\ee31";
$wms-pallete-missing: "\ee32";
$wms-pallete-missing-1: "\ee33";
$wms-pallete-okay: "\ee34";
$wms-pallete-scan: "\ee35";
$wms-performance-statistics: "\ee36";
$wms-performance-statistics-average: "\ee37";
$wms-performance-statistics-average-outline: "\ee38";
$wms-performance-statistics-detail: "\ee39";
$wms-performance-statistics-detail-outline: "\ee3a";
$wms-performance-statistics-fastest: "\ee3b";
$wms-performance-statistics-fastest-outline: "\ee3c";
$wms-performance-statistics-outline: "\ee3d";
$wms-performance-statistics-slowest: "\ee3e";
$wms-performance-statistics-slowest-outline: "\ee3f";
$wms-performance-statistics-speed: "\ee40";
$wms-performance-statistics-speed-outline: "\ee41";
$wms-performance-statistics-time: "\ee42";
$wms-performance-statistics-time-outline: "\ee43";
$wms-performance-statistics-time-sum: "\ee44";
$wms-performance-statistics-time-sum-outline: "\ee45";
$wms-pick: "\ee46";
$wms-pick-b2c: "\ee47";
$wms-pick2: "\ee48";
$wms-pick2-outline: "\ee49";
$wms-picking_poskodeny: "\ee4a";
$wms-picking_spinavy: "\ee4b";
$wms-pickovaci-hadik: "\ee4c";
$wms-pickovaci-hadik-2: "\ee4d";
$wms-pickovaci-hadik-2-none: "\ee4e";
$wms-pickovaci-hadik-add: "\ee4f";
$wms-pickovaci-hadik-none: "\ee50";
$wms-pickovanie: "\ee51";
$wms-pickovanie2: "\ee52";
$wms-pickovanie_do_krabic: "\ee53";
$wms-pickovanie_do_krabic-outline: "\ee54";
$wms-place: "\ee55";
$wms-play-and-pause-button: "\ee56";
$wms-play-and-pause-button-outline: "\ee57";
$wms-play-button: "\ee58";
$wms-play-button-outline: "\ee59";
$wms-play-start: "\ee5a";
$wms-play-start-outline: "\ee5b";
$wms-plk: "\ee5c";
$wms-plk-add: "\ee5d";
$wms-plk-add-outline: "\ee5e";
$wms-plk-choose: "\ee5f";
$wms-plk-choose-outline: "\ee60";
$wms-plk-front: "\ee61";
$wms-plk-front-lokacia: "\ee62";
$wms-plk-front-lokacia-outline: "\ee63";
$wms-plk-front-nahlad: "\ee64";
$wms-plk-front-nahlad-outline: "\ee65";
$wms-plk-front-outline: "\ee66";
$wms-plk-okay: "\ee67";
$wms-plk-okay-outline: "\ee68";
$wms-plk-outline: "\ee69";
$wms-plk-pick: "\ee6a";
$wms-plk-pick-outline: "\ee6b";
$wms-plk-put: "\ee6c";
$wms-plk-put-outline: "\ee6d";
$wms-plk-scan: "\ee6e";
$wms-plk-scan-outline: "\ee6f";
$wms-plomba: "\ee70";
$wms-plomba-outline: "\ee71";
$wms-plomby: "\ee72";
$wms-plomby-outline: "\ee73";
$wms-pracovisko: "\ee74";
$wms-predprijem: "\ee75";
$wms-predvolena: "\ee76";
$wms-predvolena-active: "\ee77";
$wms-predvolena-home: "\ee78";
$wms-prehladova: "\ee79";
$wms-prerusit: "\ee7a";
$wms-prerusit2: "\ee7b";
$wms-previous-page: "\ee7c";
$wms-previous-page2: "\ee7d";
$wms-previous-page3: "\ee7e";
$wms-previous-page4: "\ee7f";
$wms-previous-page5: "\ee80";
$wms-previous-page6: "\ee81";
$wms-prijem: "\ee82";
$wms-printing: "\ee83";
$wms-printing-outline: "\ee84";
$wms-priority: "\ee85";
$wms-priority2: "\ee86";
$wms-problem-article-dameged: "\ee87";
$wms-problem-article-dirty: "\ee88";
$wms-problem-article-missing: "\ee89";
$wms-problem-article-surplus: "\ee8a";
$wms-products-prestime: "\ee8b";
$wms-products-prestime-outline: "\ee8c";
$wms-ps: "\ee8d";
$wms-ps-outline: "\ee8e";
$wms-ps_cell: "\ee8f";
$wms-put: "\ee90";
$wms-put-b2c: "\ee91";
$wms-received: "\ee92";
$wms-regal-position: "\ee93";
$wms-regal-position-0: "\ee94";
$wms-regal-position-1: "\ee95";
$wms-regal-position-10: "\ee96";
$wms-regal-position-11: "\ee97";
$wms-regal-position-12: "\ee98";
$wms-regal-position-13: "\ee99";
$wms-regal-position-14: "\ee9a";
$wms-regal-position-15: "\ee9b";
$wms-regal-position-2: "\ee9c";
$wms-regal-position-3: "\ee9d";
$wms-regal-position-4: "\ee9e";
$wms-regal-position-5: "\ee9f";
$wms-regal-position-6: "\eea0";
$wms-regal-position-7: "\eea1";
$wms-regal-position-8: "\eea2";
$wms-regal-position-9: "\eea3";
$wms-regal-position-actual: "\eea4";
$wms-regal-position-bg: "\eea5";
$wms-regal-position-marker: "\eea6";
$wms-regal-state: "\eea7";
$wms-regal_plk: "\eea8";
$wms-regal_plk-outline: "\eea9";
$wms-reklamacie: "\eeaa";
$wms-reload: "\eeab";
$wms-remove: "\eeac";
$wms-remove-one: "\eead";
$wms-remove-outline: "\eeae";
$wms-reserved: "\eeaf";
$wms-return-goods-damaged: "\eeb0";
$wms-return-goods-dirty: "\eeb1";
$wms-return-goods-jeans: "\eeb2";
$wms-return-goods-picto: "\eeb3";
$wms-return-goods-shirt: "\eeb4";
$wms-rozcestnik: "\eeb5";
$wms-rychla_inventura: "\eeb6";
$wms-scan_doc: "\eeb7";
$wms-scan_ean: "\eeb8";
$wms-scan_ean-carton: "\eeb9";
$wms-scan_ean-lokacia: "\eeba";
$wms-scan_ean-okay: "\eebb";
$wms-scan_ean-warning: "\eebc";
$wms-scan_ean2: "\eebd";
$wms-scan_ean3: "\eebe";
$wms-scan_eye: "\eebf";
$wms-scan_half: "\eec0";
$wms-scan_id: "\eec1";
$wms-scan_item2: "\eec2";
$wms-scan_person: "\eec3";
$wms-scan_qr: "\eec4";
$wms-scan_qr2: "\eec5";
$wms-search: "\eec6";
$wms-sekcia-notofikacie: "\eec7";
$wms-sekcia_administracia: "\eec8";
$wms-sekcia_administrativa: "\eec9";
$wms-sekcia_balenie: "\eeca";
$wms-sekcia_cakanie: "\eecb";
$wms-sekcia_created: "\eecc";
$wms-sekcia_dopravca: "\eecd";
$wms-sekcia_dopravnik: "\eece";
$wms-sekcia_expedicia: "\eecf";
$wms-sekcia_foteni-vazenie: "\eed0";
$wms-sekcia_highpriority: "\eed1";
$wms-sekcia_hladat: "\eed2";
$wms-sekcia_inventura: "\eed3";
$wms-sekcia_konsolidacia: "\eed4";
$wms-sekcia_meranie: "\eed5";
$wms-sekcia_nezaradene: "\eed6";
$wms-sekcia_obrazky: "\eed7";
$wms-sekcia_picking: "\eed8";
$wms-sekcia_predprijem: "\eed9";
$wms-sekcia_prijem: "\eeda";
$wms-sekcia_problemovytovar: "\eedb";
$wms-sekcia_reklamacie: "\eedc";
$wms-sekcia_servise: "\eedd";
$wms-sekcia_stats: "\eede";
$wms-sekcia_stranky: "\eedf";
$wms-sekcia_tlac-etikety: "\eee0";
$wms-sekcia_transfer: "\eee1";
$wms-sekcia_transport: "\eee2";
$wms-sekcia_triedenie: "\eee3";
$wms-sekcia_ulohy: "\eee4";
$wms-sekcia_vazenie: "\eee5";
$wms-sekcia_velka_inventura: "\eee6";
$wms-sekcia_vermont: "\eee7";
$wms-sekcia_vms: "\eee8";
$wms-sekcia_vratky: "\eee9";
$wms-sekcia_vykladka: "\eeea";
$wms-sekcia_zabalene: "\eeeb";
$wms-selectall: "\eeec";
$wms-send: "\eeed";
$wms-sensor_sklad: "\eeee";
$wms-sensor_sklad_opticky: "\eeef";
$wms-servise: "\eef0";
$wms-settings: "\eef1";
$wms-settings-1: "\eef2";
$wms-settings-goal: "\eef3";
$wms-setup: "\eef4";
$wms-size: "\eef5";
$wms-skladnik: "\eef6";
$wms-skladnik-outline: "\eef7";
$wms-skladnik-outline-supervisor-warning: "\eef8";
$wms-skladnik-outline-warning: "\eef9";
$wms-smilee: "\eefa";
$wms-smilee-outline: "\eefb";
$wms-spustit: "\eefc";
$wms-spz: "\eefd";
$wms-spz-outline: "\eefe";
$wms-standaone-alert: "\eeff";
$wms-standaone-alert2: "\ef00";
$wms-standaone-alert3: "\ef01";
$wms-statistical-graphic: "\ef02";
$wms-status: "\ef03";
$wms-stender: "\ef04";
$wms-stepper-vertical-solo: "\ef05";
$wms-sum: "\ef06";
$wms-sum2: "\ef07";
$wms-sum3: "\ef08";
$wms-sun: "\ef09";
$wms-supervisor: "\ef0a";
$wms-switch: "\ef0b";
$wms-switch-1: "\ef0c";
$wms-switch-off: "\ef0d";
$wms-switch-on: "\ef0e";
$wms-tabulka: "\ef0f";
$wms-tabulka-outline: "\ef10";
$wms-taskgroup: "\ef11";
$wms-thumb-acepted: "\ef12";
$wms-thumb-acepted-circle: "\ef13";
$wms-thumb-acepted-circle-outline: "\ef14";
$wms-thumb-acepted-outline: "\ef15";
$wms-thumb-not-acepted: "\ef16";
$wms-thumb-not-acepted-circle: "\ef17";
$wms-thumb-not-acepted-circle-outline: "\ef18";
$wms-thumb-not-acepted-outline: "\ef19";
$wms-thumbnail: "\ef1a";
$wms-timeline-as-pick: "\ef1b";
$wms-timeline-dopravnik: "\ef1c";
$wms-timeline-mimo-nosicov: "\ef1d";
$wms-timeline-nevyskladnene-as: "\ef1e";
$wms-timeline-nevyskladnene-ps: "\ef1f";
$wms-timeline-ready-na-balenie: "\ef20";
$wms-timeline-zostava-balit: "\ef21";
$wms-timeupdate: "\ef22";
$wms-tlac-etikety: "\ef23";
$wms-to-be-replaced: "\ef24";
$wms-to_be-changed: "\ef25";
$wms-transfer: "\ef26";
$wms-transit-arrow-carton: "\ef27";
$wms-transit-arrow-carton-outline: "\ef28";
$wms-transit-arrow-dots-outline: "\ef29";
$wms-transit-dots-carton: "\ef2a";
$wms-trash: "\ef2b";
$wms-trash-outline: "\ef2c";
$wms-trediaren: "\ef2d";
$wms-ukoncit: "\ef2e";
$wms-ukony: "\ef2f";
$wms-ukony-in: "\ef30";
$wms-ukony-in-outline: "\ef31";
$wms-ukony-move: "\ef32";
$wms-ukony-move-outline: "\ef33";
$wms-ukony-outline: "\ef34";
$wms-ukony-stop: "\ef35";
$wms-ukony-stop-outline: "\ef36";
$wms-ukony-user: "\ef37";
$wms-ukony-user-outline: "\ef38";
$wms-ukony-wait: "\ef39";
$wms-ukony-wait-outline: "\ef3a";
$wms-ulohy: "\ef3b";
$wms-ulohy-1: "\ef3c";
$wms-ulohy-outline: "\ef3d";
$wms-ulohy-stop: "\ef3e";
$wms-ulohy-stop-outline: "\ef3f";
$wms-ulohy-wait: "\ef40";
$wms-ulohy-wait-outline: "\ef41";
$wms-union: "\ef42";
$wms-user-online: "\ef43";
$wms-user-pause: "\ef44";
$wms-user-unknown: "\ef45";
$wms-user-warning: "\ef46";
$wms-v_home: "\ef47";
$wms-vapenka: "\ef48";
$wms-vapenka-3r00: "\ef49";
$wms-vapenka-3r00-outline: "\ef4a";
$wms-vapenka-3s20: "\ef4b";
$wms-vapenka-3s20-outline: "\ef4c";
$wms-vapenka-outline: "\ef4d";
$wms-vazenie: "\ef4e";
$wms-vazenie-stanica: "\ef4f";
$wms-vbin: "\ef50";
$wms-vbin-filled: "\ef51";
$wms-vbin_full: "\ef52";
$wms-vbin_half: "\ef53";
$wms-vbin_prijem: "\ef54";
$wms-vbin_quarter: "\ef55";
$wms-vermont: "\ef56";
$wms-vermont-intranet-pass: "\ef57";
$wms-vermont-ucko: "\ef58";
$wms-vermont-wms-logo: "\ef59";
$wms-vermont_invert: "\ef5a";
$wms-visible: "\ef5b";
$wms-vol: "\ef5c";
$wms-vol-add: "\ef5d";
$wms-vol-add-outline: "\ef5e";
$wms-vol-info: "\ef5f";
$wms-vol-info-outline: "\ef60";
$wms-vol-move: "\ef61";
$wms-vol-move-outline: "\ef62";
$wms-vol-okay: "\ef63";
$wms-vol-okay-outline: "\ef64";
$wms-vol-outline: "\ef65";
$wms-vol-remove: "\ef66";
$wms-vol-remove-outline: "\ef67";
$wms-vol-settings: "\ef68";
$wms-vol-settings-outline: "\ef69";
$wms-vol-stop: "\ef6a";
$wms-vol-stop-outline: "\ef6b";
$wms-vol-warning: "\ef6c";
$wms-vol-warning-outline: "\ef6d";
$wms-volume: "\ef6e";
$wms-volume-add: "\ef6f";
$wms-volume-add-outline: "\ef70";
$wms-volume-info: "\ef71";
$wms-volume-info-outline: "\ef72";
$wms-volume-liter: "\ef73";
$wms-volume-liter-outline: "\ef74";
$wms-volume-liter-square: "\ef75";
$wms-volume-move: "\ef76";
$wms-volume-move-outline: "\ef77";
$wms-volume-outline: "\ef78";
$wms-volume-remove: "\ef79";
$wms-volume-remove-outline: "\ef7a";
$wms-volume-settings: "\ef7b";
$wms-volume-settings-outline: "\ef7c";
$wms-volume-stop: "\ef7d";
$wms-volume-stop-outline: "\ef7e";
$wms-volume-warning: "\ef7f";
$wms-volume-warning-outline: "\ef80";
$wms-volume_alt: "\ef81";
$wms-volume_alt-add: "\ef82";
$wms-volume_alt-add-outline: "\ef83";
$wms-volume_alt-info: "\ef84";
$wms-volume_alt-info-outline: "\ef85";
$wms-volume_alt-move: "\ef86";
$wms-volume_alt-move-outline: "\ef87";
$wms-volume_alt-outline: "\ef88";
$wms-volume_alt-remove: "\ef89";
$wms-volume_alt-remove-outline: "\ef8a";
$wms-volume_alt-settings: "\ef8b";
$wms-volume_alt-settings-outline: "\ef8c";
$wms-volume_alt-stop: "\ef8d";
$wms-volume_alt-stop-outline: "\ef8e";
$wms-volume_alt-warning: "\ef8f";
$wms-volume_alt-warning-outline: "\ef90";
$wms-vozik: "\ef91";
$wms-vozik-mandatory: "\ef92";
$wms-vratky: "\ef93";
$wms-vratky-filled: "\ef94";
$wms-vratky-single: "\ef95";
$wms-vratky-single-outline: "\ef96";
$wms-vydajka: "\ef97";
$wms-vydajka-gift: "\ef98";
$wms-vydajka-gift-ouline: "\ef99";
$wms-vydajka-move: "\ef9a";
$wms-vydajka-move-ouline: "\ef9b";
$wms-vydajka-movein: "\ef9c";
$wms-vydajka-movein-ouline: "\ef9d";
$wms-vydajka-ouline: "\ef9e";
$wms-vydajka-ready: "\ef9f";
$wms-vydajka-ready-ouline: "\efa0";
$wms-vydajka-skladnik: "\efa1";
$wms-vydajka-skladnik-ouline: "\efa2";
$wms-vydajka-stop: "\efa3";
$wms-vydajka-stop-ouline: "\efa4";
$wms-vydajka-triedenie: "\efa5";
$wms-vydajka-triedenie-ouline: "\efa6";
$wms-vydajka-wait: "\efa7";
$wms-vydajka-wait-ouline: "\efa8";
$wms-vyhladavanie: "\efa9";
$wms-vykladka: "\efaa";
$wms-vystraha_active: "\efab";
$wms-vystraha_active-bot: "\efac";
$wms-vystraha_active-outline: "\efad";
$wms-vystraha_active-outline-bot: "\efae";
$wms-vystraha_default: "\efaf";
$wms-vystraha_default-outline: "\efb0";
$wms-vystraha_fatal: "\efb1";
$wms-vystraha_fatal-outline: "\efb2";
$wms-warehouse: "\efb3";
$wms-warning: "\efb4";
$wms-warning-outline: "\efb5";
$wms-weight-gram: "\efb6";
$wms-weight-gram-outline: "\efb7";
$wms-weight-kg: "\efb8";
$wms-weight-kg-outline: "\efb9";
$wms-width: "\efba";
$wms-width2: "\efbb";
$wms-wink: "\efbc";
$wms-wink-outline: "\efbd";
$wms-wishlist: "\efbe";
$wms-wishlist-outline: "\efbf";
$wms-workdesk: "\efc0";
$wms-workdesk-okay-outline: "\efc1";
$wms-workdesk-okay-setup: "\efc2";
$wms-workdesk-outline: "\efc3";
$wms-workdesk-put: "\efc4";
$wms-workdesk-put-outline: "\efc5";
$wms-workdesk-setup: "\efc6";
$wms-workdesk-setup-outline: "\efc7";
$wms-workdesk-stop: "\efc8";
$wms-workdesk-stop-outline: "\efc9";
$wms-workerplace: "\efca";
$wms-workerplace2: "\efcb";
$wms-workload-0: "\efcc";
$wms-workload-0-outline: "\efcd";
$wms-workload-1: "\efce";
$wms-workload-1-outline: "\efcf";
$wms-workload-2: "\efd0";
$wms-workload-2-outline: "\efd1";
$wms-workload-3: "\efd2";
$wms-workload-3-outline: "\efd3";
$wms-workload-4: "\efd4";
$wms-workload-4-outline: "\efd5";
$wms-workload-5: "\efd6";
$wms-workload-5-outline: "\efd7";
$wms-workload-6: "\efd8";
$wms-workload-6-outline: "\efd9";
$wms-workload-7: "\efda";
$wms-workload-7-outline: "\efdb";
$wms-workload-8: "\efdc";
$wms-workload-8-outline: "\efdd";
$wms-workload-9: "\efde";
$wms-workload-9-outline: "\efdf";
$wms-workload-clock-0: "\efe0";
$wms-workload-clock-0-outline: "\efe1";
$wms-workload-clock-1: "\efe2";
$wms-workload-clock-1-outline: "\efe3";
$wms-workload-clock-2: "\efe4";
$wms-workload-clock-2-outline: "\efe5";
$wms-workload-clock-3: "\efe6";
$wms-workload-clock-3-outline: "\efe7";
$wms-workload-clock-4: "\efe8";
$wms-workload-clock-4-outline: "\efe9";
$wms-workload-clock-5: "\efea";
$wms-workload-clock-5-outline: "\efeb";
$wms-workload-clock-6: "\efec";
$wms-workload-clock-6-outline: "\efed";
$wms-workload-clock-7: "\efee";
$wms-workload-clock-7-outline: "\efef";
$wms-workload-clock-8: "\eff0";
$wms-workload-clock-8-outline: "\eff1";
$wms-workload-clock-9: "\eff2";
$wms-workload-clock-9-outline: "\eff3";
$wms-workstation: "\eff4";
$wms-workstation-filled: "\eff5";
$wms-wrong-destination: "\eff6";
$wms-zamestnanec: "\eff7";
$wms-zamestnanec-karta: "\eff8";
$wms-zamestnanec-karta-outline: "\eff9";
$wms-zamestnanec-karta-qr: "\effa";
$wms-zamestnanec-karta-qr-outline: "\effb";
$wms-zamestnanec-outline: "\effc";
$wms-zavesny_system: "\effd";
$wms-zavoz-cesta: "\effe";
$wms-zbalit: "\efff";
$wms-zbalit-outline: "\f000";
$wms-zbalit-zabelene: "\f001";
$wms-zbalit-zabelene-outline: "\f002";
$wms-zdvihnut: "\f003";
$wms-zrucnosti: "\f004";
$wms-zrucnosti-outline: "\f005";
$wms-zrucnosti-settings: "\f006";
$wms-zrucnosti-settings-outline: "\f007";

%wms {
  display: inline-block;
  font-family: "wms" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wms {
  @extend %wms;
}

.wms-404::before {
  content: "\ea01";
}
.wms-a1::before {
  content: "\ea02";
}
.wms-a2::before {
  content: "\ea03";
}
.wms-a3::before {
  content: "\ea04";
}
.wms-a4::before {
  content: "\ea05";
}
.wms-a5::before {
  content: "\ea06";
}
.wms-acceleration::before {
  content: "\ea07";
}
.wms-acceleration-outline::before {
  content: "\ea08";
}
.wms-accordion::before {
  content: "\ea09";
}
.wms-accordion-down::before {
  content: "\ea0a";
}
.wms-accordion-up::before {
  content: "\ea0b";
}
.wms-add::before {
  content: "\ea0c";
}
.wms-add-outline::before {
  content: "\ea0d";
}
.wms-add-solo::before {
  content: "\ea0e";
}
.wms-add_klt::before {
  content: "\ea0f";
}
.wms-again::before {
  content: "\ea10";
}
.wms-again2::before {
  content: "\ea11";
}
.wms-alert::before {
  content: "\ea12";
}
.wms-alert-outline::before {
  content: "\ea13";
}
.wms-alert2::before {
  content: "\ea14";
}
.wms-alert3::before {
  content: "\ea15";
}
.wms-alert4::before {
  content: "\ea16";
}
.wms-alert5::before {
  content: "\ea17";
}
.wms-arrow::before {
  content: "\ea18";
}
.wms-arrow-down::before {
  content: "\ea19";
}
.wms-arrow-left::before {
  content: "\ea1a";
}
.wms-arrow-outline::before {
  content: "\ea1b";
}
.wms-arrow-right::before {
  content: "\ea1c";
}
.wms-arrow-up::before {
  content: "\ea1d";
}
.wms-arrow2::before {
  content: "\ea1e";
}
.wms-arrow3::before {
  content: "\ea1f";
}
.wms-arrow4::before {
  content: "\ea20";
}
.wms-arrow5::before {
  content: "\ea21";
}
.wms-arrow6::before {
  content: "\ea22";
}
.wms-arrow7::before {
  content: "\ea23";
}
.wms-article::before {
  content: "\ea24";
}
.wms-article-check::before {
  content: "\ea25";
}
.wms-article-check-outline::before {
  content: "\ea26";
}
.wms-article-count::before {
  content: "\ea27";
}
.wms-article-count-num::before {
  content: "\ea28";
}
.wms-article-dameged::before {
  content: "\ea29";
}
.wms-article-dameged-outline::before {
  content: "\ea2a";
}
.wms-article-detai::before {
  content: "\ea2b";
}
.wms-article-detail-outline::before {
  content: "\ea2c";
}
.wms-article-dirty::before {
  content: "\ea2d";
}
.wms-article-dirty-outline::before {
  content: "\ea2e";
}
.wms-article-etiketa::before {
  content: "\ea2f";
}
.wms-article-info::before {
  content: "\ea30";
}
.wms-article-info-outline::before {
  content: "\ea31";
}
.wms-article-mandatory::before {
  content: "\ea32";
}
.wms-article-mandatory-outline::before {
  content: "\ea33";
}
.wms-article-missing::before {
  content: "\ea34";
}
.wms-article-missing-outline::before {
  content: "\ea35";
}
.wms-article-nalokacii::before {
  content: "\ea36";
}
.wms-article-nalokacii-outline::before {
  content: "\ea37";
}
.wms-article-objednavka-outline::before {
  content: "\ea38";
}
.wms-article-okay::before {
  content: "\ea39";
}
.wms-article-okay-outline::before {
  content: "\ea3a";
}
.wms-article-outline::before {
  content: "\ea3b";
}
.wms-article-outline-etiketa::before {
  content: "\ea3c";
}
.wms-article-packing-remove::before {
  content: "\ea3d";
}
.wms-article-packing-remove-outline::before {
  content: "\ea3e";
}
.wms-article-photo::before {
  content: "\ea3f";
}
.wms-article-photo-outline::before {
  content: "\ea40";
}
.wms-article-pick::before {
  content: "\ea41";
}
.wms-article-pick-outline::before {
  content: "\ea42";
}
.wms-article-ready::before {
  content: "\ea43";
}
.wms-article-ready-outline::before {
  content: "\ea44";
}
.wms-article-scanned::before {
  content: "\ea45";
}
.wms-article-scanned-outline::before {
  content: "\ea46";
}
.wms-article-stop::before {
  content: "\ea47";
}
.wms-article-stop-outline::before {
  content: "\ea48";
}
.wms-article-surplus::before {
  content: "\ea49";
}
.wms-article-surplus-outline::before {
  content: "\ea4a";
}
.wms-article-waiting::before {
  content: "\ea4b";
}
.wms-article-waiting-outline::before {
  content: "\ea4c";
}
.wms-articles-count-filled::before {
  content: "\ea4d";
}
.wms-articles-count-filled-preparing::before {
  content: "\ea4e";
}
.wms-articles-count-filled-ready::before {
  content: "\ea4f";
}
.wms-articles-count-preparing::before {
  content: "\ea50";
}
.wms-articles-count-ready::before {
  content: "\ea51";
}
.wms-articles-packing-remove::before {
  content: "\ea52";
}
.wms-articles-packing-remove-outline::before {
  content: "\ea53";
}
.wms-articles-pick-filled::before {
  content: "\ea54";
}
.wms-articles-pick-outline::before {
  content: "\ea55";
}
.wms-articles-swap::before {
  content: "\ea56";
}
.wms-articles-swap-outline::before {
  content: "\ea57";
}
.wms-articles-waiting-filled::before {
  content: "\ea58";
}
.wms-articles-waiting-outline::before {
  content: "\ea59";
}
.wms-as::before {
  content: "\ea5a";
}
.wms-as-active::before {
  content: "\ea5b";
}
.wms-as-carousel::before {
  content: "\ea5c";
}
.wms-as-conveyor::before {
  content: "\ea5d";
}
.wms-as-outline::before {
  content: "\ea5e";
}
.wms-as-requested_by_matus::before {
  content: "\ea5f";
}
.wms-asign-task::before {
  content: "\ea60";
}
.wms-asign-task-circle::before {
  content: "\ea61";
}
.wms-audit::before {
  content: "\ea62";
}
.wms-audit-add::before {
  content: "\ea63";
}
.wms-audit-add-outline::before {
  content: "\ea64";
}
.wms-audit-check::before {
  content: "\ea65";
}
.wms-audit-check-outline::before {
  content: "\ea66";
}
.wms-audit-diference::before {
  content: "\ea67";
}
.wms-audit-diference-outline::before {
  content: "\ea68";
}
.wms-audit-locked::before {
  content: "\ea69";
}
.wms-audit-locked-outline::before {
  content: "\ea6a";
}
.wms-audit-lokation::before {
  content: "\ea6b";
}
.wms-audit-lokation-outline::before {
  content: "\ea6c";
}
.wms-audit-minus::before {
  content: "\ea6d";
}
.wms-audit-minus-outline::before {
  content: "\ea6e";
}
.wms-audit-okay::before {
  content: "\ea6f";
}
.wms-audit-okay-outline::before {
  content: "\ea70";
}
.wms-audit-outline::before {
  content: "\ea71";
}
.wms-audit-problem::before {
  content: "\ea72";
}
.wms-audit-problem-outline::before {
  content: "\ea73";
}
.wms-audit-submit-changes::before {
  content: "\ea74";
}
.wms-audit-submit-changes-outline-1::before {
  content: "\ea75";
}
.wms-audit-wait::before {
  content: "\ea76";
}
.wms-audit-wait-outline::before {
  content: "\ea77";
}
.wms-audit-wrong::before {
  content: "\ea78";
}
.wms-audit-wrong-outline::before {
  content: "\ea79";
}
.wms-autostore::before {
  content: "\ea7a";
}
.wms-autostore-pickovanie::before {
  content: "\ea7b";
}
.wms-autostore-pickovanie-outline::before {
  content: "\ea7c";
}
.wms-autostore_expedicia::before {
  content: "\ea7d";
}
.wms-autostore_prijem::before {
  content: "\ea7e";
}
.wms-autostore_prijem_vbin::before {
  content: "\ea7f";
}
.wms-avizovany::before {
  content: "\ea80";
}
.wms-b2b_default::before {
  content: "\ea81";
}
.wms-b2b_default-outline::before {
  content: "\ea82";
}
.wms-b2b_gift::before {
  content: "\ea83";
}
.wms-b2b_gift-outline::before {
  content: "\ea84";
}
.wms-b2b_move::before {
  content: "\ea85";
}
.wms-b2b_move-outline::before {
  content: "\ea86";
}
.wms-b2b_movein::before {
  content: "\ea87";
}
.wms-b2b_movein-outline::before {
  content: "\ea88";
}
.wms-b2b_ready::before {
  content: "\ea89";
}
.wms-b2b_ready-outline::before {
  content: "\ea8a";
}
.wms-b2b_setup::before {
  content: "\ea8b";
}
.wms-b2b_setup-outline::before {
  content: "\ea8c";
}
.wms-b2b_skladnik::before {
  content: "\ea8d";
}
.wms-b2b_skladnik-outline::before {
  content: "\ea8e";
}
.wms-b2b_stop::before {
  content: "\ea8f";
}
.wms-b2b_stop-outline::before {
  content: "\ea90";
}
.wms-b2b_triedenie::before {
  content: "\ea91";
}
.wms-b2b_triedenie-outline::before {
  content: "\ea92";
}
.wms-b2b_visible::before {
  content: "\ea93";
}
.wms-b2b_visible-outline::before {
  content: "\ea94";
}
.wms-b2b_wait::before {
  content: "\ea95";
}
.wms-b2b_wait-outline::before {
  content: "\ea96";
}
.wms-b2c-material::before {
  content: "\ea97";
}
.wms-b2c_default::before {
  content: "\ea98";
}
.wms-b2c_default-outline::before {
  content: "\ea99";
}
.wms-b2c_gift::before {
  content: "\ea9a";
}
.wms-b2c_gift-outline::before {
  content: "\ea9b";
}
.wms-b2c_move::before {
  content: "\ea9c";
}
.wms-b2c_move-outline::before {
  content: "\ea9d";
}
.wms-b2c_movein::before {
  content: "\ea9e";
}
.wms-b2c_movein-outline::before {
  content: "\ea9f";
}
.wms-b2c_ready::before {
  content: "\eaa0";
}
.wms-b2c_ready-outline::before {
  content: "\eaa1";
}
.wms-b2c_skladnik::before {
  content: "\eaa2";
}
.wms-b2c_skladnik-outline::before {
  content: "\eaa3";
}
.wms-b2c_stop::before {
  content: "\eaa4";
}
.wms-b2c_stop-outline::before {
  content: "\eaa5";
}
.wms-b2c_triedenie::before {
  content: "\eaa6";
}
.wms-b2c_triedenie-outline::before {
  content: "\eaa7";
}
.wms-b2c_visible::before {
  content: "\eaa8";
}
.wms-b2c_visible-outline::before {
  content: "\eaa9";
}
.wms-b2c_wait::before {
  content: "\eaaa";
}
.wms-b2c_wait-outline::before {
  content: "\eaab";
}
.wms-balenie::before {
  content: "\eaac";
}
.wms-balenie-artikel::before {
  content: "\eaad";
}
.wms-balenie-artikel-filled::before {
  content: "\eaae";
}
.wms-balenie-in::before {
  content: "\eaaf";
}
.wms-balenie-move::before {
  content: "\eab0";
}
.wms-balenie-ready::before {
  content: "\eab1";
}
.wms-balenie-stop::before {
  content: "\eab2";
}
.wms-balenie-wait::before {
  content: "\eab3";
}
.wms-balenie_kompletizacia::before {
  content: "\eab4";
}
.wms-bin-closed::before {
  content: "\eab5";
}
.wms-bin-empty::before {
  content: "\eab6";
}
.wms-bin-error::before {
  content: "\eab7";
}
.wms-bin-half-20::before {
  content: "\eab8";
}
.wms-bin-half-22::before {
  content: "\eab9";
}
.wms-bin-half-blue::before {
  content: "\eaba";
}
.wms-bin-half-closed::before {
  content: "\eabb";
}
.wms-bin-half-open::before {
  content: "\eabc";
}
.wms-bin-half-port::before {
  content: "\eabd";
}
.wms-bin-half-yellow::before {
  content: "\eabe";
}
.wms-bin-history::before {
  content: "\eabf";
}
.wms-bin-m-half-blue::before {
  content: "\eac0";
}
.wms-bin-m-half-yellow::before {
  content: "\eac1";
}
.wms-bin-m-quarter-blue::before {
  content: "\eac2";
}
.wms-bin-m-quarter-green::before {
  content: "\eac3";
}
.wms-bin-m-quarter-red::before {
  content: "\eac4";
}
.wms-bin-m-quarter-yellow::before {
  content: "\eac5";
}
.wms-bin-m-whole::before {
  content: "\eac6";
}
.wms-bin-open::before {
  content: "\eac7";
}
.wms-bin-port::before {
  content: "\eac8";
}
.wms-bin-problem::before {
  content: "\eac9";
}
.wms-bin-quarter-40::before {
  content: "\eaca";
}
.wms-bin-quarter-41::before {
  content: "\eacb";
}
.wms-bin-quarter-42::before {
  content: "\eacc";
}
.wms-bin-quarter-43::before {
  content: "\eacd";
}
.wms-bin-quarter-44::before {
  content: "\eace";
}
.wms-bin-quarter-blue::before {
  content: "\eacf";
}
.wms-bin-quarter-closed::before {
  content: "\ead0";
}
.wms-bin-quarter-green::before {
  content: "\ead1";
}
.wms-bin-quarter-open::before {
  content: "\ead2";
}
.wms-bin-quarter-port::before {
  content: "\ead3";
}
.wms-bin-quarter-red::before {
  content: "\ead4";
}
.wms-bin-quarter-yellow::before {
  content: "\ead5";
}
.wms-bin-settings::before {
  content: "\ead6";
}
.wms-bin-whole::before {
  content: "\ead7";
}
.wms-bin-whole-10::before {
  content: "\ead8";
}
.wms-bin-whole-closed::before {
  content: "\ead9";
}
.wms-bin-whole-open::before {
  content: "\eada";
}
.wms-bin-whole-port::before {
  content: "\eadb";
}
.wms-bin_add-outline::before {
  content: "\eadc";
}
.wms-bin_artikle-outline::before {
  content: "\eadd";
}
.wms-bin_checked::before {
  content: "\eade";
}
.wms-bin_empty-outline::before {
  content: "\eadf";
}
.wms-bin_full::before {
  content: "\eae0";
}
.wms-bin_full-problem::before {
  content: "\eae1";
}
.wms-bin_half::before {
  content: "\eae2";
}
.wms-bin_half-problem::before {
  content: "\eae3";
}
.wms-bin_hold-outline::before {
  content: "\eae4";
}
.wms-bin_inventory-outline::before {
  content: "\eae5";
}
.wms-bin_okay-outline::before {
  content: "\eae6";
}
.wms-bin_outline::before {
  content: "\eae7";
}
.wms-bin_quarter::before {
  content: "\eae8";
}
.wms-bin_quarter-problem::before {
  content: "\eae9";
}
.wms-bin_remove-outline::before {
  content: "\eaea";
}
.wms-bin_striketrought-outline::before {
  content: "\eaeb";
}
.wms-bin_uncategorised::before {
  content: "\eaec";
}
.wms-bin_unchecked::before {
  content: "\eaed";
}
.wms-bin_waiting-outline::before {
  content: "\eaee";
}
.wms-box::before {
  content: "\eaef";
}
.wms-box-add::before {
  content: "\eaf0";
}
.wms-box-crazydays::before {
  content: "\eaf1";
}
.wms-box-down::before {
  content: "\eaf2";
}
.wms-box-info::before {
  content: "\eaf3";
}
.wms-box-location::before {
  content: "\eaf4";
}
.wms-box-okay::before {
  content: "\eaf5";
}
.wms-box-scan::before {
  content: "\eaf6";
}
.wms-box-stop::before {
  content: "\eaf7";
}
.wms-box-wait::before {
  content: "\eaf8";
}
.wms-box-warning::before {
  content: "\eaf9";
}
.wms-box-wishlist::before {
  content: "\eafa";
}
.wms-box_open::before {
  content: "\eafb";
}
.wms-box_open-add::before {
  content: "\eafc";
}
.wms-box_open-crazydays::before {
  content: "\eafd";
}
.wms-box_open-down::before {
  content: "\eafe";
}
.wms-box_open-info::before {
  content: "\eaff";
}
.wms-box_open-location::before {
  content: "\eb00";
}
.wms-box_open-okay::before {
  content: "\eb01";
}
.wms-box_open-scan::before {
  content: "\eb02";
}
.wms-box_open-stop::before {
  content: "\eb03";
}
.wms-box_open-wait::before {
  content: "\eb04";
}
.wms-box_open-warning::before {
  content: "\eb05";
}
.wms-box_open-wishlist::before {
  content: "\eb06";
}
.wms-browse-history::before {
  content: "\eb07";
}
.wms-browse-history-outline::before {
  content: "\eb08";
}
.wms-cakanie-hodinky::before {
  content: "\eb09";
}
.wms-camera::before {
  content: "\eb0a";
}
.wms-carouselport-autostore::before {
  content: "\eb0b";
}
.wms-carouselport-autostore-closed::before {
  content: "\eb0c";
}
.wms-carouselport-autostore-open::before {
  content: "\eb0d";
}
.wms-carouselport-autostore-open-outline::before {
  content: "\eb0e";
}
.wms-carouselport-autostore-outline::before {
  content: "\eb0f";
}
.wms-carouselport-autostore-outline-closed::before {
  content: "\eb10";
}
.wms-carton::before {
  content: "\eb11";
}
.wms-carton-claims-damaged::before {
  content: "\eb12";
}
.wms-carton-claims-dirty::before {
  content: "\eb13";
}
.wms-carton-count::before {
  content: "\eb14";
}
.wms-carton-damaged::before {
  content: "\eb15";
}
.wms-carton-empty::before {
  content: "\eb16";
}
.wms-carton-info::before {
  content: "\eb17";
}
.wms-carton-missing::before {
  content: "\eb18";
}
.wms-carton-objednavka::before {
  content: "\eb19";
}
.wms-carton-okay::before {
  content: "\eb1a";
}
.wms-carton-prijem::before {
  content: "\eb1b";
}
.wms-carton-scan::before {
  content: "\eb1c";
}
.wms-carton-surplus::before {
  content: "\eb1d";
}
.wms-cas::before {
  content: "\eb1e";
}
.wms-center::before {
  content: "\eb1f";
}
.wms-center-city::before {
  content: "\eb20";
}
.wms-center-city-outline::before {
  content: "\eb21";
}
.wms-center-outline::before {
  content: "\eb22";
}
.wms-change::before {
  content: "\eb23";
}
.wms-checked::before {
  content: "\eb24";
}
.wms-checked-outline::before {
  content: "\eb25";
}
.wms-chevron-arr-down::before {
  content: "\eb26";
}
.wms-chevron-arr-left::before {
  content: "\eb27";
}
.wms-chevron-arr-right::before {
  content: "\eb28";
}
.wms-chevron-arr-up::before {
  content: "\eb29";
}
.wms-chevron-arrow-down::before {
  content: "\eb2a";
}
.wms-chevron-arrow-left::before {
  content: "\eb2b";
}
.wms-chevron-arrow-right::before {
  content: "\eb2c";
}
.wms-chevron-arrow-up::before {
  content: "\eb2d";
}
.wms-chevron-down::before {
  content: "\eb2e";
}
.wms-chevron-left::before {
  content: "\eb2f";
}
.wms-chevron-right::before {
  content: "\eb30";
}
.wms-chevron-time::before {
  content: "\eb31";
}
.wms-chevron-time-outline::before {
  content: "\eb32";
}
.wms-chevron-up::before {
  content: "\eb33";
}
.wms-choice::before {
  content: "\eb34";
}
.wms-choice-outline::before {
  content: "\eb35";
}
.wms-clock::before {
  content: "\eb36";
}
.wms-clock-outline::before {
  content: "\eb37";
}
.wms-close::before {
  content: "\eb38";
}
.wms-collapse-transporthub-double::before {
  content: "\eb39";
}
.wms-collapse-transporthub-double-outline::before {
  content: "\eb3a";
}
.wms-collapse-transporthub-single::before {
  content: "\eb3b";
}
.wms-collapse-transporthub-single-outline::before {
  content: "\eb3c";
}
.wms-color-palette::before {
  content: "\eb3d";
}
.wms-color-palette-outline::before {
  content: "\eb3e";
}
.wms-crazy-days::before {
  content: "\eb3f";
}
.wms-crazy-days-bomb::before {
  content: "\eb40";
}
.wms-crossroad-accounting-groups::before {
  content: "\eb41";
}
.wms-crossroad-analytics-graph1::before {
  content: "\eb42";
}
.wms-crossroad-analytics-graph2::before {
  content: "\eb43";
}
.wms-crossroad-analytics-graph3::before {
  content: "\eb44";
}
.wms-crossroad-analytics-graph4::before {
  content: "\eb45";
}
.wms-crossroad-aviza-global::before {
  content: "\eb46";
}
.wms-crossroad-brand::before {
  content: "\eb47";
}
.wms-crossroad-buildings::before {
  content: "\eb48";
}
.wms-crossroad-clothing-sizes::before {
  content: "\eb49";
}
.wms-crossroad-colours::before {
  content: "\eb4a";
}
.wms-crossroad-colours-logistic::before {
  content: "\eb4b";
}
.wms-crossroad-documents::before {
  content: "\eb4c";
}
.wms-crossroad-genders::before {
  content: "\eb4d";
}
.wms-crossroad-goods-movement::before {
  content: "\eb4e";
}
.wms-crossroad-group4::before {
  content: "\eb4f";
}
.wms-crossroad-internal-transfer::before {
  content: "\eb50";
}
.wms-crossroad-inventory::before {
  content: "\eb51";
}
.wms-crossroad-inventory-diference::before {
  content: "\eb52";
}
.wms-crossroad-inventory-global::before {
  content: "\eb53";
}
.wms-crossroad-inventory-group::before {
  content: "\eb54";
}
.wms-crossroad-inventory-position::before {
  content: "\eb55";
}
.wms-crossroad-labels-krt::before {
  content: "\eb56";
}
.wms-crossroad-labels-pallete::before {
  content: "\eb57";
}
.wms-crossroad-logs::before {
  content: "\eb58";
}
.wms-crossroad-maingroup::before {
  content: "\eb59";
}
.wms-crossroad-measure-units::before {
  content: "\eb5a";
}
.wms-crossroad-module::before {
  content: "\eb5b";
}
.wms-crossroad-order-group::before {
  content: "\eb5c";
}
.wms-crossroad-packign-messages::before {
  content: "\eb5d";
}
.wms-crossroad-porter-mode::before {
  content: "\eb5e";
}
.wms-crossroad-porter-reservation::before {
  content: "\eb5f";
}
.wms-crossroad-prijemky-global::before {
  content: "\eb60";
}
.wms-crossroad-printedlabels::before {
  content: "\eb61";
}
.wms-crossroad-product::before {
  content: "\eb62";
}
.wms-crossroad-reklamacia-presun::before {
  content: "\eb63";
}
.wms-crossroad-seasons::before {
  content: "\eb64";
}
.wms-crossroad-subject-adress::before {
  content: "\eb65";
}
.wms-crossroad-subjects::before {
  content: "\eb66";
}
.wms-crossroad-task::before {
  content: "\eb67";
}
.wms-crossroad-temperature::before {
  content: "\eb68";
}
.wms-crossroad-tickets::before {
  content: "\eb69";
}
.wms-crossroad-trademark::before {
  content: "\eb6a";
}
.wms-crossroad-vydajky-global::before {
  content: "\eb6b";
}
.wms-crossroad-vydajky-global-1::before {
  content: "\eb6c";
}
.wms-crossroad-workstation-logs::before {
  content: "\eb6d";
}
.wms-crossroad-zvozove-global::before {
  content: "\eb6e";
}
.wms-dameged-good2::before {
  content: "\eb6f";
}
.wms-dameged-good3::before {
  content: "\eb70";
}
.wms-dameged-goods::before {
  content: "\eb71";
}
.wms-dash-b2b::before {
  content: "\eb72";
}
.wms-dash-b2c1::before {
  content: "\eb73";
}
.wms-dash-b2cm::before {
  content: "\eb74";
}
.wms-dash-boxpackage::before {
  content: "\eb75";
}
.wms-dash-boxpackage-outline::before {
  content: "\eb76";
}
.wms-dash-cons::before {
  content: "\eb77";
}
.wms-dash-count::before {
  content: "\eb78";
}
.wms-dash-high::before {
  content: "\eb79";
}
.wms-dash-inv::before {
  content: "\eb7a";
}
.wms-dash-navyse::before {
  content: "\eb7b";
}
.wms-dash-navyse-outline::before {
  content: "\eb7c";
}
.wms-dash-poskodene::before {
  content: "\eb7d";
}
.wms-dash-poskodene-outline::before {
  content: "\eb7e";
}
.wms-dash-recinv::before {
  content: "\eb7f";
}
.wms-dash-ret::before {
  content: "\eb80";
}
.wms-dash-spinave::before {
  content: "\eb81";
}
.wms-dash-spinave-outline::before {
  content: "\eb82";
}
.wms-dash-svc::before {
  content: "\eb83";
}
.wms-dashboard::before {
  content: "\eb84";
}
.wms-dashboard-1::before {
  content: "\eb85";
}
.wms-dashboard-home::before {
  content: "\eb86";
}
.wms-dashboard-return_home::before {
  content: "\eb87";
}
.wms-dashboard2::before {
  content: "\eb88";
}
.wms-dashboard_ine::before {
  content: "\eb89";
}
.wms-dashboard_ine-active::before {
  content: "\eb8a";
}
.wms-dashboard_konsolidacia::before {
  content: "\eb8b";
}
.wms-dashboard_konsolidacia-active::before {
  content: "\eb8c";
}
.wms-dashboard_prijem::before {
  content: "\eb8d";
}
.wms-dashboard_prijem-active::before {
  content: "\eb8e";
}
.wms-dashboard_vydaj::before {
  content: "\eb8f";
}
.wms-dashboard_vydaj-active::before {
  content: "\eb90";
}
.wms-datum::before {
  content: "\eb91";
}
.wms-delete::before {
  content: "\eb92";
}
.wms-delivery-cart::before {
  content: "\eb93";
}
.wms-delivery-cart-add::before {
  content: "\eb94";
}
.wms-delivery-cart-add-outline::before {
  content: "\eb95";
}
.wms-delivery-cart-info::before {
  content: "\eb96";
}
.wms-delivery-cart-info-outline::before {
  content: "\eb97";
}
.wms-delivery-cart-outline::before {
  content: "\eb98";
}
.wms-delivery-cart-remove::before {
  content: "\eb99";
}
.wms-delivery-cart-remove-outline::before {
  content: "\eb9a";
}
.wms-delivery-cart-skladnik::before {
  content: "\eb9b";
}
.wms-delivery-cart-skladnik-outline::before {
  content: "\eb9c";
}
.wms-delivery-cart-stop::before {
  content: "\eb9d";
}
.wms-delivery-cart-stop-outline::before {
  content: "\eb9e";
}
.wms-delivery-cart-transfer::before {
  content: "\eb9f";
}
.wms-delivery-cart-transfer-outline::before {
  content: "\eba0";
}
.wms-delivery-cart-warning::before {
  content: "\eba1";
}
.wms-delivery-cart-warning-outline::before {
  content: "\eba2";
}
.wms-depth::before {
  content: "\eba3";
}
.wms-depth2::before {
  content: "\eba4";
}
.wms-deselectall::before {
  content: "\eba5";
}
.wms-device-pda::before {
  content: "\eba6";
}
.wms-device-zebra-printers::before {
  content: "\eba7";
}
.wms-diagram-arrow-outline::before {
  content: "\eba8";
}
.wms-diagram-line-outline::before {
  content: "\eba9";
}
.wms-diagram-point::before {
  content: "\ebaa";
}
.wms-diagram-point-danger::before {
  content: "\ebab";
}
.wms-diagram-point-danger-outline::before {
  content: "\ebac";
}
.wms-diagram-point-outline::before {
  content: "\ebad";
}
.wms-diagram-point-time-outline::before {
  content: "\ebae";
}
.wms-diagram-point-time-outline-1::before {
  content: "\ebaf";
}
.wms-diagram-point-warning::before {
  content: "\ebb0";
}
.wms-diagram-point-warning-outline::before {
  content: "\ebb1";
}
.wms-diagram-time-danger::before {
  content: "\ebb2";
}
.wms-diagram-time-danger-1::before {
  content: "\ebb3";
}
.wms-diference::before {
  content: "\ebb4";
}
.wms-diference-circle::before {
  content: "\ebb5";
}
.wms-diference-okay::before {
  content: "\ebb6";
}
.wms-diference-outline::before {
  content: "\ebb7";
}
.wms-diference-position::before {
  content: "\ebb8";
}
.wms-digitalsignature::before {
  content: "\ebb9";
}
.wms-digitalsignature-outline::before {
  content: "\ebba";
}
.wms-digitalsignature2::before {
  content: "\ebbb";
}
.wms-digitalsignature3::before {
  content: "\ebbc";
}
.wms-dispatch::before {
  content: "\ebbd";
}
.wms-dispatch2::before {
  content: "\ebbe";
}
.wms-document::before {
  content: "\ebbf";
}
.wms-document-missing::before {
  content: "\ebc0";
}
.wms-document-missing-outline::before {
  content: "\ebc1";
}
.wms-document-okay-outline::before {
  content: "\ebc2";
}
.wms-document-outline::before {
  content: "\ebc3";
}
.wms-document-pdf::before {
  content: "\ebc4";
}
.wms-documents-outline::before {
  content: "\ebc5";
}
.wms-dodavka::before {
  content: "\ebc6";
}
.wms-done-task::before {
  content: "\ebc7";
}
.wms-done-task-circle::before {
  content: "\ebc8";
}
.wms-doprava-dpd::before {
  content: "\ebc9";
}
.wms-doprava-interna::before {
  content: "\ebca";
}
.wms-doprava-neurcena::before {
  content: "\ebcb";
}
.wms-doprava-odlozena-paleta::before {
  content: "\ebcc";
}
.wms-doprava-packeta::before {
  content: "\ebcd";
}
.wms-doprava-ppl::before {
  content: "\ebce";
}
.wms-dopravca-gls::before {
  content: "\ebcf";
}
.wms-dopravca-gls-arrow::before {
  content: "\ebd0";
}
.wms-dopravca-gls-circle::before {
  content: "\ebd1";
}
.wms-dopravca-neznamy::before {
  content: "\ebd2";
}
.wms-dopravca-neznamy-outline::before {
  content: "\ebd3";
}
.wms-dopravca-unknow::before {
  content: "\ebd4";
}
.wms-dopravnik_segment::before {
  content: "\ebd5";
}
.wms-drag-gesture::before {
  content: "\ebd6";
}
.wms-drag-horizontal::before {
  content: "\ebd7";
}
.wms-drag-left::before {
  content: "\ebd8";
}
.wms-drag-left-outline::before {
  content: "\ebd9";
}
.wms-drag-right::before {
  content: "\ebda";
}
.wms-drag-right-outline::before {
  content: "\ebdb";
}
.wms-drag-vertical::before {
  content: "\ebdc";
}
.wms-ean::before {
  content: "\ebdd";
}
.wms-ean2::before {
  content: "\ebde";
}
.wms-edit::before {
  content: "\ebdf";
}
.wms-edit-outline::before {
  content: "\ebe0";
}
.wms-empty::before {
  content: "\ebe1";
}
.wms-empty-1::before {
  content: "\ebe2";
}
.wms-empty-square::before {
  content: "\ebe3";
}
.wms-enter::before {
  content: "\ebe4";
}
.wms-enter2::before {
  content: "\ebe5";
}
.wms-eso::before {
  content: "\ebe6";
}
.wms-eso9::before {
  content: "\ebe7";
}
.wms-etikety-count::before {
  content: "\ebe8";
}
.wms-etikety-count-num::before {
  content: "\ebe9";
}
.wms-etikety-count-num-outline::before {
  content: "\ebea";
}
.wms-etikety-count-outline::before {
  content: "\ebeb";
}
.wms-expand-transporthub-double::before {
  content: "\ebec";
}
.wms-expand-transporthub-double-outline::before {
  content: "\ebed";
}
.wms-expand-transporthub-single::before {
  content: "\ebee";
}
.wms-expand-transporthub-single-outline::before {
  content: "\ebef";
}
.wms-expedicia::before {
  content: "\ebf0";
}
.wms-export::before {
  content: "\ebf1";
}
.wms-filter::before {
  content: "\ebf2";
}
.wms-forcast::before {
  content: "\ebf3";
}
.wms-foteni-vazenie::before {
  content: "\ebf4";
}
.wms-fotenie::before {
  content: "\ebf5";
}
.wms-fotografie::before {
  content: "\ebf6";
}
.wms-fotografie-outline::before {
  content: "\ebf7";
}
.wms-free::before {
  content: "\ebf8";
}
.wms-gear-setup::before {
  content: "\ebf9";
}
.wms-gear-setup-outline::before {
  content: "\ebfa";
}
.wms-gift::before {
  content: "\ebfb";
}
.wms-gift-outline::before {
  content: "\ebfc";
}
.wms-goods::before {
  content: "\ebfd";
}
.wms-goods-full::before {
  content: "\ebfe";
}
.wms-goods-id::before {
  content: "\ebff";
}
.wms-goods-id-outline::before {
  content: "\ec00";
}
.wms-goods-none::before {
  content: "\ec01";
}
.wms-height::before {
  content: "\ec02";
}
.wms-height2::before {
  content: "\ec03";
}
.wms-help::before {
  content: "\ec04";
}
.wms-help-outline::before {
  content: "\ec05";
}
.wms-hidden::before {
  content: "\ec06";
}
.wms-hidden-outline::before {
  content: "\ec07";
}
.wms-highpriority::before {
  content: "\ec08";
}
.wms-in-progress::before {
  content: "\ec09";
}
.wms-info::before {
  content: "\ec0a";
}
.wms-info-filled::before {
  content: "\ec0b";
}
.wms-input_::before {
  content: "\ec0c";
}
.wms-input_article::before {
  content: "\ec0d";
}
.wms-input_artikel::before {
  content: "\ec0e";
}
.wms-input_camera::before {
  content: "\ec0f";
}
.wms-input_carton::before {
  content: "\ec10";
}
.wms-input_carton2::before {
  content: "\ec11";
}
.wms-input_cm::before {
  content: "\ec12";
}
.wms-input_cm-height::before {
  content: "\ec13";
}
.wms-input_cm-height-square::before {
  content: "\ec14";
}
.wms-input_cm-lenght::before {
  content: "\ec15";
}
.wms-input_cm-lenght-square::before {
  content: "\ec16";
}
.wms-input_cm-square::before {
  content: "\ec17";
}
.wms-input_cm-width::before {
  content: "\ec18";
}
.wms-input_cm-width-square::before {
  content: "\ec19";
}
.wms-input_ean::before {
  content: "\ec1a";
}
.wms-input_g::before {
  content: "\ec1b";
}
.wms-input_g-square::before {
  content: "\ec1c";
}
.wms-input_image::before {
  content: "\ec1d";
}
.wms-input_kg::before {
  content: "\ec1e";
}
.wms-input_kg-square::before {
  content: "\ec1f";
}
.wms-input_klt::before {
  content: "\ec20";
}
.wms-input_num::before {
  content: "\ec21";
}
.wms-input_paleta::before {
  content: "\ec22";
}
.wms-input_pieces::before {
  content: "\ec23";
}
.wms-input_pieces-square::before {
  content: "\ec24";
}
.wms-input_qr::before {
  content: "\ec25";
}
.wms-input_stender::before {
  content: "\ec26";
}
.wms-input_type::before {
  content: "\ec27";
}
.wms-input_umiestnenie::before {
  content: "\ec28";
}
.wms-input_weight::before {
  content: "\ec29";
}
.wms-input_weight-g::before {
  content: "\ec2a";
}
.wms-input_weight-g2::before {
  content: "\ec2b";
}
.wms-input_weight-kg::before {
  content: "\ec2c";
}
.wms-input_weight-kg2::before {
  content: "\ec2d";
}
.wms-input_weight2::before {
  content: "\ec2e";
}
.wms-internadoprava::before {
  content: "\ec2f";
}
.wms-inventura::before {
  content: "\ec30";
}
.wms-inventura-outline::before {
  content: "\ec31";
}
.wms-kamera_sklad_wifi::before {
  content: "\ec32";
}
.wms-kamera_sklad_zebra::before {
  content: "\ec33";
}
.wms-kamion::before {
  content: "\ec34";
}
.wms-karantena::before {
  content: "\ec35";
}
.wms-karantena-outline::before {
  content: "\ec36";
}
.wms-klec::before {
  content: "\ec37";
}
.wms-klec-1::before {
  content: "\ec38";
}
.wms-klec-1-mandatory::before {
  content: "\ec39";
}
.wms-klec-2::before {
  content: "\ec3a";
}
.wms-klec-2-mandatory::before {
  content: "\ec3b";
}
.wms-klec-3::before {
  content: "\ec3c";
}
.wms-klec-3-mandatory::before {
  content: "\ec3d";
}
.wms-klec-4::before {
  content: "\ec3e";
}
.wms-klec-4-mandatory::before {
  content: "\ec3f";
}
.wms-klec-5::before {
  content: "\ec40";
}
.wms-klec-5-mandatory::before {
  content: "\ec41";
}
.wms-klec-ouline::before {
  content: "\ec42";
}
.wms-klt::before {
  content: "\ec43";
}
.wms-klt-1::before {
  content: "\ec44";
}
.wms-klt-add::before {
  content: "\ec45";
}
.wms-klt-artikle::before {
  content: "\ec46";
}
.wms-klt-artikle-filled::before {
  content: "\ec47";
}
.wms-klt-artikle-filled-outline::before {
  content: "\ec48";
}
.wms-klt-artikle-outline::before {
  content: "\ec49";
}
.wms-klt-done::before {
  content: "\ec4a";
}
.wms-klt-empty::before {
  content: "\ec4b";
}
.wms-klt-empty-1::before {
  content: "\ec4c";
}
.wms-klt-mandatory::before {
  content: "\ec4d";
}
.wms-klt-mandatory-outline::before {
  content: "\ec4e";
}
.wms-klt-outline::before {
  content: "\ec4f";
}
.wms-klt-packing-ready::before {
  content: "\ec50";
}
.wms-klt-packing-ready-mandatory::before {
  content: "\ec51";
}
.wms-klt-packing-ready-mandatory-outline::before {
  content: "\ec52";
}
.wms-klt-packing-ready-outline::before {
  content: "\ec53";
}
.wms-klt-packing-waiting::before {
  content: "\ec54";
}
.wms-klt-packing-waiting-outline::before {
  content: "\ec55";
}
.wms-klt-put::before {
  content: "\ec56";
}
.wms-klt-putin::before {
  content: "\ec57";
}
.wms-klt-remove::before {
  content: "\ec58";
}
.wms-klt-scan::before {
  content: "\ec59";
}
.wms-klt-spustit::before {
  content: "\ec5a";
}
.wms-klt-transfer::before {
  content: "\ec5b";
}
.wms-klt-zdvihnut::before {
  content: "\ec5c";
}
.wms-konsolidacia::before {
  content: "\ec5d";
}
.wms-laravel-pulse::before {
  content: "\ec5e";
}
.wms-ldopravnik_segment-outline::before {
  content: "\ec5f";
}
.wms-logistoc-nophoto::before {
  content: "\ec60";
}
.wms-logistoc-nophoto-outline::before {
  content: "\ec61";
}
.wms-logistoc-photo::before {
  content: "\ec62";
}
.wms-logistoc-photo-outline::before {
  content: "\ec63";
}
.wms-logo-dpd::before {
  content: "\ec64";
}
.wms-logo-packeta::before {
  content: "\ec65";
}
.wms-logo-ppl::before {
  content: "\ec66";
}
.wms-logo-zasilkovna::before {
  content: "\ec67";
}
.wms-logotype::before {
  content: "\ec68";
}
.wms-logout-user::before {
  content: "\ec69";
}
.wms-lokacia-autostore::before {
  content: "\ec6a";
}
.wms-lokacia-paletovy::before {
  content: "\ec6b";
}
.wms-magic_wand::before {
  content: "\ec6c";
}
.wms-material::before {
  content: "\ec6d";
}
.wms-material-label::before {
  content: "\ec6e";
}
.wms-material-label-outline::before {
  content: "\ec6f";
}
.wms-material-mandatory::before {
  content: "\ec70";
}
.wms-material-mandatory-outline::before {
  content: "\ec71";
}
.wms-material-outline::before {
  content: "\ec72";
}
.wms-matus_markusek_logo_v1::before {
  content: "\ec73";
}
.wms-menu_option::before {
  content: "\ec74";
}
.wms-menu_option-desktop::before {
  content: "\ec75";
}
.wms-menu_option-logout::before {
  content: "\ec76";
}
.wms-menu_option-tablet::before {
  content: "\ec77";
}
.wms-menu_option2::before {
  content: "\ec78";
}
.wms-menu_option3::before {
  content: "\ec79";
}
.wms-menu_option4::before {
  content: "\ec7a";
}
.wms-meranie::before {
  content: "\ec7b";
}
.wms-minus-solo::before {
  content: "\ec7c";
}
.wms-mn::before {
  content: "\ec7d";
}
.wms-modules::before {
  content: "\ec7e";
}
.wms-monitor-autostore::before {
  content: "\ec7f";
}
.wms-monitor-jednopolozkove::before {
  content: "\ec80";
}
.wms-monitor-paletovysklad::before {
  content: "\ec81";
}
.wms-monitor-viacpolozkove::before {
  content: "\ec82";
}
.wms-moon::before {
  content: "\ec83";
}
.wms-naceste::before {
  content: "\ec84";
}
.wms-nadriadeny::before {
  content: "\ec85";
}
.wms-nadriadeny-outline::before {
  content: "\ec86";
}
.wms-nahlad::before {
  content: "\ec87";
}
.wms-nakladka-cesta::before {
  content: "\ec88";
}
.wms-name1::before {
  content: "\ec89";
}
.wms-name1-outline::before {
  content: "\ec8a";
}
.wms-neavizovany::before {
  content: "\ec8b";
}
.wms-new-edit-text::before {
  content: "\ec8c";
}
.wms-new-edit-text-outline::before {
  content: "\ec8d";
}
.wms-no-photo::before {
  content: "\ec8e";
}
.wms-notification-seen::before {
  content: "\ec8f";
}
.wms-notification-unseen::before {
  content: "\ec90";
}
.wms-notify::before {
  content: "\ec91";
}
.wms-notify-outline::before {
  content: "\ec92";
}
.wms-objem::before {
  content: "\ec93";
}
.wms-objem-liter::before {
  content: "\ec94";
}
.wms-objem-outline::before {
  content: "\ec95";
}
.wms-odhlasit::before {
  content: "\ec96";
}
.wms-okay::before {
  content: "\ec97";
}
.wms-other::before {
  content: "\ec98";
}
.wms-packing-circle_dis-close_b2c::before {
  content: "\ec99";
}
.wms-packing-circle_dis-hold_b2b::before {
  content: "\ec9a";
}
.wms-packing-circle_dis_b2b::before {
  content: "\ec9b";
}
.wms-packing-circle_dis_b2b-add::before {
  content: "\ec9c";
}
.wms-packing-circle_dis_b2b-add-outline::before {
  content: "\ec9d";
}
.wms-packing-circle_dis_b2b-close::before {
  content: "\ec9e";
}
.wms-packing-circle_dis_b2b-close-outline::before {
  content: "\ec9f";
}
.wms-packing-circle_dis_b2b-danger::before {
  content: "\eca0";
}
.wms-packing-circle_dis_b2b-danger-outline::before {
  content: "\eca1";
}
.wms-packing-circle_dis_b2b-down::before {
  content: "\eca2";
}
.wms-packing-circle_dis_b2b-down-outline::before {
  content: "\eca3";
}
.wms-packing-circle_dis_b2b-down2::before {
  content: "\eca4";
}
.wms-packing-circle_dis_b2b-down2-outline::before {
  content: "\eca5";
}
.wms-packing-circle_dis_b2b-down3::before {
  content: "\eca6";
}
.wms-packing-circle_dis_b2b-down3-outline::before {
  content: "\eca7";
}
.wms-packing-circle_dis_b2b-hold-outline::before {
  content: "\eca8";
}
.wms-packing-circle_dis_b2b-info::before {
  content: "\eca9";
}
.wms-packing-circle_dis_b2b-info-outline::before {
  content: "\ecaa";
}
.wms-packing-circle_dis_b2b-move::before {
  content: "\ecab";
}
.wms-packing-circle_dis_b2b-move-outline::before {
  content: "\ecac";
}
.wms-packing-circle_dis_b2b-okay::before {
  content: "\ecad";
}
.wms-packing-circle_dis_b2b-okay-outline::before {
  content: "\ecae";
}
.wms-packing-circle_dis_b2b-okay-workstation-outline::before {
  content: "\ecaf";
}
.wms-packing-circle_dis_b2b-okayworkstation::before {
  content: "\ecb0";
}
.wms-packing-circle_dis_b2b-outline::before {
  content: "\ecb1";
}
.wms-packing-circle_dis_b2b-ready::before {
  content: "\ecb2";
}
.wms-packing-circle_dis_b2b-ready-outline::before {
  content: "\ecb3";
}
.wms-packing-circle_dis_b2b-return::before {
  content: "\ecb4";
}
.wms-packing-circle_dis_b2b-return-outline::before {
  content: "\ecb5";
}
.wms-packing-circle_dis_b2b-right::before {
  content: "\ecb6";
}
.wms-packing-circle_dis_b2b-right-outline::before {
  content: "\ecb7";
}
.wms-packing-circle_dis_b2b-right2::before {
  content: "\ecb8";
}
.wms-packing-circle_dis_b2b-right2-outline::before {
  content: "\ecb9";
}
.wms-packing-circle_dis_b2b-right3::before {
  content: "\ecba";
}
.wms-packing-circle_dis_b2b-right3-outline::before {
  content: "\ecbb";
}
.wms-packing-circle_dis_b2b-skladnik::before {
  content: "\ecbc";
}
.wms-packing-circle_dis_b2b-skladnik-outline::before {
  content: "\ecbd";
}
.wms-packing-circle_dis_b2b-stop::before {
  content: "\ecbe";
}
.wms-packing-circle_dis_b2b-stop-outline::before {
  content: "\ecbf";
}
.wms-packing-circle_dis_b2b-wait::before {
  content: "\ecc0";
}
.wms-packing-circle_dis_b2b-wait-outline::before {
  content: "\ecc1";
}
.wms-packing-circle_dis_b2b-wait-workstation::before {
  content: "\ecc2";
}
.wms-packing-circle_dis_b2b-wait_glass::before {
  content: "\ecc3";
}
.wms-packing-circle_dis_b2b-wait_glass-outline::before {
  content: "\ecc4";
}
.wms-packing-circle_dis_b2b-wait_glassworkstation::before {
  content: "\ecc5";
}
.wms-packing-circle_dis_b2b-wait_glassworkstation-outline::before {
  content: "\ecc6";
}
.wms-packing-circle_dis_b2b-waitworkstation-outline::before {
  content: "\ecc7";
}
.wms-packing-circle_dis_b2b-warning::before {
  content: "\ecc8";
}
.wms-packing-circle_dis_b2b-warning-outline::before {
  content: "\ecc9";
}
.wms-packing-circle_dis_b2b-workstation::before {
  content: "\ecca";
}
.wms-packing-circle_dis_b2b-workstation-outline::before {
  content: "\eccb";
}
.wms-packing-circle_dis_b2c::before {
  content: "\eccc";
}
.wms-packing-circle_dis_b2c-add::before {
  content: "\eccd";
}
.wms-packing-circle_dis_b2c-add-outline::before {
  content: "\ecce";
}
.wms-packing-circle_dis_b2c-close-outline::before {
  content: "\eccf";
}
.wms-packing-circle_dis_b2c-danger::before {
  content: "\ecd0";
}
.wms-packing-circle_dis_b2c-danger-outline::before {
  content: "\ecd1";
}
.wms-packing-circle_dis_b2c-down::before {
  content: "\ecd2";
}
.wms-packing-circle_dis_b2c-down-outline::before {
  content: "\ecd3";
}
.wms-packing-circle_dis_b2c-down2::before {
  content: "\ecd4";
}
.wms-packing-circle_dis_b2c-down2-outline::before {
  content: "\ecd5";
}
.wms-packing-circle_dis_b2c-down3::before {
  content: "\ecd6";
}
.wms-packing-circle_dis_b2c-down3-outline::before {
  content: "\ecd7";
}
.wms-packing-circle_dis_b2c-hold::before {
  content: "\ecd8";
}
.wms-packing-circle_dis_b2c-hold-outline::before {
  content: "\ecd9";
}
.wms-packing-circle_dis_b2c-info::before {
  content: "\ecda";
}
.wms-packing-circle_dis_b2c-info-outline::before {
  content: "\ecdb";
}
.wms-packing-circle_dis_b2c-move::before {
  content: "\ecdc";
}
.wms-packing-circle_dis_b2c-move-outline::before {
  content: "\ecdd";
}
.wms-packing-circle_dis_b2c-okay::before {
  content: "\ecde";
}
.wms-packing-circle_dis_b2c-okay-outline::before {
  content: "\ecdf";
}
.wms-packing-circle_dis_b2c-okay-workstation-outline::before {
  content: "\ece0";
}
.wms-packing-circle_dis_b2c-okayworkstation::before {
  content: "\ece1";
}
.wms-packing-circle_dis_b2c-outline::before {
  content: "\ece2";
}
.wms-packing-circle_dis_b2c-ready::before {
  content: "\ece3";
}
.wms-packing-circle_dis_b2c-ready-outline::before {
  content: "\ece4";
}
.wms-packing-circle_dis_b2c-return::before {
  content: "\ece5";
}
.wms-packing-circle_dis_b2c-return-outline::before {
  content: "\ece6";
}
.wms-packing-circle_dis_b2c-right::before {
  content: "\ece7";
}
.wms-packing-circle_dis_b2c-right-outline::before {
  content: "\ece8";
}
.wms-packing-circle_dis_b2c-right2::before {
  content: "\ece9";
}
.wms-packing-circle_dis_b2c-right2-outline::before {
  content: "\ecea";
}
.wms-packing-circle_dis_b2c-right3::before {
  content: "\eceb";
}
.wms-packing-circle_dis_b2c-right3-outline::before {
  content: "\ecec";
}
.wms-packing-circle_dis_b2c-skladnik::before {
  content: "\eced";
}
.wms-packing-circle_dis_b2c-skladnik-outline::before {
  content: "\ecee";
}
.wms-packing-circle_dis_b2c-stop::before {
  content: "\ecef";
}
.wms-packing-circle_dis_b2c-stop-outline::before {
  content: "\ecf0";
}
.wms-packing-circle_dis_b2c-wait::before {
  content: "\ecf1";
}
.wms-packing-circle_dis_b2c-wait-outline::before {
  content: "\ecf2";
}
.wms-packing-circle_dis_b2c-wait-workstation::before {
  content: "\ecf3";
}
.wms-packing-circle_dis_b2c-wait-workstation-outline::before {
  content: "\ecf4";
}
.wms-packing-circle_dis_b2c-wait_glass::before {
  content: "\ecf5";
}
.wms-packing-circle_dis_b2c-wait_glass-outline::before {
  content: "\ecf6";
}
.wms-packing-circle_dis_b2c-wait_glassworkstation::before {
  content: "\ecf7";
}
.wms-packing-circle_dis_b2c-wait_glassworkstation-outline::before {
  content: "\ecf8";
}
.wms-packing-circle_dis_b2c-warning::before {
  content: "\ecf9";
}
.wms-packing-circle_dis_b2c-warning-outline::before {
  content: "\ecfa";
}
.wms-packing-circle_dis_b2c-workstation::before {
  content: "\ecfb";
}
.wms-packing-circle_dis_b2c-workstation-outline::before {
  content: "\ecfc";
}
.wms-packing-circle_dispatch-okay_b2b::before {
  content: "\ecfd";
}
.wms-packing-circle_dispatch-ready_b2b::before {
  content: "\ecfe";
}
.wms-packing-circle_dispatch-right_b2b::before {
  content: "\ecff";
}
.wms-packing-circle_dispatch_b2b::before {
  content: "\ed00";
}
.wms-packing-circle_dispatch_b2b-add::before {
  content: "\ed01";
}
.wms-packing-circle_dispatch_b2b-add-outline::before {
  content: "\ed02";
}
.wms-packing-circle_dispatch_b2b-close::before {
  content: "\ed03";
}
.wms-packing-circle_dispatch_b2b-close-outline::before {
  content: "\ed04";
}
.wms-packing-circle_dispatch_b2b-danger::before {
  content: "\ed05";
}
.wms-packing-circle_dispatch_b2b-danger-outline::before {
  content: "\ed06";
}
.wms-packing-circle_dispatch_b2b-down::before {
  content: "\ed07";
}
.wms-packing-circle_dispatch_b2b-down-outline::before {
  content: "\ed08";
}
.wms-packing-circle_dispatch_b2b-down2::before {
  content: "\ed09";
}
.wms-packing-circle_dispatch_b2b-down2-outline::before {
  content: "\ed0a";
}
.wms-packing-circle_dispatch_b2b-down3::before {
  content: "\ed0b";
}
.wms-packing-circle_dispatch_b2b-down3-outline::before {
  content: "\ed0c";
}
.wms-packing-circle_dispatch_b2b-hold::before {
  content: "\ed0d";
}
.wms-packing-circle_dispatch_b2b-hold-outline::before {
  content: "\ed0e";
}
.wms-packing-circle_dispatch_b2b-info::before {
  content: "\ed0f";
}
.wms-packing-circle_dispatch_b2b-info-outline::before {
  content: "\ed10";
}
.wms-packing-circle_dispatch_b2b-move::before {
  content: "\ed11";
}
.wms-packing-circle_dispatch_b2b-move-outline::before {
  content: "\ed12";
}
.wms-packing-circle_dispatch_b2b-okay-outline::before {
  content: "\ed13";
}
.wms-packing-circle_dispatch_b2b-outline::before {
  content: "\ed14";
}
.wms-packing-circle_dispatch_b2b-ready-outline::before {
  content: "\ed15";
}
.wms-packing-circle_dispatch_b2b-return::before {
  content: "\ed16";
}
.wms-packing-circle_dispatch_b2b-return-outline::before {
  content: "\ed17";
}
.wms-packing-circle_dispatch_b2b-right-outline::before {
  content: "\ed18";
}
.wms-packing-circle_dispatch_b2b-right2::before {
  content: "\ed19";
}
.wms-packing-circle_dispatch_b2b-right2-outline::before {
  content: "\ed1a";
}
.wms-packing-circle_dispatch_b2b-right3::before {
  content: "\ed1b";
}
.wms-packing-circle_dispatch_b2b-right3-outline::before {
  content: "\ed1c";
}
.wms-packing-circle_dispatch_b2b-skladnik::before {
  content: "\ed1d";
}
.wms-packing-circle_dispatch_b2b-skladnik-outline::before {
  content: "\ed1e";
}
.wms-packing-circle_dispatch_b2b-stop::before {
  content: "\ed1f";
}
.wms-packing-circle_dispatch_b2b-stop-outline::before {
  content: "\ed20";
}
.wms-packing-circle_dispatch_b2b-wait::before {
  content: "\ed21";
}
.wms-packing-circle_dispatch_b2b-wait-1::before {
  content: "\ed22";
}
.wms-packing-circle_dispatch_b2b-wait-outline::before {
  content: "\ed23";
}
.wms-packing-circle_dispatch_b2b-wait-outline-1::before {
  content: "\ed24";
}
.wms-packing-circle_dispatch_b2b-warning::before {
  content: "\ed25";
}
.wms-packing-circle_dispatch_b2b-warning-outline::before {
  content: "\ed26";
}
.wms-packing-circle_dispatch_b2b-workstation::before {
  content: "\ed27";
}
.wms-packing-circle_dispatch_b2b-workstation-outline::before {
  content: "\ed28";
}
.wms-packing-circle_dispatch_b2c::before {
  content: "\ed29";
}
.wms-packing-circle_dispatch_b2c-add::before {
  content: "\ed2a";
}
.wms-packing-circle_dispatch_b2c-add-outline::before {
  content: "\ed2b";
}
.wms-packing-circle_dispatch_b2c-close::before {
  content: "\ed2c";
}
.wms-packing-circle_dispatch_b2c-close-outline::before {
  content: "\ed2d";
}
.wms-packing-circle_dispatch_b2c-danger::before {
  content: "\ed2e";
}
.wms-packing-circle_dispatch_b2c-danger-outline::before {
  content: "\ed2f";
}
.wms-packing-circle_dispatch_b2c-down::before {
  content: "\ed30";
}
.wms-packing-circle_dispatch_b2c-down-outline::before {
  content: "\ed31";
}
.wms-packing-circle_dispatch_b2c-down2::before {
  content: "\ed32";
}
.wms-packing-circle_dispatch_b2c-down2-outline::before {
  content: "\ed33";
}
.wms-packing-circle_dispatch_b2c-down3::before {
  content: "\ed34";
}
.wms-packing-circle_dispatch_b2c-down3-outline::before {
  content: "\ed35";
}
.wms-packing-circle_dispatch_b2c-hold::before {
  content: "\ed36";
}
.wms-packing-circle_dispatch_b2c-hold-outline::before {
  content: "\ed37";
}
.wms-packing-circle_dispatch_b2c-info::before {
  content: "\ed38";
}
.wms-packing-circle_dispatch_b2c-info-outline::before {
  content: "\ed39";
}
.wms-packing-circle_dispatch_b2c-move::before {
  content: "\ed3a";
}
.wms-packing-circle_dispatch_b2c-move-outline::before {
  content: "\ed3b";
}
.wms-packing-circle_dispatch_b2c-okay::before {
  content: "\ed3c";
}
.wms-packing-circle_dispatch_b2c-okay-outline::before {
  content: "\ed3d";
}
.wms-packing-circle_dispatch_b2c-outline::before {
  content: "\ed3e";
}
.wms-packing-circle_dispatch_b2c-ready::before {
  content: "\ed3f";
}
.wms-packing-circle_dispatch_b2c-ready-outline::before {
  content: "\ed40";
}
.wms-packing-circle_dispatch_b2c-return::before {
  content: "\ed41";
}
.wms-packing-circle_dispatch_b2c-return-outline::before {
  content: "\ed42";
}
.wms-packing-circle_dispatch_b2c-right::before {
  content: "\ed43";
}
.wms-packing-circle_dispatch_b2c-right-outline::before {
  content: "\ed44";
}
.wms-packing-circle_dispatch_b2c-right2::before {
  content: "\ed45";
}
.wms-packing-circle_dispatch_b2c-right2-outline::before {
  content: "\ed46";
}
.wms-packing-circle_dispatch_b2c-right3::before {
  content: "\ed47";
}
.wms-packing-circle_dispatch_b2c-right3-outline::before {
  content: "\ed48";
}
.wms-packing-circle_dispatch_b2c-skladnik::before {
  content: "\ed49";
}
.wms-packing-circle_dispatch_b2c-skladnik-outline::before {
  content: "\ed4a";
}
.wms-packing-circle_dispatch_b2c-stop::before {
  content: "\ed4b";
}
.wms-packing-circle_dispatch_b2c-stop-outline::before {
  content: "\ed4c";
}
.wms-packing-circle_dispatch_b2c-wait::before {
  content: "\ed4d";
}
.wms-packing-circle_dispatch_b2c-wait-outline::before {
  content: "\ed4e";
}
.wms-packing-circle_dispatch_b2c-wait_glass::before {
  content: "\ed4f";
}
.wms-packing-circle_dispatch_b2c-wait_glass-outline::before {
  content: "\ed50";
}
.wms-packing-circle_dispatch_b2c-warning::before {
  content: "\ed51";
}
.wms-packing-circle_dispatch_b2c-warning-outline::before {
  content: "\ed52";
}
.wms-packing-circle_dispatch_b2c-workstation::before {
  content: "\ed53";
}
.wms-packing-circle_dispatch_b2c-workstation-outline::before {
  content: "\ed54";
}
.wms-packing_circle_dis_b2b-right-workstation-outline::before {
  content: "\ed55";
}
.wms-packing_circle_dis_b2c-right-workstation-outline::before {
  content: "\ed56";
}
.wms-packing_dis-close_b2c::before {
  content: "\ed57";
}
.wms-packing_dis-hold_b2b::before {
  content: "\ed58";
}
.wms-packing_dis_b2b::before {
  content: "\ed59";
}
.wms-packing_dis_b2b-add::before {
  content: "\ed5a";
}
.wms-packing_dis_b2b-add-outline::before {
  content: "\ed5b";
}
.wms-packing_dis_b2b-close::before {
  content: "\ed5c";
}
.wms-packing_dis_b2b-close-outline::before {
  content: "\ed5d";
}
.wms-packing_dis_b2b-danger::before {
  content: "\ed5e";
}
.wms-packing_dis_b2b-danger-outline::before {
  content: "\ed5f";
}
.wms-packing_dis_b2b-down::before {
  content: "\ed60";
}
.wms-packing_dis_b2b-down-outline::before {
  content: "\ed61";
}
.wms-packing_dis_b2b-down2::before {
  content: "\ed62";
}
.wms-packing_dis_b2b-down2-outline::before {
  content: "\ed63";
}
.wms-packing_dis_b2b-down3::before {
  content: "\ed64";
}
.wms-packing_dis_b2b-down3-outline::before {
  content: "\ed65";
}
.wms-packing_dis_b2b-hold-outline::before {
  content: "\ed66";
}
.wms-packing_dis_b2b-info::before {
  content: "\ed67";
}
.wms-packing_dis_b2b-info-outline::before {
  content: "\ed68";
}
.wms-packing_dis_b2b-move::before {
  content: "\ed69";
}
.wms-packing_dis_b2b-move-outline::before {
  content: "\ed6a";
}
.wms-packing_dis_b2b-okay::before {
  content: "\ed6b";
}
.wms-packing_dis_b2b-okay-outline::before {
  content: "\ed6c";
}
.wms-packing_dis_b2b-okayworkstation::before {
  content: "\ed6d";
}
.wms-packing_dis_b2b-okayworkstation-outline::before {
  content: "\ed6e";
}
.wms-packing_dis_b2b-outline::before {
  content: "\ed6f";
}
.wms-packing_dis_b2b-ready::before {
  content: "\ed70";
}
.wms-packing_dis_b2b-ready-outline::before {
  content: "\ed71";
}
.wms-packing_dis_b2b-return::before {
  content: "\ed72";
}
.wms-packing_dis_b2b-return-outline::before {
  content: "\ed73";
}
.wms-packing_dis_b2b-right::before {
  content: "\ed74";
}
.wms-packing_dis_b2b-right-outline::before {
  content: "\ed75";
}
.wms-packing_dis_b2b-right-workstation-outline::before {
  content: "\ed76";
}
.wms-packing_dis_b2b-right2::before {
  content: "\ed77";
}
.wms-packing_dis_b2b-right2-outline::before {
  content: "\ed78";
}
.wms-packing_dis_b2b-right3::before {
  content: "\ed79";
}
.wms-packing_dis_b2b-right3-outline::before {
  content: "\ed7a";
}
.wms-packing_dis_b2b-skladnik::before {
  content: "\ed7b";
}
.wms-packing_dis_b2b-skladnik-outline::before {
  content: "\ed7c";
}
.wms-packing_dis_b2b-stop::before {
  content: "\ed7d";
}
.wms-packing_dis_b2b-stop-outline::before {
  content: "\ed7e";
}
.wms-packing_dis_b2b-wait::before {
  content: "\ed7f";
}
.wms-packing_dis_b2b-wait-outline::before {
  content: "\ed80";
}
.wms-packing_dis_b2b-wait_glass::before {
  content: "\ed81";
}
.wms-packing_dis_b2b-wait_glass-outline::before {
  content: "\ed82";
}
.wms-packing_dis_b2b-wait_glassworkstation::before {
  content: "\ed83";
}
.wms-packing_dis_b2b-wait_glassworkstation-outline::before {
  content: "\ed84";
}
.wms-packing_dis_b2b-waitworkstation::before {
  content: "\ed85";
}
.wms-packing_dis_b2b-waitworkstation-outline::before {
  content: "\ed86";
}
.wms-packing_dis_b2b-warning::before {
  content: "\ed87";
}
.wms-packing_dis_b2b-warning-outline::before {
  content: "\ed88";
}
.wms-packing_dis_b2b-workstation::before {
  content: "\ed89";
}
.wms-packing_dis_b2b-workstation-outline::before {
  content: "\ed8a";
}
.wms-packing_dis_b2c::before {
  content: "\ed8b";
}
.wms-packing_dis_b2c-add::before {
  content: "\ed8c";
}
.wms-packing_dis_b2c-add-outline::before {
  content: "\ed8d";
}
.wms-packing_dis_b2c-close-outline::before {
  content: "\ed8e";
}
.wms-packing_dis_b2c-danger::before {
  content: "\ed8f";
}
.wms-packing_dis_b2c-danger-outline::before {
  content: "\ed90";
}
.wms-packing_dis_b2c-down::before {
  content: "\ed91";
}
.wms-packing_dis_b2c-down-outline::before {
  content: "\ed92";
}
.wms-packing_dis_b2c-down2::before {
  content: "\ed93";
}
.wms-packing_dis_b2c-down2-outline::before {
  content: "\ed94";
}
.wms-packing_dis_b2c-down3::before {
  content: "\ed95";
}
.wms-packing_dis_b2c-down3-outline::before {
  content: "\ed96";
}
.wms-packing_dis_b2c-hold::before {
  content: "\ed97";
}
.wms-packing_dis_b2c-hold-outline::before {
  content: "\ed98";
}
.wms-packing_dis_b2c-info::before {
  content: "\ed99";
}
.wms-packing_dis_b2c-info-outline::before {
  content: "\ed9a";
}
.wms-packing_dis_b2c-move::before {
  content: "\ed9b";
}
.wms-packing_dis_b2c-move-outline::before {
  content: "\ed9c";
}
.wms-packing_dis_b2c-okay::before {
  content: "\ed9d";
}
.wms-packing_dis_b2c-okay-outline::before {
  content: "\ed9e";
}
.wms-packing_dis_b2c-okayworkstation::before {
  content: "\ed9f";
}
.wms-packing_dis_b2c-okayworkstation-outline::before {
  content: "\eda0";
}
.wms-packing_dis_b2c-outline::before {
  content: "\eda1";
}
.wms-packing_dis_b2c-ready::before {
  content: "\eda2";
}
.wms-packing_dis_b2c-ready-outline::before {
  content: "\eda3";
}
.wms-packing_dis_b2c-return::before {
  content: "\eda4";
}
.wms-packing_dis_b2c-return-outline::before {
  content: "\eda5";
}
.wms-packing_dis_b2c-right::before {
  content: "\eda6";
}
.wms-packing_dis_b2c-right-outline::before {
  content: "\eda7";
}
.wms-packing_dis_b2c-right-workstation-outline::before {
  content: "\eda8";
}
.wms-packing_dis_b2c-right2::before {
  content: "\eda9";
}
.wms-packing_dis_b2c-right2-outline::before {
  content: "\edaa";
}
.wms-packing_dis_b2c-right3::before {
  content: "\edab";
}
.wms-packing_dis_b2c-right3-outline::before {
  content: "\edac";
}
.wms-packing_dis_b2c-skladnik::before {
  content: "\edad";
}
.wms-packing_dis_b2c-skladnik-outline::before {
  content: "\edae";
}
.wms-packing_dis_b2c-stop::before {
  content: "\edaf";
}
.wms-packing_dis_b2c-stop-outline::before {
  content: "\edb0";
}
.wms-packing_dis_b2c-wait::before {
  content: "\edb1";
}
.wms-packing_dis_b2c-wait-outline::before {
  content: "\edb2";
}
.wms-packing_dis_b2c-wait_glass::before {
  content: "\edb3";
}
.wms-packing_dis_b2c-wait_glass-outline::before {
  content: "\edb4";
}
.wms-packing_dis_b2c-wait_glassworkstation::before {
  content: "\edb5";
}
.wms-packing_dis_b2c-wait_glassworkstation-outline::before {
  content: "\edb6";
}
.wms-packing_dis_b2c-waitworkstation::before {
  content: "\edb7";
}
.wms-packing_dis_b2c-waitworkstation-outline::before {
  content: "\edb8";
}
.wms-packing_dis_b2c-warning::before {
  content: "\edb9";
}
.wms-packing_dis_b2c-warning-outline::before {
  content: "\edba";
}
.wms-packing_dis_b2c-workstation::before {
  content: "\edbb";
}
.wms-packing_dis_b2c-workstation-outline::before {
  content: "\edbc";
}
.wms-packing_dispatch-okay_b2b::before {
  content: "\edbd";
}
.wms-packing_dispatch-ready_b2b::before {
  content: "\edbe";
}
.wms-packing_dispatch-right_b2b::before {
  content: "\edbf";
}
.wms-packing_dispatch_b2b::before {
  content: "\edc0";
}
.wms-packing_dispatch_b2b-add::before {
  content: "\edc1";
}
.wms-packing_dispatch_b2b-add-outline::before {
  content: "\edc2";
}
.wms-packing_dispatch_b2b-close::before {
  content: "\edc3";
}
.wms-packing_dispatch_b2b-close-outline::before {
  content: "\edc4";
}
.wms-packing_dispatch_b2b-danger::before {
  content: "\edc5";
}
.wms-packing_dispatch_b2b-danger-outline::before {
  content: "\edc6";
}
.wms-packing_dispatch_b2b-down::before {
  content: "\edc7";
}
.wms-packing_dispatch_b2b-down-outline::before {
  content: "\edc8";
}
.wms-packing_dispatch_b2b-down2::before {
  content: "\edc9";
}
.wms-packing_dispatch_b2b-down2-outline::before {
  content: "\edca";
}
.wms-packing_dispatch_b2b-down3::before {
  content: "\edcb";
}
.wms-packing_dispatch_b2b-down3-outline::before {
  content: "\edcc";
}
.wms-packing_dispatch_b2b-hold::before {
  content: "\edcd";
}
.wms-packing_dispatch_b2b-hold-outline::before {
  content: "\edce";
}
.wms-packing_dispatch_b2b-info::before {
  content: "\edcf";
}
.wms-packing_dispatch_b2b-info-outline::before {
  content: "\edd0";
}
.wms-packing_dispatch_b2b-move::before {
  content: "\edd1";
}
.wms-packing_dispatch_b2b-move-outline::before {
  content: "\edd2";
}
.wms-packing_dispatch_b2b-okay-outline::before {
  content: "\edd3";
}
.wms-packing_dispatch_b2b-outline::before {
  content: "\edd4";
}
.wms-packing_dispatch_b2b-ready-outline::before {
  content: "\edd5";
}
.wms-packing_dispatch_b2b-return::before {
  content: "\edd6";
}
.wms-packing_dispatch_b2b-return-outline::before {
  content: "\edd7";
}
.wms-packing_dispatch_b2b-right-outline::before {
  content: "\edd8";
}
.wms-packing_dispatch_b2b-right2::before {
  content: "\edd9";
}
.wms-packing_dispatch_b2b-right2-outline::before {
  content: "\edda";
}
.wms-packing_dispatch_b2b-right3::before {
  content: "\eddb";
}
.wms-packing_dispatch_b2b-right3-outline::before {
  content: "\eddc";
}
.wms-packing_dispatch_b2b-skladnik::before {
  content: "\eddd";
}
.wms-packing_dispatch_b2b-skladnik-outline::before {
  content: "\edde";
}
.wms-packing_dispatch_b2b-stop::before {
  content: "\eddf";
}
.wms-packing_dispatch_b2b-stop-outline::before {
  content: "\ede0";
}
.wms-packing_dispatch_b2b-wait::before {
  content: "\ede1";
}
.wms-packing_dispatch_b2b-wait-outline::before {
  content: "\ede2";
}
.wms-packing_dispatch_b2b-wait_glass::before {
  content: "\ede3";
}
.wms-packing_dispatch_b2b-wait_glass-outline::before {
  content: "\ede4";
}
.wms-packing_dispatch_b2b-warning::before {
  content: "\ede5";
}
.wms-packing_dispatch_b2b-warning-outline::before {
  content: "\ede6";
}
.wms-packing_dispatch_b2b-workstation::before {
  content: "\ede7";
}
.wms-packing_dispatch_b2b-workstation-outline::before {
  content: "\ede8";
}
.wms-packing_dispatch_b2c::before {
  content: "\ede9";
}
.wms-packing_dispatch_b2c-add::before {
  content: "\edea";
}
.wms-packing_dispatch_b2c-add-outline::before {
  content: "\edeb";
}
.wms-packing_dispatch_b2c-close::before {
  content: "\edec";
}
.wms-packing_dispatch_b2c-close-outline::before {
  content: "\eded";
}
.wms-packing_dispatch_b2c-danger::before {
  content: "\edee";
}
.wms-packing_dispatch_b2c-danger-outline::before {
  content: "\edef";
}
.wms-packing_dispatch_b2c-down::before {
  content: "\edf0";
}
.wms-packing_dispatch_b2c-down-outline::before {
  content: "\edf1";
}
.wms-packing_dispatch_b2c-down2::before {
  content: "\edf2";
}
.wms-packing_dispatch_b2c-down2-outline::before {
  content: "\edf3";
}
.wms-packing_dispatch_b2c-down3::before {
  content: "\edf4";
}
.wms-packing_dispatch_b2c-down3-outline::before {
  content: "\edf5";
}
.wms-packing_dispatch_b2c-hold::before {
  content: "\edf6";
}
.wms-packing_dispatch_b2c-hold-outline::before {
  content: "\edf7";
}
.wms-packing_dispatch_b2c-info::before {
  content: "\edf8";
}
.wms-packing_dispatch_b2c-info-outline::before {
  content: "\edf9";
}
.wms-packing_dispatch_b2c-move::before {
  content: "\edfa";
}
.wms-packing_dispatch_b2c-move-outline::before {
  content: "\edfb";
}
.wms-packing_dispatch_b2c-okay::before {
  content: "\edfc";
}
.wms-packing_dispatch_b2c-okay-outline::before {
  content: "\edfd";
}
.wms-packing_dispatch_b2c-outline::before {
  content: "\edfe";
}
.wms-packing_dispatch_b2c-ready::before {
  content: "\edff";
}
.wms-packing_dispatch_b2c-ready-outline::before {
  content: "\ee00";
}
.wms-packing_dispatch_b2c-return::before {
  content: "\ee01";
}
.wms-packing_dispatch_b2c-return-outline::before {
  content: "\ee02";
}
.wms-packing_dispatch_b2c-right::before {
  content: "\ee03";
}
.wms-packing_dispatch_b2c-right-outline::before {
  content: "\ee04";
}
.wms-packing_dispatch_b2c-right2::before {
  content: "\ee05";
}
.wms-packing_dispatch_b2c-right2-outline::before {
  content: "\ee06";
}
.wms-packing_dispatch_b2c-right3::before {
  content: "\ee07";
}
.wms-packing_dispatch_b2c-right3-outline::before {
  content: "\ee08";
}
.wms-packing_dispatch_b2c-skladnik::before {
  content: "\ee09";
}
.wms-packing_dispatch_b2c-skladnik-outline::before {
  content: "\ee0a";
}
.wms-packing_dispatch_b2c-stop::before {
  content: "\ee0b";
}
.wms-packing_dispatch_b2c-stop-outline::before {
  content: "\ee0c";
}
.wms-packing_dispatch_b2c-wait::before {
  content: "\ee0d";
}
.wms-packing_dispatch_b2c-wait-outline::before {
  content: "\ee0e";
}
.wms-packing_dispatch_b2c-wait_glass::before {
  content: "\ee0f";
}
.wms-packing_dispatch_b2c-wait_glass-outline::before {
  content: "\ee10";
}
.wms-packing_dispatch_b2c-warning::before {
  content: "\ee11";
}
.wms-packing_dispatch_b2c-warning-outline::before {
  content: "\ee12";
}
.wms-packing_dispatch_b2c-workstation::before {
  content: "\ee13";
}
.wms-packing_dispatch_b2c-workstation-outline::before {
  content: "\ee14";
}
.wms-paletovy::before {
  content: "\ee15";
}
.wms-paletovy-kam::before {
  content: "\ee16";
}
.wms-paletovy-odkial::before {
  content: "\ee17";
}
.wms-paletovy-outline::before {
  content: "\ee18";
}
.wms-paletovy-outline2::before {
  content: "\ee19";
}
.wms-paletovy-scan::before {
  content: "\ee1a";
}
.wms-paletovy-transfer::before {
  content: "\ee1b";
}
.wms-paletovy-zmena::before {
  content: "\ee1c";
}
.wms-paletovy2::before {
  content: "\ee1d";
}
.wms-paletovy2-1::before {
  content: "\ee1e";
}
.wms-paletovy2-outline::before {
  content: "\ee1f";
}
.wms-paletovy_pozicia::before {
  content: "\ee20";
}
.wms-paletovy_pozicia_plna::before {
  content: "\ee21";
}
.wms-paletovy_pozicia_plocha::before {
  content: "\ee22";
}
.wms-paletovy_pozicia_prazdna::before {
  content: "\ee23";
}
.wms-paletovy_pozicia_rezervovana::before {
  content: "\ee24";
}
.wms-paletovy_pozicia_vyuzita::before {
  content: "\ee25";
}
.wms-paletovy_pozicia_vyuzita-25::before {
  content: "\ee26";
}
.wms-paletovy_pozicia_vyuzita-50::before {
  content: "\ee27";
}
.wms-paletovy_pozicia_vyuzita-75::before {
  content: "\ee28";
}
.wms-paletovy_sklad::before {
  content: "\ee29";
}
.wms-paletovy_sklad-lokacia::before {
  content: "\ee2a";
}
.wms-paletovy_sklad-lokacia-outline::before {
  content: "\ee2b";
}
.wms-paletovy_sklad-pickovanie::before {
  content: "\ee2c";
}
.wms-paletovy_sklad-pickovanie-outline::before {
  content: "\ee2d";
}
.wms-paletovy_sklad_expedicia::before {
  content: "\ee2e";
}
.wms-paletovy_sklad_prijem::before {
  content: "\ee2f";
}
.wms-pallete::before {
  content: "\ee30";
}
.wms-pallete-damaged::before {
  content: "\ee31";
}
.wms-pallete-missing::before {
  content: "\ee32";
}
.wms-pallete-missing-1::before {
  content: "\ee33";
}
.wms-pallete-okay::before {
  content: "\ee34";
}
.wms-pallete-scan::before {
  content: "\ee35";
}
.wms-performance-statistics::before {
  content: "\ee36";
}
.wms-performance-statistics-average::before {
  content: "\ee37";
}
.wms-performance-statistics-average-outline::before {
  content: "\ee38";
}
.wms-performance-statistics-detail::before {
  content: "\ee39";
}
.wms-performance-statistics-detail-outline::before {
  content: "\ee3a";
}
.wms-performance-statistics-fastest::before {
  content: "\ee3b";
}
.wms-performance-statistics-fastest-outline::before {
  content: "\ee3c";
}
.wms-performance-statistics-outline::before {
  content: "\ee3d";
}
.wms-performance-statistics-slowest::before {
  content: "\ee3e";
}
.wms-performance-statistics-slowest-outline::before {
  content: "\ee3f";
}
.wms-performance-statistics-speed::before {
  content: "\ee40";
}
.wms-performance-statistics-speed-outline::before {
  content: "\ee41";
}
.wms-performance-statistics-time::before {
  content: "\ee42";
}
.wms-performance-statistics-time-outline::before {
  content: "\ee43";
}
.wms-performance-statistics-time-sum::before {
  content: "\ee44";
}
.wms-performance-statistics-time-sum-outline::before {
  content: "\ee45";
}
.wms-pick::before {
  content: "\ee46";
}
.wms-pick-b2c::before {
  content: "\ee47";
}
.wms-pick2::before {
  content: "\ee48";
}
.wms-pick2-outline::before {
  content: "\ee49";
}
.wms-picking_poskodeny::before {
  content: "\ee4a";
}
.wms-picking_spinavy::before {
  content: "\ee4b";
}
.wms-pickovaci-hadik::before {
  content: "\ee4c";
}
.wms-pickovaci-hadik-2::before {
  content: "\ee4d";
}
.wms-pickovaci-hadik-2-none::before {
  content: "\ee4e";
}
.wms-pickovaci-hadik-add::before {
  content: "\ee4f";
}
.wms-pickovaci-hadik-none::before {
  content: "\ee50";
}
.wms-pickovanie::before {
  content: "\ee51";
}
.wms-pickovanie2::before {
  content: "\ee52";
}
.wms-pickovanie_do_krabic::before {
  content: "\ee53";
}
.wms-pickovanie_do_krabic-outline::before {
  content: "\ee54";
}
.wms-place::before {
  content: "\ee55";
}
.wms-play-and-pause-button::before {
  content: "\ee56";
}
.wms-play-and-pause-button-outline::before {
  content: "\ee57";
}
.wms-play-button::before {
  content: "\ee58";
}
.wms-play-button-outline::before {
  content: "\ee59";
}
.wms-play-start::before {
  content: "\ee5a";
}
.wms-play-start-outline::before {
  content: "\ee5b";
}
.wms-plk::before {
  content: "\ee5c";
}
.wms-plk-add::before {
  content: "\ee5d";
}
.wms-plk-add-outline::before {
  content: "\ee5e";
}
.wms-plk-choose::before {
  content: "\ee5f";
}
.wms-plk-choose-outline::before {
  content: "\ee60";
}
.wms-plk-front::before {
  content: "\ee61";
}
.wms-plk-front-lokacia::before {
  content: "\ee62";
}
.wms-plk-front-lokacia-outline::before {
  content: "\ee63";
}
.wms-plk-front-nahlad::before {
  content: "\ee64";
}
.wms-plk-front-nahlad-outline::before {
  content: "\ee65";
}
.wms-plk-front-outline::before {
  content: "\ee66";
}
.wms-plk-okay::before {
  content: "\ee67";
}
.wms-plk-okay-outline::before {
  content: "\ee68";
}
.wms-plk-outline::before {
  content: "\ee69";
}
.wms-plk-pick::before {
  content: "\ee6a";
}
.wms-plk-pick-outline::before {
  content: "\ee6b";
}
.wms-plk-put::before {
  content: "\ee6c";
}
.wms-plk-put-outline::before {
  content: "\ee6d";
}
.wms-plk-scan::before {
  content: "\ee6e";
}
.wms-plk-scan-outline::before {
  content: "\ee6f";
}
.wms-plomba::before {
  content: "\ee70";
}
.wms-plomba-outline::before {
  content: "\ee71";
}
.wms-plomby::before {
  content: "\ee72";
}
.wms-plomby-outline::before {
  content: "\ee73";
}
.wms-pracovisko::before {
  content: "\ee74";
}
.wms-predprijem::before {
  content: "\ee75";
}
.wms-predvolena::before {
  content: "\ee76";
}
.wms-predvolena-active::before {
  content: "\ee77";
}
.wms-predvolena-home::before {
  content: "\ee78";
}
.wms-prehladova::before {
  content: "\ee79";
}
.wms-prerusit::before {
  content: "\ee7a";
}
.wms-prerusit2::before {
  content: "\ee7b";
}
.wms-previous-page::before {
  content: "\ee7c";
}
.wms-previous-page2::before {
  content: "\ee7d";
}
.wms-previous-page3::before {
  content: "\ee7e";
}
.wms-previous-page4::before {
  content: "\ee7f";
}
.wms-previous-page5::before {
  content: "\ee80";
}
.wms-previous-page6::before {
  content: "\ee81";
}
.wms-prijem::before {
  content: "\ee82";
}
.wms-printing::before {
  content: "\ee83";
}
.wms-printing-outline::before {
  content: "\ee84";
}
.wms-priority::before {
  content: "\ee85";
}
.wms-priority2::before {
  content: "\ee86";
}
.wms-problem-article-dameged::before {
  content: "\ee87";
}
.wms-problem-article-dirty::before {
  content: "\ee88";
}
.wms-problem-article-missing::before {
  content: "\ee89";
}
.wms-problem-article-surplus::before {
  content: "\ee8a";
}
.wms-products-prestime::before {
  content: "\ee8b";
}
.wms-products-prestime-outline::before {
  content: "\ee8c";
}
.wms-ps::before {
  content: "\ee8d";
}
.wms-ps-outline::before {
  content: "\ee8e";
}
.wms-ps_cell::before {
  content: "\ee8f";
}
.wms-put::before {
  content: "\ee90";
}
.wms-put-b2c::before {
  content: "\ee91";
}
.wms-received::before {
  content: "\ee92";
}
.wms-regal-position::before {
  content: "\ee93";
}
.wms-regal-position-0::before {
  content: "\ee94";
}
.wms-regal-position-1::before {
  content: "\ee95";
}
.wms-regal-position-10::before {
  content: "\ee96";
}
.wms-regal-position-11::before {
  content: "\ee97";
}
.wms-regal-position-12::before {
  content: "\ee98";
}
.wms-regal-position-13::before {
  content: "\ee99";
}
.wms-regal-position-14::before {
  content: "\ee9a";
}
.wms-regal-position-15::before {
  content: "\ee9b";
}
.wms-regal-position-2::before {
  content: "\ee9c";
}
.wms-regal-position-3::before {
  content: "\ee9d";
}
.wms-regal-position-4::before {
  content: "\ee9e";
}
.wms-regal-position-5::before {
  content: "\ee9f";
}
.wms-regal-position-6::before {
  content: "\eea0";
}
.wms-regal-position-7::before {
  content: "\eea1";
}
.wms-regal-position-8::before {
  content: "\eea2";
}
.wms-regal-position-9::before {
  content: "\eea3";
}
.wms-regal-position-actual::before {
  content: "\eea4";
}
.wms-regal-position-bg::before {
  content: "\eea5";
}
.wms-regal-position-marker::before {
  content: "\eea6";
}
.wms-regal-state::before {
  content: "\eea7";
}
.wms-regal_plk::before {
  content: "\eea8";
}
.wms-regal_plk-outline::before {
  content: "\eea9";
}
.wms-reklamacie::before {
  content: "\eeaa";
}
.wms-reload::before {
  content: "\eeab";
}
.wms-remove::before {
  content: "\eeac";
}
.wms-remove-one::before {
  content: "\eead";
}
.wms-remove-outline::before {
  content: "\eeae";
}
.wms-reserved::before {
  content: "\eeaf";
}
.wms-return-goods-damaged::before {
  content: "\eeb0";
}
.wms-return-goods-dirty::before {
  content: "\eeb1";
}
.wms-return-goods-jeans::before {
  content: "\eeb2";
}
.wms-return-goods-picto::before {
  content: "\eeb3";
}
.wms-return-goods-shirt::before {
  content: "\eeb4";
}
.wms-rozcestnik::before {
  content: "\eeb5";
}
.wms-rychla_inventura::before {
  content: "\eeb6";
}
.wms-scan_doc::before {
  content: "\eeb7";
}
.wms-scan_ean::before {
  content: "\eeb8";
}
.wms-scan_ean-carton::before {
  content: "\eeb9";
}
.wms-scan_ean-lokacia::before {
  content: "\eeba";
}
.wms-scan_ean-okay::before {
  content: "\eebb";
}
.wms-scan_ean-warning::before {
  content: "\eebc";
}
.wms-scan_ean2::before {
  content: "\eebd";
}
.wms-scan_ean3::before {
  content: "\eebe";
}
.wms-scan_eye::before {
  content: "\eebf";
}
.wms-scan_half::before {
  content: "\eec0";
}
.wms-scan_id::before {
  content: "\eec1";
}
.wms-scan_item2::before {
  content: "\eec2";
}
.wms-scan_person::before {
  content: "\eec3";
}
.wms-scan_qr::before {
  content: "\eec4";
}
.wms-scan_qr2::before {
  content: "\eec5";
}
.wms-search::before {
  content: "\eec6";
}
.wms-sekcia-notofikacie::before {
  content: "\eec7";
}
.wms-sekcia_administracia::before {
  content: "\eec8";
}
.wms-sekcia_administrativa::before {
  content: "\eec9";
}
.wms-sekcia_balenie::before {
  content: "\eeca";
}
.wms-sekcia_cakanie::before {
  content: "\eecb";
}
.wms-sekcia_created::before {
  content: "\eecc";
}
.wms-sekcia_dopravca::before {
  content: "\eecd";
}
.wms-sekcia_dopravnik::before {
  content: "\eece";
}
.wms-sekcia_expedicia::before {
  content: "\eecf";
}
.wms-sekcia_foteni-vazenie::before {
  content: "\eed0";
}
.wms-sekcia_highpriority::before {
  content: "\eed1";
}
.wms-sekcia_hladat::before {
  content: "\eed2";
}
.wms-sekcia_inventura::before {
  content: "\eed3";
}
.wms-sekcia_konsolidacia::before {
  content: "\eed4";
}
.wms-sekcia_meranie::before {
  content: "\eed5";
}
.wms-sekcia_nezaradene::before {
  content: "\eed6";
}
.wms-sekcia_obrazky::before {
  content: "\eed7";
}
.wms-sekcia_picking::before {
  content: "\eed8";
}
.wms-sekcia_predprijem::before {
  content: "\eed9";
}
.wms-sekcia_prijem::before {
  content: "\eeda";
}
.wms-sekcia_problemovytovar::before {
  content: "\eedb";
}
.wms-sekcia_reklamacie::before {
  content: "\eedc";
}
.wms-sekcia_servise::before {
  content: "\eedd";
}
.wms-sekcia_stats::before {
  content: "\eede";
}
.wms-sekcia_stranky::before {
  content: "\eedf";
}
.wms-sekcia_tlac-etikety::before {
  content: "\eee0";
}
.wms-sekcia_transfer::before {
  content: "\eee1";
}
.wms-sekcia_transport::before {
  content: "\eee2";
}
.wms-sekcia_triedenie::before {
  content: "\eee3";
}
.wms-sekcia_ulohy::before {
  content: "\eee4";
}
.wms-sekcia_vazenie::before {
  content: "\eee5";
}
.wms-sekcia_velka_inventura::before {
  content: "\eee6";
}
.wms-sekcia_vermont::before {
  content: "\eee7";
}
.wms-sekcia_vms::before {
  content: "\eee8";
}
.wms-sekcia_vratky::before {
  content: "\eee9";
}
.wms-sekcia_vykladka::before {
  content: "\eeea";
}
.wms-sekcia_zabalene::before {
  content: "\eeeb";
}
.wms-selectall::before {
  content: "\eeec";
}
.wms-send::before {
  content: "\eeed";
}
.wms-sensor_sklad::before {
  content: "\eeee";
}
.wms-sensor_sklad_opticky::before {
  content: "\eeef";
}
.wms-servise::before {
  content: "\eef0";
}
.wms-settings::before {
  content: "\eef1";
}
.wms-settings-1::before {
  content: "\eef2";
}
.wms-settings-goal::before {
  content: "\eef3";
}
.wms-setup::before {
  content: "\eef4";
}
.wms-size::before {
  content: "\eef5";
}
.wms-skladnik::before {
  content: "\eef6";
}
.wms-skladnik-outline::before {
  content: "\eef7";
}
.wms-skladnik-outline-supervisor-warning::before {
  content: "\eef8";
}
.wms-skladnik-outline-warning::before {
  content: "\eef9";
}
.wms-smilee::before {
  content: "\eefa";
}
.wms-smilee-outline::before {
  content: "\eefb";
}
.wms-spustit::before {
  content: "\eefc";
}
.wms-spz::before {
  content: "\eefd";
}
.wms-spz-outline::before {
  content: "\eefe";
}
.wms-standaone-alert::before {
  content: "\eeff";
}
.wms-standaone-alert2::before {
  content: "\ef00";
}
.wms-standaone-alert3::before {
  content: "\ef01";
}
.wms-statistical-graphic::before {
  content: "\ef02";
}
.wms-status::before {
  content: "\ef03";
}
.wms-stender::before {
  content: "\ef04";
}
.wms-stepper-vertical-solo::before {
  content: "\ef05";
}
.wms-sum::before {
  content: "\ef06";
}
.wms-sum2::before {
  content: "\ef07";
}
.wms-sum3::before {
  content: "\ef08";
}
.wms-sun::before {
  content: "\ef09";
}
.wms-supervisor::before {
  content: "\ef0a";
}
.wms-switch::before {
  content: "\ef0b";
}
.wms-switch-1::before {
  content: "\ef0c";
}
.wms-switch-off::before {
  content: "\ef0d";
}
.wms-switch-on::before {
  content: "\ef0e";
}
.wms-tabulka::before {
  content: "\ef0f";
}
.wms-tabulka-outline::before {
  content: "\ef10";
}
.wms-taskgroup::before {
  content: "\ef11";
}
.wms-thumb-acepted::before {
  content: "\ef12";
}
.wms-thumb-acepted-circle::before {
  content: "\ef13";
}
.wms-thumb-acepted-circle-outline::before {
  content: "\ef14";
}
.wms-thumb-acepted-outline::before {
  content: "\ef15";
}
.wms-thumb-not-acepted::before {
  content: "\ef16";
}
.wms-thumb-not-acepted-circle::before {
  content: "\ef17";
}
.wms-thumb-not-acepted-circle-outline::before {
  content: "\ef18";
}
.wms-thumb-not-acepted-outline::before {
  content: "\ef19";
}
.wms-thumbnail::before {
  content: "\ef1a";
}
.wms-timeline-as-pick::before {
  content: "\ef1b";
}
.wms-timeline-dopravnik::before {
  content: "\ef1c";
}
.wms-timeline-mimo-nosicov::before {
  content: "\ef1d";
}
.wms-timeline-nevyskladnene-as::before {
  content: "\ef1e";
}
.wms-timeline-nevyskladnene-ps::before {
  content: "\ef1f";
}
.wms-timeline-ready-na-balenie::before {
  content: "\ef20";
}
.wms-timeline-zostava-balit::before {
  content: "\ef21";
}
.wms-timeupdate::before {
  content: "\ef22";
}
.wms-tlac-etikety::before {
  content: "\ef23";
}
.wms-to-be-replaced::before {
  content: "\ef24";
}
.wms-to_be-changed::before {
  content: "\ef25";
}
.wms-transfer::before {
  content: "\ef26";
}
.wms-transit-arrow-carton::before {
  content: "\ef27";
}
.wms-transit-arrow-carton-outline::before {
  content: "\ef28";
}
.wms-transit-arrow-dots-outline::before {
  content: "\ef29";
}
.wms-transit-dots-carton::before {
  content: "\ef2a";
}
.wms-trash::before {
  content: "\ef2b";
}
.wms-trash-outline::before {
  content: "\ef2c";
}
.wms-trediaren::before {
  content: "\ef2d";
}
.wms-ukoncit::before {
  content: "\ef2e";
}
.wms-ukony::before {
  content: "\ef2f";
}
.wms-ukony-in::before {
  content: "\ef30";
}
.wms-ukony-in-outline::before {
  content: "\ef31";
}
.wms-ukony-move::before {
  content: "\ef32";
}
.wms-ukony-move-outline::before {
  content: "\ef33";
}
.wms-ukony-outline::before {
  content: "\ef34";
}
.wms-ukony-stop::before {
  content: "\ef35";
}
.wms-ukony-stop-outline::before {
  content: "\ef36";
}
.wms-ukony-user::before {
  content: "\ef37";
}
.wms-ukony-user-outline::before {
  content: "\ef38";
}
.wms-ukony-wait::before {
  content: "\ef39";
}
.wms-ukony-wait-outline::before {
  content: "\ef3a";
}
.wms-ulohy::before {
  content: "\ef3b";
}
.wms-ulohy-1::before {
  content: "\ef3c";
}
.wms-ulohy-outline::before {
  content: "\ef3d";
}
.wms-ulohy-stop::before {
  content: "\ef3e";
}
.wms-ulohy-stop-outline::before {
  content: "\ef3f";
}
.wms-ulohy-wait::before {
  content: "\ef40";
}
.wms-ulohy-wait-outline::before {
  content: "\ef41";
}
.wms-union::before {
  content: "\ef42";
}
.wms-user-online::before {
  content: "\ef43";
}
.wms-user-pause::before {
  content: "\ef44";
}
.wms-user-unknown::before {
  content: "\ef45";
}
.wms-user-warning::before {
  content: "\ef46";
}
.wms-v_home::before {
  content: "\ef47";
}
.wms-vapenka::before {
  content: "\ef48";
}
.wms-vapenka-3r00::before {
  content: "\ef49";
}
.wms-vapenka-3r00-outline::before {
  content: "\ef4a";
}
.wms-vapenka-3s20::before {
  content: "\ef4b";
}
.wms-vapenka-3s20-outline::before {
  content: "\ef4c";
}
.wms-vapenka-outline::before {
  content: "\ef4d";
}
.wms-vazenie::before {
  content: "\ef4e";
}
.wms-vazenie-stanica::before {
  content: "\ef4f";
}
.wms-vbin::before {
  content: "\ef50";
}
.wms-vbin-filled::before {
  content: "\ef51";
}
.wms-vbin_full::before {
  content: "\ef52";
}
.wms-vbin_half::before {
  content: "\ef53";
}
.wms-vbin_prijem::before {
  content: "\ef54";
}
.wms-vbin_quarter::before {
  content: "\ef55";
}
.wms-vermont::before {
  content: "\ef56";
}
.wms-vermont-intranet-pass::before {
  content: "\ef57";
}
.wms-vermont-ucko::before {
  content: "\ef58";
}
.wms-vermont-wms-logo::before {
  content: "\ef59";
}
.wms-vermont_invert::before {
  content: "\ef5a";
}
.wms-visible::before {
  content: "\ef5b";
}
.wms-vol::before {
  content: "\ef5c";
}
.wms-vol-add::before {
  content: "\ef5d";
}
.wms-vol-add-outline::before {
  content: "\ef5e";
}
.wms-vol-info::before {
  content: "\ef5f";
}
.wms-vol-info-outline::before {
  content: "\ef60";
}
.wms-vol-move::before {
  content: "\ef61";
}
.wms-vol-move-outline::before {
  content: "\ef62";
}
.wms-vol-okay::before {
  content: "\ef63";
}
.wms-vol-okay-outline::before {
  content: "\ef64";
}
.wms-vol-outline::before {
  content: "\ef65";
}
.wms-vol-remove::before {
  content: "\ef66";
}
.wms-vol-remove-outline::before {
  content: "\ef67";
}
.wms-vol-settings::before {
  content: "\ef68";
}
.wms-vol-settings-outline::before {
  content: "\ef69";
}
.wms-vol-stop::before {
  content: "\ef6a";
}
.wms-vol-stop-outline::before {
  content: "\ef6b";
}
.wms-vol-warning::before {
  content: "\ef6c";
}
.wms-vol-warning-outline::before {
  content: "\ef6d";
}
.wms-volume::before {
  content: "\ef6e";
}
.wms-volume-add::before {
  content: "\ef6f";
}
.wms-volume-add-outline::before {
  content: "\ef70";
}
.wms-volume-info::before {
  content: "\ef71";
}
.wms-volume-info-outline::before {
  content: "\ef72";
}
.wms-volume-liter::before {
  content: "\ef73";
}
.wms-volume-liter-outline::before {
  content: "\ef74";
}
.wms-volume-liter-square::before {
  content: "\ef75";
}
.wms-volume-move::before {
  content: "\ef76";
}
.wms-volume-move-outline::before {
  content: "\ef77";
}
.wms-volume-outline::before {
  content: "\ef78";
}
.wms-volume-remove::before {
  content: "\ef79";
}
.wms-volume-remove-outline::before {
  content: "\ef7a";
}
.wms-volume-settings::before {
  content: "\ef7b";
}
.wms-volume-settings-outline::before {
  content: "\ef7c";
}
.wms-volume-stop::before {
  content: "\ef7d";
}
.wms-volume-stop-outline::before {
  content: "\ef7e";
}
.wms-volume-warning::before {
  content: "\ef7f";
}
.wms-volume-warning-outline::before {
  content: "\ef80";
}
.wms-volume_alt::before {
  content: "\ef81";
}
.wms-volume_alt-add::before {
  content: "\ef82";
}
.wms-volume_alt-add-outline::before {
  content: "\ef83";
}
.wms-volume_alt-info::before {
  content: "\ef84";
}
.wms-volume_alt-info-outline::before {
  content: "\ef85";
}
.wms-volume_alt-move::before {
  content: "\ef86";
}
.wms-volume_alt-move-outline::before {
  content: "\ef87";
}
.wms-volume_alt-outline::before {
  content: "\ef88";
}
.wms-volume_alt-remove::before {
  content: "\ef89";
}
.wms-volume_alt-remove-outline::before {
  content: "\ef8a";
}
.wms-volume_alt-settings::before {
  content: "\ef8b";
}
.wms-volume_alt-settings-outline::before {
  content: "\ef8c";
}
.wms-volume_alt-stop::before {
  content: "\ef8d";
}
.wms-volume_alt-stop-outline::before {
  content: "\ef8e";
}
.wms-volume_alt-warning::before {
  content: "\ef8f";
}
.wms-volume_alt-warning-outline::before {
  content: "\ef90";
}
.wms-vozik::before {
  content: "\ef91";
}
.wms-vozik-mandatory::before {
  content: "\ef92";
}
.wms-vratky::before {
  content: "\ef93";
}
.wms-vratky-filled::before {
  content: "\ef94";
}
.wms-vratky-single::before {
  content: "\ef95";
}
.wms-vratky-single-outline::before {
  content: "\ef96";
}
.wms-vydajka::before {
  content: "\ef97";
}
.wms-vydajka-gift::before {
  content: "\ef98";
}
.wms-vydajka-gift-ouline::before {
  content: "\ef99";
}
.wms-vydajka-move::before {
  content: "\ef9a";
}
.wms-vydajka-move-ouline::before {
  content: "\ef9b";
}
.wms-vydajka-movein::before {
  content: "\ef9c";
}
.wms-vydajka-movein-ouline::before {
  content: "\ef9d";
}
.wms-vydajka-ouline::before {
  content: "\ef9e";
}
.wms-vydajka-ready::before {
  content: "\ef9f";
}
.wms-vydajka-ready-ouline::before {
  content: "\efa0";
}
.wms-vydajka-skladnik::before {
  content: "\efa1";
}
.wms-vydajka-skladnik-ouline::before {
  content: "\efa2";
}
.wms-vydajka-stop::before {
  content: "\efa3";
}
.wms-vydajka-stop-ouline::before {
  content: "\efa4";
}
.wms-vydajka-triedenie::before {
  content: "\efa5";
}
.wms-vydajka-triedenie-ouline::before {
  content: "\efa6";
}
.wms-vydajka-wait::before {
  content: "\efa7";
}
.wms-vydajka-wait-ouline::before {
  content: "\efa8";
}
.wms-vyhladavanie::before {
  content: "\efa9";
}
.wms-vykladka::before {
  content: "\efaa";
}
.wms-vystraha_active::before {
  content: "\efab";
}
.wms-vystraha_active-bot::before {
  content: "\efac";
}
.wms-vystraha_active-outline::before {
  content: "\efad";
}
.wms-vystraha_active-outline-bot::before {
  content: "\efae";
}
.wms-vystraha_default::before {
  content: "\efaf";
}
.wms-vystraha_default-outline::before {
  content: "\efb0";
}
.wms-vystraha_fatal::before {
  content: "\efb1";
}
.wms-vystraha_fatal-outline::before {
  content: "\efb2";
}
.wms-warehouse::before {
  content: "\efb3";
}
.wms-warning::before {
  content: "\efb4";
}
.wms-warning-outline::before {
  content: "\efb5";
}
.wms-weight-gram::before {
  content: "\efb6";
}
.wms-weight-gram-outline::before {
  content: "\efb7";
}
.wms-weight-kg::before {
  content: "\efb8";
}
.wms-weight-kg-outline::before {
  content: "\efb9";
}
.wms-width::before {
  content: "\efba";
}
.wms-width2::before {
  content: "\efbb";
}
.wms-wink::before {
  content: "\efbc";
}
.wms-wink-outline::before {
  content: "\efbd";
}
.wms-wishlist::before {
  content: "\efbe";
}
.wms-wishlist-outline::before {
  content: "\efbf";
}
.wms-workdesk::before {
  content: "\efc0";
}
.wms-workdesk-okay-outline::before {
  content: "\efc1";
}
.wms-workdesk-okay-setup::before {
  content: "\efc2";
}
.wms-workdesk-outline::before {
  content: "\efc3";
}
.wms-workdesk-put::before {
  content: "\efc4";
}
.wms-workdesk-put-outline::before {
  content: "\efc5";
}
.wms-workdesk-setup::before {
  content: "\efc6";
}
.wms-workdesk-setup-outline::before {
  content: "\efc7";
}
.wms-workdesk-stop::before {
  content: "\efc8";
}
.wms-workdesk-stop-outline::before {
  content: "\efc9";
}
.wms-workerplace::before {
  content: "\efca";
}
.wms-workerplace2::before {
  content: "\efcb";
}
.wms-workload-0::before {
  content: "\efcc";
}
.wms-workload-0-outline::before {
  content: "\efcd";
}
.wms-workload-1::before {
  content: "\efce";
}
.wms-workload-1-outline::before {
  content: "\efcf";
}
.wms-workload-2::before {
  content: "\efd0";
}
.wms-workload-2-outline::before {
  content: "\efd1";
}
.wms-workload-3::before {
  content: "\efd2";
}
.wms-workload-3-outline::before {
  content: "\efd3";
}
.wms-workload-4::before {
  content: "\efd4";
}
.wms-workload-4-outline::before {
  content: "\efd5";
}
.wms-workload-5::before {
  content: "\efd6";
}
.wms-workload-5-outline::before {
  content: "\efd7";
}
.wms-workload-6::before {
  content: "\efd8";
}
.wms-workload-6-outline::before {
  content: "\efd9";
}
.wms-workload-7::before {
  content: "\efda";
}
.wms-workload-7-outline::before {
  content: "\efdb";
}
.wms-workload-8::before {
  content: "\efdc";
}
.wms-workload-8-outline::before {
  content: "\efdd";
}
.wms-workload-9::before {
  content: "\efde";
}
.wms-workload-9-outline::before {
  content: "\efdf";
}
.wms-workload-clock-0::before {
  content: "\efe0";
}
.wms-workload-clock-0-outline::before {
  content: "\efe1";
}
.wms-workload-clock-1::before {
  content: "\efe2";
}
.wms-workload-clock-1-outline::before {
  content: "\efe3";
}
.wms-workload-clock-2::before {
  content: "\efe4";
}
.wms-workload-clock-2-outline::before {
  content: "\efe5";
}
.wms-workload-clock-3::before {
  content: "\efe6";
}
.wms-workload-clock-3-outline::before {
  content: "\efe7";
}
.wms-workload-clock-4::before {
  content: "\efe8";
}
.wms-workload-clock-4-outline::before {
  content: "\efe9";
}
.wms-workload-clock-5::before {
  content: "\efea";
}
.wms-workload-clock-5-outline::before {
  content: "\efeb";
}
.wms-workload-clock-6::before {
  content: "\efec";
}
.wms-workload-clock-6-outline::before {
  content: "\efed";
}
.wms-workload-clock-7::before {
  content: "\efee";
}
.wms-workload-clock-7-outline::before {
  content: "\efef";
}
.wms-workload-clock-8::before {
  content: "\eff0";
}
.wms-workload-clock-8-outline::before {
  content: "\eff1";
}
.wms-workload-clock-9::before {
  content: "\eff2";
}
.wms-workload-clock-9-outline::before {
  content: "\eff3";
}
.wms-workstation::before {
  content: "\eff4";
}
.wms-workstation-filled::before {
  content: "\eff5";
}
.wms-wrong-destination::before {
  content: "\eff6";
}
.wms-zamestnanec::before {
  content: "\eff7";
}
.wms-zamestnanec-karta::before {
  content: "\eff8";
}
.wms-zamestnanec-karta-outline::before {
  content: "\eff9";
}
.wms-zamestnanec-karta-qr::before {
  content: "\effa";
}
.wms-zamestnanec-karta-qr-outline::before {
  content: "\effb";
}
.wms-zamestnanec-outline::before {
  content: "\effc";
}
.wms-zavesny_system::before {
  content: "\effd";
}
.wms-zavoz-cesta::before {
  content: "\effe";
}
.wms-zbalit::before {
  content: "\efff";
}
.wms-zbalit-outline::before {
  content: "\f000";
}
.wms-zbalit-zabelene::before {
  content: "\f001";
}
.wms-zbalit-zabelene-outline::before {
  content: "\f002";
}
.wms-zdvihnut::before {
  content: "\f003";
}
.wms-zrucnosti::before {
  content: "\f004";
}
.wms-zrucnosti-outline::before {
  content: "\f005";
}
.wms-zrucnosti-settings::before {
  content: "\f006";
}
.wms-zrucnosti-settings-outline::before {
  content: "\f007";
}
