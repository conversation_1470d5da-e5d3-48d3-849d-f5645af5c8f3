$claims-403: "\ea01";
$claims-404: "\ea02";
$claims-404-2: "\ea03";
$claims-503: "\ea04";
$claims-a1: "\ea05";
$claims-a2: "\ea06";
$claims-a3: "\ea07";
$claims-a4: "\ea08";
$claims-acepted: "\ea09";
$claims-acepted-outline: "\ea0a";
$claims-action-info: "\ea0b";
$claims-action-print: "\ea0c";
$claims-addition: "\ea0d";
$claims-addition-outline: "\ea0e";
$claims-addition-simple: "\ea0f";
$claims-alert: "\ea10";
$claims-alert-outline: "\ea11";
$claims-alert-warning: "\ea12";
$claims-alert-warning-outline: "\ea13";
$claims-archive-all: "\ea14";
$claims-archive-all-outline: "\ea15";
$claims-arrow7: "\ea16";
$claims-article: "\ea17";
$claims-article-outline: "\ea18";
$claims-articles: "\ea19";
$claims-articles-outline: "\ea1a";
$claims-asign-task: "\ea1b";
$claims-asign-task-circle: "\ea1c";
$claims-avatar: "\ea1d";
$claims-avatar-overlay: "\ea1e";
$claims-back: "\ea1f";
$claims-blocek: "\ea20";
$claims-blocek-add: "\ea21";
$claims-blocek-add-outline: "\ea22";
$claims-blocek-outline: "\ea23";
$claims-blocek-photo: "\ea24";
$claims-blocek-photo-outline: "\ea25";
$claims-bm: "\ea26";
$claims-bm-active: "\ea27";
$claims-bm-active-outline: "\ea28";
$claims-bm-add: "\ea29";
$claims-bm-add-outline: "\ea2a";
$claims-bm-outline: "\ea2b";
$claims-bookmark: "\ea2c";
$claims-bookmark-add: "\ea2d";
$claims-bookmark-add-outline: "\ea2e";
$claims-bookmark-outline: "\ea2f";
$claims-bookmark2-active: "\ea30";
$claims-bookmark2-active-outline: "\ea31";
$claims-bookmark2-add: "\ea32";
$claims-bookmark2-add-1: "\ea33";
$claims-bookmark2-add-outline: "\ea34";
$claims-bookmark2-add-outline-1: "\ea35";
$claims-calendar: "\ea36";
$claims-calendar-add: "\ea37";
$claims-calendar-add-outline: "\ea38";
$claims-calendar-check: "\ea39";
$claims-calendar-check-outline: "\ea3a";
$claims-calendar-due: "\ea3b";
$claims-calendar-due-outline: "\ea3c";
$claims-calendar-outline: "\ea3d";
$claims-calendar-urgent: "\ea3e";
$claims-calendar-urgent-outline: "\ea3f";
$claims-calendar-vat: "\ea40";
$claims-calendar-vat-outline: "\ea41";
$claims-camel_active: "\ea42";
$claims-camelactive-logo: "\ea43";
$claims-camera: "\ea44";
$claims-cas: "\ea45";
$claims-center: "\ea46";
$claims-center-outline: "\ea47";
$claims-change: "\ea48";
$claims-change-parcel: "\ea49";
$claims-chevron-down: "\ea4a";
$claims-chevron-up: "\ea4b";
$claims-choose-items: "\ea4c";
$claims-claims-dodavatelska: "\ea4d";
$claims-claims-eshop: "\ea4e";
$claims-claims-zakaznik: "\ea4f";
$claims-clock: "\ea50";
$claims-clock-outline: "\ea51";
$claims-close: "\ea52";
$claims-copyclipboardsend: "\ea53";
$claims-creator: "\ea54";
$claims-creator-outline: "\ea55";
$claims-credit-cards: "\ea56";
$claims-customer: "\ea57";
$claims-d-t-air-transport: "\ea58";
$claims-d-t-car-wash: "\ea59";
$claims-d-t-carpet: "\ea5a";
$claims-d-t-decoration: "\ea5b";
$claims-d-t-decoration-supplies: "\ea5c";
$claims-d-t-e-air-transport: "\ea5d";
$claims-d-t-e-car-wash: "\ea5e";
$claims-d-t-e-carpet: "\ea5f";
$claims-d-t-e-decoration: "\ea60";
$claims-d-t-e-decoration-supplies: "\ea61";
$claims-d-t-e-first-aid-kit: "\ea62";
$claims-d-t-e-flowers: "\ea63";
$claims-d-t-e-flowers2: "\ea64";
$claims-d-t-e-gas-station: "\ea65";
$claims-d-t-e-highway: "\ea66";
$claims-d-t-e-hotel: "\ea67";
$claims-d-t-e-hotel2: "\ea68";
$claims-d-t-e-lamp: "\ea69";
$claims-d-t-e-parking: "\ea6a";
$claims-d-t-e-postage: "\ea6b";
$claims-d-t-e-repre: "\ea6c";
$claims-d-t-e-repre2: "\ea6d";
$claims-d-t-e-supplies-car: "\ea6e";
$claims-d-t-e-supplies-garden: "\ea6f";
$claims-d-t-e-supplies-garden2: "\ea70";
$claims-d-t-e-supplies-it: "\ea71";
$claims-d-t-e-supplies-maintance: "\ea72";
$claims-d-t-e-supplies-rest: "\ea73";
$claims-d-t-e-taxi: "\ea74";
$claims-d-t-e-travel-ticket: "\ea75";
$claims-d-t-first-aid-kit: "\ea76";
$claims-d-t-flowers: "\ea77";
$claims-d-t-flowers2: "\ea78";
$claims-d-t-gas-station: "\ea79";
$claims-d-t-highway: "\ea7a";
$claims-d-t-hotel: "\ea7b";
$claims-d-t-hotel2: "\ea7c";
$claims-d-t-lamp: "\ea7d";
$claims-d-t-parking: "\ea7e";
$claims-d-t-postage: "\ea7f";
$claims-d-t-repre: "\ea80";
$claims-d-t-repre2: "\ea81";
$claims-d-t-supplies-car: "\ea82";
$claims-d-t-supplies-garden: "\ea83";
$claims-d-t-supplies-garden2: "\ea84";
$claims-d-t-supplies-it: "\ea85";
$claims-d-t-supplies-maintance: "\ea86";
$claims-d-t-supplies-rest: "\ea87";
$claims-d-t-taxi: "\ea88";
$claims-d-t-travel-ticket: "\ea89";
$claims-dash: "\ea8a";
$claims-dash-outline: "\ea8b";
$claims-datum: "\ea8c";
$claims-delete-bookmark: "\ea8d";
$claims-delete-bookmark-success: "\ea8e";
$claims-department: "\ea8f";
$claims-department-outline: "\ea90";
$claims-detail: "\ea91";
$claims-diesel-icon: "\ea92";
$claims-dnky: "\ea93";
$claims-dnky-icon: "\ea94";
$claims-document: "\ea95";
$claims-document-acepted: "\ea96";
$claims-document-acepted-outline: "\ea97";
$claims-document-add: "\ea98";
$claims-document-add-outline: "\ea99";
$claims-document-attached: "\ea9a";
$claims-document-attached-outline: "\ea9b";
$claims-document-attachment: "\ea9c";
$claims-document-attachment-outline: "\ea9d";
$claims-document-blank: "\ea9e";
$claims-document-camera: "\ea9f";
$claims-document-camera-outline: "\eaa0";
$claims-document-doc: "\eaa1";
$claims-document-doc-outline: "\eaa2";
$claims-document-extraorder: "\eaa3";
$claims-document-extraorder-outline: "\eaa4";
$claims-document-heic: "\eaa5";
$claims-document-heic-outline: "\eaa6";
$claims-document-hold: "\eaa7";
$claims-document-hold-outline: "\eaa8";
$claims-document-image: "\eaa9";
$claims-document-image-outline: "\eaaa";
$claims-document-information: "\eaab";
$claims-document-information-outline: "\eaac";
$claims-document-jpg: "\eaad";
$claims-document-jpg-outline: "\eaae";
$claims-document-loading2-outline: "\eaaf";
$claims-document-lock: "\eab0";
$claims-document-lock-outline: "\eab1";
$claims-document-missing: "\eab2";
$claims-document-missing-outline: "\eab3";
$claims-document-naopravu: "\eab4";
$claims-document-naopravu-outline: "\eab5";
$claims-document-not-acepted: "\eab6";
$claims-document-not-acepted-outline: "\eab7";
$claims-document-notify: "\eab8";
$claims-document-notify-outline: "\eab9";
$claims-document-okay: "\eaba";
$claims-document-okay-outline: "\eabb";
$claims-document-outline: "\eabc";
$claims-document-pdf: "\eabd";
$claims-document-png: "\eabe";
$claims-document-png-outline: "\eabf";
$claims-document-scan: "\eac0";
$claims-document-scan-outline: "\eac1";
$claims-document-search: "\eac2";
$claims-document-search-1: "\eac3";
$claims-document-size: "\eac4";
$claims-document-size-outline: "\eac5";
$claims-document-tiff: "\eac6";
$claims-document-tiff-outline: "\eac7";
$claims-document-transport: "\eac8";
$claims-document-transport-outline: "\eac9";
$claims-document-trash: "\eaca";
$claims-document-trash-outline: "\eacb";
$claims-document-upload: "\eacc";
$claims-document-upload-outline: "\eacd";
$claims-document-upload2: "\eace";
$claims-document-upload2-outline: "\eacf";
$claims-document-wait: "\ead0";
$claims-document-wait-outline: "\ead1";
$claims-document-wait2: "\ead2";
$claims-document-wait2-outline: "\ead3";
$claims-document-wait3: "\ead4";
$claims-document-wait3-outline: "\ead5";
$claims-document-warning: "\ead6";
$claims-document-warning-outline: "\ead7";
$claims-document-wishlist: "\ead8";
$claims-document-wishlist-notify: "\ead9";
$claims-document-wrong: "\eada";
$claims-document-wrong-outline: "\eadb";
$claims-documents-outline: "\eadc";
$claims-done-task: "\eadd";
$claims-done-task-circle: "\eade";
$claims-doprava: "\eadf";
$claims-doprava-outline: "\eae0";
$claims-dopravca-dpd: "\eae1";
$claims-dopravca-gls: "\eae2";
$claims-dopravca-gls-arrow: "\eae3";
$claims-dopravca-gls-circle: "\eae4";
$claims-dopravca-neznamy-outline: "\eae5";
$claims-dopravca-packeta: "\eae6";
$claims-dopravca-ppl: "\eae7";
$claims-dopravca-zasilkovna: "\eae8";
$claims-down-loading: "\eae9";
$claims-drag-horizontal: "\eaea";
$claims-drag-vertical: "\eaeb";
$claims-dropzone-info-icon: "\eaec";
$claims-dropzone-info-icon-bill: "\eaed";
$claims-dropzone-info-icon-phone: "\eaee";
$claims-dsquared2: "\eaef";
$claims-ean2: "\eaf0";
$claims-edit-text: "\eaf1";
$claims-edit-text-outline: "\eaf2";
$claims-elevator: "\eaf3";
$claims-enter: "\eaf4";
$claims-envelope-open: "\eaf5";
$claims-envelope-open-outline: "\eaf6";
$claims-envelope-reply: "\eaf7";
$claims-envelope-reply-outline: "\eaf8";
$claims-exporting: "\eaf9";
$claims-external-link: "\eafa";
$claims-external-link2: "\eafb";
$claims-filter: "\eafc";
$claims-filter-active: "\eafd";
$claims-filter-filled: "\eafe";
$claims-filter-filled-active: "\eaff";
$claims-forward-email: "\eb00";
$claims-forward-email-outline: "\eb01";
$claims-gant: "\eb02";
$claims-gant-icon: "\eb03";
$claims-gant-icon-invert: "\eb04";
$claims-goods-id: "\eb05";
$claims-goods-id-outline: "\eb06";
$claims-hidden: "\eb07";
$claims-hidden-outline: "\eb08";
$claims-hierarchy: "\eb09";
$claims-hierarchy-outline: "\eb0a";
$claims-import-document: "\eb0b";
$claims-import-document-outline: "\eb0c";
$claims-info: "\eb0d";
$claims-info-filled: "\eb0e";
$claims-input-autofill: "\eb0f";
$claims-input_image: "\eb10";
$claims-input_image_desktop: "\eb11";
$claims-input_image_mobile: "\eb12";
$claims-input_qr: "\eb13";
$claims-karl-head: "\eb14";
$claims-karl-head-invert: "\eb15";
$claims-karl1: "\eb16";
$claims-karl2: "\eb17";
$claims-la_martina: "\eb18";
$claims-language: "\eb19";
$claims-language-outline: "\eb1a";
$claims-lock-outline: "\eb1b";
$claims-locked: "\eb1c";
$claims-locked-outline: "\eb1d";
$claims-log-in: "\eb1e";
$claims-lokacia: "\eb1f";
$claims-lokacia-outline: "\eb20";
$claims-mailrecords: "\eb21";
$claims-mailrecords-outline: "\eb22";
$claims-maison_margiela: "\eb23";
$claims-manuel_ritz: "\eb24";
$claims-marek-bug: "\eb25";
$claims-marek-vymeni: "\eb26";
$claims-marni: "\eb27";
$claims-menu: "\eb28";
$claims-menu-circle: "\eb29";
$claims-menu-circle-outline: "\eb2a";
$claims-menu-maximize: "\eb2b";
$claims-menu-minimize: "\eb2c";
$claims-menu_option: "\eb2d";
$claims-menu_option-desktop: "\eb2e";
$claims-menu_option-tablet: "\eb2f";
$claims-menu_option2: "\eb30";
$claims-menu_option3: "\eb31";
$claims-menu_option4: "\eb32";
$claims-missoni: "\eb33";
$claims-mn: "\eb34";
$claims-mobile-menu: "\eb35";
$claims-mobile-menu-outline: "\eb36";
$claims-money: "\eb37";
$claims-moon: "\eb38";
$claims-move-claims-transport: "\eb39";
$claims-n_21: "\eb3a";
$claims-name1: "\eb3b";
$claims-name1-outline: "\eb3c";
$claims-nepreplacat: "\eb3d";
$claims-no-main-file-attached: "\eb3e";
$claims-no-main-file-chosen: "\eb3f";
$claims-no-main-file-chosen2: "\eb40";
$claims-no-photo: "\eb41";
$claims-no-support-file-attached: "\eb42";
$claims-not-acepted: "\eb43";
$claims-not-acepted-outline: "\eb44";
$claims-notify: "\eb45";
$claims-notify-outline: "\eb46";
$claims-odhlasit: "\eb47";
$claims-okay: "\eb48";
$claims-page: "\eb49";
$claims-page-outline: "\eb4a";
$claims-paperclip: "\eb4b";
$claims-paperclip-outline: "\eb4c";
$claims-parcel-history: "\eb4d";
$claims-partnership: "\eb4e";
$claims-partnership-outline: "\eb4f";
$claims-peak-performance: "\eb50";
$claims-peakperformance-logo: "\eb51";
$claims-pin: "\eb52";
$claims-pin-outline: "\eb53";
$claims-pm-credit-cards: "\eb54";
$claims-pm-money: "\eb55";
$claims-pm-nepreplacat: "\eb56";
$claims-pm-refuel-card: "\eb57";
$claims-pm-wire-transfer: "\eb58";
$claims-pm-zapocet: "\eb59";
$claims-predajna: "\eb5a";
$claims-predajna-outline: "\eb5b";
$claims-predvolena: "\eb5c";
$claims-predvolena-active: "\eb5d";
$claims-preview: "\eb5e";
$claims-preview-detail: "\eb5f";
$claims-printing: "\eb60";
$claims-printing-outline: "\eb61";
$claims-products-multiple: "\eb62";
$claims-products-multiple-outline: "\eb63";
$claims-products-quality: "\eb64";
$claims-products-wrong_quality-outline: "\eb65";
$claims-refresh: "\eb66";
$claims-refuel-card: "\eb67";
$claims-reload: "\eb68";
$claims-remove: "\eb69";
$claims-remove-outline: "\eb6a";
$claims-retail: "\eb6b";
$claims-rotate: "\eb6c";
$claims-sad: "\eb6d";
$claims-save: "\eb6e";
$claims-save-back: "\eb6f";
$claims-save-back-outline: "\eb70";
$claims-save-outline: "\eb71";
$claims-scan_ean: "\eb72";
$claims-scan_ean2: "\eb73";
$claims-scan_ean3: "\eb74";
$claims-scan_item2: "\eb75";
$claims-scan_qr: "\eb76";
$claims-scan_qr2: "\eb77";
$claims-scan_qr3: "\eb78";
$claims-scroll-up: "\eb79";
$claims-search: "\eb7a";
$claims-search-sort: "\eb7b";
$claims-sent-email: "\eb7c";
$claims-share: "\eb7d";
$claims-share-outline: "\eb7e";
$claims-side-option: "\eb7f";
$claims-state-archived: "\eb80";
$claims-state-nonarchived: "\eb81";
$claims-state-online: "\eb82";
$claims-state-pause: "\eb83";
$claims-state-unknown: "\eb84";
$claims-status-aproved: "\eb85";
$claims-status-denied: "\eb86";
$claims-status-dot: "\eb87";
$claims-status-okay: "\eb88";
$claims-status-okay-outline: "\eb89";
$claims-status-on-hold: "\eb8a";
$claims-status-pending: "\eb8b";
$claims-status-pending2: "\eb8c";
$claims-status-progres: "\eb8d";
$claims-status-revoked: "\eb8e";
$claims-status-stop: "\eb8f";
$claims-status-stop-outline: "\eb90";
$claims-status-waiting: "\eb91";
$claims-status-waiting-user: "\eb92";
$claims-status-write: "\eb93";
$claims-sun: "\eb94";
$claims-supplier: "\eb95";
$claims-supplier-outline: "\eb96";
$claims-tap: "\eb97";
$claims-template: "\eb98";
$claims-template-outline: "\eb99";
$claims-text-editor: "\eb9a";
$claims-th-waiting: "\eb9b";
$claims-th-waiting2: "\eb9c";
$claims-thumbnail: "\eb9d";
$claims-tickets: "\eb9e";
$claims-tickets-outline: "\eb9f";
$claims-tiki-tiki: "\eba0";
$claims-tilde: "\eba1";
$claims-timeupdate: "\eba2";
$claims-transfer-data: "\eba3";
$claims-transport-hub-priority-placeholder: "\eba4";
$claims-trash: "\eba5";
$claims-trash-outline: "\eba6";
$claims-trussardi: "\eba7";
$claims-u-t-default: "\eba8";
$claims-u-t-star: "\eba9";
$claims-up-loading: "\ebaa";
$claims-upload: "\ebab";
$claims-user: "\ebac";
$claims-user-outline: "\ebad";
$claims-utilities: "\ebae";
$claims-v_home: "\ebaf";
$claims-v_home-outline: "\ebb0";
$claims-verify: "\ebb1";
$claims-verify-outline: "\ebb2";
$claims-vermont: "\ebb3";
$claims-vermont-brand: "\ebb4";
$claims-vermont-brand-logo: "\ebb5";
$claims-vermont-home: "\ebb6";
$claims-vermont-intranet-pass: "\ebb7";
$claims-vermont-ucko: "\ebb8";
$claims-vermont_invert: "\ebb9";
$claims-visible: "\ebba";
$claims-visible-outline: "\ebbb";
$claims-vratky: "\ebbc";
$claims-warehouse: "\ebbd";
$claims-warehouse-outline: "\ebbe";
$claims-wire-transfer: "\ebbf";
$claims-woolrich: "\ebc0";
$claims-woolrich-icon: "\ebc1";
$claims-wup-wup: "\ebc2";
$claims-zapocet: "\ebc3";

%claims {
  display: inline-block;
  font-family: "claims" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.claims {
  @extend %claims;
}

.claims-403::before {
  content: "\ea01";
}
.claims-404::before {
  content: "\ea02";
}
.claims-404-2::before {
  content: "\ea03";
}
.claims-503::before {
  content: "\ea04";
}
.claims-a1::before {
  content: "\ea05";
}
.claims-a2::before {
  content: "\ea06";
}
.claims-a3::before {
  content: "\ea07";
}
.claims-a4::before {
  content: "\ea08";
}
.claims-acepted::before {
  content: "\ea09";
}
.claims-acepted-outline::before {
  content: "\ea0a";
}
.claims-action-info::before {
  content: "\ea0b";
}
.claims-action-print::before {
  content: "\ea0c";
}
.claims-addition::before {
  content: "\ea0d";
}
.claims-addition-outline::before {
  content: "\ea0e";
}
.claims-addition-simple::before {
  content: "\ea0f";
}
.claims-alert::before {
  content: "\ea10";
}
.claims-alert-outline::before {
  content: "\ea11";
}
.claims-alert-warning::before {
  content: "\ea12";
}
.claims-alert-warning-outline::before {
  content: "\ea13";
}
.claims-archive-all::before {
  content: "\ea14";
}
.claims-archive-all-outline::before {
  content: "\ea15";
}
.claims-arrow7::before {
  content: "\ea16";
}
.claims-article::before {
  content: "\ea17";
}
.claims-article-outline::before {
  content: "\ea18";
}
.claims-articles::before {
  content: "\ea19";
}
.claims-articles-outline::before {
  content: "\ea1a";
}
.claims-asign-task::before {
  content: "\ea1b";
}
.claims-asign-task-circle::before {
  content: "\ea1c";
}
.claims-avatar::before {
  content: "\ea1d";
}
.claims-avatar-overlay::before {
  content: "\ea1e";
}
.claims-back::before {
  content: "\ea1f";
}
.claims-blocek::before {
  content: "\ea20";
}
.claims-blocek-add::before {
  content: "\ea21";
}
.claims-blocek-add-outline::before {
  content: "\ea22";
}
.claims-blocek-outline::before {
  content: "\ea23";
}
.claims-blocek-photo::before {
  content: "\ea24";
}
.claims-blocek-photo-outline::before {
  content: "\ea25";
}
.claims-bm::before {
  content: "\ea26";
}
.claims-bm-active::before {
  content: "\ea27";
}
.claims-bm-active-outline::before {
  content: "\ea28";
}
.claims-bm-add::before {
  content: "\ea29";
}
.claims-bm-add-outline::before {
  content: "\ea2a";
}
.claims-bm-outline::before {
  content: "\ea2b";
}
.claims-bookmark::before {
  content: "\ea2c";
}
.claims-bookmark-add::before {
  content: "\ea2d";
}
.claims-bookmark-add-outline::before {
  content: "\ea2e";
}
.claims-bookmark-outline::before {
  content: "\ea2f";
}
.claims-bookmark2-active::before {
  content: "\ea30";
}
.claims-bookmark2-active-outline::before {
  content: "\ea31";
}
.claims-bookmark2-add::before {
  content: "\ea32";
}
.claims-bookmark2-add-1::before {
  content: "\ea33";
}
.claims-bookmark2-add-outline::before {
  content: "\ea34";
}
.claims-bookmark2-add-outline-1::before {
  content: "\ea35";
}
.claims-calendar::before {
  content: "\ea36";
}
.claims-calendar-add::before {
  content: "\ea37";
}
.claims-calendar-add-outline::before {
  content: "\ea38";
}
.claims-calendar-check::before {
  content: "\ea39";
}
.claims-calendar-check-outline::before {
  content: "\ea3a";
}
.claims-calendar-due::before {
  content: "\ea3b";
}
.claims-calendar-due-outline::before {
  content: "\ea3c";
}
.claims-calendar-outline::before {
  content: "\ea3d";
}
.claims-calendar-urgent::before {
  content: "\ea3e";
}
.claims-calendar-urgent-outline::before {
  content: "\ea3f";
}
.claims-calendar-vat::before {
  content: "\ea40";
}
.claims-calendar-vat-outline::before {
  content: "\ea41";
}
.claims-camel_active::before {
  content: "\ea42";
}
.claims-camelactive-logo::before {
  content: "\ea43";
}
.claims-camera::before {
  content: "\ea44";
}
.claims-cas::before {
  content: "\ea45";
}
.claims-center::before {
  content: "\ea46";
}
.claims-center-outline::before {
  content: "\ea47";
}
.claims-change::before {
  content: "\ea48";
}
.claims-change-parcel::before {
  content: "\ea49";
}
.claims-chevron-down::before {
  content: "\ea4a";
}
.claims-chevron-up::before {
  content: "\ea4b";
}
.claims-choose-items::before {
  content: "\ea4c";
}
.claims-claims-dodavatelska::before {
  content: "\ea4d";
}
.claims-claims-eshop::before {
  content: "\ea4e";
}
.claims-claims-zakaznik::before {
  content: "\ea4f";
}
.claims-clock::before {
  content: "\ea50";
}
.claims-clock-outline::before {
  content: "\ea51";
}
.claims-close::before {
  content: "\ea52";
}
.claims-copyclipboardsend::before {
  content: "\ea53";
}
.claims-creator::before {
  content: "\ea54";
}
.claims-creator-outline::before {
  content: "\ea55";
}
.claims-credit-cards::before {
  content: "\ea56";
}
.claims-customer::before {
  content: "\ea57";
}
.claims-d-t-air-transport::before {
  content: "\ea58";
}
.claims-d-t-car-wash::before {
  content: "\ea59";
}
.claims-d-t-carpet::before {
  content: "\ea5a";
}
.claims-d-t-decoration::before {
  content: "\ea5b";
}
.claims-d-t-decoration-supplies::before {
  content: "\ea5c";
}
.claims-d-t-e-air-transport::before {
  content: "\ea5d";
}
.claims-d-t-e-car-wash::before {
  content: "\ea5e";
}
.claims-d-t-e-carpet::before {
  content: "\ea5f";
}
.claims-d-t-e-decoration::before {
  content: "\ea60";
}
.claims-d-t-e-decoration-supplies::before {
  content: "\ea61";
}
.claims-d-t-e-first-aid-kit::before {
  content: "\ea62";
}
.claims-d-t-e-flowers::before {
  content: "\ea63";
}
.claims-d-t-e-flowers2::before {
  content: "\ea64";
}
.claims-d-t-e-gas-station::before {
  content: "\ea65";
}
.claims-d-t-e-highway::before {
  content: "\ea66";
}
.claims-d-t-e-hotel::before {
  content: "\ea67";
}
.claims-d-t-e-hotel2::before {
  content: "\ea68";
}
.claims-d-t-e-lamp::before {
  content: "\ea69";
}
.claims-d-t-e-parking::before {
  content: "\ea6a";
}
.claims-d-t-e-postage::before {
  content: "\ea6b";
}
.claims-d-t-e-repre::before {
  content: "\ea6c";
}
.claims-d-t-e-repre2::before {
  content: "\ea6d";
}
.claims-d-t-e-supplies-car::before {
  content: "\ea6e";
}
.claims-d-t-e-supplies-garden::before {
  content: "\ea6f";
}
.claims-d-t-e-supplies-garden2::before {
  content: "\ea70";
}
.claims-d-t-e-supplies-it::before {
  content: "\ea71";
}
.claims-d-t-e-supplies-maintance::before {
  content: "\ea72";
}
.claims-d-t-e-supplies-rest::before {
  content: "\ea73";
}
.claims-d-t-e-taxi::before {
  content: "\ea74";
}
.claims-d-t-e-travel-ticket::before {
  content: "\ea75";
}
.claims-d-t-first-aid-kit::before {
  content: "\ea76";
}
.claims-d-t-flowers::before {
  content: "\ea77";
}
.claims-d-t-flowers2::before {
  content: "\ea78";
}
.claims-d-t-gas-station::before {
  content: "\ea79";
}
.claims-d-t-highway::before {
  content: "\ea7a";
}
.claims-d-t-hotel::before {
  content: "\ea7b";
}
.claims-d-t-hotel2::before {
  content: "\ea7c";
}
.claims-d-t-lamp::before {
  content: "\ea7d";
}
.claims-d-t-parking::before {
  content: "\ea7e";
}
.claims-d-t-postage::before {
  content: "\ea7f";
}
.claims-d-t-repre::before {
  content: "\ea80";
}
.claims-d-t-repre2::before {
  content: "\ea81";
}
.claims-d-t-supplies-car::before {
  content: "\ea82";
}
.claims-d-t-supplies-garden::before {
  content: "\ea83";
}
.claims-d-t-supplies-garden2::before {
  content: "\ea84";
}
.claims-d-t-supplies-it::before {
  content: "\ea85";
}
.claims-d-t-supplies-maintance::before {
  content: "\ea86";
}
.claims-d-t-supplies-rest::before {
  content: "\ea87";
}
.claims-d-t-taxi::before {
  content: "\ea88";
}
.claims-d-t-travel-ticket::before {
  content: "\ea89";
}
.claims-dash::before {
  content: "\ea8a";
}
.claims-dash-outline::before {
  content: "\ea8b";
}
.claims-datum::before {
  content: "\ea8c";
}
.claims-delete-bookmark::before {
  content: "\ea8d";
}
.claims-delete-bookmark-success::before {
  content: "\ea8e";
}
.claims-department::before {
  content: "\ea8f";
}
.claims-department-outline::before {
  content: "\ea90";
}
.claims-detail::before {
  content: "\ea91";
}
.claims-diesel-icon::before {
  content: "\ea92";
}
.claims-dnky::before {
  content: "\ea93";
}
.claims-dnky-icon::before {
  content: "\ea94";
}
.claims-document::before {
  content: "\ea95";
}
.claims-document-acepted::before {
  content: "\ea96";
}
.claims-document-acepted-outline::before {
  content: "\ea97";
}
.claims-document-add::before {
  content: "\ea98";
}
.claims-document-add-outline::before {
  content: "\ea99";
}
.claims-document-attached::before {
  content: "\ea9a";
}
.claims-document-attached-outline::before {
  content: "\ea9b";
}
.claims-document-attachment::before {
  content: "\ea9c";
}
.claims-document-attachment-outline::before {
  content: "\ea9d";
}
.claims-document-blank::before {
  content: "\ea9e";
}
.claims-document-camera::before {
  content: "\ea9f";
}
.claims-document-camera-outline::before {
  content: "\eaa0";
}
.claims-document-doc::before {
  content: "\eaa1";
}
.claims-document-doc-outline::before {
  content: "\eaa2";
}
.claims-document-extraorder::before {
  content: "\eaa3";
}
.claims-document-extraorder-outline::before {
  content: "\eaa4";
}
.claims-document-heic::before {
  content: "\eaa5";
}
.claims-document-heic-outline::before {
  content: "\eaa6";
}
.claims-document-hold::before {
  content: "\eaa7";
}
.claims-document-hold-outline::before {
  content: "\eaa8";
}
.claims-document-image::before {
  content: "\eaa9";
}
.claims-document-image-outline::before {
  content: "\eaaa";
}
.claims-document-information::before {
  content: "\eaab";
}
.claims-document-information-outline::before {
  content: "\eaac";
}
.claims-document-jpg::before {
  content: "\eaad";
}
.claims-document-jpg-outline::before {
  content: "\eaae";
}
.claims-document-loading2-outline::before {
  content: "\eaaf";
}
.claims-document-lock::before {
  content: "\eab0";
}
.claims-document-lock-outline::before {
  content: "\eab1";
}
.claims-document-missing::before {
  content: "\eab2";
}
.claims-document-missing-outline::before {
  content: "\eab3";
}
.claims-document-naopravu::before {
  content: "\eab4";
}
.claims-document-naopravu-outline::before {
  content: "\eab5";
}
.claims-document-not-acepted::before {
  content: "\eab6";
}
.claims-document-not-acepted-outline::before {
  content: "\eab7";
}
.claims-document-notify::before {
  content: "\eab8";
}
.claims-document-notify-outline::before {
  content: "\eab9";
}
.claims-document-okay::before {
  content: "\eaba";
}
.claims-document-okay-outline::before {
  content: "\eabb";
}
.claims-document-outline::before {
  content: "\eabc";
}
.claims-document-pdf::before {
  content: "\eabd";
}
.claims-document-png::before {
  content: "\eabe";
}
.claims-document-png-outline::before {
  content: "\eabf";
}
.claims-document-scan::before {
  content: "\eac0";
}
.claims-document-scan-outline::before {
  content: "\eac1";
}
.claims-document-search::before {
  content: "\eac2";
}
.claims-document-search-1::before {
  content: "\eac3";
}
.claims-document-size::before {
  content: "\eac4";
}
.claims-document-size-outline::before {
  content: "\eac5";
}
.claims-document-tiff::before {
  content: "\eac6";
}
.claims-document-tiff-outline::before {
  content: "\eac7";
}
.claims-document-transport::before {
  content: "\eac8";
}
.claims-document-transport-outline::before {
  content: "\eac9";
}
.claims-document-trash::before {
  content: "\eaca";
}
.claims-document-trash-outline::before {
  content: "\eacb";
}
.claims-document-upload::before {
  content: "\eacc";
}
.claims-document-upload-outline::before {
  content: "\eacd";
}
.claims-document-upload2::before {
  content: "\eace";
}
.claims-document-upload2-outline::before {
  content: "\eacf";
}
.claims-document-wait::before {
  content: "\ead0";
}
.claims-document-wait-outline::before {
  content: "\ead1";
}
.claims-document-wait2::before {
  content: "\ead2";
}
.claims-document-wait2-outline::before {
  content: "\ead3";
}
.claims-document-wait3::before {
  content: "\ead4";
}
.claims-document-wait3-outline::before {
  content: "\ead5";
}
.claims-document-warning::before {
  content: "\ead6";
}
.claims-document-warning-outline::before {
  content: "\ead7";
}
.claims-document-wishlist::before {
  content: "\ead8";
}
.claims-document-wishlist-notify::before {
  content: "\ead9";
}
.claims-document-wrong::before {
  content: "\eada";
}
.claims-document-wrong-outline::before {
  content: "\eadb";
}
.claims-documents-outline::before {
  content: "\eadc";
}
.claims-done-task::before {
  content: "\eadd";
}
.claims-done-task-circle::before {
  content: "\eade";
}
.claims-doprava::before {
  content: "\eadf";
}
.claims-doprava-outline::before {
  content: "\eae0";
}
.claims-dopravca-dpd::before {
  content: "\eae1";
}
.claims-dopravca-gls::before {
  content: "\eae2";
}
.claims-dopravca-gls-arrow::before {
  content: "\eae3";
}
.claims-dopravca-gls-circle::before {
  content: "\eae4";
}
.claims-dopravca-neznamy-outline::before {
  content: "\eae5";
}
.claims-dopravca-packeta::before {
  content: "\eae6";
}
.claims-dopravca-ppl::before {
  content: "\eae7";
}
.claims-dopravca-zasilkovna::before {
  content: "\eae8";
}
.claims-down-loading::before {
  content: "\eae9";
}
.claims-drag-horizontal::before {
  content: "\eaea";
}
.claims-drag-vertical::before {
  content: "\eaeb";
}
.claims-dropzone-info-icon::before {
  content: "\eaec";
}
.claims-dropzone-info-icon-bill::before {
  content: "\eaed";
}
.claims-dropzone-info-icon-phone::before {
  content: "\eaee";
}
.claims-dsquared2::before {
  content: "\eaef";
}
.claims-ean2::before {
  content: "\eaf0";
}
.claims-edit-text::before {
  content: "\eaf1";
}
.claims-edit-text-outline::before {
  content: "\eaf2";
}
.claims-elevator::before {
  content: "\eaf3";
}
.claims-enter::before {
  content: "\eaf4";
}
.claims-envelope-open::before {
  content: "\eaf5";
}
.claims-envelope-open-outline::before {
  content: "\eaf6";
}
.claims-envelope-reply::before {
  content: "\eaf7";
}
.claims-envelope-reply-outline::before {
  content: "\eaf8";
}
.claims-exporting::before {
  content: "\eaf9";
}
.claims-external-link::before {
  content: "\eafa";
}
.claims-external-link2::before {
  content: "\eafb";
}
.claims-filter::before {
  content: "\eafc";
}
.claims-filter-active::before {
  content: "\eafd";
}
.claims-filter-filled::before {
  content: "\eafe";
}
.claims-filter-filled-active::before {
  content: "\eaff";
}
.claims-forward-email::before {
  content: "\eb00";
}
.claims-forward-email-outline::before {
  content: "\eb01";
}
.claims-gant::before {
  content: "\eb02";
}
.claims-gant-icon::before {
  content: "\eb03";
}
.claims-gant-icon-invert::before {
  content: "\eb04";
}
.claims-goods-id::before {
  content: "\eb05";
}
.claims-goods-id-outline::before {
  content: "\eb06";
}
.claims-hidden::before {
  content: "\eb07";
}
.claims-hidden-outline::before {
  content: "\eb08";
}
.claims-hierarchy::before {
  content: "\eb09";
}
.claims-hierarchy-outline::before {
  content: "\eb0a";
}
.claims-import-document::before {
  content: "\eb0b";
}
.claims-import-document-outline::before {
  content: "\eb0c";
}
.claims-info::before {
  content: "\eb0d";
}
.claims-info-filled::before {
  content: "\eb0e";
}
.claims-input-autofill::before {
  content: "\eb0f";
}
.claims-input_image::before {
  content: "\eb10";
}
.claims-input_image_desktop::before {
  content: "\eb11";
}
.claims-input_image_mobile::before {
  content: "\eb12";
}
.claims-input_qr::before {
  content: "\eb13";
}
.claims-karl-head::before {
  content: "\eb14";
}
.claims-karl-head-invert::before {
  content: "\eb15";
}
.claims-karl1::before {
  content: "\eb16";
}
.claims-karl2::before {
  content: "\eb17";
}
.claims-la_martina::before {
  content: "\eb18";
}
.claims-language::before {
  content: "\eb19";
}
.claims-language-outline::before {
  content: "\eb1a";
}
.claims-lock-outline::before {
  content: "\eb1b";
}
.claims-locked::before {
  content: "\eb1c";
}
.claims-locked-outline::before {
  content: "\eb1d";
}
.claims-log-in::before {
  content: "\eb1e";
}
.claims-lokacia::before {
  content: "\eb1f";
}
.claims-lokacia-outline::before {
  content: "\eb20";
}
.claims-mailrecords::before {
  content: "\eb21";
}
.claims-mailrecords-outline::before {
  content: "\eb22";
}
.claims-maison_margiela::before {
  content: "\eb23";
}
.claims-manuel_ritz::before {
  content: "\eb24";
}
.claims-marek-bug::before {
  content: "\eb25";
}
.claims-marek-vymeni::before {
  content: "\eb26";
}
.claims-marni::before {
  content: "\eb27";
}
.claims-menu::before {
  content: "\eb28";
}
.claims-menu-circle::before {
  content: "\eb29";
}
.claims-menu-circle-outline::before {
  content: "\eb2a";
}
.claims-menu-maximize::before {
  content: "\eb2b";
}
.claims-menu-minimize::before {
  content: "\eb2c";
}
.claims-menu_option::before {
  content: "\eb2d";
}
.claims-menu_option-desktop::before {
  content: "\eb2e";
}
.claims-menu_option-tablet::before {
  content: "\eb2f";
}
.claims-menu_option2::before {
  content: "\eb30";
}
.claims-menu_option3::before {
  content: "\eb31";
}
.claims-menu_option4::before {
  content: "\eb32";
}
.claims-missoni::before {
  content: "\eb33";
}
.claims-mn::before {
  content: "\eb34";
}
.claims-mobile-menu::before {
  content: "\eb35";
}
.claims-mobile-menu-outline::before {
  content: "\eb36";
}
.claims-money::before {
  content: "\eb37";
}
.claims-moon::before {
  content: "\eb38";
}
.claims-move-claims-transport::before {
  content: "\eb39";
}
.claims-n_21::before {
  content: "\eb3a";
}
.claims-name1::before {
  content: "\eb3b";
}
.claims-name1-outline::before {
  content: "\eb3c";
}
.claims-nepreplacat::before {
  content: "\eb3d";
}
.claims-no-main-file-attached::before {
  content: "\eb3e";
}
.claims-no-main-file-chosen::before {
  content: "\eb3f";
}
.claims-no-main-file-chosen2::before {
  content: "\eb40";
}
.claims-no-photo::before {
  content: "\eb41";
}
.claims-no-support-file-attached::before {
  content: "\eb42";
}
.claims-not-acepted::before {
  content: "\eb43";
}
.claims-not-acepted-outline::before {
  content: "\eb44";
}
.claims-notify::before {
  content: "\eb45";
}
.claims-notify-outline::before {
  content: "\eb46";
}
.claims-odhlasit::before {
  content: "\eb47";
}
.claims-okay::before {
  content: "\eb48";
}
.claims-page::before {
  content: "\eb49";
}
.claims-page-outline::before {
  content: "\eb4a";
}
.claims-paperclip::before {
  content: "\eb4b";
}
.claims-paperclip-outline::before {
  content: "\eb4c";
}
.claims-parcel-history::before {
  content: "\eb4d";
}
.claims-partnership::before {
  content: "\eb4e";
}
.claims-partnership-outline::before {
  content: "\eb4f";
}
.claims-peak-performance::before {
  content: "\eb50";
}
.claims-peakperformance-logo::before {
  content: "\eb51";
}
.claims-pin::before {
  content: "\eb52";
}
.claims-pin-outline::before {
  content: "\eb53";
}
.claims-pm-credit-cards::before {
  content: "\eb54";
}
.claims-pm-money::before {
  content: "\eb55";
}
.claims-pm-nepreplacat::before {
  content: "\eb56";
}
.claims-pm-refuel-card::before {
  content: "\eb57";
}
.claims-pm-wire-transfer::before {
  content: "\eb58";
}
.claims-pm-zapocet::before {
  content: "\eb59";
}
.claims-predajna::before {
  content: "\eb5a";
}
.claims-predajna-outline::before {
  content: "\eb5b";
}
.claims-predvolena::before {
  content: "\eb5c";
}
.claims-predvolena-active::before {
  content: "\eb5d";
}
.claims-preview::before {
  content: "\eb5e";
}
.claims-preview-detail::before {
  content: "\eb5f";
}
.claims-printing::before {
  content: "\eb60";
}
.claims-printing-outline::before {
  content: "\eb61";
}
.claims-products-multiple::before {
  content: "\eb62";
}
.claims-products-multiple-outline::before {
  content: "\eb63";
}
.claims-products-quality::before {
  content: "\eb64";
}
.claims-products-wrong_quality-outline::before {
  content: "\eb65";
}
.claims-refresh::before {
  content: "\eb66";
}
.claims-refuel-card::before {
  content: "\eb67";
}
.claims-reload::before {
  content: "\eb68";
}
.claims-remove::before {
  content: "\eb69";
}
.claims-remove-outline::before {
  content: "\eb6a";
}
.claims-retail::before {
  content: "\eb6b";
}
.claims-rotate::before {
  content: "\eb6c";
}
.claims-sad::before {
  content: "\eb6d";
}
.claims-save::before {
  content: "\eb6e";
}
.claims-save-back::before {
  content: "\eb6f";
}
.claims-save-back-outline::before {
  content: "\eb70";
}
.claims-save-outline::before {
  content: "\eb71";
}
.claims-scan_ean::before {
  content: "\eb72";
}
.claims-scan_ean2::before {
  content: "\eb73";
}
.claims-scan_ean3::before {
  content: "\eb74";
}
.claims-scan_item2::before {
  content: "\eb75";
}
.claims-scan_qr::before {
  content: "\eb76";
}
.claims-scan_qr2::before {
  content: "\eb77";
}
.claims-scan_qr3::before {
  content: "\eb78";
}
.claims-scroll-up::before {
  content: "\eb79";
}
.claims-search::before {
  content: "\eb7a";
}
.claims-search-sort::before {
  content: "\eb7b";
}
.claims-sent-email::before {
  content: "\eb7c";
}
.claims-share::before {
  content: "\eb7d";
}
.claims-share-outline::before {
  content: "\eb7e";
}
.claims-side-option::before {
  content: "\eb7f";
}
.claims-state-archived::before {
  content: "\eb80";
}
.claims-state-nonarchived::before {
  content: "\eb81";
}
.claims-state-online::before {
  content: "\eb82";
}
.claims-state-pause::before {
  content: "\eb83";
}
.claims-state-unknown::before {
  content: "\eb84";
}
.claims-status-aproved::before {
  content: "\eb85";
}
.claims-status-denied::before {
  content: "\eb86";
}
.claims-status-dot::before {
  content: "\eb87";
}
.claims-status-okay::before {
  content: "\eb88";
}
.claims-status-okay-outline::before {
  content: "\eb89";
}
.claims-status-on-hold::before {
  content: "\eb8a";
}
.claims-status-pending::before {
  content: "\eb8b";
}
.claims-status-pending2::before {
  content: "\eb8c";
}
.claims-status-progres::before {
  content: "\eb8d";
}
.claims-status-revoked::before {
  content: "\eb8e";
}
.claims-status-stop::before {
  content: "\eb8f";
}
.claims-status-stop-outline::before {
  content: "\eb90";
}
.claims-status-waiting::before {
  content: "\eb91";
}
.claims-status-waiting-user::before {
  content: "\eb92";
}
.claims-status-write::before {
  content: "\eb93";
}
.claims-sun::before {
  content: "\eb94";
}
.claims-supplier::before {
  content: "\eb95";
}
.claims-supplier-outline::before {
  content: "\eb96";
}
.claims-tap::before {
  content: "\eb97";
}
.claims-template::before {
  content: "\eb98";
}
.claims-template-outline::before {
  content: "\eb99";
}
.claims-text-editor::before {
  content: "\eb9a";
}
.claims-th-waiting::before {
  content: "\eb9b";
}
.claims-th-waiting2::before {
  content: "\eb9c";
}
.claims-thumbnail::before {
  content: "\eb9d";
}
.claims-tickets::before {
  content: "\eb9e";
}
.claims-tickets-outline::before {
  content: "\eb9f";
}
.claims-tiki-tiki::before {
  content: "\eba0";
}
.claims-tilde::before {
  content: "\eba1";
}
.claims-timeupdate::before {
  content: "\eba2";
}
.claims-transfer-data::before {
  content: "\eba3";
}
.claims-transport-hub-priority-placeholder::before {
  content: "\eba4";
}
.claims-trash::before {
  content: "\eba5";
}
.claims-trash-outline::before {
  content: "\eba6";
}
.claims-trussardi::before {
  content: "\eba7";
}
.claims-u-t-default::before {
  content: "\eba8";
}
.claims-u-t-star::before {
  content: "\eba9";
}
.claims-up-loading::before {
  content: "\ebaa";
}
.claims-upload::before {
  content: "\ebab";
}
.claims-user::before {
  content: "\ebac";
}
.claims-user-outline::before {
  content: "\ebad";
}
.claims-utilities::before {
  content: "\ebae";
}
.claims-v_home::before {
  content: "\ebaf";
}
.claims-v_home-outline::before {
  content: "\ebb0";
}
.claims-verify::before {
  content: "\ebb1";
}
.claims-verify-outline::before {
  content: "\ebb2";
}
.claims-vermont::before {
  content: "\ebb3";
}
.claims-vermont-brand::before {
  content: "\ebb4";
}
.claims-vermont-brand-logo::before {
  content: "\ebb5";
}
.claims-vermont-home::before {
  content: "\ebb6";
}
.claims-vermont-intranet-pass::before {
  content: "\ebb7";
}
.claims-vermont-ucko::before {
  content: "\ebb8";
}
.claims-vermont_invert::before {
  content: "\ebb9";
}
.claims-visible::before {
  content: "\ebba";
}
.claims-visible-outline::before {
  content: "\ebbb";
}
.claims-vratky::before {
  content: "\ebbc";
}
.claims-warehouse::before {
  content: "\ebbd";
}
.claims-warehouse-outline::before {
  content: "\ebbe";
}
.claims-wire-transfer::before {
  content: "\ebbf";
}
.claims-woolrich::before {
  content: "\ebc0";
}
.claims-woolrich-icon::before {
  content: "\ebc1";
}
.claims-wup-wup::before {
  content: "\ebc2";
}
.claims-zapocet::before {
  content: "\ebc3";
}
