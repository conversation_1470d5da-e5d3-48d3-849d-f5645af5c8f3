:root {
    --nunito-sans-aalt: "aalt" off;
    --nunito-sans-case: "case" off;
    --nunito-sans-dlig: "dlig" off;
    --nunito-sans-dnom: "dnom" off;
    --nunito-sans-frac: "frac" off;
    --nunito-sans-numr: "numr" off;
    --nunito-sans-onum: "onum" off;
    --nunito-sans-ordn: "ordn" off;
    --nunito-sans-sinf: "sinf" off;
    --nunito-sans-ss01: "ss01" off;
    --nunito-sans-ss02: "ss02" off;
    --nunito-sans-subs: "subs" off;
    --nunito-sans-sups: "sups" off;
    --bs-font-sans-serif: "nunito-sans", system-ui, roboto, sans-serif;
    --bs-body-font-family: "nunito-sans", Verdana, sans-serif;
    --heading-font: "nunito-sans", <PERSON>erd<PERSON>, serif;
}

/* Nunito-sans Layout options*/
.nunito-sans-aalt {
    --nunito-sans-aalt: "aalt" on;
}

.nunito-sans-case {
    --nunito-sans-case: "case" on;
}

.nunito-sans-dlig {
    --nunito-sans-dlig: "dlig" on;
}


@supports (font-variant-ligatures: discretionary-ligatures) {
    .nunito-sans-dlig {
        --nunito-sans-dlig: "____";
        font-variant-ligatures: discretionary-ligatures;
    }
}

.nunito-sans-dnom {
    --nunito-sans-dnom: "dnom" on;
}

.nunito-sans-frac {
    --nunito-sans-frac: "frac" on;
}

@supports (font-variant-numeric: diagonal-fractions) {
    .nunito-sans-frac {
        --nunito-sans-frac: "____";
        font-variant-numeric: diagonal-fractions;
    }
}

.nunito-sans-numr {
    --nunito-sans-numr: "numr" on;
}

.nunito-sans-onum {
    --nunito-sans-onum: "onum" on;
}

@supports (font-variant-numeric: oldstyle-nums) {
    .nunito-sans-onum {
        --nunito-sans-onum: "____";
        font-variant-numeric: oldstyle-nums;
    }
}

.nunito-sans-ordn {
    --nunito-sans-ordn: "ordn" on;
}

@supports (font-variant-numeric: ordinal) {
    .nunito-sans-ordn {
        --nunito-sans-ordn: "____";
        font-variant-numeric: ordinal;
    }
}

.nunito-sans-sinf {
    --nunito-sans-sinf: "sinf" on;
}

.nunito-sans-ss01 {
    --nunito-sans-ss01: "ss01" on;
}

.nunito-sans-ss02 {
    --nunito-sans-ss02: "ss02" on;
}

.nunito-sans-subs {
    --nunito-sans-subs: "subs" on;
}

@supports (font-variant-position: sub) {
    .nunito-sans-subs {
        --nunito-sans-subs: "____";
        font-variant-position: sub;
    }
}

.nunito-sans-sups {
    --nunito-sans-sups: "sups" on;
}

/* Apply current state of all custom properties whenever a class is being applied */
.nunito-sans-aalt, .nunito-sans-case, .nunito-sans-dlig, .nunito-sans-dnom, .nunito-sans-frac, .nunito-sans-numr, .nunito-sans-onum, .nunito-sans-ordn, .nunito-sans-sinf, .nunito-sans-ss01, .nunito-sans-ss02, .nunito-sans-subs, .nunito-sans-sups {
    font-feature-settings: var(--nunito-sans-aalt), var(--nunito-sans-case), var(--nunito-sans-dlig), var(--nunito-sans-dnom), var(--nunito-sans-frac), var(--nunito-sans-numr), var(--nunito-sans-onum), var(--nunito-sans-ordn), var(--nunito-sans-sinf), var(--nunito-sans-ss01), var(--nunito-sans-ss02), var(--nunito-sans-subs), var(--nunito-sans-sups);
}

/* Variable instances Nunito-sans*/
.nunito-sans-extralight {
    font-variation-settings: "wght" 200, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-light {
    font-variation-settings: "wght" 300, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-regular {
    font-variation-settings: "wght" 400, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-medium {
    font-variation-settings: "wght" 500, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;

}

.nunito-sans-semibold {
    font-variation-settings: "wght" 600, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-bold {
    font-variation-settings: "wght" 700, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-extrabold {
    font-variation-settings: "wght" 800, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}

.nunito-sans-black {
    font-variation-settings: "wght" 900, "wdth" 100, "opsz" 12, "YTLC" 500;
    letter-spacing: -0.04rem;
}