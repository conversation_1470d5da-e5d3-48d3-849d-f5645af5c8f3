$hrms-acc: "\ea01";
$hrms-acepted: "\ea02";
$hrms-acepted-circle: "\ea03";
$hrms-acepted-circle-outline: "\ea04";
$hrms-acepted-outline: "\ea05";
$hrms-ad: "\ea06";
$hrms-add-circle: "\ea07";
$hrms-add-circle-outline: "\ea08";
$hrms-alert: "\ea09";
$hrms-alert-outline: "\ea0a";
$hrms-alert-warning: "\ea0b";
$hrms-alert-warning-outline: "\ea0c";
$hrms-all-column: "\ea0d";
$hrms-all-row: "\ea0e";
$hrms-appropriate: "\ea0f";
$hrms-appropriate-outline: "\ea10";
$hrms-archive: "\ea11";
$hrms-archive-outline: "\ea12";
$hrms-arr-down: "\ea13";
$hrms-arr-left: "\ea14";
$hrms-arr-right: "\ea15";
$hrms-arr-up: "\ea16";
$hrms-arrow-circle-down: "\ea17";
$hrms-arrow-circle-down-outline: "\ea18";
$hrms-arrow-down: "\ea19";
$hrms-arrow-down-1: "\ea1a";
$hrms-arrow-left: "\ea1b";
$hrms-arrow-right: "\ea1c";
$hrms-asign-task: "\ea1d";
$hrms-asign-task-circle: "\ea1e";
$hrms-asign-task-circle-outline: "\ea1f";
$hrms-back: "\ea20";
$hrms-back-outline: "\ea21";
$hrms-bm: "\ea22";
$hrms-bm-1: "\ea23";
$hrms-bm-active: "\ea24";
$hrms-bm-active-outline: "\ea25";
$hrms-bm-add: "\ea26";
$hrms-bm-add-outline: "\ea27";
$hrms-bm-outline: "\ea28";
$hrms-bonus: "\ea29";
$hrms-bonus-all: "\ea2a";
$hrms-bonus-all-outline: "\ea2b";
$hrms-bonus-outline: "\ea2c";
$hrms-bookmark: "\ea2d";
$hrms-bookmark-add: "\ea2e";
$hrms-bookmark-add-outline: "\ea2f";
$hrms-bookmark-outline: "\ea30";
$hrms-bookmark2-active: "\ea31";
$hrms-bookmark2-active-outline: "\ea32";
$hrms-bookmark2-add: "\ea33";
$hrms-bookmark2-add-1: "\ea34";
$hrms-bookmark2-add-outline: "\ea35";
$hrms-bookmark2-add-outline-1: "\ea36";
$hrms-brig-cz: "\ea37";
$hrms-browse-history: "\ea38";
$hrms-browse-history-outline: "\ea39";
$hrms-bt: "\ea3a";
$hrms-calendar: "\ea3b";
$hrms-calendar-add: "\ea3c";
$hrms-calendar-add-outline: "\ea3d";
$hrms-calendar-check: "\ea3e";
$hrms-calendar-check-outline: "\ea3f";
$hrms-calendar-due: "\ea40";
$hrms-calendar-due-outline: "\ea41";
$hrms-calendar-outline: "\ea42";
$hrms-calendar-urgent: "\ea43";
$hrms-calendar-urgent-outline: "\ea44";
$hrms-calendar-vat: "\ea45";
$hrms-calendar-vat-outline: "\ea46";
$hrms-camelactive-logo: "\ea47";
$hrms-center: "\ea48";
$hrms-center-not-selected: "\ea49";
$hrms-center-outline: "\ea4a";
$hrms-change-history-outline: "\ea4b";
$hrms-changelog-first: "\ea4c";
$hrms-changelog-first-popover: "\ea4d";
$hrms-changelog-item: "\ea4e";
$hrms-changelog-last: "\ea4f";
$hrms-changelog-last-popover: "\ea50";
$hrms-changelog-line: "\ea51";
$hrms-changelog-solo: "\ea52";
$hrms-checked: "\ea53";
$hrms-checked-outline: "\ea54";
$hrms-chevron-big-down: "\ea55";
$hrms-chevron-big-left: "\ea56";
$hrms-chevron-big-right: "\ea57";
$hrms-chevron-big-up: "\ea58";
$hrms-chevron-down: "\ea59";
$hrms-chevron-left: "\ea5a";
$hrms-chevron-right: "\ea5b";
$hrms-chevron-up: "\ea5c";
$hrms-ciel2: "\ea5d";
$hrms-ciel2-outline: "\ea5e";
$hrms-circle-dot-border: "\ea5f";
$hrms-clear: "\ea60";
$hrms-clear-outline: "\ea61";
$hrms-clock-outline: "\ea62";
$hrms-close: "\ea63";
$hrms-coffee: "\ea64";
$hrms-continue-form: "\ea65";
$hrms-copy-link: "\ea66";
$hrms-copy-link2: "\ea67";
$hrms-copyclipboard: "\ea68";
$hrms-copyclipboard-outline: "\ea69";
$hrms-copyclipboard-success: "\ea6a";
$hrms-copyclipboard-success-outline: "\ea6b";
$hrms-copyclipboardsend: "\ea6c";
$hrms-creating-proposal-approval: "\ea6d";
$hrms-creating-proposal-approval-outline: "\ea6e";
$hrms-czech: "\ea6f";
$hrms-datum: "\ea70";
$hrms-datum-outline: "\ea71";
$hrms-dd: "\ea72";
$hrms-deal: "\ea73";
$hrms-deal-outline: "\ea74";
$hrms-deduction: "\ea75";
$hrms-deduction-all: "\ea76";
$hrms-deduction-all-outline: "\ea77";
$hrms-deduction-outline: "\ea78";
$hrms-delete-bookmark: "\ea79";
$hrms-delete-bookmark-success: "\ea7a";
$hrms-deselectall: "\ea7b";
$hrms-document-add: "\ea7c";
$hrms-document-add-outline: "\ea7d";
$hrms-document-clip-preview: "\ea7e";
$hrms-document-clip-preview-outline: "\ea7f";
$hrms-document-doc: "\ea80";
$hrms-document-doc-outline: "\ea81";
$hrms-document-extraorder: "\ea82";
$hrms-document-extraorder-outline: "\ea83";
$hrms-document-fail: "\ea84";
$hrms-document-fail-outline: "\ea85";
$hrms-document-heic: "\ea86";
$hrms-document-heic-outline: "\ea87";
$hrms-document-hold: "\ea88";
$hrms-document-hold-outline: "\ea89";
$hrms-document-image: "\ea8a";
$hrms-document-image-outline: "\ea8b";
$hrms-document-information: "\ea8c";
$hrms-document-information-outline: "\ea8d";
$hrms-document-jpg: "\ea8e";
$hrms-document-jpg-outline: "\ea8f";
$hrms-document-loading: "\ea90";
$hrms-document-loading-outline: "\ea91";
$hrms-document-nope: "\ea92";
$hrms-document-nope-outline: "\ea93";
$hrms-document-okay: "\ea94";
$hrms-document-okay-outline: "\ea95";
$hrms-document-pdf: "\ea96";
$hrms-document-pdf-outline: "\ea97";
$hrms-document-png: "\ea98";
$hrms-document-png-outline: "\ea99";
$hrms-document-size: "\ea9a";
$hrms-document-size-outline: "\ea9b";
$hrms-document-tiff: "\ea9c";
$hrms-document-tiff-outline: "\ea9d";
$hrms-document-transport: "\ea9e";
$hrms-document-transport-outline: "\ea9f";
$hrms-document-upload: "\eaa0";
$hrms-document-upload-outline: "\eaa1";
$hrms-document-upload2: "\eaa2";
$hrms-document-upload2-outline: "\eaa3";
$hrms-document-wait3: "\eaa4";
$hrms-document-waiting-outline: "\eaa5";
$hrms-document-warning: "\eaa6";
$hrms-document-warning-outline: "\eaa7";
$hrms-document-wishlist: "\eaa8";
$hrms-document-wishlist-notify: "\eaa9";
$hrms-done-task: "\eaaa";
$hrms-done-task-circle: "\eaab";
$hrms-done-task-circle-outline: "\eaac";
$hrms-double-arrow-left: "\eaad";
$hrms-double-arrow-right: "\eaae";
$hrms-down-loading: "\eaaf";
$hrms-duplicate: "\eab0";
$hrms-duplicate-outline: "\eab1";
$hrms-ec: "\eab2";
$hrms-edit-text: "\eab3";
$hrms-edit-text-outline: "\eab4";
$hrms-education: "\eab5";
$hrms-education-outline: "\eab6";
$hrms-empty: "\eab7";
$hrms-empty-count: "\eab8";
$hrms-empty-count2: "\eab9";
$hrms-english: "\eaba";
$hrms-error: "\eabb";
$hrms-excel: "\eabc";
$hrms-exchange: "\eabd";
$hrms-exchange-outline: "\eabe";
$hrms-expand: "\eabf";
$hrms-external-link: "\eac0";
$hrms-filter: "\eac1";
$hrms-filter-active: "\eac2";
$hrms-filter-filled: "\eac3";
$hrms-filter-filled-active: "\eac4";
$hrms-fin: "\eac5";
$hrms-forward-to: "\eac6";
$hrms-gant-icon: "\eac7";
$hrms-gant-icon-invert: "\eac8";
$hrms-gridview: "\eac9";
$hrms-hidden: "\eaca";
$hrms-hidden-fill: "\eacb";
$hrms-history: "\eacc";
$hrms-history-outline: "\eacd";
$hrms-hr: "\eace";
$hrms-hungary: "\eacf";
$hrms-info: "\ead0";
$hrms-info-outline: "\ead1";
$hrms-input-options: "\ead2";
$hrms-it: "\ead3";
$hrms-karl-head: "\ead4";
$hrms-karl-head-invert: "\ead5";
$hrms-knowledge: "\ead6";
$hrms-knowledge-outline: "\ead7";
$hrms-knowledge-outline-1: "\ead8";
$hrms-knowlegde-base: "\ead9";
$hrms-knowlegde-base-outline: "\eada";
$hrms-language: "\eadb";
$hrms-language-outline: "\eadc";
$hrms-laravel-pulse: "\eadd";
$hrms-list-ascendent: "\eade";
$hrms-list-descendent: "\eadf";
$hrms-list-proposals: "\eae0";
$hrms-list-proposals-outline: "\eae1";
$hrms-list-view: "\eae2";
$hrms-list-view-outline: "\eae3";
$hrms-listview: "\eae4";
$hrms-locked: "\eae5";
$hrms-locked-outline: "\eae6";
$hrms-log: "\eae7";
$hrms-logout: "\eae8";
$hrms-logout-outline: "\eae9";
$hrms-maintance: "\eaea";
$hrms-mighty-matus: "\eaeb";
$hrms-minus: "\eaec";
$hrms-mkt: "\eaed";
$hrms-modal: "\eaee";
$hrms-modal-outline: "\eaef";
$hrms-negative: "\eaf0";
$hrms-new-mobile: "\eaf1";
$hrms-not-acepted: "\eaf2";
$hrms-not-acepted-circle: "\eaf3";
$hrms-not-acepted-circle-outline: "\eaf4";
$hrms-not-acepted-outline: "\eaf5";
$hrms-not-authorized: "\eaf6";
$hrms-not-found: "\eaf7";
$hrms-note: "\eaf8";
$hrms-note-add: "\eaf9";
$hrms-note-delete: "\eafa";
$hrms-note-first: "\eafb";
$hrms-note-last: "\eafc";
$hrms-note-middle: "\eafd";
$hrms-notify: "\eafe";
$hrms-notify-outline: "\eaff";
$hrms-notofikacie: "\eb00";
$hrms-obalka: "\eb01";
$hrms-obalka-open: "\eb02";
$hrms-obalka-open-outline: "\eb03";
$hrms-obalka-outline: "\eb04";
$hrms-office: "\eb05";
$hrms-office-cz: "\eb06";
$hrms-office-hu: "\eb07";
$hrms-office-outline: "\eb08";
$hrms-office-sk: "\eb09";
$hrms-okay: "\eb0a";
$hrms-oprava: "\eb0b";
$hrms-oprava-circle: "\eb0c";
$hrms-oprava-outline: "\eb0d";
$hrms-own: "\eb0e";
$hrms-parcel-history: "\eb0f";
$hrms-paycheck: "\eb10";
$hrms-paycheck-all: "\eb11";
$hrms-paycheck-all-outline: "\eb12";
$hrms-paycheck-outline: "\eb13";
$hrms-peakperformance-logo: "\eb14";
$hrms-pizza: "\eb15";
$hrms-placeholder-vermont: "\eb16";
$hrms-plus: "\eb17";
$hrms-positive: "\eb18";
$hrms-prinos: "\eb19";
$hrms-prinos-outline: "\eb1a";
$hrms-proposal-no-info: "\eb1b";
$hrms-proposal-no-info-outline: "\eb1c";
$hrms-proposal-no-info2: "\eb1d";
$hrms-proposal-no-info2-outline: "\eb1e";
$hrms-proposal-no-info3: "\eb1f";
$hrms-proposal-no-info3-1: "\eb20";
$hrms-refresh: "\eb21";
$hrms-reload: "\eb22";
$hrms-remove: "\eb23";
$hrms-remove-outline: "\eb24";
$hrms-reset: "\eb25";
$hrms-ret: "\eb26";
$hrms-sc: "\eb27";
$hrms-search: "\eb28";
$hrms-select-none: "\eb29";
$hrms-selectall: "\eb2a";
$hrms-sent: "\eb2b";
$hrms-sent-circle: "\eb2c";
$hrms-sent-circle-outline: "\eb2d";
$hrms-sent-outline: "\eb2e";
$hrms-settings: "\eb2f";
$hrms-settings-outline: "\eb30";
$hrms-share: "\eb31";
$hrms-share-circle: "\eb32";
$hrms-share-circle-outline: "\eb33";
$hrms-share-outline: "\eb34";
$hrms-slovakia: "\eb35";
$hrms-small-dot: "\eb36";
$hrms-smilee: "\eb37";
$hrms-smilee-outline: "\eb38";
$hrms-state: "\eb39";
$hrms-status-aproved: "\eb3a";
$hrms-status-okay-outline: "\eb3b";
$hrms-status-pending: "\eb3c";
$hrms-status-revoked: "\eb3d";
$hrms-status-stop-outline: "\eb3e";
$hrms-status-waiting: "\eb3f";
$hrms-sum: "\eb40";
$hrms-sum2: "\eb41";
$hrms-sum3: "\eb42";
$hrms-sutaz: "\eb43";
$hrms-sutaz-add: "\eb44";
$hrms-sutaz-add-outline: "\eb45";
$hrms-sutaz-chart: "\eb46";
$hrms-sutaz-chart-outline: "\eb47";
$hrms-sutaz-fail: "\eb48";
$hrms-sutaz-fail-outline: "\eb49";
$hrms-sutaz-home: "\eb4a";
$hrms-sutaz-home-outline: "\eb4b";
$hrms-sutaz-home2: "\eb4c";
$hrms-sutaz-home2-outline: "\eb4d";
$hrms-sutaz-info: "\eb4e";
$hrms-sutaz-info-outline: "\eb4f";
$hrms-sutaz-now: "\eb50";
$hrms-sutaz-now-outline: "\eb51";
$hrms-sutaz-ongoing: "\eb52";
$hrms-sutaz-ongoing-outline: "\eb53";
$hrms-sutaz-outline: "\eb54";
$hrms-sutaz-retail: "\eb55";
$hrms-sutaz-retail-outline: "\eb56";
$hrms-sutaz-success: "\eb57";
$hrms-sutaz-success-outline: "\eb58";
$hrms-swap: "\eb59";
$hrms-swap-outline: "\eb5a";
$hrms-table-change: "\eb5b";
$hrms-table-change-outline: "\eb5c";
$hrms-table-confirm: "\eb5d";
$hrms-table-confirm-outline: "\eb5e";
$hrms-table-deny: "\eb5f";
$hrms-table-deny-outline: "\eb60";
$hrms-table-view: "\eb61";
$hrms-table-view-outline: "\eb62";
$hrms-tag: "\eb63";
$hrms-tag-outline: "\eb64";
$hrms-tilde: "\eb65";
$hrms-timeline: "\eb66";
$hrms-timeline-outline: "\eb67";
$hrms-to-do-list: "\eb68";
$hrms-trash: "\eb69";
$hrms-trash-outline: "\eb6a";
$hrms-trvanie: "\eb6b";
$hrms-trvanie-outline: "\eb6c";
$hrms-up-loading: "\eb6d";
$hrms-user-edit: "\eb6e";
$hrms-user-outline: "\eb6f";
$hrms-user-pause: "\eb70";
$hrms-user-unknown: "\eb71";
$hrms-user-warning: "\eb72";
$hrms-v_home: "\eb73";
$hrms-vermont-def: "\eb74";
$hrms-vermont-intranet-pass: "\eb75";
$hrms-vermont-ucko: "\eb76";
$hrms-vermont-ucko-circle: "\eb77";
$hrms-vermont-ucko-circle-outline: "\eb78";
$hrms-vermont_invert: "\eb79";
$hrms-visible: "\eb7a";
$hrms-visible-fill: "\eb7b";
$hrms-vm: "\eb7c";
$hrms-vouchers: "\eb7d";
$hrms-vouchers-all: "\eb7e";
$hrms-vouchers-all-outline: "\eb7f";
$hrms-vouchers-outline: "\eb80";
$hrms-vyhodnotenie: "\eb81";
$hrms-vyhodnotenie-outline: "\eb82";
$hrms-waiting-aproval-circle: "\eb83";
$hrms-waiting-aproval-circle-outline: "\eb84";
$hrms-waiting2-aproval-circle: "\eb85";
$hrms-waiting2-aproval-circle-outline: "\eb86";
$hrms-waiting3-aproval-circle: "\eb87";
$hrms-waiting3-aproval-circle-outline: "\eb88";
$hrms-waiting4-aproval-circle: "\eb89";
$hrms-waiting4-aproval-circle-outline: "\eb8a";
$hrms-waiting5-aproval-circle: "\eb8b";
$hrms-waiting5-aproval-circle-outline: "\eb8c";
$hrms-warehouse: "\eb8d";
$hrms-warehouse-outline: "\eb8e";
$hrms-web-stuff: "\eb8f";
$hrms-web-upload: "\eb90";
$hrms-whole-collum: "\eb91";
$hrms-whole-row: "\eb92";
$hrms-whole-table: "\eb93";
$hrms-wink: "\eb94";
$hrms-wink-outline: "\eb95";
$hrms-woolrich-icon: "\eb96";
$hrms-write: "\eb97";
$hrms-write-outline: "\eb98";
$hrms-ws: "\eb99";
$hrms-zoom-in-1: "\eb9a";
$hrms-zoom-in-1-outline: "\eb9b";
$hrms-zoom-out-1: "\eb9c";
$hrms-zoom-out-1-outline: "\eb9d";

%hrms {
  display: inline-block;
  font-family: "hrms" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hrms {
  @extend %hrms;
}

.hrms-acc::before {
  content: "\ea01";
}
.hrms-acepted::before {
  content: "\ea02";
}
.hrms-acepted-circle::before {
  content: "\ea03";
}
.hrms-acepted-circle-outline::before {
  content: "\ea04";
}
.hrms-acepted-outline::before {
  content: "\ea05";
}
.hrms-ad::before {
  content: "\ea06";
}
.hrms-add-circle::before {
  content: "\ea07";
}
.hrms-add-circle-outline::before {
  content: "\ea08";
}
.hrms-alert::before {
  content: "\ea09";
}
.hrms-alert-outline::before {
  content: "\ea0a";
}
.hrms-alert-warning::before {
  content: "\ea0b";
}
.hrms-alert-warning-outline::before {
  content: "\ea0c";
}
.hrms-all-column::before {
  content: "\ea0d";
}
.hrms-all-row::before {
  content: "\ea0e";
}
.hrms-appropriate::before {
  content: "\ea0f";
}
.hrms-appropriate-outline::before {
  content: "\ea10";
}
.hrms-archive::before {
  content: "\ea11";
}
.hrms-archive-outline::before {
  content: "\ea12";
}
.hrms-arr-down::before {
  content: "\ea13";
}
.hrms-arr-left::before {
  content: "\ea14";
}
.hrms-arr-right::before {
  content: "\ea15";
}
.hrms-arr-up::before {
  content: "\ea16";
}
.hrms-arrow-circle-down::before {
  content: "\ea17";
}
.hrms-arrow-circle-down-outline::before {
  content: "\ea18";
}
.hrms-arrow-down::before {
  content: "\ea19";
}
.hrms-arrow-down-1::before {
  content: "\ea1a";
}
.hrms-arrow-left::before {
  content: "\ea1b";
}
.hrms-arrow-right::before {
  content: "\ea1c";
}
.hrms-asign-task::before {
  content: "\ea1d";
}
.hrms-asign-task-circle::before {
  content: "\ea1e";
}
.hrms-asign-task-circle-outline::before {
  content: "\ea1f";
}
.hrms-back::before {
  content: "\ea20";
}
.hrms-back-outline::before {
  content: "\ea21";
}
.hrms-bm::before {
  content: "\ea22";
}
.hrms-bm-1::before {
  content: "\ea23";
}
.hrms-bm-active::before {
  content: "\ea24";
}
.hrms-bm-active-outline::before {
  content: "\ea25";
}
.hrms-bm-add::before {
  content: "\ea26";
}
.hrms-bm-add-outline::before {
  content: "\ea27";
}
.hrms-bm-outline::before {
  content: "\ea28";
}
.hrms-bonus::before {
  content: "\ea29";
}
.hrms-bonus-all::before {
  content: "\ea2a";
}
.hrms-bonus-all-outline::before {
  content: "\ea2b";
}
.hrms-bonus-outline::before {
  content: "\ea2c";
}
.hrms-bookmark::before {
  content: "\ea2d";
}
.hrms-bookmark-add::before {
  content: "\ea2e";
}
.hrms-bookmark-add-outline::before {
  content: "\ea2f";
}
.hrms-bookmark-outline::before {
  content: "\ea30";
}
.hrms-bookmark2-active::before {
  content: "\ea31";
}
.hrms-bookmark2-active-outline::before {
  content: "\ea32";
}
.hrms-bookmark2-add::before {
  content: "\ea33";
}
.hrms-bookmark2-add-1::before {
  content: "\ea34";
}
.hrms-bookmark2-add-outline::before {
  content: "\ea35";
}
.hrms-bookmark2-add-outline-1::before {
  content: "\ea36";
}
.hrms-brig-cz::before {
  content: "\ea37";
}
.hrms-browse-history::before {
  content: "\ea38";
}
.hrms-browse-history-outline::before {
  content: "\ea39";
}
.hrms-bt::before {
  content: "\ea3a";
}
.hrms-calendar::before {
  content: "\ea3b";
}
.hrms-calendar-add::before {
  content: "\ea3c";
}
.hrms-calendar-add-outline::before {
  content: "\ea3d";
}
.hrms-calendar-check::before {
  content: "\ea3e";
}
.hrms-calendar-check-outline::before {
  content: "\ea3f";
}
.hrms-calendar-due::before {
  content: "\ea40";
}
.hrms-calendar-due-outline::before {
  content: "\ea41";
}
.hrms-calendar-outline::before {
  content: "\ea42";
}
.hrms-calendar-urgent::before {
  content: "\ea43";
}
.hrms-calendar-urgent-outline::before {
  content: "\ea44";
}
.hrms-calendar-vat::before {
  content: "\ea45";
}
.hrms-calendar-vat-outline::before {
  content: "\ea46";
}
.hrms-camelactive-logo::before {
  content: "\ea47";
}
.hrms-center::before {
  content: "\ea48";
}
.hrms-center-not-selected::before {
  content: "\ea49";
}
.hrms-center-outline::before {
  content: "\ea4a";
}
.hrms-change-history-outline::before {
  content: "\ea4b";
}
.hrms-changelog-first::before {
  content: "\ea4c";
}
.hrms-changelog-first-popover::before {
  content: "\ea4d";
}
.hrms-changelog-item::before {
  content: "\ea4e";
}
.hrms-changelog-last::before {
  content: "\ea4f";
}
.hrms-changelog-last-popover::before {
  content: "\ea50";
}
.hrms-changelog-line::before {
  content: "\ea51";
}
.hrms-changelog-solo::before {
  content: "\ea52";
}
.hrms-checked::before {
  content: "\ea53";
}
.hrms-checked-outline::before {
  content: "\ea54";
}
.hrms-chevron-big-down::before {
  content: "\ea55";
}
.hrms-chevron-big-left::before {
  content: "\ea56";
}
.hrms-chevron-big-right::before {
  content: "\ea57";
}
.hrms-chevron-big-up::before {
  content: "\ea58";
}
.hrms-chevron-down::before {
  content: "\ea59";
}
.hrms-chevron-left::before {
  content: "\ea5a";
}
.hrms-chevron-right::before {
  content: "\ea5b";
}
.hrms-chevron-up::before {
  content: "\ea5c";
}
.hrms-ciel2::before {
  content: "\ea5d";
}
.hrms-ciel2-outline::before {
  content: "\ea5e";
}
.hrms-circle-dot-border::before {
  content: "\ea5f";
}
.hrms-clear::before {
  content: "\ea60";
}
.hrms-clear-outline::before {
  content: "\ea61";
}
.hrms-clock-outline::before {
  content: "\ea62";
}
.hrms-close::before {
  content: "\ea63";
}
.hrms-coffee::before {
  content: "\ea64";
}
.hrms-continue-form::before {
  content: "\ea65";
}
.hrms-copy-link::before {
  content: "\ea66";
}
.hrms-copy-link2::before {
  content: "\ea67";
}
.hrms-copyclipboard::before {
  content: "\ea68";
}
.hrms-copyclipboard-outline::before {
  content: "\ea69";
}
.hrms-copyclipboard-success::before {
  content: "\ea6a";
}
.hrms-copyclipboard-success-outline::before {
  content: "\ea6b";
}
.hrms-copyclipboardsend::before {
  content: "\ea6c";
}
.hrms-creating-proposal-approval::before {
  content: "\ea6d";
}
.hrms-creating-proposal-approval-outline::before {
  content: "\ea6e";
}
.hrms-czech::before {
  content: "\ea6f";
}
.hrms-datum::before {
  content: "\ea70";
}
.hrms-datum-outline::before {
  content: "\ea71";
}
.hrms-dd::before {
  content: "\ea72";
}
.hrms-deal::before {
  content: "\ea73";
}
.hrms-deal-outline::before {
  content: "\ea74";
}
.hrms-deduction::before {
  content: "\ea75";
}
.hrms-deduction-all::before {
  content: "\ea76";
}
.hrms-deduction-all-outline::before {
  content: "\ea77";
}
.hrms-deduction-outline::before {
  content: "\ea78";
}
.hrms-delete-bookmark::before {
  content: "\ea79";
}
.hrms-delete-bookmark-success::before {
  content: "\ea7a";
}
.hrms-deselectall::before {
  content: "\ea7b";
}
.hrms-document-add::before {
  content: "\ea7c";
}
.hrms-document-add-outline::before {
  content: "\ea7d";
}
.hrms-document-clip-preview::before {
  content: "\ea7e";
}
.hrms-document-clip-preview-outline::before {
  content: "\ea7f";
}
.hrms-document-doc::before {
  content: "\ea80";
}
.hrms-document-doc-outline::before {
  content: "\ea81";
}
.hrms-document-extraorder::before {
  content: "\ea82";
}
.hrms-document-extraorder-outline::before {
  content: "\ea83";
}
.hrms-document-fail::before {
  content: "\ea84";
}
.hrms-document-fail-outline::before {
  content: "\ea85";
}
.hrms-document-heic::before {
  content: "\ea86";
}
.hrms-document-heic-outline::before {
  content: "\ea87";
}
.hrms-document-hold::before {
  content: "\ea88";
}
.hrms-document-hold-outline::before {
  content: "\ea89";
}
.hrms-document-image::before {
  content: "\ea8a";
}
.hrms-document-image-outline::before {
  content: "\ea8b";
}
.hrms-document-information::before {
  content: "\ea8c";
}
.hrms-document-information-outline::before {
  content: "\ea8d";
}
.hrms-document-jpg::before {
  content: "\ea8e";
}
.hrms-document-jpg-outline::before {
  content: "\ea8f";
}
.hrms-document-loading::before {
  content: "\ea90";
}
.hrms-document-loading-outline::before {
  content: "\ea91";
}
.hrms-document-nope::before {
  content: "\ea92";
}
.hrms-document-nope-outline::before {
  content: "\ea93";
}
.hrms-document-okay::before {
  content: "\ea94";
}
.hrms-document-okay-outline::before {
  content: "\ea95";
}
.hrms-document-pdf::before {
  content: "\ea96";
}
.hrms-document-pdf-outline::before {
  content: "\ea97";
}
.hrms-document-png::before {
  content: "\ea98";
}
.hrms-document-png-outline::before {
  content: "\ea99";
}
.hrms-document-size::before {
  content: "\ea9a";
}
.hrms-document-size-outline::before {
  content: "\ea9b";
}
.hrms-document-tiff::before {
  content: "\ea9c";
}
.hrms-document-tiff-outline::before {
  content: "\ea9d";
}
.hrms-document-transport::before {
  content: "\ea9e";
}
.hrms-document-transport-outline::before {
  content: "\ea9f";
}
.hrms-document-upload::before {
  content: "\eaa0";
}
.hrms-document-upload-outline::before {
  content: "\eaa1";
}
.hrms-document-upload2::before {
  content: "\eaa2";
}
.hrms-document-upload2-outline::before {
  content: "\eaa3";
}
.hrms-document-wait3::before {
  content: "\eaa4";
}
.hrms-document-waiting-outline::before {
  content: "\eaa5";
}
.hrms-document-warning::before {
  content: "\eaa6";
}
.hrms-document-warning-outline::before {
  content: "\eaa7";
}
.hrms-document-wishlist::before {
  content: "\eaa8";
}
.hrms-document-wishlist-notify::before {
  content: "\eaa9";
}
.hrms-done-task::before {
  content: "\eaaa";
}
.hrms-done-task-circle::before {
  content: "\eaab";
}
.hrms-done-task-circle-outline::before {
  content: "\eaac";
}
.hrms-double-arrow-left::before {
  content: "\eaad";
}
.hrms-double-arrow-right::before {
  content: "\eaae";
}
.hrms-down-loading::before {
  content: "\eaaf";
}
.hrms-duplicate::before {
  content: "\eab0";
}
.hrms-duplicate-outline::before {
  content: "\eab1";
}
.hrms-ec::before {
  content: "\eab2";
}
.hrms-edit-text::before {
  content: "\eab3";
}
.hrms-edit-text-outline::before {
  content: "\eab4";
}
.hrms-education::before {
  content: "\eab5";
}
.hrms-education-outline::before {
  content: "\eab6";
}
.hrms-empty::before {
  content: "\eab7";
}
.hrms-empty-count::before {
  content: "\eab8";
}
.hrms-empty-count2::before {
  content: "\eab9";
}
.hrms-english::before {
  content: "\eaba";
}
.hrms-error::before {
  content: "\eabb";
}
.hrms-excel::before {
  content: "\eabc";
}
.hrms-exchange::before {
  content: "\eabd";
}
.hrms-exchange-outline::before {
  content: "\eabe";
}
.hrms-expand::before {
  content: "\eabf";
}
.hrms-external-link::before {
  content: "\eac0";
}
.hrms-filter::before {
  content: "\eac1";
}
.hrms-filter-active::before {
  content: "\eac2";
}
.hrms-filter-filled::before {
  content: "\eac3";
}
.hrms-filter-filled-active::before {
  content: "\eac4";
}
.hrms-fin::before {
  content: "\eac5";
}
.hrms-forward-to::before {
  content: "\eac6";
}
.hrms-gant-icon::before {
  content: "\eac7";
}
.hrms-gant-icon-invert::before {
  content: "\eac8";
}
.hrms-gridview::before {
  content: "\eac9";
}
.hrms-hidden::before {
  content: "\eaca";
}
.hrms-hidden-fill::before {
  content: "\eacb";
}
.hrms-history::before {
  content: "\eacc";
}
.hrms-history-outline::before {
  content: "\eacd";
}
.hrms-hr::before {
  content: "\eace";
}
.hrms-hungary::before {
  content: "\eacf";
}
.hrms-info::before {
  content: "\ead0";
}
.hrms-info-outline::before {
  content: "\ead1";
}
.hrms-input-options::before {
  content: "\ead2";
}
.hrms-it::before {
  content: "\ead3";
}
.hrms-karl-head::before {
  content: "\ead4";
}
.hrms-karl-head-invert::before {
  content: "\ead5";
}
.hrms-knowledge::before {
  content: "\ead6";
}
.hrms-knowledge-outline::before {
  content: "\ead7";
}
.hrms-knowledge-outline-1::before {
  content: "\ead8";
}
.hrms-knowlegde-base::before {
  content: "\ead9";
}
.hrms-knowlegde-base-outline::before {
  content: "\eada";
}
.hrms-language::before {
  content: "\eadb";
}
.hrms-language-outline::before {
  content: "\eadc";
}
.hrms-laravel-pulse::before {
  content: "\eadd";
}
.hrms-list-ascendent::before {
  content: "\eade";
}
.hrms-list-descendent::before {
  content: "\eadf";
}
.hrms-list-proposals::before {
  content: "\eae0";
}
.hrms-list-proposals-outline::before {
  content: "\eae1";
}
.hrms-list-view::before {
  content: "\eae2";
}
.hrms-list-view-outline::before {
  content: "\eae3";
}
.hrms-listview::before {
  content: "\eae4";
}
.hrms-locked::before {
  content: "\eae5";
}
.hrms-locked-outline::before {
  content: "\eae6";
}
.hrms-log::before {
  content: "\eae7";
}
.hrms-logout::before {
  content: "\eae8";
}
.hrms-logout-outline::before {
  content: "\eae9";
}
.hrms-maintance::before {
  content: "\eaea";
}
.hrms-mighty-matus::before {
  content: "\eaeb";
}
.hrms-minus::before {
  content: "\eaec";
}
.hrms-mkt::before {
  content: "\eaed";
}
.hrms-modal::before {
  content: "\eaee";
}
.hrms-modal-outline::before {
  content: "\eaef";
}
.hrms-negative::before {
  content: "\eaf0";
}
.hrms-new-mobile::before {
  content: "\eaf1";
}
.hrms-not-acepted::before {
  content: "\eaf2";
}
.hrms-not-acepted-circle::before {
  content: "\eaf3";
}
.hrms-not-acepted-circle-outline::before {
  content: "\eaf4";
}
.hrms-not-acepted-outline::before {
  content: "\eaf5";
}
.hrms-not-authorized::before {
  content: "\eaf6";
}
.hrms-not-found::before {
  content: "\eaf7";
}
.hrms-note::before {
  content: "\eaf8";
}
.hrms-note-add::before {
  content: "\eaf9";
}
.hrms-note-delete::before {
  content: "\eafa";
}
.hrms-note-first::before {
  content: "\eafb";
}
.hrms-note-last::before {
  content: "\eafc";
}
.hrms-note-middle::before {
  content: "\eafd";
}
.hrms-notify::before {
  content: "\eafe";
}
.hrms-notify-outline::before {
  content: "\eaff";
}
.hrms-notofikacie::before {
  content: "\eb00";
}
.hrms-obalka::before {
  content: "\eb01";
}
.hrms-obalka-open::before {
  content: "\eb02";
}
.hrms-obalka-open-outline::before {
  content: "\eb03";
}
.hrms-obalka-outline::before {
  content: "\eb04";
}
.hrms-office::before {
  content: "\eb05";
}
.hrms-office-cz::before {
  content: "\eb06";
}
.hrms-office-hu::before {
  content: "\eb07";
}
.hrms-office-outline::before {
  content: "\eb08";
}
.hrms-office-sk::before {
  content: "\eb09";
}
.hrms-okay::before {
  content: "\eb0a";
}
.hrms-oprava::before {
  content: "\eb0b";
}
.hrms-oprava-circle::before {
  content: "\eb0c";
}
.hrms-oprava-outline::before {
  content: "\eb0d";
}
.hrms-own::before {
  content: "\eb0e";
}
.hrms-parcel-history::before {
  content: "\eb0f";
}
.hrms-paycheck::before {
  content: "\eb10";
}
.hrms-paycheck-all::before {
  content: "\eb11";
}
.hrms-paycheck-all-outline::before {
  content: "\eb12";
}
.hrms-paycheck-outline::before {
  content: "\eb13";
}
.hrms-peakperformance-logo::before {
  content: "\eb14";
}
.hrms-pizza::before {
  content: "\eb15";
}
.hrms-placeholder-vermont::before {
  content: "\eb16";
}
.hrms-plus::before {
  content: "\eb17";
}
.hrms-positive::before {
  content: "\eb18";
}
.hrms-prinos::before {
  content: "\eb19";
}
.hrms-prinos-outline::before {
  content: "\eb1a";
}
.hrms-proposal-no-info::before {
  content: "\eb1b";
}
.hrms-proposal-no-info-outline::before {
  content: "\eb1c";
}
.hrms-proposal-no-info2::before {
  content: "\eb1d";
}
.hrms-proposal-no-info2-outline::before {
  content: "\eb1e";
}
.hrms-proposal-no-info3::before {
  content: "\eb1f";
}
.hrms-proposal-no-info3-1::before {
  content: "\eb20";
}
.hrms-refresh::before {
  content: "\eb21";
}
.hrms-reload::before {
  content: "\eb22";
}
.hrms-remove::before {
  content: "\eb23";
}
.hrms-remove-outline::before {
  content: "\eb24";
}
.hrms-reset::before {
  content: "\eb25";
}
.hrms-ret::before {
  content: "\eb26";
}
.hrms-sc::before {
  content: "\eb27";
}
.hrms-search::before {
  content: "\eb28";
}
.hrms-select-none::before {
  content: "\eb29";
}
.hrms-selectall::before {
  content: "\eb2a";
}
.hrms-sent::before {
  content: "\eb2b";
}
.hrms-sent-circle::before {
  content: "\eb2c";
}
.hrms-sent-circle-outline::before {
  content: "\eb2d";
}
.hrms-sent-outline::before {
  content: "\eb2e";
}
.hrms-settings::before {
  content: "\eb2f";
}
.hrms-settings-outline::before {
  content: "\eb30";
}
.hrms-share::before {
  content: "\eb31";
}
.hrms-share-circle::before {
  content: "\eb32";
}
.hrms-share-circle-outline::before {
  content: "\eb33";
}
.hrms-share-outline::before {
  content: "\eb34";
}
.hrms-slovakia::before {
  content: "\eb35";
}
.hrms-small-dot::before {
  content: "\eb36";
}
.hrms-smilee::before {
  content: "\eb37";
}
.hrms-smilee-outline::before {
  content: "\eb38";
}
.hrms-state::before {
  content: "\eb39";
}
.hrms-status-aproved::before {
  content: "\eb3a";
}
.hrms-status-okay-outline::before {
  content: "\eb3b";
}
.hrms-status-pending::before {
  content: "\eb3c";
}
.hrms-status-revoked::before {
  content: "\eb3d";
}
.hrms-status-stop-outline::before {
  content: "\eb3e";
}
.hrms-status-waiting::before {
  content: "\eb3f";
}
.hrms-sum::before {
  content: "\eb40";
}
.hrms-sum2::before {
  content: "\eb41";
}
.hrms-sum3::before {
  content: "\eb42";
}
.hrms-sutaz::before {
  content: "\eb43";
}
.hrms-sutaz-add::before {
  content: "\eb44";
}
.hrms-sutaz-add-outline::before {
  content: "\eb45";
}
.hrms-sutaz-chart::before {
  content: "\eb46";
}
.hrms-sutaz-chart-outline::before {
  content: "\eb47";
}
.hrms-sutaz-fail::before {
  content: "\eb48";
}
.hrms-sutaz-fail-outline::before {
  content: "\eb49";
}
.hrms-sutaz-home::before {
  content: "\eb4a";
}
.hrms-sutaz-home-outline::before {
  content: "\eb4b";
}
.hrms-sutaz-home2::before {
  content: "\eb4c";
}
.hrms-sutaz-home2-outline::before {
  content: "\eb4d";
}
.hrms-sutaz-info::before {
  content: "\eb4e";
}
.hrms-sutaz-info-outline::before {
  content: "\eb4f";
}
.hrms-sutaz-now::before {
  content: "\eb50";
}
.hrms-sutaz-now-outline::before {
  content: "\eb51";
}
.hrms-sutaz-ongoing::before {
  content: "\eb52";
}
.hrms-sutaz-ongoing-outline::before {
  content: "\eb53";
}
.hrms-sutaz-outline::before {
  content: "\eb54";
}
.hrms-sutaz-retail::before {
  content: "\eb55";
}
.hrms-sutaz-retail-outline::before {
  content: "\eb56";
}
.hrms-sutaz-success::before {
  content: "\eb57";
}
.hrms-sutaz-success-outline::before {
  content: "\eb58";
}
.hrms-swap::before {
  content: "\eb59";
}
.hrms-swap-outline::before {
  content: "\eb5a";
}
.hrms-table-change::before {
  content: "\eb5b";
}
.hrms-table-change-outline::before {
  content: "\eb5c";
}
.hrms-table-confirm::before {
  content: "\eb5d";
}
.hrms-table-confirm-outline::before {
  content: "\eb5e";
}
.hrms-table-deny::before {
  content: "\eb5f";
}
.hrms-table-deny-outline::before {
  content: "\eb60";
}
.hrms-table-view::before {
  content: "\eb61";
}
.hrms-table-view-outline::before {
  content: "\eb62";
}
.hrms-tag::before {
  content: "\eb63";
}
.hrms-tag-outline::before {
  content: "\eb64";
}
.hrms-tilde::before {
  content: "\eb65";
}
.hrms-timeline::before {
  content: "\eb66";
}
.hrms-timeline-outline::before {
  content: "\eb67";
}
.hrms-to-do-list::before {
  content: "\eb68";
}
.hrms-trash::before {
  content: "\eb69";
}
.hrms-trash-outline::before {
  content: "\eb6a";
}
.hrms-trvanie::before {
  content: "\eb6b";
}
.hrms-trvanie-outline::before {
  content: "\eb6c";
}
.hrms-up-loading::before {
  content: "\eb6d";
}
.hrms-user-edit::before {
  content: "\eb6e";
}
.hrms-user-outline::before {
  content: "\eb6f";
}
.hrms-user-pause::before {
  content: "\eb70";
}
.hrms-user-unknown::before {
  content: "\eb71";
}
.hrms-user-warning::before {
  content: "\eb72";
}
.hrms-v_home::before {
  content: "\eb73";
}
.hrms-vermont-def::before {
  content: "\eb74";
}
.hrms-vermont-intranet-pass::before {
  content: "\eb75";
}
.hrms-vermont-ucko::before {
  content: "\eb76";
}
.hrms-vermont-ucko-circle::before {
  content: "\eb77";
}
.hrms-vermont-ucko-circle-outline::before {
  content: "\eb78";
}
.hrms-vermont_invert::before {
  content: "\eb79";
}
.hrms-visible::before {
  content: "\eb7a";
}
.hrms-visible-fill::before {
  content: "\eb7b";
}
.hrms-vm::before {
  content: "\eb7c";
}
.hrms-vouchers::before {
  content: "\eb7d";
}
.hrms-vouchers-all::before {
  content: "\eb7e";
}
.hrms-vouchers-all-outline::before {
  content: "\eb7f";
}
.hrms-vouchers-outline::before {
  content: "\eb80";
}
.hrms-vyhodnotenie::before {
  content: "\eb81";
}
.hrms-vyhodnotenie-outline::before {
  content: "\eb82";
}
.hrms-waiting-aproval-circle::before {
  content: "\eb83";
}
.hrms-waiting-aproval-circle-outline::before {
  content: "\eb84";
}
.hrms-waiting2-aproval-circle::before {
  content: "\eb85";
}
.hrms-waiting2-aproval-circle-outline::before {
  content: "\eb86";
}
.hrms-waiting3-aproval-circle::before {
  content: "\eb87";
}
.hrms-waiting3-aproval-circle-outline::before {
  content: "\eb88";
}
.hrms-waiting4-aproval-circle::before {
  content: "\eb89";
}
.hrms-waiting4-aproval-circle-outline::before {
  content: "\eb8a";
}
.hrms-waiting5-aproval-circle::before {
  content: "\eb8b";
}
.hrms-waiting5-aproval-circle-outline::before {
  content: "\eb8c";
}
.hrms-warehouse::before {
  content: "\eb8d";
}
.hrms-warehouse-outline::before {
  content: "\eb8e";
}
.hrms-web-stuff::before {
  content: "\eb8f";
}
.hrms-web-upload::before {
  content: "\eb90";
}
.hrms-whole-collum::before {
  content: "\eb91";
}
.hrms-whole-row::before {
  content: "\eb92";
}
.hrms-whole-table::before {
  content: "\eb93";
}
.hrms-wink::before {
  content: "\eb94";
}
.hrms-wink-outline::before {
  content: "\eb95";
}
.hrms-woolrich-icon::before {
  content: "\eb96";
}
.hrms-write::before {
  content: "\eb97";
}
.hrms-write-outline::before {
  content: "\eb98";
}
.hrms-ws::before {
  content: "\eb99";
}
.hrms-zoom-in-1::before {
  content: "\eb9a";
}
.hrms-zoom-in-1-outline::before {
  content: "\eb9b";
}
.hrms-zoom-out-1::before {
  content: "\eb9c";
}
.hrms-zoom-out-1-outline::before {
  content: "\eb9d";
}
