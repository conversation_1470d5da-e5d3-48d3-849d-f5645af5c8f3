// If use are using Nunito Sans (Variable Font), you can use these font weights to replace the default ones.
 
 //.fontWeights.scss

 @import "~bootstrap/scss/functions";

 // Default variable overrides
 $font-weight-light: "wght" 300, "wdth" 90, "opsz" 12, "YTLC" 500;
 $font-weight-lighter: "wght" 400, "wdth" 90, "opsz" 12, "YTLC" 500;
 $font-weight-normal: "wght" 500, "wdth" 90, "opsz" 12, "YTLC" 500;
 $font-weight-bold: "wght" 700, "wdth" 90, "opsz" 12, "YTLC" 500;
 $font-weight-bolder: "wght" 900, "wdth" 90, "opsz" 12, "YTLC" 500;
 
 @import "~bootstrap/scss/variables";
 
 @import "~bootstrap/scss/utilities";
 
 $utilities: (
         "font-weight": (
                 property: font-variation-settings,
                 class: fw,
                 values: (
                         light: $font-weight-light,
                         lighter: $font-weight-lighter,
                         normal: $font-weight-normal,
                         bold: $font-weight-bold,
                         bolder: $font-weight-bolder
                 )
         ),
 );