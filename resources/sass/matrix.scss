$matrix-absence: "\ea01";
$matrix-absence_bg: "\ea02";
$matrix-administration: "\ea03";
$matrix-administration_bg: "\ea04";
$matrix-anniversary-free-fay: "\ea05";
$matrix-anniversary-free-fay_bg: "\ea06";
$matrix-calendar: "\ea07";
$matrix-calendar_bg: "\ea08";
$matrix-compensatory-leave: "\ea09";
$matrix-compensatory-leave_bg: "\ea0a";
$matrix-create: "\ea0b";
$matrix-dashboard: "\ea0c";
$matrix-dashboard_bg: "\ea0d";
$matrix-free-day: "\ea0e";
$matrix-free-day_bg: "\ea0f";
$matrix-free-shift: "\ea10";
$matrix-free-shift_bg: "\ea11";
$matrix-holiday: "\ea12";
$matrix-holiday_bg: "\ea13";
$matrix-home-office: "\ea14";
$matrix-home-office_bg: "\ea15";
$matrix-home-office_small: "\ea16";
$matrix-home-office_small-1: "\ea17";
$matrix-interruption-of-work: "\ea18";
$matrix-interruption-of-work_bg: "\ea19";
$matrix-interruption-of-work_small: "\ea1a";
$matrix-invalid-contract: "\ea1b";
$matrix-invalid-contract_bg: "\ea1c";
$matrix-invalid-contract_small: "\ea1d";
$matrix-maternity-leave: "\ea1e";
$matrix-maternity-leave_bg: "\ea1f";
$matrix-matrix: "\ea20";
$matrix-mn: "\ea21";
$matrix-moon: "\ea22";
$matrix-obstacles-on-the-employers-part: "\ea23";
$matrix-obstacles-on-the-employers-part_bg: "\ea24";
$matrix-overtime-leave: "\ea25";
$matrix-overtime-leave_bg: "\ea26";
$matrix-paid-leave: "\ea27";
$matrix-paid-leave_bg: "\ea28";
$matrix-parental-leave: "\ea29";
$matrix-parental-leave_bg: "\ea2a";
$matrix-settings: "\ea2b";
$matrix-settings-1: "\ea2c";
$matrix-settings_bg: "\ea2d";
$matrix-settings_bg-1: "\ea2e";
$matrix-sick-leave: "\ea2f";
$matrix-sick-leave_bg: "\ea30";
$matrix-sick-leave_small: "\ea31";
$matrix-sun: "\ea32";
$matrix-team: "\ea33";
$matrix-team_bg: "\ea34";
$matrix-thumbnail: "\ea35";
$matrix-time-off-for-dependants: "\ea36";
$matrix-time-off-for-dependants_bg: "\ea37";
$matrix-time-off-for-doctors: "\ea38";
$matrix-time-off-for-doctors_bg: "\ea39";
$matrix-tooltip: "\ea3a";
$matrix-topmanager: "\ea3b";
$matrix-topmanager_bg: "\ea3c";
$matrix-treatment-of-a-family-member: "\ea3d";
$matrix-treatment-of-a-family-member_bg: "\ea3e";
$matrix-unpaid-leave: "\ea3f";
$matrix-unpaid-leave_bg: "\ea40";
$matrix-vacation: "\ea41";
$matrix-vacation_bg: "\ea42";
$matrix-vacations: "\ea43";
$matrix-vacations_bg: "\ea44";
$matrix-vermont-intranet-pass: "\ea45";
$matrix-vermont-ucko: "\ea46";
$matrix-warehouse: "\ea47";
$matrix-warehouse_bg: "\ea48";

%matrix {
  display: inline-block;
  font-family: "matrix" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.matrix {
  @extend %matrix;
}

.matrix-absence::before {
  content: "\ea01";
}
.matrix-absence_bg::before {
  content: "\ea02";
}
.matrix-administration::before {
  content: "\ea03";
}
.matrix-administration_bg::before {
  content: "\ea04";
}
.matrix-anniversary-free-fay::before {
  content: "\ea05";
}
.matrix-anniversary-free-fay_bg::before {
  content: "\ea06";
}
.matrix-calendar::before {
  content: "\ea07";
}
.matrix-calendar_bg::before {
  content: "\ea08";
}
.matrix-compensatory-leave::before {
  content: "\ea09";
}
.matrix-compensatory-leave_bg::before {
  content: "\ea0a";
}
.matrix-create::before {
  content: "\ea0b";
}
.matrix-dashboard::before {
  content: "\ea0c";
}
.matrix-dashboard_bg::before {
  content: "\ea0d";
}
.matrix-free-day::before {
  content: "\ea0e";
}
.matrix-free-day_bg::before {
  content: "\ea0f";
}
.matrix-free-shift::before {
  content: "\ea10";
}
.matrix-free-shift_bg::before {
  content: "\ea11";
}
.matrix-holiday::before {
  content: "\ea12";
}
.matrix-holiday_bg::before {
  content: "\ea13";
}
.matrix-home-office::before {
  content: "\ea14";
}
.matrix-home-office_bg::before {
  content: "\ea15";
}
.matrix-home-office_small::before {
  content: "\ea16";
}
.matrix-home-office_small-1::before {
  content: "\ea17";
}
.matrix-interruption-of-work::before {
  content: "\ea18";
}
.matrix-interruption-of-work_bg::before {
  content: "\ea19";
}
.matrix-interruption-of-work_small::before {
  content: "\ea1a";
}
.matrix-invalid-contract::before {
  content: "\ea1b";
}
.matrix-invalid-contract_bg::before {
  content: "\ea1c";
}
.matrix-invalid-contract_small::before {
  content: "\ea1d";
}
.matrix-maternity-leave::before {
  content: "\ea1e";
}
.matrix-maternity-leave_bg::before {
  content: "\ea1f";
}
.matrix-matrix::before {
  content: "\ea20";
}
.matrix-mn::before {
  content: "\ea21";
}
.matrix-moon::before {
  content: "\ea22";
}
.matrix-obstacles-on-the-employers-part::before {
  content: "\ea23";
}
.matrix-obstacles-on-the-employers-part_bg::before {
  content: "\ea24";
}
.matrix-overtime-leave::before {
  content: "\ea25";
}
.matrix-overtime-leave_bg::before {
  content: "\ea26";
}
.matrix-paid-leave::before {
  content: "\ea27";
}
.matrix-paid-leave_bg::before {
  content: "\ea28";
}
.matrix-parental-leave::before {
  content: "\ea29";
}
.matrix-parental-leave_bg::before {
  content: "\ea2a";
}
.matrix-settings::before {
  content: "\ea2b";
}
.matrix-settings-1::before {
  content: "\ea2c";
}
.matrix-settings_bg::before {
  content: "\ea2d";
}
.matrix-settings_bg-1::before {
  content: "\ea2e";
}
.matrix-sick-leave::before {
  content: "\ea2f";
}
.matrix-sick-leave_bg::before {
  content: "\ea30";
}
.matrix-sick-leave_small::before {
  content: "\ea31";
}
.matrix-sun::before {
  content: "\ea32";
}
.matrix-team::before {
  content: "\ea33";
}
.matrix-team_bg::before {
  content: "\ea34";
}
.matrix-thumbnail::before {
  content: "\ea35";
}
.matrix-time-off-for-dependants::before {
  content: "\ea36";
}
.matrix-time-off-for-dependants_bg::before {
  content: "\ea37";
}
.matrix-time-off-for-doctors::before {
  content: "\ea38";
}
.matrix-time-off-for-doctors_bg::before {
  content: "\ea39";
}
.matrix-tooltip::before {
  content: "\ea3a";
}
.matrix-topmanager::before {
  content: "\ea3b";
}
.matrix-topmanager_bg::before {
  content: "\ea3c";
}
.matrix-treatment-of-a-family-member::before {
  content: "\ea3d";
}
.matrix-treatment-of-a-family-member_bg::before {
  content: "\ea3e";
}
.matrix-unpaid-leave::before {
  content: "\ea3f";
}
.matrix-unpaid-leave_bg::before {
  content: "\ea40";
}
.matrix-vacation::before {
  content: "\ea41";
}
.matrix-vacation_bg::before {
  content: "\ea42";
}
.matrix-vacations::before {
  content: "\ea43";
}
.matrix-vacations_bg::before {
  content: "\ea44";
}
.matrix-vermont-intranet-pass::before {
  content: "\ea45";
}
.matrix-vermont-ucko::before {
  content: "\ea46";
}
.matrix-warehouse::before {
  content: "\ea47";
}
.matrix-warehouse_bg::before {
  content: "\ea48";
}
