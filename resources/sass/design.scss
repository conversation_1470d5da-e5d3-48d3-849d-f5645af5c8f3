$design-01test: "\ea01";
$design-acceleration: "\ea02";
$design-acceleration-outline: "\ea03";
$design-add: "\ea04";
$design-addition: "\ea05";
$design-alert: "\ea06";
$design-animatesvg: "\ea07";
$design-are: "\ea08";
$design-arl: "\ea09";
$design-arm: "\ea0a";
$design-arr: "\ea0b";
$design-arrow-down: "\ea0c";
$design-arrow-left: "\ea0d";
$design-arrow-right: "\ea0e";
$design-arrow-up: "\ea0f";
$design-ascend: "\ea10";
$design-asterisk: "\ea11";
$design-asterisk2: "\ea12";
$design-asterisk3: "\ea13";
$design-atoms: "\ea14";
$design-barcode: "\ea15";
$design-birthday: "\ea16";
$design-bond: "\ea17";
$design-book: "\ea18";
$design-bookmark: "\ea19";
$design-bootstrap: "\ea1a";
$design-bracket2: "\ea1b";
$design-bracket3: "\ea1c";
$design-brochure: "\ea1d";
$design-calendar: "\ea1e";
$design-change: "\ea1f";
$design-changes: "\ea20";
$design-chc: "\ea21";
$design-check-list: "\ea22";
$design-check-mark: "\ea23";
$design-checkbox-checked: "\ea24";
$design-checkbox-unchecked: "\ea25";
$design-checked: "\ea26";
$design-checklist2: "\ea27";
$design-chevron-down: "\ea28";
$design-chevron-left: "\ea29";
$design-chevron-right: "\ea2a";
$design-chevron-up: "\ea2b";
$design-circle-checked: "\ea2c";
$design-circle-remove: "\ea2d";
$design-clipboard: "\ea2e";
$design-clock: "\ea2f";
$design-close: "\ea30";
$design-code: "\ea31";
$design-coding: "\ea32";
$design-coding2: "\ea33";
$design-coffee: "\ea34";
$design-col-hidden: "\ea35";
$design-col-visible: "\ea36";
$design-color-picker: "\ea37";
$design-color-theory: "\ea38";
$design-color-theory2: "\ea39";
$design-colour: "\ea3a";
$design-command-pallete: "\ea3b";
$design-comment: "\ea3c";
$design-comment2: "\ea3d";
$design-commit: "\ea3e";
$design-component: "\ea3f";
$design-component2: "\ea40";
$design-copyclipboard: "\ea41";
$design-copyclipboard-outline: "\ea42";
$design-copyclipboard-scss: "\ea43";
$design-copyclipboard-scss-outline: "\ea44";
$design-copyclipboard-success: "\ea45";
$design-copyclipboard-success-outline: "\ea46";
$design-crop: "\ea47";
$design-css: "\ea48";
$design-css-filled: "\ea49";
$design-customization: "\ea4a";
$design-database: "\ea4b";
$design-deadline: "\ea4c";
$design-descend: "\ea4d";
$design-design: "\ea4e";
$design-design_icon: "\ea4f";
$design-design_invert: "\ea50";
$design-device-android: "\ea51";
$design-device-imac: "\ea52";
$design-device-iphone: "\ea53";
$design-device-iwatch: "\ea54";
$design-device-monitor: "\ea55";
$design-device-notebook: "\ea56";
$design-device-pda: "\ea57";
$design-device-tablet: "\ea58";
$design-devmode: "\ea59";
$design-docker: "\ea5a";
$design-documents: "\ea5b";
$design-double-arrow-left: "\ea5c";
$design-double-arrow-right: "\ea5d";
$design-download-file: "\ea5e";
$design-download-file-outline: "\ea5f";
$design-download-pdf: "\ea60";
$design-download-png: "\ea61";
$design-download-svg: "\ea62";
$design-edit: "\ea63";
$design-edit-filled: "\ea64";
$design-emotion: "\ea65";
$design-emotion-sad: "\ea66";
$design-error-404: "\ea67";
$design-error-500: "\ea68";
$design-eshopresizer: "\ea69";
$design-figma: "\ea6a";
$design-filter: "\ea6b";
$design-filter2: "\ea6c";
$design-filter3: "\ea6d";
$design-filter4: "\ea6e";
$design-find-me: "\ea6f";
$design-floorplan: "\ea70";
$design-flow-chart: "\ea71";
$design-folder: "\ea72";
$design-font-category: "\ea73";
$design-font-cd-img: "\ea74";
$design-font-cd-img-invert: "\ea75";
$design-font-claims: "\ea76";
$design-font-claims-invert: "\ea77";
$design-font-design: "\ea78";
$design-font-design-1: "\ea79";
$design-font-design-invert: "\ea7a";
$design-font-design2: "\ea7b";
$design-font-design3: "\ea7c";
$design-font-design4: "\ea7d";
$design-font-design4-filled: "\ea7e";
$design-font-design5: "\ea7f";
$design-font-design6: "\ea80";
$design-font-eshop: "\ea81";
$design-font-eshop-invert: "\ea82";
$design-font-matrix: "\ea83";
$design-font-matrix-invert: "\ea84";
$design-font-ordergorup: "\ea85";
$design-font-ordergorup-invert: "\ea86";
$design-font-retail: "\ea87";
$design-font-retail-invert: "\ea88";
$design-font-tos: "\ea89";
$design-font-tos-invert: "\ea8a";
$design-font-wms: "\ea8b";
$design-font-wms-invert: "\ea8c";
$design-gallery: "\ea8d";
$design-geo: "\ea8e";
$design-github: "\ea8f";
$design-go-back-arrow: "\ea90";
$design-google-docs: "\ea91";
$design-grid: "\ea92";
$design-gridview: "\ea93";
$design-guide-book: "\ea94";
$design-hand-block: "\ea95";
$design-hello-collegue: "\ea96";
$design-hidden: "\ea97";
$design-hidden-fill: "\ea98";
$design-html: "\ea99";
$design-html-filled: "\ea9a";
$design-idea: "\ea9b";
$design-idea-1: "\ea9c";
$design-images: "\ea9d";
$design-index: "\ea9e";
$design-instaresizer: "\ea9f";
$design-json: "\eaa0";
$design-json-filled: "\eaa1";
$design-kanbanview: "\eaa2";
$design-lab: "\eaa3";
$design-laravel: "\eaa4";
$design-layout2: "\eaa5";
$design-lenovo: "\eaa6";
$design-letter: "\eaa7";
$design-letter-filled: "\eaa8";
$design-link: "\eaa9";
$design-listview: "\eaaa";
$design-logotype: "\eaab";
$design-logout: "\eaac";
$design-magic_wand: "\eaad";
$design-mailgun: "\eaae";
$design-mailpit: "\eaaf";
$design-mailtrap: "\eab0";
$design-measure2: "\eab1";
$design-measure3: "\eab2";
$design-measuring: "\eab3";
$design-merge: "\eab4";
$design-mighty-matus: "\eab5";
$design-mirec-backy: "\eab6";
$design-mixer: "\eab7";
$design-mn: "\eab8";
$design-molecule: "\eab9";
$design-monitor-check: "\eaba";
$design-moon: "\eabb";
$design-moonrise: "\eabc";
$design-moonset: "\eabd";
$design-morph-tool: "\eabe";
$design-mouse-scroll: "\eabf";
$design-newlogo: "\eac0";
$design-newlogo-line: "\eac1";
$design-opacity: "\eac2";
$design-open-book: "\eac3";
$design-option: "\eac4";
$design-options: "\eac5";
$design-order_group_images: "\eac6";
$design-ordergroup: "\eac7";
$design-organism: "\eac8";
$design-pages: "\eac9";
$design-paint-bucket: "\eaca";
$design-pdf: "\eacb";
$design-pen-tool: "\eacc";
$design-phpmyadmin: "\eacd";
$design-pizza: "\eace";
$design-placeholder: "\eacf";
$design-playground-copy: "\ead0";
$design-playground-download: "\ead1";
$design-playground-file-code: "\ead2";
$design-playground-file-text: "\ead3";
$design-playground-folder-closed: "\ead4";
$design-playground-folder-oped: "\ead5";
$design-playground-upload: "\ead6";
$design-plus: "\ead7";
$design-presentation: "\ead8";
$design-printer: "\ead9";
$design-prototype: "\eada";
$design-prototype2: "\eadb";
$design-puzzle: "\eadc";
$design-qrgenerator: "\eadd";
$design-race: "\eade";
$design-race-outline: "\eadf";
$design-redis: "\eae0";
$design-refactoring: "\eae1";
$design-refresh: "\eae2";
$design-resolution_kasa: "\eae3";
$design-resolution_monitor: "\eae4";
$design-resolution_pda: "\eae5";
$design-resolution_smartphone: "\eae6";
$design-resolution_tablet: "\eae7";
$design-responsive: "\eae8";
$design-roadmapview: "\eae9";
$design-samsung: "\eaea";
$design-sass: "\eaeb";
$design-sass-filled: "\eaec";
$design-save: "\eaed";
$design-scss: "\eaee";
$design-scss-filled: "\eaef";
$design-search-fill: "\eaf0";
$design-service: "\eaf1";
$design-settings: "\eaf2";
$design-sort: "\eaf3";
$design-sun: "\eaf4";
$design-sunrise: "\eaf5";
$design-sunset: "\eaf6";
$design-svg-stuff: "\eaf7";
$design-swap: "\eaf8";
$design-table-football: "\eaf9";
$design-tableview: "\eafa";
$design-tag: "\eafb";
$design-tag-outline: "\eafc";
$design-tailwind: "\eafd";
$design-template: "\eafe";
$design-text-editor: "\eaff";
$design-thumb-down: "\eb00";
$design-thumb-up: "\eb01";
$design-thumbnail: "\eb02";
$design-time: "\eb03";
$design-to-do-list: "\eb04";
$design-transparency: "\eb05";
$design-trash: "\eb06";
$design-trash-filled: "\eb07";
$design-ty: "\eb08";
$design-ty2: "\eb09";
$design-ty3: "\eb0a";
$design-type: "\eb0b";
$design-uncd: "\eb0c";
$design-unchecked: "\eb0d";
$design-user-avatar: "\eb0e";
$design-user-outline: "\eb0f";
$design-variable-typeface: "\eb10";
$design-vector: "\eb11";
$design-vermont-intranet-pass: "\eb12";
$design-vermont-ucko: "\eb13";
$design-visible: "\eb14";
$design-visible-fill: "\eb15";
$design-warehouse: "\eb16";
$design-webfont: "\eb17";
$design-webfont-outline: "\eb18";
$design-websitelogoart-min: "\eb19";
$design-wikipedia: "\eb1a";
$design-woff: "\eb1b";
$design-woff-filled: "\eb1c";
$design-word: "\eb1d";
$design-wrench: "\eb1e";
$design-xiaomi: "\eb1f";
$design-zebra: "\eb20";
$design-zoom-in: "\eb21";
$design-zoom-in-1: "\eb22";
$design-zoom-in-1-outline: "\eb23";
$design-zoom-in-2: "\eb24";
$design-zoom-out: "\eb25";
$design-zoom-out-1: "\eb26";
$design-zoom-out-1-outline: "\eb27";
$design-zoom-out-2: "\eb28";

%design {
  display: inline-block;
  font-family: "design" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.design {
  @extend %design;
}

.design-01test::before {
  content: "\ea01";
}
.design-acceleration::before {
  content: "\ea02";
}
.design-acceleration-outline::before {
  content: "\ea03";
}
.design-add::before {
  content: "\ea04";
}
.design-addition::before {
  content: "\ea05";
}
.design-alert::before {
  content: "\ea06";
}
.design-animatesvg::before {
  content: "\ea07";
}
.design-are::before {
  content: "\ea08";
}
.design-arl::before {
  content: "\ea09";
}
.design-arm::before {
  content: "\ea0a";
}
.design-arr::before {
  content: "\ea0b";
}
.design-arrow-down::before {
  content: "\ea0c";
}
.design-arrow-left::before {
  content: "\ea0d";
}
.design-arrow-right::before {
  content: "\ea0e";
}
.design-arrow-up::before {
  content: "\ea0f";
}
.design-ascend::before {
  content: "\ea10";
}
.design-asterisk::before {
  content: "\ea11";
}
.design-asterisk2::before {
  content: "\ea12";
}
.design-asterisk3::before {
  content: "\ea13";
}
.design-atoms::before {
  content: "\ea14";
}
.design-barcode::before {
  content: "\ea15";
}
.design-birthday::before {
  content: "\ea16";
}
.design-bond::before {
  content: "\ea17";
}
.design-book::before {
  content: "\ea18";
}
.design-bookmark::before {
  content: "\ea19";
}
.design-bootstrap::before {
  content: "\ea1a";
}
.design-bracket2::before {
  content: "\ea1b";
}
.design-bracket3::before {
  content: "\ea1c";
}
.design-brochure::before {
  content: "\ea1d";
}
.design-calendar::before {
  content: "\ea1e";
}
.design-change::before {
  content: "\ea1f";
}
.design-changes::before {
  content: "\ea20";
}
.design-chc::before {
  content: "\ea21";
}
.design-check-list::before {
  content: "\ea22";
}
.design-check-mark::before {
  content: "\ea23";
}
.design-checkbox-checked::before {
  content: "\ea24";
}
.design-checkbox-unchecked::before {
  content: "\ea25";
}
.design-checked::before {
  content: "\ea26";
}
.design-checklist2::before {
  content: "\ea27";
}
.design-chevron-down::before {
  content: "\ea28";
}
.design-chevron-left::before {
  content: "\ea29";
}
.design-chevron-right::before {
  content: "\ea2a";
}
.design-chevron-up::before {
  content: "\ea2b";
}
.design-circle-checked::before {
  content: "\ea2c";
}
.design-circle-remove::before {
  content: "\ea2d";
}
.design-clipboard::before {
  content: "\ea2e";
}
.design-clock::before {
  content: "\ea2f";
}
.design-close::before {
  content: "\ea30";
}
.design-code::before {
  content: "\ea31";
}
.design-coding::before {
  content: "\ea32";
}
.design-coding2::before {
  content: "\ea33";
}
.design-coffee::before {
  content: "\ea34";
}
.design-col-hidden::before {
  content: "\ea35";
}
.design-col-visible::before {
  content: "\ea36";
}
.design-color-picker::before {
  content: "\ea37";
}
.design-color-theory::before {
  content: "\ea38";
}
.design-color-theory2::before {
  content: "\ea39";
}
.design-colour::before {
  content: "\ea3a";
}
.design-command-pallete::before {
  content: "\ea3b";
}
.design-comment::before {
  content: "\ea3c";
}
.design-comment2::before {
  content: "\ea3d";
}
.design-commit::before {
  content: "\ea3e";
}
.design-component::before {
  content: "\ea3f";
}
.design-component2::before {
  content: "\ea40";
}
.design-copyclipboard::before {
  content: "\ea41";
}
.design-copyclipboard-outline::before {
  content: "\ea42";
}
.design-copyclipboard-scss::before {
  content: "\ea43";
}
.design-copyclipboard-scss-outline::before {
  content: "\ea44";
}
.design-copyclipboard-success::before {
  content: "\ea45";
}
.design-copyclipboard-success-outline::before {
  content: "\ea46";
}
.design-crop::before {
  content: "\ea47";
}
.design-css::before {
  content: "\ea48";
}
.design-css-filled::before {
  content: "\ea49";
}
.design-customization::before {
  content: "\ea4a";
}
.design-database::before {
  content: "\ea4b";
}
.design-deadline::before {
  content: "\ea4c";
}
.design-descend::before {
  content: "\ea4d";
}
.design-design::before {
  content: "\ea4e";
}
.design-design_icon::before {
  content: "\ea4f";
}
.design-design_invert::before {
  content: "\ea50";
}
.design-device-android::before {
  content: "\ea51";
}
.design-device-imac::before {
  content: "\ea52";
}
.design-device-iphone::before {
  content: "\ea53";
}
.design-device-iwatch::before {
  content: "\ea54";
}
.design-device-monitor::before {
  content: "\ea55";
}
.design-device-notebook::before {
  content: "\ea56";
}
.design-device-pda::before {
  content: "\ea57";
}
.design-device-tablet::before {
  content: "\ea58";
}
.design-devmode::before {
  content: "\ea59";
}
.design-docker::before {
  content: "\ea5a";
}
.design-documents::before {
  content: "\ea5b";
}
.design-double-arrow-left::before {
  content: "\ea5c";
}
.design-double-arrow-right::before {
  content: "\ea5d";
}
.design-download-file::before {
  content: "\ea5e";
}
.design-download-file-outline::before {
  content: "\ea5f";
}
.design-download-pdf::before {
  content: "\ea60";
}
.design-download-png::before {
  content: "\ea61";
}
.design-download-svg::before {
  content: "\ea62";
}
.design-edit::before {
  content: "\ea63";
}
.design-edit-filled::before {
  content: "\ea64";
}
.design-emotion::before {
  content: "\ea65";
}
.design-emotion-sad::before {
  content: "\ea66";
}
.design-error-404::before {
  content: "\ea67";
}
.design-error-500::before {
  content: "\ea68";
}
.design-eshopresizer::before {
  content: "\ea69";
}
.design-figma::before {
  content: "\ea6a";
}
.design-filter::before {
  content: "\ea6b";
}
.design-filter2::before {
  content: "\ea6c";
}
.design-filter3::before {
  content: "\ea6d";
}
.design-filter4::before {
  content: "\ea6e";
}
.design-find-me::before {
  content: "\ea6f";
}
.design-floorplan::before {
  content: "\ea70";
}
.design-flow-chart::before {
  content: "\ea71";
}
.design-folder::before {
  content: "\ea72";
}
.design-font-category::before {
  content: "\ea73";
}
.design-font-cd-img::before {
  content: "\ea74";
}
.design-font-cd-img-invert::before {
  content: "\ea75";
}
.design-font-claims::before {
  content: "\ea76";
}
.design-font-claims-invert::before {
  content: "\ea77";
}
.design-font-design::before {
  content: "\ea78";
}
.design-font-design-1::before {
  content: "\ea79";
}
.design-font-design-invert::before {
  content: "\ea7a";
}
.design-font-design2::before {
  content: "\ea7b";
}
.design-font-design3::before {
  content: "\ea7c";
}
.design-font-design4::before {
  content: "\ea7d";
}
.design-font-design4-filled::before {
  content: "\ea7e";
}
.design-font-design5::before {
  content: "\ea7f";
}
.design-font-design6::before {
  content: "\ea80";
}
.design-font-eshop::before {
  content: "\ea81";
}
.design-font-eshop-invert::before {
  content: "\ea82";
}
.design-font-matrix::before {
  content: "\ea83";
}
.design-font-matrix-invert::before {
  content: "\ea84";
}
.design-font-ordergorup::before {
  content: "\ea85";
}
.design-font-ordergorup-invert::before {
  content: "\ea86";
}
.design-font-retail::before {
  content: "\ea87";
}
.design-font-retail-invert::before {
  content: "\ea88";
}
.design-font-tos::before {
  content: "\ea89";
}
.design-font-tos-invert::before {
  content: "\ea8a";
}
.design-font-wms::before {
  content: "\ea8b";
}
.design-font-wms-invert::before {
  content: "\ea8c";
}
.design-gallery::before {
  content: "\ea8d";
}
.design-geo::before {
  content: "\ea8e";
}
.design-github::before {
  content: "\ea8f";
}
.design-go-back-arrow::before {
  content: "\ea90";
}
.design-google-docs::before {
  content: "\ea91";
}
.design-grid::before {
  content: "\ea92";
}
.design-gridview::before {
  content: "\ea93";
}
.design-guide-book::before {
  content: "\ea94";
}
.design-hand-block::before {
  content: "\ea95";
}
.design-hello-collegue::before {
  content: "\ea96";
}
.design-hidden::before {
  content: "\ea97";
}
.design-hidden-fill::before {
  content: "\ea98";
}
.design-html::before {
  content: "\ea99";
}
.design-html-filled::before {
  content: "\ea9a";
}
.design-idea::before {
  content: "\ea9b";
}
.design-idea-1::before {
  content: "\ea9c";
}
.design-images::before {
  content: "\ea9d";
}
.design-index::before {
  content: "\ea9e";
}
.design-instaresizer::before {
  content: "\ea9f";
}
.design-json::before {
  content: "\eaa0";
}
.design-json-filled::before {
  content: "\eaa1";
}
.design-kanbanview::before {
  content: "\eaa2";
}
.design-lab::before {
  content: "\eaa3";
}
.design-laravel::before {
  content: "\eaa4";
}
.design-layout2::before {
  content: "\eaa5";
}
.design-lenovo::before {
  content: "\eaa6";
}
.design-letter::before {
  content: "\eaa7";
}
.design-letter-filled::before {
  content: "\eaa8";
}
.design-link::before {
  content: "\eaa9";
}
.design-listview::before {
  content: "\eaaa";
}
.design-logotype::before {
  content: "\eaab";
}
.design-logout::before {
  content: "\eaac";
}
.design-magic_wand::before {
  content: "\eaad";
}
.design-mailgun::before {
  content: "\eaae";
}
.design-mailpit::before {
  content: "\eaaf";
}
.design-mailtrap::before {
  content: "\eab0";
}
.design-measure2::before {
  content: "\eab1";
}
.design-measure3::before {
  content: "\eab2";
}
.design-measuring::before {
  content: "\eab3";
}
.design-merge::before {
  content: "\eab4";
}
.design-mighty-matus::before {
  content: "\eab5";
}
.design-mirec-backy::before {
  content: "\eab6";
}
.design-mixer::before {
  content: "\eab7";
}
.design-mn::before {
  content: "\eab8";
}
.design-molecule::before {
  content: "\eab9";
}
.design-monitor-check::before {
  content: "\eaba";
}
.design-moon::before {
  content: "\eabb";
}
.design-moonrise::before {
  content: "\eabc";
}
.design-moonset::before {
  content: "\eabd";
}
.design-morph-tool::before {
  content: "\eabe";
}
.design-mouse-scroll::before {
  content: "\eabf";
}
.design-newlogo::before {
  content: "\eac0";
}
.design-newlogo-line::before {
  content: "\eac1";
}
.design-opacity::before {
  content: "\eac2";
}
.design-open-book::before {
  content: "\eac3";
}
.design-option::before {
  content: "\eac4";
}
.design-options::before {
  content: "\eac5";
}
.design-order_group_images::before {
  content: "\eac6";
}
.design-ordergroup::before {
  content: "\eac7";
}
.design-organism::before {
  content: "\eac8";
}
.design-pages::before {
  content: "\eac9";
}
.design-paint-bucket::before {
  content: "\eaca";
}
.design-pdf::before {
  content: "\eacb";
}
.design-pen-tool::before {
  content: "\eacc";
}
.design-phpmyadmin::before {
  content: "\eacd";
}
.design-pizza::before {
  content: "\eace";
}
.design-placeholder::before {
  content: "\eacf";
}
.design-playground-copy::before {
  content: "\ead0";
}
.design-playground-download::before {
  content: "\ead1";
}
.design-playground-file-code::before {
  content: "\ead2";
}
.design-playground-file-text::before {
  content: "\ead3";
}
.design-playground-folder-closed::before {
  content: "\ead4";
}
.design-playground-folder-oped::before {
  content: "\ead5";
}
.design-playground-upload::before {
  content: "\ead6";
}
.design-plus::before {
  content: "\ead7";
}
.design-presentation::before {
  content: "\ead8";
}
.design-printer::before {
  content: "\ead9";
}
.design-prototype::before {
  content: "\eada";
}
.design-prototype2::before {
  content: "\eadb";
}
.design-puzzle::before {
  content: "\eadc";
}
.design-qrgenerator::before {
  content: "\eadd";
}
.design-race::before {
  content: "\eade";
}
.design-race-outline::before {
  content: "\eadf";
}
.design-redis::before {
  content: "\eae0";
}
.design-refactoring::before {
  content: "\eae1";
}
.design-refresh::before {
  content: "\eae2";
}
.design-resolution_kasa::before {
  content: "\eae3";
}
.design-resolution_monitor::before {
  content: "\eae4";
}
.design-resolution_pda::before {
  content: "\eae5";
}
.design-resolution_smartphone::before {
  content: "\eae6";
}
.design-resolution_tablet::before {
  content: "\eae7";
}
.design-responsive::before {
  content: "\eae8";
}
.design-roadmapview::before {
  content: "\eae9";
}
.design-samsung::before {
  content: "\eaea";
}
.design-sass::before {
  content: "\eaeb";
}
.design-sass-filled::before {
  content: "\eaec";
}
.design-save::before {
  content: "\eaed";
}
.design-scss::before {
  content: "\eaee";
}
.design-scss-filled::before {
  content: "\eaef";
}
.design-search-fill::before {
  content: "\eaf0";
}
.design-service::before {
  content: "\eaf1";
}
.design-settings::before {
  content: "\eaf2";
}
.design-sort::before {
  content: "\eaf3";
}
.design-sun::before {
  content: "\eaf4";
}
.design-sunrise::before {
  content: "\eaf5";
}
.design-sunset::before {
  content: "\eaf6";
}
.design-svg-stuff::before {
  content: "\eaf7";
}
.design-swap::before {
  content: "\eaf8";
}
.design-table-football::before {
  content: "\eaf9";
}
.design-tableview::before {
  content: "\eafa";
}
.design-tag::before {
  content: "\eafb";
}
.design-tag-outline::before {
  content: "\eafc";
}
.design-tailwind::before {
  content: "\eafd";
}
.design-template::before {
  content: "\eafe";
}
.design-text-editor::before {
  content: "\eaff";
}
.design-thumb-down::before {
  content: "\eb00";
}
.design-thumb-up::before {
  content: "\eb01";
}
.design-thumbnail::before {
  content: "\eb02";
}
.design-time::before {
  content: "\eb03";
}
.design-to-do-list::before {
  content: "\eb04";
}
.design-transparency::before {
  content: "\eb05";
}
.design-trash::before {
  content: "\eb06";
}
.design-trash-filled::before {
  content: "\eb07";
}
.design-ty::before {
  content: "\eb08";
}
.design-ty2::before {
  content: "\eb09";
}
.design-ty3::before {
  content: "\eb0a";
}
.design-type::before {
  content: "\eb0b";
}
.design-uncd::before {
  content: "\eb0c";
}
.design-unchecked::before {
  content: "\eb0d";
}
.design-user-avatar::before {
  content: "\eb0e";
}
.design-user-outline::before {
  content: "\eb0f";
}
.design-variable-typeface::before {
  content: "\eb10";
}
.design-vector::before {
  content: "\eb11";
}
.design-vermont-intranet-pass::before {
  content: "\eb12";
}
.design-vermont-ucko::before {
  content: "\eb13";
}
.design-visible::before {
  content: "\eb14";
}
.design-visible-fill::before {
  content: "\eb15";
}
.design-warehouse::before {
  content: "\eb16";
}
.design-webfont::before {
  content: "\eb17";
}
.design-webfont-outline::before {
  content: "\eb18";
}
.design-websitelogoart-min::before {
  content: "\eb19";
}
.design-wikipedia::before {
  content: "\eb1a";
}
.design-woff::before {
  content: "\eb1b";
}
.design-woff-filled::before {
  content: "\eb1c";
}
.design-word::before {
  content: "\eb1d";
}
.design-wrench::before {
  content: "\eb1e";
}
.design-xiaomi::before {
  content: "\eb1f";
}
.design-zebra::before {
  content: "\eb20";
}
.design-zoom-in::before {
  content: "\eb21";
}
.design-zoom-in-1::before {
  content: "\eb22";
}
.design-zoom-in-1-outline::before {
  content: "\eb23";
}
.design-zoom-in-2::before {
  content: "\eb24";
}
.design-zoom-out::before {
  content: "\eb25";
}
.design-zoom-out-1::before {
  content: "\eb26";
}
.design-zoom-out-1-outline::before {
  content: "\eb27";
}
.design-zoom-out-2::before {
  content: "\eb28";
}
