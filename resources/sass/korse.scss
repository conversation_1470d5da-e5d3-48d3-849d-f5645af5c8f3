// Colors
$color-white: white;
$color-border: #dee2e6;
$color-outline: #c3dbff;
$color-text: rgba(33, 37, 41, 0.75);

// Font sizes
$font-size-base: 1.2rem;
$font-size-small: 0.9rem;

// Spacing
$spacing-xs: 0.375rem;
$spacing-sm: 0.5rem;
$spacing-md: 0.75rem;
$spacing-lg: 1rem;

// Border radius
$border-radius: 0.375rem;


@mixin input-common {
  background: white;
  width: 100%;
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 120%;
  appearance: none;
  background-clip: padding-box;
  border: none;
}

$input-types: (
  "select-input-v2-wrapper",
  "select-input-v2",
  "date-input",
  "base-input"
);

%input-wrapper {
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
}

.input-component-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  border: 1px solid $color-border;
  border-radius: $border-radius;
}

@each $type in $input-types {
  .#{$type} {
    @include input-common;
    
    @if $type == "select-input-v2" {
      padding: 0 0.75rem;
    } @else {
      padding: 0 0.75rem 0.375rem 0.75rem;
    }
    
    @if $type == "date-input" or $type == "base-input" {
      border: none !important;
      outline: none !important;
    }
  }
  
  @if $type == "date-input" or $type == "base-input" {
    .#{$type}-wrapper {
      @extend %input-wrapper;
    }
  }
}


.select-input-v2-focus {
  outline: none;
  border-radius: $border-radius;
}

.date-input:focus {
  border: none !important;
  outline: none !important;
}
