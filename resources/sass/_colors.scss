// 
// ██████╗ ███████╗ █████╗ ██████╗ ██╗   ██╗    ████████╗ ██████╗     ██╗   ██╗███████╗███████╗
// ██╔══██╗██╔════╝██╔══██╗██╔══██╗╚██╗ ██╔╝    ╚══██╔══╝██╔═══██╗    ██║   ██║██╔════╝██╔════╝
// ██████╔╝█████╗  ███████║██║  ██║ ╚████╔╝        ██║   ██║   ██║    ██║   ██║███████╗█████╗  
// ██╔══██╗██╔══╝  ██╔══██║██║  ██║  ╚██╔╝         ██║   ██║   ██║    ██║   ██║╚════██║██╔══╝  
// ██║  ██║███████╗██║  ██║██████╔╝   ██║          ██║   ╚██████╔╝    ╚██████╔╝███████║███████╗
// ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝    ╚═╝          ╚═╝    ╚═════╝      ╚═════╝ ╚══════╝╚══════╝

// Vermont project colours superset needed in the design system. This is a collection of all the colours used in Dev projects.

// _colors.scss

@use "sass:map";
:root {
  // Theme colors (used in $color-system)
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;

  // RGB values (used for opacity utilities)
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;

  // Text emphasis colors
  --bs-primary-text-emphasis: #052c65;
  --bs-secondary-text-emphasis: #2b2f32;
  --bs-success-text-emphasis: #0a3622;
  --bs-info-text-emphasis: #055160;
  --bs-warning-text-emphasis: #664d03;
  --bs-danger-text-emphasis: #58151c;
  --bs-light-text-emphasis: #495057;
  --bs-dark-text-emphasis: #495057;

  // Subtle background colors
  --bs-primary-bg-subtle: #cfe2ff;
  --bs-secondary-bg-subtle: #e2e3e5;
  --bs-success-bg-subtle: #d1e7dd;
  --bs-info-bg-subtle: #cff4fc;
  --bs-warning-bg-subtle: #fff3cd;
  --bs-danger-bg-subtle: #f8d7da;
  --bs-light-bg-subtle: #fcfcfd;
  --bs-dark-bg-subtle: #ced4da;

  // Subtle border colors
  --bs-primary-border-subtle: #9ec5fe;
  --bs-secondary-border-subtle: #c4c8cb;
  --bs-success-border-subtle: #a3cfbb;
  --bs-info-border-subtle: #9eeaf9;
  --bs-warning-border-subtle: #ffe69c;
  --bs-danger-border-subtle: #f1aeb5;
  --bs-light-border-subtle: #e9ecef;
  --bs-dark-border-subtle: #adb5bd;
}

// Single comprehensive color system map
$color-system: (
"primary": (
  "base": var(--bs-primary),
  "rgb": var(--bs-primary-rgb),
  "text-emphasis": var(--bs-primary-text-emphasis),
  "bg-subtle": var(--bs-primary-bg-subtle),
  "border-subtle": var(--bs-primary-border-subtle)
),
"secondary": (
  "base": var(--bs-secondary),
  "rgb": var(--bs-secondary-rgb),
  "text-emphasis": var(--bs-secondary-text-emphasis),
  "bg-subtle": var(--bs-secondary-bg-subtle),
  "border-subtle": var(--bs-secondary-border-subtle)
),
"success": (
  "base": var(--bs-success),
  "rgb": var(--bs-success-rgb),
  "text-emphasis": var(--bs-success-text-emphasis),
  "bg-subtle": var(--bs-success-bg-subtle),
  "border-subtle": var(--bs-success-border-subtle)
),
"danger": (
  "base": var(--bs-danger),
  "rgb": var(--bs-danger-rgb),
  "text-emphasis": var(--bs-danger-text-emphasis),
  "bg-subtle": var(--bs-danger-bg-subtle),
  "border-subtle": var(--bs-danger-border-subtle)
),
"warning": (
  "base": var(--bs-warning),
  "rgb": var(--bs-warning-rgb),
  "text-emphasis": var(--bs-warning-text-emphasis),
  "bg-subtle": var(--bs-warning-bg-subtle),
  "border-subtle": var(--bs-warning-border-subtle)
),
"info": (
  "base": var(--bs-info),
  "rgb": var(--bs-info-rgb),
  "text-emphasis": var(--bs-info-text-emphasis),
  "bg-subtle": var(--bs-info-bg-subtle),
  "border-subtle": var(--bs-info-border-subtle)
),
"light": (
  "base": var(--bs-light),
  "rgb": var(--bs-light-rgb),
  "text-emphasis": var(--bs-light-text-emphasis),
  "bg-subtle": var(--bs-light-bg-subtle),
  "border-subtle": var(--bs-light-border-subtle)
),
"dark": (
  "base": var(--bs-dark),
  "rgb": var(--bs-dark-rgb),
  "text-emphasis": var(--bs-dark-text-emphasis),
  "bg-subtle": var(--bs-dark-bg-subtle),
  "border-subtle": var(--bs-dark-border-subtle)
)
);

// Generate all color utility classes in a single efficient loop
@each $color-name, $color-variants in $color-system {
  // Base theme colors
  .bg-#{$color-name} {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-#{$color-name}-rgb), var(--bs-bg-opacity)) !important;
  }

  .text-#{$color-name} {
    color: map.get($color-variants, "base") !important;
  }

  .border-#{$color-name} {
    border-color: map.get($color-variants, "base") !important;
  }

  .btn-#{$color-name}:hover {
    filter: brightness(90%);
  }

  // Subtle variants
  .bg-#{$color-name}-subtle {
    background-color: map.get($color-variants, "bg-subtle") !important;
  }

  .bg-#{$color-name}-emphasis {
    background-color: map.get($color-variants, "bg-emphasis") !important;
  }

  .text-#{$color-name}-subtle {
    color: map.get($color-variants, "text-subtle") !important;
  }

  .text-#{$color-name}-emphasis {
    color: map.get($color-variants, "text-emphasis") !important;
  }

  .border-#{$color-name}-subtle {
    border-color: map.get($color-variants, "border-subtle") !important;
  }

  .border-#{$color-name}-emphasis {
    border-color: map.get($color-variants, "border-emphasis") !important;
  }

  // RGB utilities for transparency support
  .bg-#{$color-name}-rgb {
    --bs-bg-opacity: 1;
    background-color: rgba(var(--bs-#{$color-name}-rgb), var(--bs-bg-opacity)) !important;
  }

  .text-#{$color-name}-rgb {
    --bs-text-opacity: 1;
    color: rgba(var(--bs-#{$color-name}-rgb), var(--bs-text-opacity)) !important;
  }
}

// Opacity utilities that work with the RGB variants
@for $i from 1 through 9 {
  $opacity: $i * 0.1;
  .opacity-#{$i}0 {
    --bs-bg-opacity: #{$opacity};
    --bs-text-opacity: #{$opacity};
  }
}

// Standard Bootstrap background opacity utilities
$bg-opacity-values: (
  10: 0.1,
  25: 0.25,
  50: 0.5,
  75: 0.75,
  100: 1
);

@each $level, $opacity in $bg-opacity-values {
  .bg-opacity-#{$level} {
    --bs-bg-opacity: #{$opacity} !important;
  }
}

