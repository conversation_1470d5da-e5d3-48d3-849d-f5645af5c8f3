$transporthub-back: "\ea01";
$transporthub-back-outline: "\ea02";
$transporthub-cancel: "\ea03";
$transporthub-check: "\ea04";
$transporthub-check-1: "\ea05";
$transporthub-close: "\ea06";
$transporthub-close-outline: "\ea07";
$transporthub-empty: "\ea08";
$transporthub-empty-outline: "\ea09";
$transporthub-forward: "\ea0a";
$transporthub-forward-outline: "\ea0b";
$transporthub-info: "\ea0c";
$transporthub-info-outline: "\ea0d";
$transporthub-okay: "\ea0e";
$transporthub-remove-outline: "\ea0f";
$transporthub-remove-outline-1: "\ea10";
$transporthub-select: "\ea11";
$transporthub-select-all: "\ea12";
$transporthub-select-all-outline: "\ea13";
$transporthub-select-none: "\ea14";
$transporthub-select-none-outline: "\ea15";
$transporthub-select-outline: "\ea16";
$transporthub-time: "\ea17";
$transporthub-time-outline: "\ea18";
$transporthub-trash: "\ea19";
$transporthub-trash-outline: "\ea1a";

%transporthub {
  display: inline-block;
  font-family: "transporthub" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  vertical-align: -.125em;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.transporthub {
  @extend %transporthub;
}

.transporthub-back::before {
  content: "\ea01";
}
.transporthub-back-outline::before {
  content: "\ea02";
}
.transporthub-cancel::before {
  content: "\ea03";
}
.transporthub-check::before {
  content: "\ea04";
}
.transporthub-check-1::before {
  content: "\ea05";
}
.transporthub-close::before {
  content: "\ea06";
}
.transporthub-close-outline::before {
  content: "\ea07";
}
.transporthub-empty::before {
  content: "\ea08";
}
.transporthub-empty-outline::before {
  content: "\ea09";
}
.transporthub-forward::before {
  content: "\ea0a";
}
.transporthub-forward-outline::before {
  content: "\ea0b";
}
.transporthub-info::before {
  content: "\ea0c";
}
.transporthub-info-outline::before {
  content: "\ea0d";
}
.transporthub-okay::before {
  content: "\ea0e";
}
.transporthub-remove-outline::before {
  content: "\ea0f";
}
.transporthub-remove-outline-1::before {
  content: "\ea10";
}
.transporthub-select::before {
  content: "\ea11";
}
.transporthub-select-all::before {
  content: "\ea12";
}
.transporthub-select-all-outline::before {
  content: "\ea13";
}
.transporthub-select-none::before {
  content: "\ea14";
}
.transporthub-select-none-outline::before {
  content: "\ea15";
}
.transporthub-select-outline::before {
  content: "\ea16";
}
.transporthub-time::before {
  content: "\ea17";
}
.transporthub-time-outline::before {
  content: "\ea18";
}
.transporthub-trash::before {
  content: "\ea19";
}
.transporthub-trash-outline::before {
  content: "\ea1a";
}
