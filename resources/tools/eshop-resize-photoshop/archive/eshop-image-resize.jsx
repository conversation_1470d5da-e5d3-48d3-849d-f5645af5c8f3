//@target photoshop

// Configuration
const CONFIG = {
    defaults: {
        width: 1600,
        height: 2400,
        bgColor: "#FFFFFF",
        quality: 12
    },
    supportedFormats: /\.(jpg|jpeg|png|tif|psd|psb)$/i
};

// State management
var state = {
    shouldAbortProcessing: false,
    selectedInputFolder: null,
    selectedOutputFolder: null
};

// Font configuration
const FONTS = (function() {
    try {
        return {
            normal: ScriptUI.newFont("Arial", "REGULAR", 14),
            header: ScriptUI.newFont("Arial", "REGULAR", 16),
            button: ScriptUI.newFont("Arial", "REGULAR", 13),
            title: ScriptUI.newFont("Arial", "REGULAR", 20)
        };
    } catch(e) {
        return {
            normal: ScriptUI.newFont("dialog", "REGULAR", 14),
            header: ScriptUI.newFont("dialog", "REGULAR", 16),
            button: ScriptUI.newFont("dialog", "REGULAR", 13),
            title: ScriptUI.newFont("dialog", "REGULAR", 20)
        };
    }
})();

// Utility functions
const Utils = {
    hexToRgb: function(hex) {
        hex = hex.replace('#', '');
        return {
            r: parseInt(hex.substring(0, 2), 16),
            g: parseInt(hex.substring(2, 4), 16),
            b: parseInt(hex.substring(4, 6), 16)
        };
    },

    rgbToHex: function(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
    },

    isValidHex: function(hex) {
        return /^#[0-9A-F]{6}$/i.test(hex);
    },

    isValidRGB: function(value) {
        return !isNaN(value) && value >= 0 && value <= 255;
    },

    formatTime: function(seconds) {
        if (seconds < 60) return seconds + "s";
        const minutes = Math.floor(seconds / 60);
        seconds = seconds % 60;
        return minutes + "m " + seconds + "s";
    },

    createFolder: function(folder) {
        if (!folder.exists) {
            folder.create();
        }
        return folder;
    },

    cleanupDocument: function(doc) {
        if (doc) {
            try {
                doc.close(SaveOptions.DONOTSAVECHANGES);
            } catch(e) {
                // Silently handle any cleanup errors
            }
        }
    },

    getAnchorPosition: function(alignment) {
        switch(alignment) {
            case 'top': return AnchorPosition.TOPCENTER;
            case 'bottom': return AnchorPosition.BOTTOMCENTER;
            default: return AnchorPosition.MIDDLECENTER;
        }
    }
};

// Image Processing Functions
var ImageProcessor = {
    resizeAndCenterImage: function(doc, bgColor, fitMethod, targetWidth, targetHeight, alignment) {
        var docCopy = null;
        try {
            docCopy = doc.duplicate();
            app.preferences.rulerUnits = Units.PIXELS;
            
            if (fitMethod === "crop") {
                var ratio = Math.max(targetWidth / doc.width, targetHeight / doc.height);
                var newWidth = Math.round(doc.width * ratio);
                var newHeight = Math.round(doc.height * ratio);
                
                docCopy.resizeImage(UnitValue(newWidth, "px"), UnitValue(newHeight, "px"), null, ResampleMethod.BICUBIC);
                docCopy.resizeCanvas(UnitValue(targetWidth, "px"), UnitValue(targetHeight, "px"), Utils.getAnchorPosition(alignment));
            } else {
                var ratio = Math.min(targetWidth / doc.width, targetHeight / doc.height);
                var newWidth = Math.round(doc.width * ratio);
                var newHeight = Math.round(doc.height * ratio);
                
                docCopy.resizeImage(UnitValue(newWidth, "px"), UnitValue(newHeight, "px"), null, ResampleMethod.BICUBIC);
                
                var rgb = Utils.hexToRgb(bgColor);
                var solidColor = new SolidColor();
                solidColor.rgb.red = rgb.r;
                solidColor.rgb.green = rgb.g;
                solidColor.rgb.blue = rgb.b;
                app.backgroundColor = solidColor;
                
                docCopy.resizeCanvas(UnitValue(targetWidth, "px"), UnitValue(targetHeight, "px"), Utils.getAnchorPosition(alignment));
            }

            return docCopy;
        } catch(e) {
            if (docCopy) docCopy.close(SaveOptions.DONOTSAVECHANGES);
            alert("Error in resizeAndCenterImage: " + e);
            return null;
        }
    },

    resizeWithContentAwareFill: function(doc, fitMethod, targetWidth, targetHeight) {
        try {
            var docCopy = doc.duplicate();
            
            if (fitMethod === "crop") {
                var ratio = Math.max(targetWidth / docCopy.width, targetHeight / docCopy.height);
            } else {
                var ratio = Math.min(targetWidth / docCopy.width, targetHeight / docCopy.height);
            }
            
            var newWidth = Math.round(docCopy.width * ratio);
            var newHeight = Math.round(docCopy.height * ratio);
            
            docCopy.resizeImage(newWidth, newHeight, null, ResampleMethod.BICUBIC);
            docCopy.resizeCanvas(targetWidth, targetHeight, AnchorPosition.MIDDLECENTER);
            
            docCopy.selection.selectAll();
            docCopy.selection.invert();
            
            if (!docCopy.selection.bounds.isEmpty) {
                var desc = new ActionDescriptor();
                var ref = new ActionReference();
                ref.putProperty(charIDToTypeID('Prpr'), charIDToTypeID('fill'));
                ref.putEnumerated(charIDToTypeID('Lyr '), charIDToTypeID('Ordn'), charIDToTypeID('Trgt'));
                desc.putReference(charIDToTypeID('null'), ref);
                desc.putEnumerated(charIDToTypeID('Usng'), charIDToTypeID('FlCn'), stringIDToTypeID('contentAware'));
                executeAction(charIDToTypeID('Fl  '), desc, DialogModes.NO);
                
                docCopy.selection.deselect();
            }
            
            return docCopy;
        } catch(e) {
            alert("Error in contentAwareFill: " + e);
            return this.resizeAndCenterImage(doc, CONFIG.defaults.bgColor, fitMethod, targetWidth, targetHeight);
        }
    },

    processImage: function(file, outputFolder, settings) {
        var doc = null;
        var docCopy = null;
        
        try {
            doc = app.open(file);
            
            docCopy = this.resizeAndCenterImage(
                doc,
                settings.bgColor,
                settings.fitMethod,
                parseInt(settings.targetWidth),
                parseInt(settings.targetHeight),
                settings.alignment
            );

            if (docCopy) {
                var saveFile = new File(outputFolder.fsName + "/" + file.name);
                var saveOptions = new JPEGSaveOptions();
                saveOptions.quality = parseInt(settings.quality);
                saveOptions.formatOptions = FormatOptions.OPTIMIZEDBASELINE;
                saveOptions.embedColorProfile = true;

                docCopy.saveAs(saveFile, saveOptions);
                return true;
            }
            return false;
        } catch(e) {
            alert("Error processing " + file.name + ": " + e);
            return false;
        } finally {
            if (docCopy) docCopy.close(SaveOptions.DONOTSAVECHANGES);
            if (doc) doc.close(SaveOptions.DONOTSAVECHANGES);
        }
    },

    createPreview: function(file, settings) {
        var doc = null;
        var docCopy = null;
        var originalDoc = null;
        
        try {
            doc = app.open(file);
            originalDoc = doc.duplicate();
            
            // Resize preview to fit screen
            var screenWidth = app.activeDocument.width;
            var screenHeight = app.activeDocument.height;
            var previewSettings = {
                targetWidth: Math.min(screenWidth / 2, settings.targetWidth),
                targetHeight: Math.min(screenHeight / 2, settings.targetHeight),
                fillMethod: settings.fillMethod,
                fitMethod: settings.fitMethod,
                quality: settings.quality,
                bgColor: settings.bgColor,
                alignment: settings.alignment
            };

            // Process preview
            docCopy = this.resizeAndCenterImage(
                doc,
                previewSettings.bgColor,
                previewSettings.fitMethod,
                previewSettings.targetWidth,
                previewSettings.targetHeight,
                previewSettings.alignment
            );

            if (docCopy) {
                // Arrange documents side by side
                originalDoc.activeView.zoom = originalDoc.activeView.fitPage;
                docCopy.activeView.zoom = docCopy.activeView.fitPage;
                
                // Position windows
                app.preferences.rulerUnits = Units.PIXELS;
                originalDoc.activeView.bounds = [0, 0, screenWidth/2, screenHeight];
                docCopy.activeView.bounds = [screenWidth/2, 0, screenWidth, screenHeight];

                // Wait for user confirmation
                var response = confirm("Preview: Click OK to continue with processing, Cancel to adjust settings.");
                
                return response;
            }
            return false;
        } catch(e) {
            alert("Preview error: " + e);
            return false;
        } finally {
            Utils.cleanupDocument(docCopy);
            Utils.cleanupDocument(originalDoc);
            Utils.cleanupDocument(doc);
        }
    },

    validateSettings: function(settings) {
        var errors = [];
        
        if (!settings.targetWidth || settings.targetWidth <= 0) {
            errors.push("Invalid target width");
        }
        if (!settings.targetHeight || settings.targetHeight <= 0) {
            errors.push("Invalid target height");
        }
        if (settings.fillMethod === "normal" && !Utils.isValidHex(settings.bgColor)) {
            errors.push("Invalid background color");
        }
        if (settings.quality < 0 || settings.quality > 12) {
            errors.push("Quality must be between 0 and 12");
        }
        
        return errors;
    }
};

// Add translations object with better organization
const TRANSLATIONS = {
    en: {
        title: "Vermont eShop - Image Processing",
        settings: "Settings",
        dimensions: "Dimensions",
        pixels: "pixels",
        cropOption: "Crop Option",
        cropOptionDesc: "Choose how to handle image dimensions:",
        cropOptionExtend: "Extend (Add Background)",
        cropOptionCrop: "Crop to Fit",
        fillMethod: "Fill Method",
        fillMethodDesc: "Choose method for the background color:",
        fillMethodNormal: "Normal Fill (Solid Color)",
        fillMethodAware: "Content-Aware Fill",
        backgroundColor: "Background Color",
        quality: "JPEG Quality",
        qualityLabel: "Quality:",
        inputFolder: "Input Folder",
        selectedFolder: "Selected folder:",
        noFolderSelected: "No folder selected",
        selectInputButton: "Select Input Folder",
        outputFolder: "Output Folder",
        useInputFolder: "Use Input Folder",
        useCustomFolder: "Custom Folder",
        selectOutputButton: "Select Output Folder",
        ready: "Ready to process...",
        cancel: "Cancel",
        abort: "Abort Processing",
        run: "Run Process",
        imageAlignment: "Image Alignment:",
        alignTop: "Top",
        alignCenter: "Center",
        alignBottom: "Bottom",
        outputFolderInfo: "Files will be saved in a new 'processed' folder within the input directory. Original files will not be modified.",
        dimensionsDesc: "Target dimensions:",
        processing: "Processing: {0}",
        progress: "Progress: {0}% ({1} of {2})",
        processedCount: "Processed: {0} | Errors: {1}",
        timeRemaining: "Estimated time remaining: {0}",
        completed: "Completed!\nTotal files processed: {0}\nErrors encountered: {1}\nTotal time: {2}\nAverage time per image: {3}s",
        noValidFiles: "No valid image files found in the folder.",
        processingError: "Error processing {0}: {1}",
        contentAwareFallback: "Warning: Content-aware fill failed for {0}. Using normal fill."
    },
    sk: {
        title: "Vermont eShop - Spracovanie Obrázkov",
        settings: "Nastavenia",
        dimensions: "Rozmery",
        pixels: "pixelov",
        cropOption: "Možnosť Orezania",
        cropOptionDesc: "Vyberte spôsob úpravy rozmerov:",
        cropOptionExtend: "Rozšíriť (Pridať Pozadie)",
        cropOptionCrop: "Orezať na Rozmer",
        fillMethod: "Spôsob Výplne",
        fillMethodDesc: "Vyberte spôsob pre farbu pozadia:",
        fillMethodNormal: "Normálna Výplň (Plná Farba)",
        fillMethodAware: "Kontextová Výplň",
        backgroundColor: "Farba Pozadia",
        quality: "JPEG Kvalita",
        qualityLabel: "Kvalita:",
        inputFolder: "Vstupný Priečinok",
        selectedFolder: "Vybraný priečinok:",
        noFolderSelected: "Žiadny priečinok nie je vybraný",
        selectInputButton: "Vybrať Vstupný Priečinok",
        outputFolder: "Výstupný Priečinok",
        useInputFolder: "Použiť Vstupný Priečinok",
        useCustomFolder: "Vlastný Priečinok",
        selectOutputButton: "Vybrať Výstupný Priečinok",
        ready: "Pripravené na spracovanie...",
        cancel: "Zrušiť",
        abort: "Prerušiť Spracovanie",
        run: "Spustiť Proces",
        imageAlignment: "Zarovnanie Obrázka:",
        alignTop: "Hore",
        alignCenter: "Stred",
        alignBottom: "Dole",
        outputFolderInfo: "Súbory budú uložené v novom priečinku 'processed' vo vstupnom adresári. Pôvodné súbory nebudú upravené.",
        dimensionsDesc: "Cieľové rozmery:",
    },
    hu: {
        title: "Vermont eShop - Képfeldolgozó",
        settings: "Beállítások",
        dimensions: "Méretek",
        pixels: "pixel",
        cropOption: "Vágási Beállítás",
        cropOptionDesc: "Válassza ki a képméret kezelésének módját:",
        cropOptionExtend: "Kiterjesztés (Háttér Hozzáadása)",
        cropOptionCrop: "Vágás Méretre",
        fillMethod: "Kitöltési Mód",
        fillMethodDesc: "Válassza ki a háttérszín módszerét:",
        fillMethodNormal: "Normál Kitöltés (Egyszínű)",
        fillMethodAware: "Tartalomtudatos Kitöltés",
        backgroundColor: "Háttérszín",
        quality: "JPEG Minőség",
        qualityLabel: "Minőség:",
        inputFolder: "Bemeneti Mappa",
        selectedFolder: "Kiválasztott mappa:",
        noFolderSelected: "Nincs mappa kiválasztva",
        selectInputButton: "Bemeneti Mappa Kiválasztása",
        outputFolder: "Kimeneti Mappa",
        useInputFolder: "Bemeneti Mappa Használata",
        useCustomFolder: "Egyéni Mappa",
        selectOutputButton: "Kimeneti Mappa Kiválasztása",
        ready: "Feldolgozásra kész...",
        cancel: "Mégse",
        abort: "Feldolgozás Megszakítása",
        run: "Folyamat Indítása",
        imageAlignment: "Kép Igazítása:",
        alignTop: "Felül",
        alignCenter: "Közép",
        alignBottom: "Alul",
        outputFolderInfo: "A fájlok az input könyvtáron belül egy új 'processed' mappába kerülnek mentésre. Az eredeti fájlok nem módosulnak.",
        dimensionsDesc: "Célméretek:",
    }
};

// Initialize fonts
var defaultFont, headerFont, buttonFont, titleFont;
try {
    defaultFont = ScriptUI.newFont("Arial", "REGULAR", 14);
    headerFont = ScriptUI.newFont("Arial", "REGULAR", 16);
    buttonFont = ScriptUI.newFont("Arial", "REGULAR", 13);
    titleFont = ScriptUI.newFont("Arial", "REGULAR", 20);
} catch(e) {
    defaultFont = ScriptUI.newFont("dialog", "REGULAR", 14);
    headerFont = ScriptUI.newFont("dialog", "REGULAR", 16);
    buttonFont = ScriptUI.newFont("dialog", "REGULAR", 13);
    titleFont = ScriptUI.newFont("dialog", "REGULAR", 20);
}

// Helper function to convert hex to RGB
function hexToRgb(hex) {
    hex = hex.replace('#', '');
    return {
        r: parseInt(hex.substring(0, 2), 16),
        g: parseInt(hex.substring(2, 4), 16),
        b: parseInt(hex.substring(4, 6), 16)
    };
}

// Helper function to convert RGB to Hex
function rgbToHex(r, g, b) {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

// Helper function to validate hex color
function isValidHex(hex) {
    return /^#[0-9A-F]{6}$/i.test(hex);
}

// Helper function to validate RGB values
function isValidRGB(value) {
    return !isNaN(value) && value >= 0 && value <= 255;
}

// Function to decode base64 and save to a file
function saveBase64AsFile(base64, filePath) {
    var decoded = decodeBase64(base64);
    var file = new File(filePath);
    file.open("w");
    file.encoding = "BINARY";
    for (var i = 0; i < decoded.length; i++) {
        file.write(String.fromCharCode(decoded[i]));
    }
    file.close();
    alert("Temp file path: " + filePath + "\nFile exists: " + file.exists);
    return file;
}

// Decode base64 string
function decodeBase64(base64) {
    var keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    var output = [];
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;

    // Clean the base64 string
    var cleanBase64 = [];
    for (var j = 0; j < base64.length; j++) {
        if (keyStr.indexOf(base64.charAt(j)) !== -1 || base64.charAt(j) === '=') {
            cleanBase64.push(base64.charAt(j));
        }
    }
    cleanBase64 = cleanBase64.join('');

    while (i < cleanBase64.length) {
        enc1 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc2 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc3 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc4 = keyStr.indexOf(cleanBase64.charAt(i++));

        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;

        output.push(chr1);

        if (enc3 !== 64) {
            output.push(chr2);
        }
        if (enc4 !== 64) {
            output.push(chr3);
        }
    }

    return output; // Return a regular array
}

// Create the main dialog
function createDialog() {
    var dialog = new Window("dialog", "Vermont eShop - Image Processing");
    dialog.orientation = "column";
    dialog.alignChildren = "fill";
    
    // Initialize the ui object
    var ui = {};

    // Add language selector at the top
    var langGroup = dialog.add("group");
    langGroup.orientation = "row";
    langGroup.alignment = "right";
    langGroup.spacing = 10;
    
    langGroup.add("statictext", undefined, "Language:");
    var languageDropdown = langGroup.add("dropdownlist", undefined, ["English", "Slovensky", "Magyar"]);
    languageDropdown.selection = 0; // Default to English

    // Add language change handler
    languageDropdown.onChange = function() {
        var lang = languageDropdown.selection.index === 0 ? 'en' : 
                  languageDropdown.selection.index === 1 ? 'sk' : 'hu';
        
        // Update all text elements
        dialog.text = TRANSLATIONS[lang].title;
        settingsPanel.text = TRANSLATIONS[lang].settings;
        ui.dimensionsPanel.text = TRANSLATIONS[lang].dimensions;
        ui.dimLabel.text = TRANSLATIONS[lang].dimensionsDesc;
        ui.pixelsLabel.text = TRANSLATIONS[lang].pixels;
        
        cropOptionPanel.text = TRANSLATIONS[lang].cropOption;
        cropOptionLabel.text = TRANSLATIONS[lang].cropOptionDesc;
        cropOptionDropdown.items[0].text = TRANSLATIONS[lang].cropOptionExtend;
        cropOptionDropdown.items[1].text = TRANSLATIONS[lang].cropOptionCrop;
        
        ui.alignmentLabel.text = TRANSLATIONS[lang].imageAlignment;
        ui.topAlign.text = TRANSLATIONS[lang].alignTop;
        ui.centerAlign.text = TRANSLATIONS[lang].alignCenter;
        ui.bottomAlign.text = TRANSLATIONS[lang].alignBottom;
        
        ui.fillMethodPanel.text = TRANSLATIONS[lang].fillMethod;
        ui.fillMethodLabel.text = TRANSLATIONS[lang].fillMethodDesc;
        ui.fillMethodDropdown.items[0].text = TRANSLATIONS[lang].fillMethodNormal;
        ui.fillMethodDropdown.items[1].text = TRANSLATIONS[lang].fillMethodAware;
        
        ui.bgColorPanel.text = TRANSLATIONS[lang].backgroundColor;
        qualityPanel.text = TRANSLATIONS[lang].quality;
        qualityLabel.text = TRANSLATIONS[lang].qualityLabel;
        
        inputFolderPanel.text = TRANSLATIONS[lang].inputFolder;
        folderLabel.text = TRANSLATIONS[lang].selectedFolder;
        inputFolderPath.text = ui.selectedInputFolder ? ui.selectedInputFolder.fsName : TRANSLATIONS[lang].noFolderSelected;
        selectInputButton.text = TRANSLATIONS[lang].selectInputButton;
        
        outputFolderPanel.text = TRANSLATIONS[lang].outputFolder;
        ui.useInputFolder.text = TRANSLATIONS[lang].useInputFolder;
        ui.useCustomFolder.text = TRANSLATIONS[lang].useCustomFolder;
        ui.selectOutputButton.text = TRANSLATIONS[lang].selectOutputButton;
        
        ui.statusText.text = TRANSLATIONS[lang].ready;
        ui.cancelButton.text = TRANSLATIONS[lang].cancel;
        ui.abortButton.text = TRANSLATIONS[lang].abort;
        ui.runButton.text = TRANSLATIONS[lang].run;
        
        ui.infoMessage.text = TRANSLATIONS[lang].outputFolderInfo;
    };

    // Add logo below the language selector
    var logoGroup = dialog.add("group");
    logoGroup.orientation = "row";
    logoGroup.alignment = "center";
    logoGroup.alignChildren = "center";
    
    // Create a static image resource
    var logoImage = logoGroup.add("statictext", [0, 0, 280, 30], "Vermont Eshop Resizer");
    logoImage.graphics.font = titleFont;
    logoImage.justify = "center";

    // Add settings panel below the logo
    var settingsPanel = dialog.add("panel", undefined, "Settings");
    settingsPanel.orientation = "column";
    settingsPanel.alignChildren = "fill";
    settingsPanel.margins = 20;
    settingsPanel.spacing = 15; // Increased spacing between elements
    settingsPanel.graphics.font = headerFont;

    // Create dimensions panel
    ui.dimensionsPanel = settingsPanel.add("panel", undefined, "Dimensions");
    ui.dimensionsPanel.orientation = "column";
    ui.dimensionsPanel.alignChildren = "left";
    ui.dimensionsPanel.margins = 15;
    ui.dimensionsPanel.spacing = 10; // Added spacing
    ui.dimensionsPanel.graphics.font = headerFont;

    // Add dimensions group inside the panel
    ui.dimensionsGroup = ui.dimensionsPanel.add("group");
    ui.dimensionsGroup.orientation = "column";
    ui.dimensionsGroup.alignChildren = "left";
    
    // Add label on first line
    ui.dimLabel = ui.dimensionsGroup.add("statictext", undefined, "Lorem:");
    ui.dimLabel.graphics.font = defaultFont;
    
    // Add values on second line
    ui.dimensionsValueGroup = ui.dimensionsGroup.add("group");
    ui.dimensionsValueGroup.orientation = "row";
    ui.dimensionsValueGroup.spacing = 5;
    
    ui.widthInput = ui.dimensionsValueGroup.add("edittext", undefined, CONFIG.defaults.width);
    ui.widthInput.characters = 5;
    ui.widthInput.graphics.font = defaultFont;
    
    ui.xLabel = ui.dimensionsValueGroup.add("statictext", undefined, "x");
    ui.xLabel.graphics.font = defaultFont;
    
    ui.heightInput = ui.dimensionsValueGroup.add("edittext", undefined, CONFIG.defaults.height);
    ui.heightInput.characters = 5;
    ui.heightInput.graphics.font = defaultFont;
    
    ui.pixelsLabel = ui.dimensionsValueGroup.add("statictext", undefined, "pixels");
    ui.pixelsLabel.graphics.font = defaultFont;

    // Add crop option panel to settings
    var cropOptionPanel = settingsPanel.add("panel", undefined, "Crop Option");
    cropOptionPanel.graphics.font = headerFont;
    cropOptionPanel.orientation = "column";
    cropOptionPanel.alignChildren = "left";
    cropOptionPanel.margins = 15;
    cropOptionPanel.spacing = 10;

    // Add description label
    var cropOptionLabel = cropOptionPanel.add("statictext", undefined, "Choose how to handle image dimensions:");
    cropOptionLabel.graphics.font = defaultFont;

    var cropOptionDropdown = cropOptionPanel.add("dropdownlist", undefined, ["Extend (Add Background)", "Crop to Fit"]);
    cropOptionDropdown.graphics.font = defaultFont;
    cropOptionDropdown.selection = 0;

    // Add alignment options group
    ui.alignmentGroup = cropOptionPanel.add("group");
    ui.alignmentGroup.orientation = "column";
    ui.alignmentGroup.alignChildren = "left";
    ui.alignmentGroup.spacing = 5;

    ui.alignmentLabel = ui.alignmentGroup.add("statictext", undefined, "Image Alignment:");
    ui.alignmentLabel.graphics.font = defaultFont;

    var alignmentOptions = ui.alignmentGroup.add("group");
    alignmentOptions.orientation = "row";
    alignmentOptions.spacing = 10;

    ui.topAlign = alignmentOptions.add("radiobutton", undefined, "Top");
    ui.centerAlign = alignmentOptions.add("radiobutton", undefined, "Center");
    ui.bottomAlign = alignmentOptions.add("radiobutton", undefined, "Bottom");
    
    ui.topAlign.graphics.font = defaultFont;
    ui.centerAlign.graphics.font = defaultFont;
    ui.bottomAlign.graphics.font = defaultFont;
    ui.centerAlign.value = true; // Default to center alignment

    // Update visibility based on crop option selection
    cropOptionDropdown.onChange = function() {
        var isExtend = (cropOptionDropdown.selection.index === 0);
        ui.bgColorPanel.visible = isExtend;
        ui.alignmentGroup.visible = isExtend;
    };

    // Add fill method panel
    ui.fillMethodPanel = settingsPanel.add("panel", undefined, "Fill Method");
    ui.fillMethodPanel.graphics.font = headerFont;
    ui.fillMethodPanel.orientation = "column";
    ui.fillMethodPanel.alignChildren = "left";
    ui.fillMethodPanel.margins = 15;
    ui.fillMethodPanel.spacing = 10;

    // Add description label
    ui.fillMethodLabel = ui.fillMethodPanel.add("statictext", undefined, "Lorem:");
    ui.fillMethodLabel.graphics.font = defaultFont;

    ui.fillMethodDropdown = ui.fillMethodPanel.add("dropdownlist", undefined, ["Normal Fill (Solid Color)", "Content-Aware Fill"]);
    ui.fillMethodDropdown.graphics.font = defaultFont;
    ui.fillMethodDropdown.selection = 0;

    // Update visibility based on fill method selection
    ui.fillMethodDropdown.onChange = function() {
        ui.bgColorPanel.visible = (ui.fillMethodDropdown.selection.index === 0); // Show only for Normal Fill
    };

    // Add background color panel
    ui.bgColorPanel = settingsPanel.add("panel", undefined, "Background Color");
    ui.bgColorPanel.graphics.font = headerFont;
    ui.bgColorPanel.orientation = "column";
    ui.bgColorPanel.alignChildren = "left";
    ui.bgColorPanel.margins = 15;
    ui.bgColorPanel.spacing = 5; // Reduced spacing

    // Add hex input group
    ui.hexGroup = ui.bgColorPanel.add("group");
    ui.hexGroup.orientation = "row";
    ui.hexGroup.spacing = 5;
    ui.hexLabel = ui.hexGroup.add("statictext", undefined, "Hex:");
    ui.hexLabel.graphics.font = defaultFont;
    ui.bgColorInput = ui.hexGroup.add("edittext", undefined, CONFIG.defaults.bgColor);
    ui.bgColorInput.characters = 7;
    ui.bgColorInput.graphics.font = defaultFont;

    // Add RGB input group with color preview in same group
    ui.rgbAndPreviewGroup = ui.bgColorPanel.add("group");
    ui.rgbAndPreviewGroup.orientation = "row";
    ui.rgbAndPreviewGroup.spacing = 5;
    
    // RGB inputs
    ui.rgbGroup = ui.rgbAndPreviewGroup.add("group");
    ui.rgbGroup.orientation = "row";
    ui.rgbGroup.spacing = 5;
    ui.rgbLabel = ui.rgbGroup.add("statictext", undefined, "RGB:");
    ui.rgbLabel.graphics.font = defaultFont;
    
    var defaultRgb = hexToRgb(CONFIG.defaults.bgColor);
    ui.rInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.r);
    ui.rInput.characters = 3;
    ui.rInput.graphics.font = defaultFont;
    ui.rgbGroup.add("statictext", undefined, ",").graphics.font = defaultFont;
    ui.gInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.g);
    ui.gInput.characters = 3;
    ui.gInput.graphics.font = defaultFont;
    ui.rgbGroup.add("statictext", undefined, ",").graphics.font = defaultFont;
    ui.bInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.b);
    ui.bInput.characters = 3;
    ui.bInput.graphics.font = defaultFont;

    // Color preview in same group as RGB
    ui.colorPreview = ui.rgbAndPreviewGroup.add("group");
    ui.colorPreview.size = [50, 20];
    ui.colorPreview.backgroundColor = CONFIG.defaults.bgColor;

    // Update color preview and values when hex changes
    ui.bgColorInput.onChanging = function() {
        var hex = ui.bgColorInput.text;
        if (isValidHex(hex)) {
            ui.colorPreview.backgroundColor = hex;
            var rgb = hexToRgb(hex);
            ui.rInput.text = rgb.r;
            ui.gInput.text = rgb.g;
            ui.bInput.text = rgb.b;
        }
    };

    // Update color preview and hex when RGB changes
    function updateFromRGB() {
        var r = parseInt(ui.rInput.text);
        var g = parseInt(ui.gInput.text);
        var b = parseInt(ui.bInput.text);
        
        if (isValidRGB(r) && isValidRGB(g) && isValidRGB(b)) {
            var hex = rgbToHex(r, g, b);
            ui.bgColorInput.text = hex;
            ui.colorPreview.backgroundColor = hex;
        }
    }

    ui.rInput.onChanging = updateFromRGB;
    ui.gInput.onChanging = updateFromRGB;
    ui.bInput.onChanging = updateFromRGB;

    // Right column
    var rightColumn = dialog.add("group");
    rightColumn.orientation = "column";
    rightColumn.alignChildren = "fill";
    rightColumn.spacing = 15;
    rightColumn.preferredSize.width = 300;

    // Input folder panel first
    var inputFolderPanel = rightColumn.add("panel", undefined, "Input Folder");
    inputFolderPanel.orientation = "column";
    inputFolderPanel.alignChildren = "fill";
    inputFolderPanel.margins = 10;
    inputFolderPanel.spacing = 5;
    inputFolderPanel.graphics.font = headerFont;

    // Selected folder info group with tighter spacing
    var folderInfoGroup = inputFolderPanel.add("group");
    folderInfoGroup.orientation = "column";
    folderInfoGroup.alignChildren = "left";
    folderInfoGroup.spacing = 2; // Reduced spacing

    var folderLabel = folderInfoGroup.add("statictext", undefined, "Selected folder:");
    folderLabel.graphics.font = defaultFont;
    
    var inputFolderPath = folderInfoGroup.add("statictext", [0, 0, 270, 35], "No folder selected", {multiline: true}); // Reduced height
    inputFolderPath.graphics.font = ScriptUI.newFont("Arial", "REGULAR", 16);
    inputFolderPath.justify = "center";
    inputFolderPath.graphics.foregroundColor = inputFolderPath.graphics.newPen(inputFolderPath.graphics.PenType.SOLID_COLOR, [1, 0, 0], 1);

    var selectInputButton = inputFolderPanel.add("button", undefined, "Select Input Folder");
    selectInputButton.size = [undefined, 30]; // Reduced button height
    selectInputButton.graphics.font = buttonFont;

    // JPEG Quality panel second
    var qualityPanel = rightColumn.add("panel", undefined, "JPEG Quality");
    qualityPanel.orientation = "column";
    qualityPanel.alignChildren = "fill";
    qualityPanel.margins = 10;
    qualityPanel.spacing = 5;
    qualityPanel.graphics.font = headerFont;

    var qualityGroup = qualityPanel.add("group");
    qualityGroup.orientation = "row";
    qualityGroup.spacing = 10;
    var qualityLabel = qualityGroup.add("statictext", undefined, "Quality:");
    qualityLabel.graphics.font = defaultFont;
    var qualitySlider = qualityGroup.add("slider", undefined, 12, 1, 12);
    var qualityValue = qualityGroup.add("statictext", undefined, "12");
    qualityValue.graphics.font = defaultFont;
    qualityValue.characters = 2;

    // Output folder panel last
    var outputFolderPanel = rightColumn.add("panel", undefined, "Output Folder");
    outputFolderPanel.orientation = "column";
    outputFolderPanel.alignChildren = "fill";
    outputFolderPanel.margins = 10;
    outputFolderPanel.spacing = 5;
    outputFolderPanel.graphics.font = headerFont;

    var outputOptions = outputFolderPanel.add("group");
    outputOptions.orientation = "row";
    outputOptions.alignChildren = "center";
    outputOptions.spacing = 5;
    
    ui.useInputFolder = outputOptions.add("radiobutton", undefined, "Use Input Folder");
    ui.useInputFolder.graphics.font = defaultFont;
    ui.useCustomFolder = outputOptions.add("radiobutton", undefined, "Custom Folder");
    ui.useCustomFolder.graphics.font = defaultFont;
    ui.useInputFolder.value = true;

    var outputPathContainer = outputFolderPanel.add("group");
    outputPathContainer.orientation = "column";
    outputPathContainer.alignChildren = "center";
    outputPathContainer.alignment = "center";
    outputPathContainer.spacing = 2;
    
    ui.outputFolderPath = outputPathContainer.add("statictext", [0, 0, 270, 35], "", {multiline: true});
    ui.outputFolderPath.graphics.font = ScriptUI.newFont("Arial", "REGULAR", 16);
    ui.outputFolderPath.justify = "center";
    ui.outputFolderPath.visible = false;

    ui.infoMessage = outputFolderPanel.add("statictext", [0, 0, 270, 35], "Files will be saved in a new 'processed' folder within the input directory. Original files will not be modified.", {multiline: true});
    ui.infoMessage.graphics.font = defaultFont;
    ui.infoMessage.graphics.foregroundColor = ui.infoMessage.graphics.newPen(ui.infoMessage.graphics.PenType.SOLID_COLOR, [1, 1, 1], 1);
    ui.infoMessage.justify = "center";
    ui.infoMessage.visible = true;

    ui.selectOutputButton = outputFolderPanel.add("button", undefined, "Select Output Folder");
    ui.selectOutputButton.size = [undefined, 30];
    ui.selectOutputButton.enabled = false;
    ui.selectOutputButton.visible = false;
    ui.selectOutputButton.graphics.font = buttonFont;

    // Radio button handlers
    ui.useInputFolder.onClick = function() {
        ui.selectOutputButton.enabled = false;
        ui.selectOutputButton.visible = false;
        ui.infoMessage.visible = true;
        ui.outputFolderPath.visible = false;
        if (state.selectedInputFolder) {
            state.selectedOutputFolder = state.selectedInputFolder;
        }
    };

    ui.useCustomFolder.onClick = function() {
        ui.selectOutputButton.enabled = true;
        ui.selectOutputButton.visible = true;
        ui.infoMessage.visible = false;
        ui.outputFolderPath.visible = true;
        ui.outputFolderPath.text = "No folder selected";
        ui.outputFolderPath.graphics.foregroundColor = ui.outputFolderPath.graphics.newPen(ui.outputFolderPath.graphics.PenType.SOLID_COLOR, [1, 0, 0], 1);
        ui.outputFolderPath.justify = "center";
    };

    // Create execution group above buttons but below columns
    ui.executionGroup = dialog.add("group");
    ui.executionGroup.orientation = "column";
    ui.executionGroup.alignChildren = "center"; // Center alignment
    ui.executionGroup.spacing = 10;
    ui.executionGroup.margins = 15;

    // Add progress bar directly (no panel)
    ui.progressBar = ui.executionGroup.add("progressbar", undefined, 0, 100);
    ui.progressBar.size = [300, 10];

    ui.statusText = ui.executionGroup.add("statictext", undefined, "Ready to process...");
    ui.statusText.graphics.font = defaultFont;

    // Button group below execution
    ui.buttonGroup = dialog.add("group");
    ui.buttonGroup.orientation = "row";
    ui.buttonGroup.alignment = "center";
    ui.buttonGroup.spacing = 20;

    ui.cancelButton = ui.buttonGroup.add("button", undefined, "Cancel");
    ui.cancelButton.size = [150, 45];
    ui.cancelButton.graphics.font = buttonFont;
    
    ui.abortButton = ui.buttonGroup.add("button", undefined, "Abort Processing");
    ui.abortButton.size = [150, 45];
    ui.abortButton.enabled = false;
    ui.abortButton.graphics.font = buttonFont;
    
    ui.runButton = ui.buttonGroup.add("button", undefined, "Run Process");
    ui.runButton.size = [150, 45];
    ui.runButton.enabled = false;
    ui.runButton.graphics.font = buttonFont;

    // Create preview button with proper size
    ui.previewButton = ui.buttonGroup.add("button", undefined, "Preview");
    ui.previewButton.size = [150, 45];
    ui.previewButton.graphics.font = buttonFont;
    ui.previewButton.onClick = function() {
        if (!state.selectedInputFolder) {
            alert("Please select an input folder first");
            return;
        }

        var files = state.selectedInputFolder.getFiles(function(file) {
            return file instanceof File && CONFIG.supportedFormats.test(file.name);
        });

        if (files.length === 0) {
            alert("No valid images found in selected folder");
            return;
        }

        // Get current settings
        var settings = {
            targetWidth: parseInt(ui.widthInput.text),
            targetHeight: parseInt(ui.heightInput.text),
            fillMethod: ui.fillMethodDropdown.selection.index === 0 ? "normal" : "contentAware",
            fitMethod: ui.cropOptionDropdown.selection.index === 0 ? "extend" : "crop",
            quality: parseInt(ui.qualityValue.text),
            bgColor: ui.bgColorInput.text,
            alignment: getSelectedAlignment()
        };

        // Validate settings
        var validationErrors = ImageProcessor.validateSettings(settings);
        if (validationErrors.length > 0) {
            alert("Please correct the following:\n" + validationErrors.join("\n"));
            return;
        }

        // Show preview with first image
        ImageProcessor.createPreview(files[0], settings);
    };

    // Position preview button between run and abort buttons
    ui.previewButton.alignment = ["left", "bottom"];
    
    // Copyright at the bottom
    ui.copyrightGroup = dialog.add("group");
    ui.copyrightGroup.orientation = "row";
    ui.copyrightGroup.alignment = "center";
    ui.copyrightText = ui.copyrightGroup.add("statictext", undefined, "© " + new Date().getFullYear() + " Vermont Services Slovakia");
    ui.copyrightText.graphics.font = defaultFont;
    ui.copyrightText.graphics.foregroundColor = ui.copyrightText.graphics.newPen(ui.copyrightText.graphics.PenType.SOLID_COLOR, [0.5, 0.5, 0.5], 1);

    // Handle the abort button click
    ui.abortButton.onClick = function() {
        state.shouldAbortProcessing = true;
        ui.statusText.text = "Aborting process...";
        ui.abortButton.enabled = false;
    };

    // Update run button click handler
    ui.runButton.onClick = function() {
        if (!state.selectedInputFolder) {
            alert("Please select an input folder");
            return;
        }

        if (ui.fillMethodDropdown.selection.index === 0 && !Utils.isValidHex(ui.bgColorInput.text)) {
            alert("Please enter a valid hex color (e.g., #FFFFFF)");
            return;
        }

        var outputFolder = ui.useInputFolder.value ? 
            new Folder(state.selectedInputFolder.fsName + "/processed") : 
            state.selectedOutputFolder;

        if (!outputFolder) {
            alert("Please select an output folder");
            return;
        }

        if (!outputFolder.exists) {
            outputFolder.create();
        }

        state.shouldAbortProcessing = false;
        ui.abortButton.enabled = true;
        ui.runButton.enabled = false;
        ui.cancelButton.enabled = false;

        var settings = {
            targetWidth: parseInt(ui.widthInput.text),
            targetHeight: parseInt(ui.heightInput.text),
            fillMethod: ui.fillMethodDropdown.selection.index === 0 ? "normal" : "contentAware",
            fitMethod: ui.cropOptionDropdown.selection.index === 0 ? "extend" : "crop",
            quality: parseInt(ui.qualityValue.text),
            bgColor: ui.bgColorInput.text,
            alignment: getSelectedAlignment()
        };

        // Process files
        var files = state.selectedInputFolder.getFiles(function(file) {
            return file instanceof File && CONFIG.supportedFormats.test(file.name);
        });

        if (files.length === 0) {
            alert("No valid image files found in the folder");
            return;
        }

        var processed = 0;
        var errors = 0;
        var startTime = new Date();

        for (var i = 0; i < files.length && !state.shouldAbortProcessing; i++) {
            var progress = Math.round((i / files.length) * 100);
            ui.progressBar.value = progress;
            ui.statusText.text = "Processing: " + files[i].name + " (" + (i + 1) + " of " + files.length + ")";
            
            if (ImageProcessor.processImage(files[i], outputFolder, settings)) {
                processed++;
            } else {
                errors++;
            }
            
            dialog.update();
        }

        var totalDuration = Math.round((new Date() - startTime) / 1000);
        ui.progressBar.value = 100;
        ui.statusText.text = "Completed!\nProcessed: " + processed + " files\nErrors: " + errors + 
                            "\nTotal time: " + totalDuration + "s";

        ui.abortButton.enabled = false;
        ui.runButton.enabled = true;
        ui.cancelButton.enabled = true;
    };

    // Handle the cancel button click
    ui.cancelButton.onClick = function() {
        dialog.close();
    };

    // Add folder selection functionality
    selectInputButton.onClick = function() {
        var folder = Folder.selectDialog("Select Input Folder");
        if (folder) {
            state.selectedInputFolder = folder;  // Update the global variable
            ui.selectedInputFolder = folder;  // Update the UI reference
            inputFolderPath.text = folder.fsName;
            inputFolderPath.graphics.foregroundColor = inputFolderPath.graphics.newPen(inputFolderPath.graphics.PenType.SOLID_COLOR, [0, 0, 0], 1);
            ui.runButton.enabled = true;
            
            // If using input folder for output, update that as well
            if (ui.useInputFolder.value) {
                state.selectedOutputFolder = folder;
                ui.selectedOutputFolder = folder;
            }
            
            // Force dialog to update
            dialog.update();
        }
    };

    ui.selectOutputButton.onClick = function() {
        var folder = Folder.selectDialog("Select Output Folder");
        if (folder) {
            state.selectedOutputFolder = folder;  // Update the global variable
            ui.selectedOutputFolder = folder;  // Update the UI reference
            ui.outputFolderPath.text = folder.fsName;
            ui.outputFolderPath.graphics.foregroundColor = ui.outputFolderPath.graphics.newPen(ui.outputFolderPath.graphics.PenType.SOLID_COLOR, [0, 0, 0], 1);
            ui.runButton.enabled = true;
            
            // Force dialog to update
            dialog.update();
        }
    };

    return dialog;
}

// Show the dialog
var dialog = createDialog();
dialog.show();

// Store the alignment value for processing
function getSelectedAlignment() {
    if (ui.topAlign.value) return "top";
    if (ui.centerAlign.value) return "center";
    if (ui.bottomAlign.value) return "bottom";
    return "center"; // default fallback
}

// Add a UI helper object
const UIHelper = {
    formatMessage: function(template) {
        var args = Array.prototype.slice.call(arguments, 1);
        return template.replace(/{(\d+)}/g, function(match, number) {
            return typeof args[number] !== 'undefined' ? args[number] : match;
        });
    },

    updateProgressStatus: function(ui, currentFile, progress, total, processed, errors, timeRemaining) {
        var lang = ui.currentLanguage || 'en';
        var t = TRANSLATIONS[lang];
        
        ui.statusText.text = [
            this.formatMessage(t.processing, currentFile),
            this.formatMessage(t.progress, progress, processed + 1, total),
            this.formatMessage(t.processedCount, processed, errors),
            this.formatMessage(t.timeRemaining, Utils.formatTime(timeRemaining))
        ].join('\n');
    },

    updateCompletionStatus: function(ui, processed, errors, totalTime, averageTime) {
        var lang = ui.currentLanguage || 'en';
        var t = TRANSLATIONS[lang];
        
        ui.statusText.text = this.formatMessage(t.completed, 
            processed, 
            errors, 
            Utils.formatTime(totalTime),
            averageTime.toFixed(1)
        );
    },

    createButton: function(parent, text, size, onClick) {
        var button = parent.add("button", undefined, text);
        if (size) {
            button.size = size;
        }
        button.graphics.font = FONTS.button;
        if (onClick) {
            button.onClick = onClick;
        }
        return button;
    },

    createPanel: function(parent, title, options) {
        options = options || {};
        var panel = parent.add("panel", undefined, title);
        panel.orientation = options.orientation || "column";
        panel.alignChildren = options.alignChildren || "fill";
        panel.margins = options.margins || 10;
        panel.spacing = options.spacing || 5;
        panel.graphics.font = FONTS.header;
        return panel;
    }
};

// Add a ProcessingManager object
var ProcessingManager = {
    processFiles: function(inputFolder, outputFolder, settings, ui) {
        ErrorHandler.clearLogs();
        
        try {
            var files = inputFolder.getFiles(function(file) {
                return file instanceof File && CONFIG.supportedFormats.test(file.name);
            });

            if (files.length === 0) {
                ui.statusText.text = TRANSLATIONS[ui.currentLanguage].noValidFiles;
                alert(TRANSLATIONS[ui.currentLanguage].noValidFiles);
                return;
            }

            var processed = 0;
            var errors = 0;
            var startTime = new Date();

            for (var i = 0; i < files.length && !state.shouldAbortProcessing; i++) {
                var file = files[i];
                var progress = Math.round((i / files.length) * 100);
                var elapsed = new Date() - startTime;
                var estimatedTotal = (elapsed / (i + 1)) * files.length;
                var remaining = Math.round((estimatedTotal - elapsed) / 1000);

                ui.progressBar.value = progress;
                UIHelper.updateProgressStatus(ui, file.name, progress, files.length, processed, errors, remaining);

                if (ImageProcessor.processImage(file, outputFolder, settings)) {
                    processed++;
                } else {
                    errors++;
                }

                dialog.update();
            }

            var totalDuration = Math.round((new Date() - startTime) / 1000);
            var averageTime = totalDuration / processed;

            ui.progressBar.value = 100;
            UIHelper.updateCompletionStatus(ui, processed, errors, totalDuration, averageTime);

            if (ErrorHandler.getErrorSummary().hasErrors) {
                ui.statusText.text += "\n\nWarning: Some errors occurred during processing. Check the log for details.";
            }
            
        } catch(e) {
            ErrorHandler.logError(e, "ProcessingManager.processFiles");
            alert("Processing error: " + e);
        }
    }
};

// Add error handling utilities
var ErrorHandler = {
    errors: [],
    warnings: [],

    logError: function(error, context) {
        this.errors.push({
            message: error.message || error,
            context: context,
            timestamp: new Date()
        });
        $.writeln("Error in " + context + ": " + error);
    },

    logWarning: function(warning, context) {
        this.warnings.push({
            message: warning,
            context: context,
            timestamp: new Date()
        });
        $.writeln("Warning in " + context + ": " + warning);
    },

    getErrorSummary: function() {
        return {
            errorCount: this.errors.length,
            warningCount: this.warnings.length,
            lastError: this.errors[this.errors.length - 1],
            hasErrors: this.errors.length > 0
        };
    },

    clearLogs: function() {
        this.errors = [];
        this.warnings = [];
    }
};
