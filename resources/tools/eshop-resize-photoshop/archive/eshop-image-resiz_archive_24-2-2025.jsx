//@target photoshop

// Set the default size Vermont Gant 
var targetWidth = 1600;
var targetHeight = 2400;
var defaultBgColor = "#FFFFFF";

// Add global variable for abort flag
var shouldAbortProcessing = false;

// Add these variables at the top level, before createDialog()
var selectedInputFolder = null;
var selectedOutputFolder = null;

// Add language translations
var translations = {
    en: {
        title: "Vermont eShop - Image Processing",
        settings: "Settings",
        dimensions: "Dimensions",
        pixels: "pixels",
        cropOption: "Crop Option",
        cropOptionDesc: "Choose how to handle image dimensions:",
        cropOptionExtend: "Extend (Add Background)",
        cropOptionCrop: "Crop to Fit",
        fillMethod: "Fill Method",
        fillMethodDesc: "Choose method for the background color:",
        fillMethodNormal: "Normal Fill (Solid Color)",
        fillMethodAware: "Content-Aware Fill",
        backgroundColor: "Background Color",
        quality: "JPEG Quality",
        qualityLabel: "Quality:",
        inputFolder: "Input Folder",
        selectedFolder: "Selected folder:",
        noFolderSelected: "No folder selected",
        selectInputButton: "Select Input Folder",
        outputFolder: "Output Folder",
        useInputFolder: "Use Input Folder",
        useCustomFolder: "Custom Folder",
        selectOutputButton: "Select Output Folder",
        ready: "Ready to process...",
        cancel: "Cancel",
        abort: "Abort Processing",
        run: "Run Process",
        imageAlignment: "Image Alignment:",
        alignTop: "Top",
        alignCenter: "Center",
        alignBottom: "Bottom",
        outputFolderInfo: "Files will be saved in a new 'processed' folder within the input directory. Original files will not be modified.",
        dimensionsDesc: "Target dimensions:",
    },
    sk: {
        title: "Vermont eShop - Spracovanie Obrázkov",
        settings: "Nastavenia",
        dimensions: "Rozmery",
        pixels: "pixelov",
        cropOption: "Možnosť Orezania",
        cropOptionDesc: "Vyberte spôsob úpravy rozmerov:",
        cropOptionExtend: "Rozšíriť (Pridať Pozadie)",
        cropOptionCrop: "Orezať na Rozmer",
        fillMethod: "Spôsob Výplne",
        fillMethodDesc: "Vyberte spôsob pre farbu pozadia:",
        fillMethodNormal: "Normálna Výplň (Plná Farba)",
        fillMethodAware: "Kontextová Výplň",
        backgroundColor: "Farba Pozadia",
        quality: "JPEG Kvalita",
        qualityLabel: "Kvalita:",
        inputFolder: "Vstupný Priečinok",
        selectedFolder: "Vybraný priečinok:",
        noFolderSelected: "Žiadny priečinok nie je vybraný",
        selectInputButton: "Vybrať Vstupný Priečinok",
        outputFolder: "Výstupný Priečinok",
        useInputFolder: "Použiť Vstupný Priečinok",
        useCustomFolder: "Vlastný Priečinok",
        selectOutputButton: "Vybrať Výstupný Priečinok",
        ready: "Pripravené na spracovanie...",
        cancel: "Zrušiť",
        abort: "Prerušiť Spracovanie",
        run: "Spustiť Proces",
        imageAlignment: "Zarovnanie Obrázka:",
        alignTop: "Hore",
        alignCenter: "Stred",
        alignBottom: "Dole",
        outputFolderInfo: "Súbory budú uložené v novom priečinku 'processed' vo vstupnom adresári. Pôvodné súbory nebudú upravené.",
        dimensionsDesc: "Cieľové rozmery:",
    },
    hu: {
        title: "Vermont eShop - Képfeldolgozó",
        settings: "Beállítások",
        dimensions: "Méretek",
        pixels: "pixel",
        cropOption: "Vágási Beállítás",
        cropOptionDesc: "Válassza ki a képméret kezelésének módját:",
        cropOptionExtend: "Kiterjesztés (Háttér Hozzáadása)",
        cropOptionCrop: "Vágás Méretre",
        fillMethod: "Kitöltési Mód",
        fillMethodDesc: "Válassza ki a háttérszín módszerét:",
        fillMethodNormal: "Normál Kitöltés (Egyszínű)",
        fillMethodAware: "Tartalomtudatos Kitöltés",
        backgroundColor: "Háttérszín",
        quality: "JPEG Minőség",
        qualityLabel: "Minőség:",
        inputFolder: "Bemeneti Mappa",
        selectedFolder: "Kiválasztott mappa:",
        noFolderSelected: "Nincs mappa kiválasztva",
        selectInputButton: "Bemeneti Mappa Kiválasztása",
        outputFolder: "Kimeneti Mappa",
        useInputFolder: "Bemeneti Mappa Használata",
        useCustomFolder: "Egyéni Mappa",
        selectOutputButton: "Kimeneti Mappa Kiválasztása",
        ready: "Feldolgozásra kész...",
        cancel: "Mégse",
        abort: "Feldolgozás Megszakítása",
        run: "Folyamat Indítása",
        imageAlignment: "Kép Igazítása:",
        alignTop: "Felül",
        alignCenter: "Közép",
        alignBottom: "Alul",
        outputFolderInfo: "A fájlok az input könyvtáron belül egy új 'processed' mappába kerülnek mentésre. Az eredeti fájlok nem módosulnak.",
        dimensionsDesc: "Célméretek:",
    }
};

// Initialize fonts
var defaultFont, headerFont, buttonFont, titleFont;
try {
    defaultFont = ScriptUI.newFont("Arial", "REGULAR", 14);
    headerFont = ScriptUI.newFont("Arial", "REGULAR", 16);
    buttonFont = ScriptUI.newFont("Arial", "REGULAR", 13);
    titleFont = ScriptUI.newFont("Arial", "REGULAR", 20);
} catch(e) {
    defaultFont = ScriptUI.newFont("dialog", "REGULAR", 14);
    headerFont = ScriptUI.newFont("dialog", "REGULAR", 16);
    buttonFont = ScriptUI.newFont("dialog", "REGULAR", 13);
    titleFont = ScriptUI.newFont("dialog", "REGULAR", 20);
}

// Helper function to convert hex to RGB
function hexToRgb(hex) {
    hex = hex.replace('#', '');
    return {
        r: parseInt(hex.substring(0, 2), 16),
        g: parseInt(hex.substring(2, 4), 16),
        b: parseInt(hex.substring(4, 6), 16)
    };
}

// Helper function to convert RGB to Hex
function rgbToHex(r, g, b) {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

// Helper function to validate hex color
function isValidHex(hex) {
    return /^#[0-9A-F]{6}$/i.test(hex);
}

// Helper function to validate RGB values
function isValidRGB(value) {
    return !isNaN(value) && value >= 0 && value <= 255;
}

// Function to decode base64 and save to a file
function saveBase64AsFile(base64, filePath) {
    var decoded = decodeBase64(base64);
    var file = new File(filePath);
    file.open("w");
    file.encoding = "BINARY";
    for (var i = 0; i < decoded.length; i++) {
        file.write(String.fromCharCode(decoded[i]));
    }
    file.close();
    alert("Temp file path: " + filePath + "\nFile exists: " + file.exists);
    return file;
}

// Decode base64 string
function decodeBase64(base64) {
    var keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
    var output = [];
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;

    // Clean the base64 string
    var cleanBase64 = [];
    for (var j = 0; j < base64.length; j++) {
        if (keyStr.indexOf(base64.charAt(j)) !== -1 || base64.charAt(j) === '=') {
            cleanBase64.push(base64.charAt(j));
        }
    }
    cleanBase64 = cleanBase64.join('');

    while (i < cleanBase64.length) {
        enc1 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc2 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc3 = keyStr.indexOf(cleanBase64.charAt(i++));
        enc4 = keyStr.indexOf(cleanBase64.charAt(i++));

        chr1 = (enc1 << 2) | (enc2 >> 4);
        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        chr3 = ((enc3 & 3) << 6) | enc4;

        output.push(chr1);

        if (enc3 !== 64) {
            output.push(chr2);
        }
        if (enc4 !== 64) {
            output.push(chr3);
        }
    }

    return output; // Return a regular array
}

// Create the main dialog
function createDialog() {
    var dialog = new Window("dialog", "Vermont eShop - Image Processing");
    dialog.orientation = "column";
    dialog.alignChildren = "fill";
    
    // Initialize the ui object
    var ui = {};

    // Add language selector at the top
    var langGroup = dialog.add("group");
    langGroup.orientation = "row";
    langGroup.alignment = "right";
    langGroup.spacing = 10;
    
    langGroup.add("statictext", undefined, "Language:");
    var languageDropdown = langGroup.add("dropdownlist", undefined, ["English", "Slovensky", "Magyar"]);
    languageDropdown.selection = 0; // Default to English

    // Add language change handler
    languageDropdown.onChange = function() {
        var lang = languageDropdown.selection.index === 0 ? 'en' : 
                  languageDropdown.selection.index === 1 ? 'sk' : 'hu';
        
        // Update all text elements
        dialog.text = translations[lang].title;
        settingsPanel.text = translations[lang].settings;
        ui.dimensionsPanel.text = translations[lang].dimensions;
        ui.dimLabel.text = translations[lang].dimensionsDesc;
        ui.pixelsLabel.text = translations[lang].pixels;
        
        cropOptionPanel.text = translations[lang].cropOption;
        cropOptionLabel.text = translations[lang].cropOptionDesc;
        cropOptionDropdown.items[0].text = translations[lang].cropOptionExtend;
        cropOptionDropdown.items[1].text = translations[lang].cropOptionCrop;
        
        ui.alignmentLabel.text = translations[lang].imageAlignment;
        ui.topAlign.text = translations[lang].alignTop;
        ui.centerAlign.text = translations[lang].alignCenter;
        ui.bottomAlign.text = translations[lang].alignBottom;
        
        ui.fillMethodPanel.text = translations[lang].fillMethod;
        ui.fillMethodLabel.text = translations[lang].fillMethodDesc;
        ui.fillMethodDropdown.items[0].text = translations[lang].fillMethodNormal;
        ui.fillMethodDropdown.items[1].text = translations[lang].fillMethodAware;
        
        ui.bgColorPanel.text = translations[lang].backgroundColor;
        qualityPanel.text = translations[lang].quality;
        qualityLabel.text = translations[lang].qualityLabel;
        
        inputFolderPanel.text = translations[lang].inputFolder;
        folderLabel.text = translations[lang].selectedFolder;
        inputFolderPath.text = ui.selectedInputFolder ? ui.selectedInputFolder.fsName : translations[lang].noFolderSelected;
        selectInputButton.text = translations[lang].selectInputButton;
        
        outputFolderPanel.text = translations[lang].outputFolder;
        ui.useInputFolder.text = translations[lang].useInputFolder;
        ui.useCustomFolder.text = translations[lang].useCustomFolder;
        ui.selectOutputButton.text = translations[lang].selectOutputButton;
        
        ui.statusText.text = translations[lang].ready;
        ui.cancelButton.text = translations[lang].cancel;
        ui.abortButton.text = translations[lang].abort;
        ui.runButton.text = translations[lang].run;
        
        ui.infoMessage.text = translations[lang].outputFolderInfo;
    };

    // Add logo below the language selector
    var logoGroup = dialog.add("group");
    logoGroup.orientation = "row";
    logoGroup.alignment = "center";
    logoGroup.alignChildren = "center";
    
    // Create a static image resource
    var logoImage = logoGroup.add("statictext", [0, 0, 280, 30], "Vermont Eshop Resizer");
    logoImage.graphics.font = titleFont;
    logoImage.justify = "center";

    // Add settings panel below the logo
    var settingsPanel = dialog.add("panel", undefined, "Settings");
    settingsPanel.orientation = "column";
    settingsPanel.alignChildren = "fill";
    settingsPanel.margins = 20;
    settingsPanel.spacing = 15; // Increased spacing between elements
    settingsPanel.graphics.font = headerFont;

    // Create dimensions panel
    ui.dimensionsPanel = settingsPanel.add("panel", undefined, "Dimensions");
    ui.dimensionsPanel.orientation = "column";
    ui.dimensionsPanel.alignChildren = "left";
    ui.dimensionsPanel.margins = 15;
    ui.dimensionsPanel.spacing = 10; // Added spacing
    ui.dimensionsPanel.graphics.font = headerFont;

    // Add dimensions group inside the panel
    ui.dimensionsGroup = ui.dimensionsPanel.add("group");
    ui.dimensionsGroup.orientation = "column";
    ui.dimensionsGroup.alignChildren = "left";
    
    // Add label on first line
    ui.dimLabel = ui.dimensionsGroup.add("statictext", undefined, "Lorem:");
    ui.dimLabel.graphics.font = defaultFont;
    
    // Add values on second line
    ui.dimensionsValueGroup = ui.dimensionsGroup.add("group");
    ui.dimensionsValueGroup.orientation = "row";
    ui.dimensionsValueGroup.spacing = 5;
    
    ui.widthInput = ui.dimensionsValueGroup.add("edittext", undefined, targetWidth);
    ui.widthInput.characters = 5;
    ui.widthInput.graphics.font = defaultFont;
    
    ui.xLabel = ui.dimensionsValueGroup.add("statictext", undefined, "x");
    ui.xLabel.graphics.font = defaultFont;
    
    ui.heightInput = ui.dimensionsValueGroup.add("edittext", undefined, targetHeight);
    ui.heightInput.characters = 5;
    ui.heightInput.graphics.font = defaultFont;
    
    ui.pixelsLabel = ui.dimensionsValueGroup.add("statictext", undefined, "pixels");
    ui.pixelsLabel.graphics.font = defaultFont;

    // Add crop option panel to settings
    var cropOptionPanel = settingsPanel.add("panel", undefined, "Crop Option");
    cropOptionPanel.graphics.font = headerFont;
    cropOptionPanel.orientation = "column";
    cropOptionPanel.alignChildren = "left";
    cropOptionPanel.margins = 15;
    cropOptionPanel.spacing = 10;

    // Add description label
    var cropOptionLabel = cropOptionPanel.add("statictext", undefined, "Choose how to handle image dimensions:");
    cropOptionLabel.graphics.font = defaultFont;

    var cropOptionDropdown = cropOptionPanel.add("dropdownlist", undefined, ["Extend (Add Background)", "Crop to Fit"]);
    cropOptionDropdown.graphics.font = defaultFont;
    cropOptionDropdown.selection = 0;

    // Add alignment options group
    ui.alignmentGroup = cropOptionPanel.add("group");
    ui.alignmentGroup.orientation = "column";
    ui.alignmentGroup.alignChildren = "left";
    ui.alignmentGroup.spacing = 5;

    ui.alignmentLabel = ui.alignmentGroup.add("statictext", undefined, "Image Alignment:");
    ui.alignmentLabel.graphics.font = defaultFont;

    var alignmentOptions = ui.alignmentGroup.add("group");
    alignmentOptions.orientation = "row";
    alignmentOptions.spacing = 10;

    ui.topAlign = alignmentOptions.add("radiobutton", undefined, "Top");
    ui.centerAlign = alignmentOptions.add("radiobutton", undefined, "Center");
    ui.bottomAlign = alignmentOptions.add("radiobutton", undefined, "Bottom");
    
    ui.topAlign.graphics.font = defaultFont;
    ui.centerAlign.graphics.font = defaultFont;
    ui.bottomAlign.graphics.font = defaultFont;
    ui.centerAlign.value = true; // Default to center alignment

    // Update visibility based on crop option selection
    cropOptionDropdown.onChange = function() {
        var isExtend = (cropOptionDropdown.selection.index === 0);
        ui.bgColorPanel.visible = isExtend;
        ui.alignmentGroup.visible = isExtend;
    };

    // Add fill method panel
    ui.fillMethodPanel = settingsPanel.add("panel", undefined, "Fill Method");
    ui.fillMethodPanel.graphics.font = headerFont;
    ui.fillMethodPanel.orientation = "column";
    ui.fillMethodPanel.alignChildren = "left";
    ui.fillMethodPanel.margins = 15;
    ui.fillMethodPanel.spacing = 10;

    // Add description label
    ui.fillMethodLabel = ui.fillMethodPanel.add("statictext", undefined, "Lorem:");
    ui.fillMethodLabel.graphics.font = defaultFont;

    ui.fillMethodDropdown = ui.fillMethodPanel.add("dropdownlist", undefined, ["Normal Fill (Solid Color)", "Content-Aware Fill"]);
    ui.fillMethodDropdown.graphics.font = defaultFont;
    ui.fillMethodDropdown.selection = 0;

    // Update visibility based on fill method selection
    ui.fillMethodDropdown.onChange = function() {
        ui.bgColorPanel.visible = (ui.fillMethodDropdown.selection.index === 0); // Show only for Normal Fill
    };

    // Add background color panel
    ui.bgColorPanel = settingsPanel.add("panel", undefined, "Background Color");
    ui.bgColorPanel.graphics.font = headerFont;
    ui.bgColorPanel.orientation = "column";
    ui.bgColorPanel.alignChildren = "left";
    ui.bgColorPanel.margins = 15;
    ui.bgColorPanel.spacing = 5; // Reduced spacing

    // Add hex input group
    ui.hexGroup = ui.bgColorPanel.add("group");
    ui.hexGroup.orientation = "row";
    ui.hexGroup.spacing = 5;
    ui.hexLabel = ui.hexGroup.add("statictext", undefined, "Hex:");
    ui.hexLabel.graphics.font = defaultFont;
    ui.bgColorInput = ui.hexGroup.add("edittext", undefined, defaultBgColor);
    ui.bgColorInput.characters = 7;
    ui.bgColorInput.graphics.font = defaultFont;

    // Add RGB input group with color preview in same group
    ui.rgbAndPreviewGroup = ui.bgColorPanel.add("group");
    ui.rgbAndPreviewGroup.orientation = "row";
    ui.rgbAndPreviewGroup.spacing = 5;
    
    // RGB inputs
    ui.rgbGroup = ui.rgbAndPreviewGroup.add("group");
    ui.rgbGroup.orientation = "row";
    ui.rgbGroup.spacing = 5;
    ui.rgbLabel = ui.rgbGroup.add("statictext", undefined, "RGB:");
    ui.rgbLabel.graphics.font = defaultFont;
    
    var defaultRgb = hexToRgb(defaultBgColor);
    ui.rInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.r);
    ui.rInput.characters = 3;
    ui.rInput.graphics.font = defaultFont;
    ui.rgbGroup.add("statictext", undefined, ",").graphics.font = defaultFont;
    ui.gInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.g);
    ui.gInput.characters = 3;
    ui.gInput.graphics.font = defaultFont;
    ui.rgbGroup.add("statictext", undefined, ",").graphics.font = defaultFont;
    ui.bInput = ui.rgbGroup.add("edittext", undefined, defaultRgb.b);
    ui.bInput.characters = 3;
    ui.bInput.graphics.font = defaultFont;

    // Color preview in same group as RGB
    ui.colorPreview = ui.rgbAndPreviewGroup.add("group");
    ui.colorPreview.size = [50, 20];
    ui.colorPreview.backgroundColor = defaultBgColor;

    // Update color preview and values when hex changes
    ui.bgColorInput.onChanging = function() {
        var hex = ui.bgColorInput.text;
        if (isValidHex(hex)) {
            ui.colorPreview.backgroundColor = hex;
            var rgb = hexToRgb(hex);
            ui.rInput.text = rgb.r;
            ui.gInput.text = rgb.g;
            ui.bInput.text = rgb.b;
        }
    };

    // Update color preview and hex when RGB changes
    function updateFromRGB() {
        var r = parseInt(ui.rInput.text);
        var g = parseInt(ui.gInput.text);
        var b = parseInt(ui.bInput.text);
        
        if (isValidRGB(r) && isValidRGB(g) && isValidRGB(b)) {
            var hex = rgbToHex(r, g, b);
            ui.bgColorInput.text = hex;
            ui.colorPreview.backgroundColor = hex;
        }
    }

    ui.rInput.onChanging = updateFromRGB;
    ui.gInput.onChanging = updateFromRGB;
    ui.bInput.onChanging = updateFromRGB;

    // Right column
    var rightColumn = dialog.add("group");
    rightColumn.orientation = "column";
    rightColumn.alignChildren = "fill";
    rightColumn.spacing = 15;
    rightColumn.preferredSize.width = 300;

    // Input folder panel first
    var inputFolderPanel = rightColumn.add("panel", undefined, "Input Folder");
    inputFolderPanel.orientation = "column";
    inputFolderPanel.alignChildren = "fill";
    inputFolderPanel.margins = 10;
    inputFolderPanel.spacing = 5;
    inputFolderPanel.graphics.font = headerFont;

    // Selected folder info group with tighter spacing
    var folderInfoGroup = inputFolderPanel.add("group");
    folderInfoGroup.orientation = "column";
    folderInfoGroup.alignChildren = "left";
    folderInfoGroup.spacing = 2; // Reduced spacing

    var folderLabel = folderInfoGroup.add("statictext", undefined, "Selected folder:");
    folderLabel.graphics.font = defaultFont;
    
    var inputFolderPath = folderInfoGroup.add("statictext", [0, 0, 270, 35], "No folder selected", {multiline: true}); // Reduced height
    inputFolderPath.graphics.font = ScriptUI.newFont("Arial", "REGULAR", 16);
    inputFolderPath.justify = "center";
    inputFolderPath.graphics.foregroundColor = inputFolderPath.graphics.newPen(inputFolderPath.graphics.PenType.SOLID_COLOR, [1, 0, 0], 1);

    var selectInputButton = inputFolderPanel.add("button", undefined, "Select Input Folder");
    selectInputButton.size = [undefined, 30]; // Reduced button height
    selectInputButton.graphics.font = buttonFont;

    // JPEG Quality panel second
    var qualityPanel = rightColumn.add("panel", undefined, "JPEG Quality");
    qualityPanel.orientation = "column";
    qualityPanel.alignChildren = "fill";
    qualityPanel.margins = 10;
    qualityPanel.spacing = 5;
    qualityPanel.graphics.font = headerFont;

    var qualityGroup = qualityPanel.add("group");
    qualityGroup.orientation = "row";
    qualityGroup.spacing = 10;
    var qualityLabel = qualityGroup.add("statictext", undefined, "Quality:");
    qualityLabel.graphics.font = defaultFont;
    var qualitySlider = qualityGroup.add("slider", undefined, 12, 1, 12);
    var qualityValue = qualityGroup.add("statictext", undefined, "12");
    qualityValue.graphics.font = defaultFont;
    qualityValue.characters = 2;

    // Output folder panel last
    var outputFolderPanel = rightColumn.add("panel", undefined, "Output Folder");
    outputFolderPanel.orientation = "column";
    outputFolderPanel.alignChildren = "fill";
    outputFolderPanel.margins = 10;
    outputFolderPanel.spacing = 5;
    outputFolderPanel.graphics.font = headerFont;

    var outputOptions = outputFolderPanel.add("group");
    outputOptions.orientation = "row";
    outputOptions.alignChildren = "center";
    outputOptions.spacing = 5;
    
    ui.useInputFolder = outputOptions.add("radiobutton", undefined, "Use Input Folder");
    ui.useInputFolder.graphics.font = defaultFont;
    ui.useCustomFolder = outputOptions.add("radiobutton", undefined, "Custom Folder");
    ui.useCustomFolder.graphics.font = defaultFont;
    ui.useInputFolder.value = true;

    var outputPathContainer = outputFolderPanel.add("group");
    outputPathContainer.orientation = "column";
    outputPathContainer.alignChildren = "center";
    outputPathContainer.alignment = "center";
    outputPathContainer.spacing = 2;
    
    ui.outputFolderPath = outputPathContainer.add("statictext", [0, 0, 270, 35], "", {multiline: true});
    ui.outputFolderPath.graphics.font = ScriptUI.newFont("Arial", "REGULAR", 16);
    ui.outputFolderPath.justify = "center";
    ui.outputFolderPath.visible = false;

    ui.infoMessage = outputFolderPanel.add("statictext", [0, 0, 270, 35], "Files will be saved in a new 'processed' folder within the input directory. Original files will not be modified.", {multiline: true});
    ui.infoMessage.graphics.font = defaultFont;
    ui.infoMessage.graphics.foregroundColor = ui.infoMessage.graphics.newPen(ui.infoMessage.graphics.PenType.SOLID_COLOR, [1, 1, 1], 1);
    ui.infoMessage.justify = "center";
    ui.infoMessage.visible = true;

    ui.selectOutputButton = outputFolderPanel.add("button", undefined, "Select Output Folder");
    ui.selectOutputButton.size = [undefined, 30];
    ui.selectOutputButton.enabled = false;
    ui.selectOutputButton.visible = false;
    ui.selectOutputButton.graphics.font = buttonFont;

    // Radio button handlers
    ui.useInputFolder.onClick = function() {
        ui.selectOutputButton.enabled = false;
        ui.selectOutputButton.visible = false;
        ui.infoMessage.visible = true;
        ui.outputFolderPath.visible = false;
        if (selectedInputFolder) {
            selectedOutputFolder = selectedInputFolder;
        }
    };

    ui.useCustomFolder.onClick = function() {
        ui.selectOutputButton.enabled = true;
        ui.selectOutputButton.visible = true;
        ui.infoMessage.visible = false;
        ui.outputFolderPath.visible = true;
        ui.outputFolderPath.text = "No folder selected";
        ui.outputFolderPath.graphics.foregroundColor = ui.outputFolderPath.graphics.newPen(ui.outputFolderPath.graphics.PenType.SOLID_COLOR, [1, 0, 0], 1);
        ui.outputFolderPath.justify = "center";
    };

    // Create execution group above buttons but below columns
    ui.executionGroup = dialog.add("group");
    ui.executionGroup.orientation = "column";
    ui.executionGroup.alignChildren = "center"; // Center alignment
    ui.executionGroup.spacing = 10;
    ui.executionGroup.margins = 15;

    // Add progress bar directly (no panel)
    ui.progressBar = ui.executionGroup.add("progressbar", undefined, 0, 100);
    ui.progressBar.size = [300, 10];

    ui.statusText = ui.executionGroup.add("statictext", undefined, "Ready to process...");
    ui.statusText.graphics.font = defaultFont;

    // Button group below execution
    ui.buttonGroup = dialog.add("group");
    ui.buttonGroup.orientation = "row";
    ui.buttonGroup.alignment = "center";
    ui.buttonGroup.spacing = 20;

    ui.cancelButton = ui.buttonGroup.add("button", undefined, "Cancel");
    ui.cancelButton.size = [150, 45];
    ui.cancelButton.graphics.font = buttonFont;
    
    ui.abortButton = ui.buttonGroup.add("button", undefined, "Abort Processing");
    ui.abortButton.size = [150, 45];
    ui.abortButton.enabled = false;
    ui.abortButton.graphics.font = buttonFont;
    
    ui.runButton = ui.buttonGroup.add("button", undefined, "Run Process");
    ui.runButton.size = [150, 45];
    ui.runButton.enabled = false;
    ui.runButton.graphics.font = buttonFont;

    // Copyright at the bottom
    ui.copyrightGroup = dialog.add("group");
    ui.copyrightGroup.orientation = "row";
    ui.copyrightGroup.alignment = "center";
    ui.copyrightText = ui.copyrightGroup.add("statictext", undefined, "© " + new Date().getFullYear() + " Vermont Services Slovakia");
    ui.copyrightText.graphics.font = defaultFont;
    ui.copyrightText.graphics.foregroundColor = ui.copyrightText.graphics.newPen(ui.copyrightText.graphics.PenType.SOLID_COLOR, [0.5, 0.5, 0.5], 1);

    // Handle the abort button click
    ui.abortButton.onClick = function() {
        shouldAbortProcessing = true;
        ui.statusText.text = "Aborting process...";
        ui.abortButton.enabled = false;
    };

    // Update run button click handler
    ui.runButton.onClick = function() {
        if (!selectedInputFolder) {
            alert("Please select an input folder");
            return;
        }

        if (ui.fillMethodDropdown.selection.index === 0 && !isValidHex(ui.bgColorInput.text)) {
            alert("Please enter a valid hex color (e.g., #FFFFFF)");
            return;
        }

        // Create output folder path
        var outputFolder = ui.useInputFolder.value ? 
            new Folder(selectedInputFolder.fsName + "/processed") : 
            selectedOutputFolder;

        if (!outputFolder) {
            alert("Please select an output folder");
            return;
        }

        // Create output folder if it doesn't exist
        if (!outputFolder.exists) {
            outputFolder.create();
        }

        // Reset abort flag and enable abort button
        shouldAbortProcessing = false;
        ui.abortButton.enabled = true;
        ui.runButton.enabled = false;
        ui.cancelButton.enabled = false;

        // Get current settings
        targetWidth = parseInt(ui.widthInput.text);
        targetHeight = parseInt(ui.heightInput.text);
        var fillMethod = ui.fillMethodDropdown.selection.index === 0 ? "normal" : "contentAware";
        var fitMethod = cropOptionDropdown.selection.index === 0 ? "extend" : "crop";
        var quality = parseInt(qualityValue.text);
        var bgColor = ui.bgColorInput.text;

        // Start processing
        processFolder(
            selectedInputFolder, 
            outputFolder, 
            quality, 
            ui.progressBar, 
            ui.statusText, 
            bgColor, 
            fillMethod, 
            fitMethod
        );

        // Re-enable buttons after processing
        ui.abortButton.enabled = false;
        ui.runButton.enabled = true;
        ui.cancelButton.enabled = true;
    };

    // Handle the cancel button click
    ui.cancelButton.onClick = function() {
        dialog.close();
    };

    // Add folder selection functionality
    selectInputButton.onClick = function() {
        var folder = Folder.selectDialog("Select Input Folder");
        if (folder) {
            selectedInputFolder = folder;  // Update the global variable
            ui.selectedInputFolder = folder;  // Update the UI reference
            inputFolderPath.text = folder.fsName;
            inputFolderPath.graphics.foregroundColor = inputFolderPath.graphics.newPen(inputFolderPath.graphics.PenType.SOLID_COLOR, [0, 0, 0], 1);
            ui.runButton.enabled = true;
            
            // If using input folder for output, update that as well
            if (ui.useInputFolder.value) {
                selectedOutputFolder = folder;
                ui.selectedOutputFolder = folder;
            }
            
            // Force dialog to update
            dialog.update();
        }
    };

    ui.selectOutputButton.onClick = function() {
        var folder = Folder.selectDialog("Select Output Folder");
        if (folder) {
            selectedOutputFolder = folder;  // Update the global variable
            ui.selectedOutputFolder = folder;  // Update the UI reference
            ui.outputFolderPath.text = folder.fsName;
            ui.outputFolderPath.graphics.foregroundColor = ui.outputFolderPath.graphics.newPen(ui.outputFolderPath.graphics.PenType.SOLID_COLOR, [0, 0, 0], 1);
            ui.runButton.enabled = true;
            
            // Force dialog to update
            dialog.update();
        }
    };

    return dialog;
}

// Function to resize and center the image
function resizeAndCenterImage(doc, bgColor, fitMethod) {
    try {
        var docCopy = doc.duplicate();
        
        if (fitMethod === "crop") {
            // Calculate dimensions to maintain aspect ratio and cover target size
            var ratio = Math.max(targetWidth / docCopy.width, targetHeight / docCopy.height);
            var newWidth = Math.round(docCopy.width * ratio);
            var newHeight = Math.round(docCopy.height * ratio);
            
            // Resize larger than needed first
            docCopy.resizeImage(newWidth, newHeight, null, ResampleMethod.BICUBIC);
            
            // Center and crop to target size
            docCopy.resizeCanvas(targetWidth, targetHeight, AnchorPosition.MIDDLECENTER);
        } else {
            // Original extend behavior
            var ratio = Math.min(targetWidth / docCopy.width, targetHeight / docCopy.height);
            var newWidth = Math.round(docCopy.width * ratio);
            var newHeight = Math.round(docCopy.height * ratio);
            
            // Resize the image
            docCopy.resizeImage(newWidth, newHeight, null, ResampleMethod.BICUBIC);
            
            // Set background color
            var rgb = hexToRgb(bgColor);
            var solidColor = new SolidColor();
            solidColor.rgb.red = rgb.r;
            solidColor.rgb.green = rgb.g;
            solidColor.rgb.blue = rgb.b;
            app.backgroundColor = solidColor;
            
            // Extend canvas with background color
            docCopy.resizeCanvas(targetWidth, targetHeight, AnchorPosition.MIDDLECENTER);
        }

        return docCopy;
    } catch(e) {
        alert("Error processing image: " + e);
        return null;
    }
}

// Function to resize with content-aware fill
function resizeWithContentAwareFill(doc, fitMethod) {
    try {
        var docCopy = doc.duplicate();
        
        if (fitMethod === "crop") {
            var ratio = Math.max(targetWidth / docCopy.width, targetHeight / docCopy.height);
            var newWidth = Math.round(docCopy.width * ratio);
            var newHeight = Math.round(docCopy.height * ratio);
        } else {
            var ratio = Math.min(targetWidth / docCopy.width, targetHeight / docCopy.height);
            var newWidth = Math.round(docCopy.width * ratio);
            var newHeight = Math.round(docCopy.height * ratio);
        }
        
        // Resize the image
        docCopy.resizeImage(newWidth, newHeight, null, ResampleMethod.BICUBIC);
        
        // Create new canvas with target dimensions
        docCopy.resizeCanvas(targetWidth, targetHeight, AnchorPosition.MIDDLECENTER);
        
        // Create a selection of the transparent/empty areas
        docCopy.selection.selectAll();
        docCopy.selection.invert();
        
        // Check if there's actually a selection to fill
        if (!docCopy.selection.bounds.isEmpty) {
            var desc = new ActionDescriptor();
            var ref = new ActionReference();
            ref.putProperty(charIDToTypeID('Prpr'), charIDToTypeID('fill'));
            ref.putEnumerated(charIDToTypeID('Lyr '), charIDToTypeID('Ordn'), charIDToTypeID('Trgt'));
            desc.putReference(charIDToTypeID('null'), ref);
            desc.putEnumerated(charIDToTypeID('Usng'), charIDToTypeID('FlCn'), stringIDToTypeID('contentAware'));
            executeAction(charIDToTypeID('Fl  '), desc, DialogModes.NO);
            
            docCopy.selection.deselect();
        }
        
        return docCopy;
    } catch(e) {
        alert("Error in content-aware fill: " + e);
        return resizeAndCenterImage(doc, "#FFFFFF", fitMethod);
    }
}

// Function to process each image in the selected folder
function processFolder(inputFolder, outputFolder, quality, progressBar, statusText, bgColor, fillMethod, fitMethod) {
    try {
        // Get all image files from the input folder
        var files = inputFolder.getFiles(function(file) {
            return file instanceof File && 
                   /\.(jpg|jpeg|png|tif|psd|psb)$/i.test(file.name);
        });
        
        if (files.length > 0) {
            var processed = 0;
            var errors = 0;
            var startTime = new Date();

            for (var i = 0; i < files.length; i++) {
                if (shouldAbortProcessing) {
                    var duration = Math.round((new Date() - startTime) / 1000);
                    statusText.text = "Process aborted. Processed: " + processed + " files, Errors: " + errors + 
                                    " (Time elapsed: " + duration + "s)";
                    return;
                }

                try {
                    // Calculate progress
                    var progress = Math.round((i / files.length) * 100);
                    progressBar.value = progress;
                    
                    // Calculate time estimates
                    var elapsed = new Date() - startTime;
                    var estimatedTotal = (elapsed / (i + 1)) * files.length;
                    var remaining = Math.round((estimatedTotal - elapsed) / 1000);
                    
                    // Update status
                    statusText.text = "Processing: " + files[i].name + 
                                    "\nProgress: " + progress + "% (" + (i + 1) + " of " + files.length + ")" +
                                    "\nProcessed: " + processed + " | Errors: " + errors +
                                    "\nEstimated time remaining: " + remaining + "s";

                    // Open and process the image
                    var doc = app.open(files[i]);
                    var docCopy = null;

                    // Process based on selected method
                    if (fillMethod === "normal") {
                        docCopy = resizeAndCenterImage(doc, bgColor, fitMethod);
                    } else {
                        try {
                            docCopy = resizeWithContentAwareFill(doc, fitMethod);
                        } catch(fillError) {
                            statusText.text += "\nWarning: Content-aware fill failed for " + files[i].name + ". Using normal fill.";
                            docCopy = resizeAndCenterImage(doc, "#FFFFFF", fitMethod);
                        }
                    }

                    if (docCopy) {
                        // Prepare save options
                        var saveFile = new File(outputFolder.fsName + "/" + files[i].name);
                        var saveOptions = new JPEGSaveOptions();
                        saveOptions.quality = quality;
                        saveOptions.formatOptions = FormatOptions.OPTIMIZEDBASELINE;
                        saveOptions.embedColorProfile = true;

                        // Save the processed image
                        docCopy.saveAs(saveFile, saveOptions, true);
                        processed++;

                        // Cleanup
                        docCopy.close(SaveOptions.DONOTSAVECHANGES);
                    }

                    // Close original document
                    doc.close(SaveOptions.DONOTSAVECHANGES);

                } catch(e) {
                    errors++;
                    statusText.text += "\nError processing " + files[i].name + ": " + e.message;
                }

                // Update UI
                dialog.update();
            }

            // Final status update
            var totalDuration = Math.round((new Date() - startTime) / 1000);
            var averageTime = Math.round((totalDuration / processed) * 10) / 10;
            
            progressBar.value = 100;
            statusText.text = "Completed!\n" +
                            "Total files processed: " + processed + "\n" +
                            "Errors encountered: " + errors + "\n" +
                            "Total time: " + totalDuration + "s\n" +
                            "Average time per image: " + averageTime + "s";
        } else {
            statusText.text = "No valid image files found in the folder.";
            alert("No valid image files found in the folder.");
        }
    } catch(e) {
        statusText.text = "Error: " + e.message;
        alert("Error: " + e);
    }
}

// Show the dialog
var dialog = createDialog();
dialog.show();

// Store the alignment value for processing
function getSelectedAlignment() {
    if (ui.topAlign.value) return "top";
    if (ui.centerAlign.value) return "center";
    if (ui.bottomAlign.value) return "bottom";
    return "center"; // default fallback
}
