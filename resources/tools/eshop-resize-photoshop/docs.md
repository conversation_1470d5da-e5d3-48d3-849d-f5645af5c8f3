# E-Shop Image Resizer (Photoshop)


## ✨ Features

- 🖼️ Batch process multiple images at once
- 📐 Resize images to 1600x2400px (configurable)
- 🎨 Three smart fill methods:
  - Normal (User input custom background color)
  - Content-Aware (Photoshop self computed background color)
  - Generative (Ai generate - Warning paid service)
- 🎯 Customizable background color (HEX/RGB)
- ⚙️ JPEG quality control (1-12)
- 📊 Real-time progress tracking
- 📁 Flexible input/output folder selection
- 📂 Organized output in "processed" subfolder

## 🚀 Requirements

- Adobe Photoshop (Compatible with CS6 and newer versions)
- Basic knowledge of Photoshop scripting

## 💻 Installation

1. Download the `eshop-image-resize.jsx` script
2. In Photoshop, go to `File > Scripts > Browse...`
3. Navigate to and select the `eshop-image-resize.jsx` file
4. The script dialog will appear

## 📖 Usage

### Getting Started

1. Launch the script in Photoshop
2. Configure your settings:
   ```
   ├── Dimensions: 1600x2400px (default)
   ├── Fill Method
   │   ├── Normal Fill (User input custom background color)
   │   ├── Content-Aware Fill (Photoshop self computed background color)
   │   └── Generative Fill (Ai generate - Warning paid service)
   ├── JPEG Quality (1-12)
   └── Background Color (for Normal Fill)
   ```
3. Select input folder containing your images
4. Choose output location:
   - Use input folder (creates a "processed" subfolder)
   - Select custom output folder
5. Click "Run Process" to start batch processing

### 📄 Supported File Formats

| Format    | Extensions    |
|-----------|---------------|
| JPEG      | .jpg, .jpeg   |
| PNG       | .png          |
| TIFF      | .tif          |
| Photoshop | .psd, .psb    |

## ⚠️ Error Handling

The script includes robust error handling and monitoring:

- ✅ Comprehensive error handling and logging
- 📊 Real-time progress bar
- 🔍 Detailed error messages for failed operations
- 📝 Processing summary upon completion

## 🔧 Troubleshooting

If you encounter any issues:

1. Ensure Photoshop has necessary permissions
2. Check input images are not corrupted
3. Verify sufficient disk space for output
4. Confirm Photoshop version compatibility

## 📝 License

This project is proprietary software.  
© Vermont Services Slovakia. All rights reserved.

## 💬 Support

For support or bug reports:
- Create an issue in the GitHub repository
- Include detailed steps to reproduce any issues
- Attach example images if relevant

---

<div align="center">
  <p>Made with ❤️ by Vermont Services Slovakia</p>
</div>