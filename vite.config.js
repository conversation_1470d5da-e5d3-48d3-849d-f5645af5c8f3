import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue'; 
import path from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/main.css',
                'resources/css/plugins.css',
                'resources/css/modalboxes.css',
                'resources/css/background-animation.css',
                'resources/css/gradient-svg.css',
                'resources/css/navigation.css',
                'resources/css/snap-scroll.css',
                'resources/css/tos.css',
                'resources/js/app.js',
                'resources/js/sell_through.js',
                'resources/js/hello-page.js',
                'resources/js/tools/scroll-spy.js',
                'resources/js/svg-animation.js',
                'resources/js/snap-scroll.js',
                'resources/js/tools/svg-optimizer.js',
                'resources/sass/template.scss',
            ],
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: { 
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
            'vue': 'vue/dist/vue.esm-bundler.js',
        },
    },
    build: {
        rollupOptions: {
            output: {
                entryFileNames: `assets/[name].[hash].js`,
                chunkFileNames: `assets/[name].[hash].js`,
                assetFileNames: `assets/[name].[hash].[ext]`
            }
        },
        hmr: process.env.NODE_ENV === 'production' ? false : {},
    },
    server: {
        allowedHosts: [
            'management-app.localhost-manager.orb.local',
        ],
    },
    publicDir: 'public',
    assetsInclude: ['**/*.woff2']
});
