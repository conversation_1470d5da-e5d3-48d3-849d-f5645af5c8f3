<?php

namespace App\Traits;

use Illuminate\Support\Facades\Storage;

trait HasAvatar
{
    public function getAvatarUrlAttribute(): string
    {
        $login = $this->login;
        
        // Check if a custom avatar exists in storage
        if ($this->avatar && Storage::disk('public')->exists($this->avatar)) {
            return Storage::disk('public')->url($this->avatar);
        }
        
        // Check for login-based jpeg in the users directory
        $loginBasedPath = "img/users/{$login}.jpeg";
        if (file_exists(public_path($loginBasedPath))) {
            return asset($loginBasedPath);
        }
        
        // Return default avatar if no custom avatar exists
        return asset('img/default-avatar.png');
    }
} 