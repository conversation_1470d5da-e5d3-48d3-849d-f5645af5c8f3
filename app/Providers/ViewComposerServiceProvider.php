<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Font;
use App\Models\User;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Share admin action counts with all views
        View::composer('*', function ($view) {
            try {
                // Set default values
                $needsUpdateCount = 0;
                $newUsersCount = 0;
                
                // Only calculate if user is logged in and is admin
                if (auth()->check() && in_array(auth()->user()->login, config('vermont.admin_user_logins', []))) {
                    try {
                        // Count new users - with error handling
                        try {
                            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
                        } catch (\Exception $e) {
                            \Log::error("Error counting new users: " . $e->getMessage());
                            $newUsersCount = 0;
                        }
                        
                        // Count fonts needing updates - with error handling and optimized query
                        try {
                            // Use a more targeted query with pagination to avoid memory issues
                            Font::select('id', 'path', 'icons_max_updated_at')
                                ->chunk(50, function ($fonts) use (&$needsUpdateCount) {
                                    foreach ($fonts as $font) {
                                        if (!empty($font->path) && 
                                            file_exists(resource_path('sass/'.$font->path)) && 
                                            (is_null($font->icons_max_updated_at) || 
                                            (filemtime(resource_path('sass/'.$font->path)) > \Carbon\Carbon::parse($font->icons_max_updated_at)->timestamp))) {
                                            $needsUpdateCount++;
                                        }
                                    }
                                });
                        } catch (\Exception $e) {
                            \Log::error("Error checking fonts for updates: " . $e->getMessage());
                            $needsUpdateCount = 0;
                        }
                    } catch (\Exception $e) {
                        \Log::error("Error in ViewComposer admin calculations: " . $e->getMessage());
                    }
                }
                
                // Share variables with the view
                $view->with('needsUpdateCount', $needsUpdateCount);
                $view->with('newUsersCount', $newUsersCount);
            } catch (\Exception $e) {
                // Fail gracefully if there's an error
                \Log::error("Fatal error in ViewComposer: " . $e->getMessage());
                $view->with('needsUpdateCount', 0);
                $view->with('newUsersCount', 0);
            }
        });
    }
} 