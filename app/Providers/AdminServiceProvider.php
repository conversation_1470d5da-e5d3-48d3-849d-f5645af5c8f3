<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Log;
use App\Models\Font;
use App\Models\User;

class AdminServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Log that the service provider is loading
        Log::info('AdminServiceProvider is booting...');

        // Share admin action counts with all views
        View::composer('*', function ($view) {
            try {
                // Only calculate if the user is logged in and is an admin
                if (auth()->check() && in_array(auth()->user()->login, config('vermont.admin_user_logins', []))) {
                    // Count new users in the last 7 days
                    $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
                    
                    // Count fonts that need updates - simplified version
                    $needsUpdateCount = 0;
                    $fonts = Font::all();
                    
                    foreach ($fonts as $font) {
                        if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                            $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                            
                            // Check if file is newer than the font's last update
                            if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                                $needsUpdateCount++;
                            }
                        }
                    }
                    
                    // Total admin action count
                    $adminActionCount = $needsUpdateCount + $newUsersCount;
                    
                    // Share variables with the view
                    $view->with([
                        'adminActionCount' => $adminActionCount,
                        'needsUpdateCount' => $needsUpdateCount,
                        'newUsersCount' => $newUsersCount,
                    ]);
                } else {
                    // Default values for non-admins
                    $view->with([
                        'adminActionCount' => 0,
                        'needsUpdateCount' => 0,
                        'newUsersCount' => 0,
                    ]);
                }
            } catch (\Exception $e) {
                // Log any errors
                Log::error('Error in AdminServiceProvider view composer: ' . $e->getMessage());
                
                // In case of any errors, set counts to 0 to prevent breaking the view
                $view->with([
                    'adminActionCount' => 0,
                    'needsUpdateCount' => 0,
                    'newUsersCount' => 0,
                ]);
            }
        });
    }
} 