<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class FontServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register the FontHelper in the container
        $this->app->singleton('font', function ($app) {
            return new \App\Helpers\FontHelper();
        });
    }
}