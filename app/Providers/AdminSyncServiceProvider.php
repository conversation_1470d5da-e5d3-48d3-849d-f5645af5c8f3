<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;

class AdminSyncServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // When a user logs in, sync their admin status with config values
        Event::listen('Illuminate\Auth\Events\Login', function ($event) {
            $this->syncAdminStatus($event->user);
        });

        // If user is already logged in, sync admin status on every request
        if (Auth::check()) {
            $this->syncAdminStatus(Auth::user());
        }
    }

    /**
     * Synchronize the user's is_admin database field with the config value.
     */
    protected function syncAdminStatus(User $user): void
    {
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        $isAdminInConfig = in_array($user->login, $adminLogins);
        
        // Only update the database if there's a mismatch to avoid unnecessary queries
        if ($user->is_admin !== $isAdminInConfig) {
            try {
                $user->is_admin = $isAdminInConfig;
                $user->save();
                
                Log::info("Admin status synchronized for user {$user->login}", [
                    'user_id' => $user->id,
                    'login' => $user->login,
                    'is_admin' => $isAdminInConfig
                ]);
            } catch (\Exception $e) {
                Log::error("Failed to sync admin status for user {$user->login}", [
                    'user_id' => $user->id,
                    'login' => $user->login,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
    
    /**
     * Synchronize all users' admin status.
     * This can be called from migrations or seeders during deployment.
     *
     * @return int Number of users updated
     */
    public function syncAllAdminStatus(): int
    {
        $updatedCount = 0;
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        
        User::chunk(100, function ($users) use (&$updatedCount, $adminLogins) {
            foreach ($users as $user) {
                $isAdminInConfig = in_array($user->login, $adminLogins);
                
                if ($user->is_admin !== $isAdminInConfig) {
                    $user->is_admin = $isAdminInConfig;
                    $user->save();
                    $updatedCount++;
                    
                    Log::info("Admin status synchronized for user {$user->login}", [
                        'user_id' => $user->id,
                        'login' => $user->login,
                        'is_admin' => $isAdminInConfig
                    ]);
                }
            }
        });
        
        return $updatedCount;
    }
}
