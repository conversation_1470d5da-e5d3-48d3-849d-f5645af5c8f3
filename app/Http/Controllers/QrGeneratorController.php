<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class QrGeneratorController extends Controller
{
    /**
     * Generate a QR code and return SVG content
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
            'size' => 'required|integer|min:100|max:1000',
            'color' => 'required|string|regex:/^#[a-fA-F0-9]{6}$/',
            'bgColor' => 'required|string|regex:/^#[a-fA-F0-9]{6}$/',
            'errorCorrection' => 'nullable|string|in:L,M,Q,H',
            'margin' => 'nullable|integer|min:0|max:10',
            'cornerRadius' => 'nullable|integer|min:0|max:50',
            'qrStyle' => 'nullable|string|in:square,rounded,dots',
            'logo' => 'nullable|image|max:2048',
            'logoSize' => 'nullable|integer|min:5|max:30',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Extract data from request
        $content = $request->input('content');
        $size = $request->input('size');
        $color = $request->input('color');
        $bgColor = $request->input('bgColor');
        $errorCorrection = $request->input('errorCorrection', 'M');
        $margin = $request->input('margin', 4);
        $cornerRadius = $request->input('cornerRadius', 0);
        $qrStyle = $request->input('qrStyle', 'square');
        $logoSize = $request->input('logoSize', 15);
        
        // Process logo if uploaded
        $logoImage = null;
        if ($request->hasFile('logo')) {
            $logoFile = $request->file('logo');
            $logoImage = $this->processLogoImage($logoFile);
        }

        // Generate QR code
        $qrData = $this->generateQrCodeMatrix($content, $errorCorrection);
        
        // Convert to SVG
        $svg = $this->matrixToSvg(
            $qrData, 
            $size, 
            $color, 
            $bgColor, 
            $margin, 
            $cornerRadius, 
            $qrStyle,
            $logoImage,
            $logoSize
        );

        return response()->json(['svg' => $svg]);
    }

    /**
     * Download QR code in the requested format
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function download(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
            'size' => 'required|integer|min:100|max:1000',
            'color' => 'required|string|regex:/^#[a-fA-F0-9]{6}$/',
            'bgColor' => 'required|string|regex:/^#[a-fA-F0-9]{6}$/',
            'format' => 'required|string|in:svg,png,pdf',
            'errorCorrection' => 'nullable|string|in:L,M,Q,H',
            'margin' => 'nullable|integer|min:0|max:10',
            'cornerRadius' => 'nullable|integer|min:0|max:50',
            'qrStyle' => 'nullable|string|in:square,rounded,dots',
            'logo' => 'nullable|image|max:2048',
            'logoSize' => 'nullable|integer|min:5|max:30',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Extract data from request
        $content = $request->input('content');
        $size = $request->input('size');
        $color = $request->input('color');
        $bgColor = $request->input('bgColor');
        $format = $request->input('format');
        $errorCorrection = $request->input('errorCorrection', 'M');
        $margin = $request->input('margin', 4);
        $cornerRadius = $request->input('cornerRadius', 0);
        $qrStyle = $request->input('qrStyle', 'square');
        $logoSize = $request->input('logoSize', 15);
        
        // Process logo if uploaded
        $logoImage = null;
        if ($request->hasFile('logo')) {
            $logoFile = $request->file('logo');
            $logoImage = $this->processLogoImage($logoFile);
        }

        // Generate QR code
        $qrData = $this->generateQrCodeMatrix($content, $errorCorrection);
        
        // Generate file name
        $timestamp = date('Ymd_His');
        $filename = 'qrcode_' . $timestamp . '.' . $format;
        
        if ($format === 'svg') {
            $svg = $this->matrixToSvg(
                $qrData, 
                $size, 
                $color, 
                $bgColor, 
                $margin, 
                $cornerRadius, 
                $qrStyle,
                $logoImage,
                $logoSize
            );
            
            return response($svg, 200, [
                'Content-Type' => 'image/svg+xml',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);
        } elseif ($format === 'png') {
            $png = $this->matrixToPng(
                $qrData, 
                $size, 
                $color, 
                $bgColor, 
                $margin, 
                $cornerRadius, 
                $qrStyle,
                $logoImage,
                $logoSize
            );
            
            return response($png, 200, [
                'Content-Type' => 'image/png',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);
        } elseif ($format === 'pdf') {
            $pdf = $this->matrixToPdf(
                $qrData, 
                $size, 
                $color, 
                $bgColor, 
                $margin, 
                $cornerRadius, 
                $qrStyle,
                $logoImage,
                $logoSize,
                $content
            );
            
            return response($pdf, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);
        }
    }

    /**
     * Process uploaded logo image
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return array
     */
    private function processLogoImage($file)
    {
        // Create a GD image resource from the uploaded file
        $logoImage = null;
        $extension = strtolower($file->getClientOriginalExtension());
        
        if ($extension === 'jpg' || $extension === 'jpeg') {
            $logoImage = imagecreatefromjpeg($file->getPathname());
        } elseif ($extension === 'png') {
            $logoImage = imagecreatefrompng($file->getPathname());
        } elseif ($extension === 'gif') {
            $logoImage = imagecreatefromgif($file->getPathname());
        }
        
        if (!$logoImage) {
            return null;
        }
        
        // Get logo dimensions
        $logoWidth = imagesx($logoImage);
        $logoHeight = imagesy($logoImage);
        
        // Create a clean copy with transparency
        $newLogo = imagecreatetruecolor($logoWidth, $logoHeight);
        imagealphablending($newLogo, false);
        imagesavealpha($newLogo, true);
        
        // Fill with transparent background
        $transparent = imagecolorallocatealpha($newLogo, 0, 0, 0, 127);
        imagefilledrectangle($newLogo, 0, 0, $logoWidth, $logoHeight, $transparent);
        
        // Copy and preserve transparency
        imagecopy($newLogo, $logoImage, 0, 0, 0, 0, $logoWidth, $logoHeight);
        
        // Convert to base64 for SVG embedding
        ob_start();
        imagepng($newLogo);
        $logoData = ob_get_clean();
        $logoBase64 = base64_encode($logoData);
        
        // Clean up
        imagedestroy($logoImage);
        imagedestroy($newLogo);
        
        return [
            'data' => $logoBase64,
            'width' => $logoWidth,
            'height' => $logoHeight
        ];
    }

    /**
     * Generate QR code matrix
     *
     * @param string $content
     * @param string $errorCorrectionLevel
     * @return array
     */
    private function generateQrCodeMatrix($content, $errorCorrectionLevel = 'M')
    {
        // Simplest QR Code Version 1 (21x21)
        // This is a very basic implementation that works for short text
        // For a production system, you'd want to implement proper error correction and encoding
        
        $size = 21; // Version 1 QR code is 21x21
        $matrix = array_fill(0, $size, array_fill(0, $size, 0));
        
        // Add finder patterns (the three square patterns in corners)
        $this->addFinderPattern($matrix, 0, 0);
        $this->addFinderPattern($matrix, $size - 7, 0);
        $this->addFinderPattern($matrix, 0, $size - 7);
        
        // Add timing patterns
        for ($i = 8; $i < $size - 8; $i++) {
            $matrix[6][$i] = ($i % 2 == 0) ? 1 : 0;
            $matrix[$i][6] = ($i % 2 == 0) ? 1 : 0;
        }
        
        // Add alignment pattern for Version 1
        $this->addAlignmentPattern($matrix, $size - 9, $size - 9);
        
        // Add format information
        // This is a simplified version - real QR codes use specific format patterns
        $this->addFormatInformation($matrix, $errorCorrectionLevel);
        
        // Encode data using a simple method
        // For short alphanumeric content
        $contentBits = $this->encodeSimple($content, $errorCorrectionLevel);
        
        // Place data bits in the matrix
        $this->placeDataBits($matrix, $contentBits);
        
        return $matrix;
    }
    
    /**
     * Add a finder pattern to the QR matrix
     *
     * @param array $matrix
     * @param int $x
     * @param int $y
     */
    private function addFinderPattern(array &$matrix, $x, $y)
    {
        // Outer square
        for ($i = 0; $i < 7; $i++) {
            $matrix[$y][$x + $i] = 1;
            $matrix[$y + 6][$x + $i] = 1;
            $matrix[$y + $i][$x] = 1;
            $matrix[$y + $i][$x + 6] = 1;
        }
        
        // Inner square
        for ($i = 0; $i < 5; $i++) {
            for ($j = 0; $j < 5; $j++) {
                $matrix[$y + 1 + $i][$x + 1 + $j] = 0;
            }
        }
        
        // Center dot
        for ($i = 0; $i < 3; $i++) {
            for ($j = 0; $j < 3; $j++) {
                $matrix[$y + 2 + $i][$x + 2 + $j] = 1;
            }
        }
    }
    
    /**
     * Add an alignment pattern to the QR matrix
     *
     * @param array $matrix
     * @param int $x
     * @param int $y
     */
    private function addAlignmentPattern(array &$matrix, $x, $y)
    {
        for ($i = -2; $i <= 2; $i++) {
            for ($j = -2; $j <= 2; $j++) {
                $matrix[$y + $i][$x + $j] = (max(abs($i), abs($j)) % 2 == 0) ? 1 : 0;
            }
        }
    }
    
    /**
     * Add format information to the QR matrix
     *
     * @param array $matrix
     * @param string $errorCorrectionLevel
     */
    private function addFormatInformation(array &$matrix, $errorCorrectionLevel)
    {
        // Map error correction level to bits
        $errorCorrectionBits = [
            'L' => [0, 1], // 01
            'M' => [0, 0], // 00
            'Q' => [1, 1], // 11
            'H' => [1, 0]  // 10
        ];
        
        $ecBits = $errorCorrectionBits[$errorCorrectionLevel] ?? $errorCorrectionBits['M'];
        
        // Simplified format information with error correction level
        $format = array_merge($ecBits, [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0]);
        
        // Place format information around the top-left finder pattern
        for ($i = 0; $i < 8; $i++) {
            if ($i < 6) {
                $matrix[8][$i] = $format[$i];
            } elseif ($i < 8) {
                $matrix[8][$i + 1] = $format[$i];
            }
            
            if ($i < 7) {
                $matrix[7 - $i][8] = $format[14 - $i];
            } else {
                $matrix[15 - $i][8] = $format[14 - $i];
            }
        }
        
        // Reserved dark module
        $matrix[8][8] = 1;
    }
    
    /**
     * Simple encoding of text data for QR code
     *
     * @param string $content
     * @param string $errorCorrectionLevel
     * @return array
     */
    private function encodeSimple($content, $errorCorrectionLevel)
    {
        // Convert string to binary representation
        $bits = [];
        $bytes = unpack('C*', $content);
        
        // Add mode indicator (4 bits) - using byte mode (0100)
        $bits = array_merge($bits, [0, 1, 0, 0]);
        
        // Add length indicator (8 bits for version 1-9 in byte mode)
        $length = count($bytes);
        $lengthBits = str_pad(decbin($length), 8, '0', STR_PAD_LEFT);
        for ($i = 0; $i < 8; $i++) {
            $bits[] = (int) $lengthBits[$i];
        }
        
        // Add data
        foreach ($bytes as $byte) {
            $byteBits = str_pad(decbin($byte), 8, '0', STR_PAD_LEFT);
            for ($i = 0; $i < 8; $i++) {
                $bits[] = (int) $byteBits[$i];
            }
        }
        
        // Calculate required capacity based on error correction level
        $capacity = match ($errorCorrectionLevel) {
            'L' => 152, // 19 bytes (Low - 7%)
            'M' => 128, // 16 bytes (Medium - 15%)
            'Q' => 104, // 13 bytes (Quartile - 25%)
            'H' => 72,  // 9 bytes (High - 30%)
            default => 128 // Default to Medium
        };
        
        // Add terminator and padding if needed
        while (count($bits) < $capacity) {
            $bits[] = 0;
        }
        
        return $bits;
    }
    
    /**
     * Place data bits in the QR matrix
     *
     * @param array $matrix
     * @param array $bits
     */
    private function placeDataBits(array &$matrix, array $bits)
    {
        $size = count($matrix);
        $bitIndex = 0;
        $maxBits = count($bits);
        
        // Start from bottom right and move in zig-zag pattern
        for ($col = $size - 1; $col >= 0; $col -= 2) {
            // Skip the vertical timing pattern column
            if ($col == 6) {
                $col--;
            }
            
            // Direction alternates up and down
            for ($row = $size - 1; $row >= 0; $row--) {
                for ($c = 0; $c < 2; $c++) {
                    $currentCol = $col - $c;
                    if ($currentCol >= 0 && $matrix[$row][$currentCol] === 0) {
                        if ($bitIndex < $maxBits) {
                            $matrix[$row][$currentCol] = $bits[$bitIndex];
                            $bitIndex++;
                        }
                    }
                }
            }
            
            // Go up
            for ($row = 0; $row < $size; $row++) {
                for ($c = 0; $c < 2; $c++) {
                    $currentCol = $col - $c;
                    if ($currentCol >= 0 && $matrix[$row][$currentCol] === 0) {
                        if ($bitIndex < $maxBits) {
                            $matrix[$row][$currentCol] = $bits[$bitIndex];
                            $bitIndex++;
                        }
                    }
                }
            }
        }
    }

    /**
     * Convert QR matrix to SVG
     *
     * @param array $matrix
     * @param int $size
     * @param string $color
     * @param string $bgColor
     * @param int $margin
     * @param int $cornerRadius
     * @param string $qrStyle
     * @param array|null $logoImage
     * @param int $logoSize
     * @return string
     */
    private function matrixToSvg($matrix, $size, $color, $bgColor, $margin = 4, $cornerRadius = 0, $qrStyle = 'square', $logoImage = null, $logoSize = 15)
    {
        $matrixSize = count($matrix);
        
        // Calculate the size of a single module/cell including margin
        $fullSize = $size + ($margin * 2);
        $cellSize = $size / ($matrixSize - (2 * $margin));
        
        // Calculate the viewBox size with margin
        $viewBoxSize = $matrixSize + ($margin * 2);
        
        $svg = '<?xml version="1.0" encoding="UTF-8"?>';
        $svg .= '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="' . $fullSize . '" height="' . $fullSize . '" viewBox="0 0 ' . $viewBoxSize . ' ' . $viewBoxSize . '">';
        
        // Background
        $svg .= '<rect width="100%" height="100%" fill="' . $bgColor . '"/>';
        
        // Logo dimensions and position
        $logoPlacement = null;
        if ($logoImage) {
            $logoPlacementSize = ($matrixSize * $logoSize) / 100;
            $logoPlacementPosition = ($matrixSize - $logoPlacementSize) / 2 + $margin;
            $logoPlacement = [
                'x' => $logoPlacementPosition,
                'y' => $logoPlacementPosition,
                'width' => $logoPlacementSize,
                'height' => $logoPlacementSize
            ];
        }
        
        // QR Code cells
        for ($y = 0; $y < $matrixSize; $y++) {
            for ($x = 0; $x < $matrixSize; $x++) {
                if ($matrix[$y][$x] == 1) {
                    // Skip drawing cells where the logo will be placed
                    if ($logoPlacement && 
                        $x + $margin >= $logoPlacement['x'] && 
                        $x + $margin < $logoPlacement['x'] + $logoPlacement['width'] && 
                        $y + $margin >= $logoPlacement['y'] && 
                        $y + $margin < $logoPlacement['y'] + $logoPlacement['height']) {
                        continue;
                    }
                    
                    // Position with margin
                    $posX = $x + $margin;
                    $posY = $y + $margin;
                    
                    // Different styles of QR code modules
                    if ($qrStyle === 'dots') {
                        // Draw circles instead of squares
                        $cx = $posX + 0.5;
                        $cy = $posY + 0.5;
                        $radius = 0.45; // Slightly smaller than 0.5 to have spacing
                        $svg .= '<circle cx="' . $cx . '" cy="' . $cy . '" r="' . $radius . '" fill="' . $color . '"/>';
                    } else {
                        // For square or rounded styles
                        $rx = ($qrStyle === 'rounded') ? ($cornerRadius / 100 * 0.5) : 0;
                        $svg .= '<rect x="' . $posX . '" y="' . $posY . '" width="1" height="1" rx="' . $rx . '" fill="' . $color . '"/>';
                    }
                }
            }
        }
        
        // Add logo if provided
        if ($logoImage) {
            $svg .= '<image x="' . $logoPlacement['x'] . '" y="' . $logoPlacement['y'] . '" width="' . $logoPlacement['width'] . '" height="' . $logoPlacement['height'] . '" href="data:image/png;base64,' . $logoImage['data'] . '" preserveAspectRatio="xMidYMid meet"/>';
        }
        
        $svg .= '</svg>';
        
        return $svg;
    }

    /**
     * Convert QR matrix to PNG
     *
     * @param array $matrix
     * @param int $size
     * @param string $color
     * @param string $bgColor
     * @param int $margin
     * @param int $cornerRadius
     * @param string $qrStyle
     * @param array|null $logoImage
     * @param int $logoSize
     * @return string
     */
    private function matrixToPng($matrix, $size, $color, $bgColor, $margin = 4, $cornerRadius = 0, $qrStyle = 'square', $logoImage = null, $logoSize = 15)
    {
        $matrixSize = count($matrix);
        
        // Calculate the size with margins
        $fullSize = $size + (2 * $margin * $size / $matrixSize);
        $moduleSize = $size / $matrixSize;
        $marginPx = $margin * $moduleSize;
        
        // Create image
        $image = imagecreatetruecolor($fullSize, $fullSize);
        
        // Enable alpha blending and save alpha channel
        imagealphablending($image, true);
        imagesavealpha($image, true);
        
        // Convert hex colors to RGB
        list($r, $g, $b) = sscanf($bgColor, "#%02x%02x%02x");
        $bgColorAllocated = imagecolorallocate($image, $r, $g, $b);
        
        list($r, $g, $b) = sscanf($color, "#%02x%02x%02x");
        $fgColorAllocated = imagecolorallocate($image, $r, $g, $b);
        
        // Fill background
        imagefill($image, 0, 0, $bgColorAllocated);
        
        // Logo dimensions and position
        $logoPlacement = null;
        if ($logoImage) {
            $logoPlacementSize = ($size * $logoSize) / 100;
            $logoPlacementX = ($fullSize - $logoPlacementSize) / 2;
            $logoPlacementY = ($fullSize - $logoPlacementSize) / 2;
            $logoPlacement = [
                'x' => $logoPlacementX,
                'y' => $logoPlacementY,
                'width' => $logoPlacementSize,
                'height' => $logoPlacementSize
            ];
        }
        
        // Draw QR code pixels
        for ($y = 0; $y < $matrixSize; $y++) {
            for ($x = 0; $x < $matrixSize; $x++) {
                if ($matrix[$y][$x] == 1) {
                    // Calculate pixel position with margin
                    $pixelX = $x * $moduleSize + $marginPx;
                    $pixelY = $y * $moduleSize + $marginPx;
                    
                    // Skip drawing cells where the logo will be placed
                    if ($logoPlacement &&
                        $pixelX >= $logoPlacement['x'] - $moduleSize &&
                        $pixelX <= $logoPlacement['x'] + $logoPlacement['width'] &&
                        $pixelY >= $logoPlacement['y'] - $moduleSize &&
                        $pixelY <= $logoPlacement['y'] + $logoPlacement['height']) {
                        continue;
                    }
                    
                    if ($qrStyle === 'dots') {
                        // Draw a filled circle
                        $centerX = $pixelX + $moduleSize / 2;
                        $centerY = $pixelY + $moduleSize / 2;
                        $radius = $moduleSize * 0.45;
                        imagefilledellipse($image, (int)$centerX, (int)$centerY, (int)($radius * 2), (int)($radius * 2), $fgColorAllocated);
                    } else if ($qrStyle === 'rounded' && $cornerRadius > 0) {
                        // Draw a rounded rectangle
                        $this->imageFilledRoundedRect(
                            $image,
                            (int)$pixelX,
                            (int)$pixelY,
                            (int)($pixelX + $moduleSize - 1),
                            (int)($pixelY + $moduleSize - 1),
                            (int)($moduleSize * $cornerRadius / 100),
                            $fgColorAllocated
                        );
                    } else {
                        // Draw a regular rectangle
                        imagefilledrectangle(
                            $image,
                            (int)$pixelX,
                            (int)$pixelY,
                            (int)($pixelX + $moduleSize - 1),
                            (int)($pixelY + $moduleSize - 1),
                            $fgColorAllocated
                        );
                    }
                }
            }
        }
        
        // Add logo if provided
        if ($logoImage) {
            // Decode the base64 logo
            $logoImg = imagecreatefromstring(base64_decode($logoImage['data']));
            
            // Add the logo to the QR code
            imagecopyresampled(
                $image,
                $logoImg,
                (int)$logoPlacement['x'],
                (int)$logoPlacement['y'],
                0, 0,
                (int)$logoPlacement['width'],
                (int)$logoPlacement['height'],
                imagesx($logoImg),
                imagesy($logoImg)
            );
            
            imagedestroy($logoImg);
        }
        
        // Output PNG
        ob_start();
        imagepng($image);
        $pngData = ob_get_clean();
        
        // Clean up
        imagedestroy($image);
        
        return $pngData;
    }
    
    /**
     * Draw a filled rounded rectangle with GD
     *
     * @param resource $image GD image resource
     * @param int $x1 Top left X coordinate
     * @param int $y1 Top left Y coordinate
     * @param int $x2 Bottom right X coordinate
     * @param int $y2 Bottom right Y coordinate
     * @param int $radius Radius of corners
     * @param int $color Color to fill with
     */
    private function imageFilledRoundedRect($image, $x1, $y1, $x2, $y2, $radius, $color)
    {
        // Draw the main rectangle
        imagefilledrectangle($image, $x1 + $radius, $y1, $x2 - $radius, $y2, $color);
        imagefilledrectangle($image, $x1, $y1 + $radius, $x2, $y2 - $radius, $color);
        
        // Draw the four corner quadrants
        imagefilledarc($image, $x1 + $radius, $y1 + $radius, $radius * 2, $radius * 2, 180, 270, $color, IMG_ARC_PIE);
        imagefilledarc($image, $x2 - $radius, $y1 + $radius, $radius * 2, $radius * 2, 270, 360, $color, IMG_ARC_PIE);
        imagefilledarc($image, $x1 + $radius, $y2 - $radius, $radius * 2, $radius * 2, 90, 180, $color, IMG_ARC_PIE);
        imagefilledarc($image, $x2 - $radius, $y2 - $radius, $radius * 2, $radius * 2, 0, 90, $color, IMG_ARC_PIE);
    }
    
    /**
     * Convert QR matrix to PDF
     *
     * @param array $matrix
     * @param int $size
     * @param string $color
     * @param string $bgColor
     * @param int $margin
     * @param int $cornerRadius
     * @param string $qrStyle
     * @param array|null $logoImage
     * @param int $logoSize
     * @param string $content
     * @return string
     */
    private function matrixToPdf($matrix, $size, $color, $bgColor, $margin = 4, $cornerRadius = 0, $qrStyle = 'square', $logoImage = null, $logoSize = 15, $content = '')
    {
        // Generate SVG first
        $svg = $this->matrixToSvg($matrix, $size, $color, $bgColor, $margin, $cornerRadius, $qrStyle, $logoImage, $logoSize);
        
        // Create PDF content
        $pdf = "%PDF-1.7\n";
        
        // Object 1: Catalog
        $pdf .= "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
        
        // Object 2: Pages
        $pdf .= "2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n";
        
        // Object 3: Page
        $pageWidth = 595;
        $pageHeight = 842;
        $pdf .= "3 0 obj\n<< /Type /Page /Parent 2 0 R /Resources << /XObject << /Im0 4 0 R >> >> /MediaBox [0 0 $pageWidth $pageHeight] /Contents 5 0 R >>\nendobj\n";
        
        // Object 4: XObject (image as SVG)
        $svgData = gzcompress($svg);
        $svgLength = strlen($svgData);
        $pdf .= "4 0 obj\n<< /Type /XObject /Subtype /Image /Width $size /Height $size /ColorSpace /DeviceRGB /Filter /FlateDecode /Length $svgLength >>\nstream\n$svgData\nendstream\nendobj\n";
        
        // Object 5: Content Stream
        $imageX = ($pageWidth - $size) / 2;
        $imageY = ($pageHeight - $size) / 2;
        $contentStream = "q\n$size 0 0 $size $imageX $imageY cm\n/Im0 Do\nQ\n";
        
        // Add text content below the QR code
        $textY = $imageY - 20;
        $contentStream .= "BT\n/F1 10 Tf\n" . ($pageWidth / 2 - 100) . " $textY Td\n(QR Code for: " . addslashes($content) . ") Tj\nET\n";
        
        $contentStreamData = gzcompress($contentStream);
        $contentStreamLength = strlen($contentStreamData);
        $pdf .= "5 0 obj\n<< /Length $contentStreamLength /Filter /FlateDecode >>\nstream\n$contentStreamData\nendstream\nendobj\n";
        
        // Cross-reference table
        $startxref = strlen($pdf);
        $pdf .= "xref\n0 6\n0000000000 65535 f \n";
        
        $offsets = [];
        $lines = explode("\n", $pdf);
        $offset = 0;
        
        foreach ($lines as $i => $line) {
            if (preg_match('/^(\d+)\s+0\s+obj/', $line, $matches)) {
                $offsets[$matches[1]] = $offset;
            }
            $offset += strlen($line) + 1; // +1 for the newline
        }
        
        foreach (range(1, 5) as $i) {
            $pdf .= sprintf("%010d 00000 n \n", $offsets[$i]);
        }
        
        // Trailer
        $pdf .= "trailer\n<< /Size 6 /Root 1 0 R >>\nstartxref\n$startxref\n%%EOF";
        
        return $pdf;
    }
} 