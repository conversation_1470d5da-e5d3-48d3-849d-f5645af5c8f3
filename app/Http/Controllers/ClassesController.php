<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Symfony\Component\HttpFoundation\Response;

class ClassesController extends Controller
{
    public function index()
    {
        $colorsScssPath = resource_path('sass/_colors.scss');
        $fontWeightsScssPath = resource_path('sass/_fontWeights.scss');
        
        $colorsContent = File::exists($colorsScssPath) ? File::get($colorsScssPath) : '// File not found';
        $fontWeightsContent = File::exists($fontWeightsScssPath) ? File::get($fontWeightsScssPath) : '// File not found';
        
        return view('main.classes', [
            'colorsContent' => $colorsContent,
            'fontWeightsContent' => $fontWeightsContent
        ]);
    }

    public function downloadScss(Request $request)
    {
        $filename = $request->get('file', '_colors.scss');
        $allowedFiles = ['_colors.scss', '_fontWeights.scss'];

        if (!in_array($filename, $allowedFiles)) {
            abort(404, 'SCSS file not found');
        }

        $scssPath = resource_path('sass/' . $filename);
        
        if (!File::exists($scssPath)) {
            abort(404, 'SCSS file not found');
        }

        return response()->download($scssPath, $filename, [
            'Content-Type' => 'text/x-scss',
        ]);
    }
}
