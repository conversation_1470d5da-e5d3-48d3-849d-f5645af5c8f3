<?php

namespace App\Http\Controllers;

use App\Console\Commands\ParseFontScssFiles;
use App\Models\Font;
use App\Models\FontIcon;
use App\Models\Project;
use App\Models\User;
use App\Models\Activity;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\View\View;
use Illuminate\Support\Facades\View as ViewFacade;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use App\Models\Icon;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Middleware will be applied in routes file
    }

    /**
     * Show the admin dashboard.
     *
     * @return View
     */
    public function index(): View
    {
        try {
            // Get admin and developer user logins
            $adminUserLogins = config('vermont.admin_user_logins', []);
            $developerUserLogins = config('vermont.developer_user_logins', []);
            $devUserLogins = config('vermont.dev_user_logins', []);
            $pmUserLogins = config('vermont.pm_user_logins', []);
            $tibiUserLogins = config('vermont.tibi_user_logins', []);
            
            // Get all fonts
            $fonts = Font::select('*')
                ->orderBy('order_nr')
                ->get();
                
            // Calculate new users count
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            // Calculate fonts that need updates for the badge
            $needsUpdateCount = 0;
            foreach ($fonts as $font) {
                if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                    $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                    
                    // Check if file is newer than the font's last update
                    if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                        $needsUpdateCount++;
                    }
                }
            }
            
            // Calculate icon stats
            $iconStats = [
                'totalIcons' => 0,
                'totalIconsPerFont' => []
            ];
            
            foreach ($fonts as $font) {
                $iconCount = !empty($font->icons_data) ? count($font->icons_data) : 0;
                $iconStats['totalIcons'] += $iconCount;
                $iconStats['totalIconsPerFont'][$font->name] = $iconCount;
            }
            
            // Count all users
            $usersCount = User::count();
            
            // Count recent admin actions
            $adminActionCount = 3; // Just a placeholder
            
            // Count projects
            $projectsCount = Project::count();
            
            // Get most active users
            $activeUsers = User::whereNotNull('last_login_at')
                ->orderBy('last_login_at', 'desc')
                ->limit(5)
                ->get();
            
            // Calculate tags count - adjust this query based on your database structure
            try {
                // If your tags are stored as JSON in the icons table
                $totalTagsCount = DB::table('icons')
                    ->whereNotNull('tags')
                    ->where('tags', '<>', '[]')
                    ->count();
                
                // If the above doesn't work, you might need a different approach
                // based on your actual database structure
            } catch (\Exception $e) {
                // Fallback in case the query fails
                $totalTagsCount = 0;
            }

            // Correctly calculate tags from icons_data
            $totalTagsCount = 0;
            $fonts->each(function($font) use (&$totalTagsCount) {
                // Use icons_data property instead of icons relationship
                if (isset($font->icons_data) && is_array($font->icons_data)) {
                    foreach ($font->icons_data as $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            $totalTagsCount += count($icon['tags']);
                        }
                    }
                }
            });

            return view('admin.index', compact(
                'fonts',
                'newUsersCount',
                'needsUpdateCount',
                'adminActionCount',
                'iconStats',
                'projectsCount',
                'usersCount',
                'activeUsers',
                'totalTagsCount'
            ));
        } catch (\Exception $e) {
            \Log::error("Error in admin index: " . $e->getMessage());
            return view('admin.index', [
                'error' => $e->getMessage(),
                'fonts' => [],
                'newUsersCount' => 0,
                'needsUpdateCount' => 0,
                'adminActionCount' => 0,
                'iconStats' => ['totalIcons' => 0, 'totalIconsPerFont' => []],
                'projectsCount' => 0,
                'usersCount' => 0,
                'activeUsers' => [],
                'totalTagsCount' => 0
            ]);
        }
    }

    public function updateFontIcons(Font $font): RedirectResponse
    {
        $exitCode = Artisan::call(ParseFontScssFiles::class, [
            '--font' => $font->id
        ]);

        request()->session()->flash('updateFontIconsCommandOutput', Artisan::output());

        return to_route('admin.icons');
    }

    public function runFontSeeder(): RedirectResponse
    {
        $exitCode = Artisan::call('db:seed', [
            '--class' => 'FontSeeder',
            '--force' => true
        ]);

        request()->session()->flash('updateFontIconsCommandOutput', Artisan::output());

        return to_route('admin.icons');
    }

    public function svgAnimation(): View
    {
        return view('admin.svg-animation');
    }

    /**
     * Display the admin users page
     *
     * @return View
     */
    public function users(): View
    {
        try {
            // Get all users
            $users = User::orderBy('last_login_at', 'desc')->get();
            
            // Get all fonts for the select input
            $fonts = Font::orderBy('name')->get();
            
            // Get all projects for the select input
            $projects = Project::orderBy('name')->get();
            
            // Calculate new users count for the badge
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            // Get admin and developer user logins
            $adminUserLogins = config('vermont.admin_user_logins', []);
            $developerUserLogins = config('vermont.developer_user_logins', []);
            $devUserLogins = config('vermont.dev_user_logins', []);
            $pmUserLogins = config('vermont.pm_user_logins', []);
            
            return view('admin.users', compact(
                'users', 
                'fonts', 
                'projects', 
                'newUsersCount',
                'adminUserLogins',
                'developerUserLogins',
                'devUserLogins',
                'pmUserLogins'
            ));
        } catch (\Exception $e) {
            \Log::error("Error in admin users method: " . $e->getMessage());
            return view('admin.users', [
                'error' => $e->getMessage(),
                'users' => [],
                'fonts' => [],
                'projects' => [],
                'newUsersCount' => 0,
                'adminUserLogins' => [],
                'developerUserLogins' => [],
                'devUserLogins' => [],
                'pmUserLogins' => []
            ]);
        }
    }

    /**
     * Show user detail page.
     */
    public function userDetail(User $user): View
    {
        try {
            // Check if the current user is an admin
            if (!in_array(auth()->user()->login, config('vermont.admin_user_logins'))) {
                abort(403, 'Unauthorized action.');
            }

            // Calculate new users count for the badge
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            // Calculate fonts that need updates for the badge - use a simpler query
            // that works across different database drivers
            $fonts = Font::all();
            $needsUpdateCount = 0;
            
            foreach ($fonts as $font) {
                if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                    $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                    
                    // Check if file is newer than the font's last update
                    if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                        $needsUpdateCount++;
                    }
                }
            }

            // Get available projects that are not already assigned to the user
            $availableProjects = Project::whereNotIn('id', $user->projects->pluck('id'))->get();
            
            // Get available fonts that are not already assigned to the user
            $availableFonts = Font::whereNotIn('id', $user->fonts->pluck('id'))->get();

            return view('admin.user-detail', compact('user', 'needsUpdateCount', 'newUsersCount', 'availableProjects', 'availableFonts'));
        } catch (\Exception $e) {
            \Log::error("Error in admin user detail: " . $e->getMessage());
            return view('admin.user-detail', [
                'error' => $e->getMessage(),
                'user' => $user,
                'needsUpdateCount' => 0,
                'newUsersCount' => 0,
                'availableProjects' => collect([]),
                'availableFonts' => collect([])
            ]);
        }
    }

    /**
     * Toggle user active status.
     */
    public function toggleUserStatus(User $user): RedirectResponse
    {
        // Check if the current user is an admin
        if (!in_array(auth()->user()->login, config('vermont.admin_user_logins'))) {
            abort(403, 'Unauthorized action.');
        }

        // Don't allow deactivating yourself
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users')->with('error', 'Nemôžete deaktivovať svoj vlastný účet.');
        }

        $user->active = !$user->active;
        $user->save();

        $status = $user->active ? 'aktivovaný' : 'deaktivovaný';
        return redirect()->route('admin.users')->with('success', "Používateľ {$user->login} bol úspešne {$status}.");
    }

    /**
     * Delete a user from the database.
     */
    public function deleteUser(User $user): RedirectResponse
    {
        // Check if the current user is an admin
        if (!in_array(auth()->user()->login, config('vermont.admin_user_logins'))) {
            abort(403, 'Unauthorized action.');
        }

        // Don't allow deleting yourself
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users')->with('error', 'Nemôžete vymazať svoj vlastný účet.');
        }

        // Get user login for the success message
        $userLogin = $user->login;

        // Remove user from all role arrays
        $adminLogins = config('vermont.admin_user_logins');
        $developerLogins = config('vermont.developer_user_logins');
        $devLogins = config('vermont.dev_user_logins');
        $tibiLogins = config('vermont.tibi_user_logins');
        $pmLogins = config('vermont.pm_user_logins');

        $adminLogins = array_diff($adminLogins, [$user->login]);
        $developerLogins = array_diff($developerLogins, [$user->login]);
        $devLogins = array_diff($devLogins, [$user->login]);
        $tibiLogins = array_diff($tibiLogins, [$user->login]);
        $pmLogins = array_diff($pmLogins, [$user->login]);

        // Update the config files
        $this->updateConfigFile('vermont.admin_user_logins', $adminLogins);
        $this->updateConfigFile('vermont.developer_user_logins', $developerLogins);
        $this->updateConfigFile('vermont.dev_user_logins', $devLogins);
        $this->updateConfigFile('vermont.tibi_user_logins', $tibiLogins);
        $this->updateConfigFile('vermont.pm_user_logins', $pmLogins);

        // Delete the user
        $user->delete();

        return redirect()->route('admin.users')->with('success', "Používateľ {$userLogin} bol úspešne vymazaný.");
    }

    /**
     * Update user role.
     */
    public function updateUserRole(User $user, Request $request): RedirectResponse
    {
        // Check if the current user is an admin
        if (!in_array(auth()->user()->login, config('vermont.admin_user_logins'))) {
            abort(403, 'Unauthorized action.');
        }

        // Don't allow changing your own role if you're an admin
        if ($user->id === auth()->id() && in_array($user->login, config('vermont.admin_user_logins')) && $request->role !== 'admin') {
            return redirect()->route('admin.users')->with('error', 'Nemôžete zmeniť svoju vlastnú rolu z admin.');
        }

        $newRole = $request->role;
        $validRoles = ['user', 'admin', 'developer', 'dev', 'tibi', 'pm'];

        if (!in_array($newRole, $validRoles)) {
            return redirect()->route('admin.users')->with('error', 'Neplatná rola.');
        }

        // Get all role arrays
        $adminLogins = config('vermont.admin_user_logins');
        $developerLogins = config('vermont.developer_user_logins');
        $devLogins = config('vermont.dev_user_logins');
        $tibiLogins = config('vermont.tibi_user_logins');
        $pmLogins = config('vermont.pm_user_logins');

        // Remove user from all role arrays
        $adminLogins = array_diff($adminLogins, [$user->login]);
        $developerLogins = array_diff($developerLogins, [$user->login]);
        $devLogins = array_diff($devLogins, [$user->login]);
        $tibiLogins = array_diff($tibiLogins, [$user->login]);
        $pmLogins = array_diff($pmLogins, [$user->login]);

        // Add user to the appropriate role array
        if ($newRole === 'admin') {
            $adminLogins[] = $user->login;
        } elseif ($newRole === 'developer') {
            $developerLogins[] = $user->login;
        } elseif ($newRole === 'dev') {
            $devLogins[] = $user->login;
        } elseif ($newRole === 'tibi') {
            $tibiLogins[] = $user->login;
        } elseif ($newRole === 'pm') {
            $pmLogins[] = $user->login;
        }
        // If role is 'user', don't add to any array

        // Update the config files
        $this->updateConfigFile('vermont.admin_user_logins', $adminLogins);
        $this->updateConfigFile('vermont.developer_user_logins', $developerLogins);
        $this->updateConfigFile('vermont.dev_user_logins', $devLogins);
        $this->updateConfigFile('vermont.tibi_user_logins', $tibiLogins);
        $this->updateConfigFile('vermont.pm_user_logins', $pmLogins);

        return redirect()->route('admin.users')->with('success', "Rola používateľa {$user->login} bola úspešne zmenená na {$newRole}.");
    }

    /**
     * Update custom greetings for a user
     *
     * @param User $user
     * @param Request $request
     * @return RedirectResponse
     */
    public function updateUserGreetings(User $user, Request $request): RedirectResponse
    {
        try {
            $greetings = json_decode($request->input('greetings'), true);
            
            // Validate each greeting
            if (is_array($greetings)) {
                foreach ($greetings as $greeting) {
                    if (!is_string($greeting) || strlen($greeting) > 100) {
                        return back()->with('error', 'Neplatný formát pozdravu. Maximálna dĺžka je 100 znakov.');
                    }
                }
            } else {
                $greetings = [];
            }
            
            // Update user with custom greetings
            $user->custom_greetings = $greetings;
            $user->save();
            
            return back()->with('success', 'Vlastné pozdravy boli úspešne aktualizované.');
        } catch (\Exception $e) {
            return back()->with('error', 'Nastala chyba pri ukladaní pozdravov: ' . $e->getMessage());
        }
    }

    /**
     * Update user email
     * 
     * @param User $user
     * @param Request $request
     * @return RedirectResponse
     */
    public function updateUserEmail(User $user, Request $request): RedirectResponse
    {
        try {
            $request->validate([
                'email' => 'required|email|max:255',
            ]);
            
            $user->email = $request->input('email');
            $user->save();
            
            return back()->with('success', 'Email používateľa bol úspešne aktualizovaný.');
        } catch (\Exception $e) {
            return back()->with('error', 'Nastala chyba pri aktualizácii emailu: ' . $e->getMessage());
        }
    }

    /**
     * Update user birthday
     * 
     * @param User $user
     * @param Request $request
     * @return RedirectResponse
     */
    public function updateUserBirthday(User $user, Request $request): RedirectResponse
    {
        try {
            $request->validate([
                'birthday' => 'nullable|date',
            ]);
            
            $user->birthday = $request->input('birthday');
            $user->save();
            
            return back()->with('success', 'Dátum narodenia používateľa bol úspešne aktualizovaný.');
        } catch (\Exception $e) {
            return back()->with('error', 'Nastala chyba pri aktualizácii dátumu narodenia: ' . $e->getMessage());
        }
    }

    /**
     * Update the config file with new values.
     */
    private function updateConfigFile(string $configKey, array $values): void
    {
        // This is a simplified approach. In a production environment, you might want to use
        // a more robust solution for updating config files or store these settings in the database.
        
        // For now, we'll just update the runtime config
        Config::set($configKey, $values);
        
        // Note: This change will only persist for the current request.
        // In a real application, you would need to update the actual config file or use a database.
    }

    /**
     * Display the admin typography page
     */
    public function typography()
    {
        return view('admin.typography');
    }

    /**
     * Display the admin assets page
     */
    public function assets()
    {
        return view('admin.assets');
    }

    /**
     * Display the admin tools page
     */
    public function tools()
    {
        return view('admin.tools');
    }

    /**
     * Display the admin code page
     */
    public function code()
    {
        return view('admin.code');
    }

    /**
     * Display the admin animations page
     */
    public function animations()
    {
        return view('admin.animations');
    }

    /**
     * Show the icons page.
     *
     * @return View
     */
    public function icons()
    {
        try {
            $fonts = Font::select('*')->orderBy('order_nr')->get();

            // Transform data to get the counts
            foreach ($fonts as $font) {
                // Set icon count
                $font->icons_count = is_array($font->icons_data) ? count($font->icons_data) : 0;
                
                // Calculate tag count for this font
                $font->tags_count = 0;
                if (isset($font->icons_data) && is_array($font->icons_data)) {
                    foreach ($font->icons_data as $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            $font->tags_count += count($icon['tags']);
                        }
                    }
                }
                
                // Check if the font file exists and needs an update
                $font->needs_update = false;
                if (file_exists(resource_path('sass/'.$font->path ?? ''))) {
                    $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                    
                    // Check if update is needed
                    try {
                        if (isset($font->updated_at)) {
                            $font->needs_update = $fileTimestamp > $font->updated_at->timestamp;
                        } else {
                            $font->needs_update = true; // No update timestamp means update needed
                        }
                    } catch (\Exception $e) {
                        \Log::error("Error checking font update status: " . $e->getMessage());
                        $font->needs_update = true; // Error means assume update needed
                    }
                }
            }
            
            return view('admin.icons', compact('fonts'));
        } catch (\Exception $e) {
            \Log::error("Error in admin icons method: " . $e->getMessage());
            return view('admin.icons', [
                'error' => $e->getMessage(),
                'fonts' => []
            ]);
        }
    }

    /**
     * Show the font icon management page.
     *
     * @param Font $font
     * @return View
     */
    public function fontIconManagement(Font $font)
    {
        try {
            // Get icon count
            $font->icons_count = is_array($font->icons_data) ? count($font->icons_data) : 0;
            
            // Check if the font file exists and needs an update
            $needsUpdate = false;
            if (file_exists(resource_path('sass/'.$font->path ?? ''))) {
                $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                
                // Check if update is needed
                if (isset($font->updated_at)) {
                    $needsUpdate = $fileTimestamp > $font->updated_at->timestamp;
                } else {
                    $needsUpdate = true; // No update timestamp means update needed
                }
            }
            
            // Prepare icons for the Vue component
            $icons = [];
            $fontIcons = is_array($font->icons_data) ? $font->icons_data : [];
            
            foreach ($fontIcons as $icon) {
                if (isset($icon['css_class'])) {
                    $icons[] = [
                        'id' => (string)($icon['css_class'] ?? ''),
                        'css_class' => (string)($icon['css_class'] ?? ''),
                        'css_content' => (string)($icon['css_content'] ?? ''),
                        'tags' => isset($icon['tags']) && is_array($icon['tags']) ? array_values(array_map('strval', $icon['tags'])) : [],
                    ];
                }
            }
            
            // Pass variables to the view
            return view('admin.font-icons-management', [
                'font' => $font,
                'icons' => $icons,
                'needsUpdate' => $needsUpdate,
                'needsUpdateCount' => $needsUpdate ? 1 : 0,
                'newUsersCount' => 0
            ]);
        } catch (\Exception $e) {
            \Log::error("Error in fontIconManagement method: " . $e->getMessage());
            return redirect()->route('admin.icons')
                ->with('error', 'Error loading font icon management: ' . $e->getMessage());
        }
    }

    /**
     * Show the activities page.
     *
     * @return View
     */
    public function activities(): View
    {
        try {
            // Calculate new users count 
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            // Use a simpler query that's more compatible with different DB drivers
            $fonts = Font::select('*')->orderBy('order_nr')->get();
            
            // Calculate fonts that need updates - simplified version
            $needsUpdateCount = 0;
            foreach ($fonts as $font) {
                if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                    $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                    
                    // Check if file is newer than the font's last update
                    if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                        $needsUpdateCount++;
                    }
                }
            }
    
            // Calculate total activities
            $totalActivities = $newUsersCount + $needsUpdateCount;
            
            // Add 1 for icon updates if any fonts exist
            if (count($fonts) > 0) {
                $totalActivities++;
            }
    
            return view('admin.activities', compact('fonts', 'newUsersCount', 'needsUpdateCount', 'totalActivities'));
        } catch (\Exception $e) {
            \Log::error("Error in admin activities: " . $e->getMessage());
            return view('admin.activities', [
                'error' => $e->getMessage(),
                'fonts' => collect([]),
                'newUsersCount' => 0,
                'needsUpdateCount' => 0,
                'totalActivities' => 0
            ]);
        }
    }

    /**
     * Show the tag management page.
     *
     * @return \Illuminate\View\View
     */
    public function tagManager()
    {
        return view('admin.tag-manager');
    }

    /**
     * Initialize tags for all fonts
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function initializeFontTags()
    {
        try {
            // Run the command to initialize font tags
            $output = [];
            \Artisan::call('fonts:initialize-tags', [], $output);
            
            // Get the output from the command
            $commandOutput = \Artisan::output();
            
            return redirect()->route('admin.icons')
                ->with('success', 'Font tags have been initialized successfully!')
                ->with('initializeFontTagsOutput', $commandOutput);
        } catch (\Exception $e) {
            \Log::error("Error initializing font tags: " . $e->getMessage());
            return redirect()->route('admin.icons')
                ->with('error', 'An error occurred while initializing font tags: ' . $e->getMessage());
        }
    }
}
