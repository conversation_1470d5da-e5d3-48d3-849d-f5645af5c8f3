<?php

namespace App\Http\Controllers;

use App\Models\Font;
use App\Models\UserFontView;
use Illuminate\Support\Facades\Auth;

class UserFontViewController extends Controller
{
    public function updateView(Font $font)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        UserFontView::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'font_id' => $font->id,
            ],
            [
                'last_icons_count' => $font->icons_count,
                'last_viewed_at' => now(),
            ]
        );

        return response()->json(['success' => true]);
    }
}
