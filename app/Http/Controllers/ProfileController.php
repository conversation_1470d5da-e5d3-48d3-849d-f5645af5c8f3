<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    /**
     * Display the profile page with eager loaded relationships
     */
    public function index()
    {
        // Eager load the user with their projects and fonts to optimize performance
        $user = Auth::user()->load(['projects', 'fonts']);
        
        return view('profile.index', compact('user'));
    }

    public function update(Request $request)
    {
        // Handle JSON requests (which our Vue component sends)
        if ($request->isJson()) {
            // If updating default page
            if ($request->has('default_page')) {
                $validated = $request->validate([
                    'default_page' => ['required', 'string', 'in:projectassets,profile,tools'],
                ]);

                auth()->user()->update([
                    'default_page' => $validated['default_page']
                ]);
                
                return response()->json([
                    'message' => 'Predvolená stránka bola úspešne aktualizovaná',
                    'user' => auth()->user()
                ]);
            }
            
            // Handle other JSON requests...
        }
        
        // Get the authenticated user for the rest of the method
        $user = auth()->user();
        
        // Validate the request first
        $validated = $request->validate([
            'email' => ['sometimes', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'default_page' => ['sometimes', 'string', 'in:projectassets,profile,tools'],
            // Add other fields you're updating
        ]);
        
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            $path = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $path;
        }

        $user->update($validated);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user
        ]);
    }
} 