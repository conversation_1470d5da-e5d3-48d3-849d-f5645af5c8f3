<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class DatabaseController extends Controller
{
    private function getOrCreateValidDatabasePath()
    {
        // Get current environment connection info
        $connectionName = config('database.default');
        $currentDriver = config("database.connections.{$connectionName}.driver");
        
        // Log connection info
        \Illuminate\Support\Facades\Log::info("Database connection: $connectionName, Driver: $currentDriver");
        
        // Ensure using SQLite
        if ($currentDriver !== 'sqlite') {
            \Illuminate\Support\Facades\Log::info("Switching to SQLite driver from $currentDriver");
            config(['database.default' => 'sqlite']);
        }
        
        // Get the current database path configuration
        $configuredPath = config('database.connections.sqlite.database');
        \Illuminate\Support\Facades\Log::info("SQLite database path from config: $configuredPath");
        
        // Check if the path looks valid
        if ($configuredPath == ':memory:') {
            \Illuminate\Support\Facades\Log::info("Memory database is configured, will use a file-based database instead");
            $configuredPath = database_path('database.sqlite');
        }
        
        // List of locations to check for existing database or create a new one
        $potentialPaths = [
            $configuredPath,
            database_path('database.sqlite'),
            base_path('database.sqlite'),
            storage_path('app/database.sqlite'),
        ];
        
        $dbPath = null;
        
        // First try to find an existing database file
        foreach ($potentialPaths as $path) {
            if (file_exists($path)) {
                $dbPath = $path;
                \Illuminate\Support\Facades\Log::info("Found existing SQLite database at: $path");
                break;
            }
        }
        
        // If no database found, create one in the database directory
        if (!$dbPath) {
            $dbPath = database_path('database.sqlite');
            $directory = dirname($dbPath);
            
            // Ensure directory exists
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
                \Illuminate\Support\Facades\Log::info("Created directory for SQLite database at: $directory");
            }
            
            // Create empty database file
            touch($dbPath);
            chmod($dbPath, 0664);
            \Illuminate\Support\Facades\Log::info("Created new SQLite database at: $dbPath");
        }
        
        // Update configuration to use this path
        config(['database.connections.sqlite.database' => $dbPath]);
        
        // Test connection
        try {
            \DB::disconnect();
            $pdo = \DB::connection('sqlite')->getPdo();
            \Illuminate\Support\Facades\Log::info("Successfully connected to SQLite database at: $dbPath");
            return $dbPath;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Failed to connect to SQLite database: " . $e->getMessage());
            throw new \Exception("Failed to connect to SQLite database: " . $e->getMessage());
        }
    }

    public function export()
    {
        try {
            // Get or create a valid database path
            $dbPath = $this->getOrCreateValidDatabasePath();
            
            $timestamp = date('Y-m-d_H-i-s');
            $backupPath = storage_path("app/backups/database_{$timestamp}.sqlite");
            
            // Ensure backup directory exists
            if (!file_exists(storage_path('app/backups'))) {
                mkdir(storage_path('app/backups'), 0755, true);
            }

            // Check if important tables exist and have data
            $tables = \DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            $tableNames = array_map(function($table) { return $table->name; }, $tables);
            
            // Log table counts for important tables
            $tableCounts = [];
            foreach ($tableNames as $tableName) {
                try {
                    $count = \DB::table($tableName)->count();
                    $tableCounts[$tableName] = $count;
                } catch (\Exception $e) {
                    $tableCounts[$tableName] = "Error: " . $e->getMessage();
                }
            }
            
            \Illuminate\Support\Facades\Log::info("Database export: exporting tables: " . print_r($tableCounts, true));
            
            // Disconnect from the database to ensure all writes are completed
            \DB::disconnect();
            
            // Copy the database file
            if (!copy($dbPath, $backupPath)) {
                throw new \Exception("Failed to create backup at {$backupPath}");
            }
            
            // Ensure proper permissions
            chmod($backupPath, 0664);
            
            // Reconnect to the database
            \DB::reconnect();
            
            // Log the export
            \Illuminate\Support\Facades\Log::info("Database exported successfully to {$backupPath}");
            
            // Set proper headers for SQLite database download
            $headers = [
                'Content-Type' => 'application/x-sqlite3',
                'Content-Disposition' => 'attachment; filename="database_backup_' . $timestamp . '.sqlite"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ];

            return response()->download($backupPath, "database_backup_{$timestamp}.sqlite", $headers)->deleteFileAfterSend();
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Database export failed: ' . $e->getMessage());
            return redirect()->route('admin.index')
                ->with('error', 'Failed to export database: ' . $e->getMessage());
        }
    }

    public function import(Request $request)
    {
        try {
            // Get or create a valid database path
            $dbPath = $this->getOrCreateValidDatabasePath();
            
            $request->validate([
                'database' => 'required|file|max:10240', // Max 10MB, removed mimes validation
            ]);

            // Store the uploaded file with a specific name
            $uploadedFile = $request->file('database');
            $fileName = 'imported_' . time() . '.sqlite';
            $importPath = storage_path('app/imports/' . $fileName);
            
            // Ensure imports directory exists
            if (!file_exists(storage_path('app/imports'))) {
                mkdir(storage_path('app/imports'), 0755, true);
            }
            
            // Move the uploaded file to a known location
            $uploadedFile->move(storage_path('app/imports'), $fileName);
            
            // Ensure the file is readable
            chmod($importPath, 0664);
            
            // Backup current database
            $backupPath = storage_path('app/backups/database_backup_' . date('Y-m-d_H-i-s') . '.sqlite');
            
            // Ensure backup directory exists
            if (!file_exists(storage_path('app/backups'))) {
                mkdir(storage_path('app/backups'), 0755, true);
            }
            
            // Backup current database
            copy($dbPath, $backupPath);
            
            // Close any open database connections
            \DB::disconnect();

            // Replace current database with uploaded one
            if (!copy($importPath, $dbPath)) {
                throw new \Exception("Failed to copy imported database to {$dbPath}. Please check file permissions.");
            }
            
            // Ensure correct permissions on the new database file
            chmod($dbPath, 0664);
            
            // Clear cached config and views
            \Illuminate\Support\Facades\Artisan::call('config:clear');
            \Illuminate\Support\Facades\Artisan::call('view:clear');
            \Illuminate\Support\Facades\Artisan::call('cache:clear');
            
            // Reconnect to the database
            \DB::reconnect();
            
            // Try to check the database content
            try {
                // Get all tables
                $tables = \DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
                $tableNames = array_map(function($table) { return $table->name; }, $tables);
                
                // Check table counts for important tables
                $tableCounts = [];
                foreach ($tableNames as $tableName) {
                    try {
                        $count = \DB::table($tableName)->count();
                        $tableCounts[$tableName] = $count;
                    } catch (\Exception $e) {
                        $tableCounts[$tableName] = "Error: " . $e->getMessage();
                    }
                }
                
                \Illuminate\Support\Facades\Log::info("Database import successful. Tables: " . print_r($tableCounts, true));
                
                // Check if this is an AJAX request
                if ($request->ajax()) {
                    return response()->json([
                        'message' => 'Database imported successfully',
                        'tables' => $tableNames,
                        'table_counts' => $tableCounts,
                        'import_path' => $importPath,
                        'database_path' => $dbPath
                    ]);
                } else {
                    // This is a regular form submission, redirect back with success message
                    return redirect()->route('admin.index')
                        ->with('success', "Database imported successfully. Found " . count($tableNames) . " tables with data.");
                }
            } catch (\Exception $dbException) {
                // If we can't check tables, log the error but don't try to fix it
                \Illuminate\Support\Facades\Log::error("Error checking tables: " . $dbException->getMessage());
                
                return redirect()->route('admin.index')
                    ->with('warning', "Database imported but couldn't verify content: " . $dbException->getMessage());
            }
        } catch (\Exception $e) {
            // Add detailed error information
            $errorDetail = $e->getMessage();
            
            \Illuminate\Support\Facades\Log::error('Database import failed: ' . $errorDetail);
            
            // Check if this is an AJAX request
            if ($request->ajax()) {
                return response()->json(['error' => 'Failed to import database: ' . $errorDetail], 500);
            } else {
                // This is a regular form submission, redirect back with error message
                return redirect()->route('admin.index')
                    ->with('error', 'Failed to import database: ' . $errorDetail);
            }
        }
    }
} 