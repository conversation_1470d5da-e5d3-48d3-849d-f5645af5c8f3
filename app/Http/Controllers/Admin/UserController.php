<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Font;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Update custom greetings for a user
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\User $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateGreetings(Request $request, User $user)
    {
        try {
            $greetings = json_decode($request->input('greetings'), true);
            
            // Validate each greeting
            if (is_array($greetings)) {
                foreach ($greetings as $greeting) {
                    if (!is_string($greeting) || strlen($greeting) > 100) {
                        return back()->with('error', 'Neplatný formát pozdravu. Maximálna dĺžka je 100 znakov.');
                    }
                }
            } else {
                $greetings = [];
            }
            
            // Update user with custom greetings
            $user->custom_greetings = $greetings;
            $user->save();
            
            return back()->with('success', 'Vlastné pozdravy boli úspešne aktualizované.');
        } catch (\Exception $e) {
            return back()->with('error', 'Nastala chyba pri ukladaní pozdravov: ' . $e->getMessage());
        }
    }

    /**
     * Add a project to a user
     */
    public function addProject(Request $request, User $user)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
        ]);

        // Check if the user already has this project
        if (!$user->projects->contains($validated['project_id'])) {
            $user->projects()->attach($validated['project_id'], ['role' => 'member']);
            return back()->with('success', 'Projekt bol úspešne priradený používateľovi.');
        }

        return back()->with('error', 'Používateľ už má tento projekt priradený.');
    }

    /**
     * Remove a project from a user
     */
    public function removeProject(User $user, Project $project)
    {
        $user->projects()->detach($project->id);
        return back()->with('success', 'Projekt bol úspešne odobratý od používateľa.');
    }

    /**
     * Add a font to a user
     */
    public function addFont(Request $request, User $user)
    {
        $validated = $request->validate([
            'font_id' => 'required|exists:fonts,id',
        ]);

        // Check if the user already has this font
        if (!$user->fonts->contains($validated['font_id'])) {
            $user->fonts()->attach($validated['font_id']);
            return back()->with('success', 'Font bol úspešne priradený používateľovi.');
        }

        return back()->with('error', 'Používateľ už má tento font priradený.');
    }

    /**
     * Remove a font from a user
     */
    public function removeFont(User $user, Font $font)
    {
        $user->fonts()->detach($font->id);
        return back()->with('success', 'Font bol úspešne odobratý od používateľa.');
    }
} 