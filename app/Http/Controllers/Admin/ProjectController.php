<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Font;
use App\Models\Project;
use App\Models\ProjectStatus;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index()
    {
        $projects = Project::with(['users', 'fonts', 'projectStatus'])->get();
        $fonts = Font::orderBy('name')->get();
        $users = User::orderBy('name')->get();
        $statuses = ProjectStatus::where('is_active', true)->orderBy('sort_order')->get();
        $needsUpdateCount = config('vermont.needs_update_count', 0);
        $newUsersCount = config('vermont.new_users_count', 0);

        return view('admin.projects', compact('projects', 'fonts', 'users', 'statuses', 'needsUpdateCount', 'newUsersCount'));
    }

    /**
     * Show the form for creating a new project
     */
    public function create(): View
    {
        $fonts = Font::orderBy('name')->get();
        $users = User::orderBy('name')->get();
        $statuses = ProjectStatus::where('is_active', true)->orderBy('sort_order')->get();
        $needsUpdateCount = config('vermont.needs_update_count', 0);
        $newUsersCount = config('vermont.new_users_count', 0);

        return view('admin.projects.create', compact('fonts', 'users', 'statuses', 'needsUpdateCount', 'newUsersCount'));
    }

    /**
     * Store a newly created project
     */
    public function store(Request $request)
    {
        // Get all the active status slugs for validation
        $validStatuses = ProjectStatus::where('is_active', true)->pluck('slug')->toArray();
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'backend' => 'nullable|string|max:255',
            'frontend' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|string|in:' . implode(',', $validStatuses),
            'font_ids' => 'nullable|array',
            'font_ids.*' => 'exists:fonts,id',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $project = Project::create($request->only([
            'name', 'description', 'backend', 'frontend', 'start_date', 'end_date', 'status'
        ]));

        // Attach fonts if provided
        if ($request->has('font_ids')) {
            $project->fonts()->attach($request->font_ids);
        }

        // Attach users if provided
        if ($request->has('user_ids')) {
            $userRoles = [];
            foreach ($request->user_ids as $userId) {
                $userRoles[$userId] = ['role' => 'member'];
            }
            $project->users()->attach($userRoles);
        }

        return redirect()->route('admin.projects')->with('success', 'Projekt bol úspešne vytvorený.');
    }

    /**
     * Update the specified project
     */
    public function update(Request $request, Project $project)
    {
        // Debug the request data
        \Log::debug('Project update request data:', [
            'all' => $request->all(),
            'only' => $request->only(['name', 'description', 'backend', 'frontend', 'start_date', 'end_date', 'status']),
            'has_backend' => $request->has('backend'),
            'backend' => $request->input('backend'),
            'has_frontend' => $request->has('frontend'),
            'frontend' => $request->input('frontend'),
        ]);
        
        // Get all the active status slugs for validation
        $validStatuses = ProjectStatus::where('is_active', true)->pluck('slug')->toArray();
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'backend' => 'nullable|string|max:255',
            'frontend' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|string|in:' . implode(',', $validStatuses),
            'font_ids' => 'nullable|array',
            'font_ids.*' => 'exists:fonts,id',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $project->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'backend' => $request->input('backend'),
            'frontend' => $request->input('frontend'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'status' => $request->input('status'),
        ]);

        // Sync fonts
        if ($request->has('font_ids')) {
            $project->fonts()->sync($request->font_ids);
        } else {
            $project->fonts()->detach();
        }

        // Sync users with roles
        if ($request->has('user_ids')) {
            $userRoles = [];
            foreach ($request->user_ids as $userId) {
                $userRoles[$userId] = ['role' => 'member'];
            }
            $project->users()->sync($userRoles);
        } else {
            $project->users()->detach();
        }

        return back()->with('success', 'Projekt bol úspešne aktualizovaný.');
    }

    /**
     * Remove the specified project
     */
    public function destroy(Project $project)
    {
        $project->delete();
        return redirect()->route('admin.projects')->with('success', 'Projekt bol úspešne odstránený.');
    }

    /**
     * Display the specified project.
     *
     * @param  \App\Models\Project  $project
     * @return \Illuminate\View\View
     */
    public function show(Project $project)
    {
        // Load the projectStatus relationship
        $project->load('projectStatus');
        
        // Get all statuses, fonts, and users for the edit modal
        $statuses = ProjectStatus::all();
        $fonts = Font::orderBy('name')->get();
        $users = User::orderBy('name')->get();
        
        return view('admin.project-detail', compact('project', 'statuses', 'fonts', 'users'));
    }
} 