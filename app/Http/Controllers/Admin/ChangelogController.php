<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\User;
use App\Models\Font;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;

class ChangelogController extends Controller
{
    public function index(): View
    {
        try {
            $entries = Post::orderByDesc('release_date')
                          ->orderByDesc('order')
                          ->get();
    
            // Calculate new users count for the badge
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            // Use a simpler query that's more compatible with different DB drivers
            $fonts = Font::select('*')->orderBy('order_nr')->get();
            
            // Calculate fonts that need updates - simplified version
            $needsUpdateCount = 0;
            foreach ($fonts as $font) {
                if (!empty($font->path) && file_exists(resource_path('sass/'.$font->path))) {
                    $fileTimestamp = filemtime(resource_path('sass/'.$font->path));
                    
                    // Check if file is newer than the font's last update
                    if (isset($font->updated_at) && $fileTimestamp > $font->updated_at->timestamp) {
                        $needsUpdateCount++;
                    }
                }
            }
    
            return view('admin.changelog.index', compact('entries', 'newUsersCount', 'needsUpdateCount'));
        } catch (\Exception $e) {
            \Log::error("Error in admin changelog index: " . $e->getMessage());
            return view('admin.changelog.index', [
                'error' => $e->getMessage(),
                'entries' => collect([]),
                'newUsersCount' => 0,
                'needsUpdateCount' => 0
            ]);
        }
    }

    /**
     * Show the form for creating a new changelog entry
     */
    public function create(): View
    {
        return view('admin.changelog.create', [
            'needsUpdateCount' => 0,
            'newUsersCount' => 0
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'version' => 'required|string|max:50',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'release_date' => 'required|date',
            'changes' => 'nullable|array',
            'changes.*' => 'string',
            'is_major' => 'nullable|boolean',
            'order' => 'nullable|integer'
        ]);

        // Get the highest order number and add 1
        $maxOrder = Post::max('order') ?? -1;
        $nextOrder = $maxOrder + 1;

        try {
            $post = Post::create([
                'version' => $validated['version'],
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'release_date' => $validated['release_date'],
                'changes' => array_values(array_filter($request->input('changes', []), fn($change) => !empty(trim($change)))),
                'is_major' => $request->boolean('is_major', false),
                'order' => $validated['order'] ?? $nextOrder
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Post created successfully.',
                'entry' => $post
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save post.'
            ], 500);
        }
    }

    public function update(Request $request, Post $post)
    {
        $validated = $request->validate([
            'version' => 'required|string|max:50',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'release_date' => 'required|date',
            'changes' => 'nullable|array',
            'changes.*' => 'string',
            'is_major' => 'nullable|boolean',
            'order' => 'nullable|integer'
        ]);

        try {
            $post->update([
                'version' => $validated['version'],
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'release_date' => $validated['release_date'],
                'changes' => array_values(array_filter($request->input('changes', []), fn($change) => !empty(trim($change)))),
                'is_major' => $request->boolean('is_major', false),
                'order' => $validated['order'] ?? $post->order
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Post updated successfully.',
                'entry' => $post
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update post.'
            ], 500);
        }
    }

    public function destroy(Post $post)
    {
        try {
            $post->delete();

            return response()->json([
                'success' => true,
                'message' => 'Post deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete post.'
            ], 500);
        }
    }

    public function show(Post $post)
    {
        return response()->json($post);
    }
}
