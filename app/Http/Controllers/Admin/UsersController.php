<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UsersController extends Controller
{
    public function index()
    {
        try {
            // Only load essential data initially - pagination and eager loading
            $users = User::with(['projects:id,name', 'fonts:id,name'])
                ->latest('last_login_at')
                ->paginate(50); // Add pagination to avoid loading all users at once
                
            $needsUpdateCount = User::where('needs_update', true)->count();
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            return view('admin.users', compact('users', 'needsUpdateCount', 'newUsersCount'));
        } catch (\Exception $e) {
            return view('admin.users', ['error' => $e->getMessage()]);
        }
    }
} 