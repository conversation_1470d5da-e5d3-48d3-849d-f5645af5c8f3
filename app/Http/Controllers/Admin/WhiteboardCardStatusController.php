<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WhiteboardCardStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class WhiteboardCardStatusController extends Controller
{
    /**
     * Display a listing of the statuses.
     */
    public function index()
    {
        $statuses = WhiteboardCardStatus::orderBy('order')->get();
        return view('admin.whiteboard_statuses.index', compact('statuses'));
    }

    /**
     * Store a newly created status.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'color' => 'required|string|max:50',
            'is_default' => 'boolean',
        ]);

        // Generate a slug from the name
        $slug = Str::slug($validated['name']);
        
        // Ensure slug is unique
        $originalSlug = $slug;
        $count = 1;
        
        while (WhiteboardCardStatus::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        // If this is set as default, unset any existing default
        if ($request->has('is_default') && $request->is_default) {
            WhiteboardCardStatus::where('is_default', true)
                ->update(['is_default' => false]);
        }

        // Get the highest order number and add 1
        $maxOrder = WhiteboardCardStatus::max('order') ?? 0;
        
        WhiteboardCardStatus::create([
            'name' => $validated['name'],
            'slug' => $slug,
            'color' => $validated['color'],
            'order' => $maxOrder + 1,
            'is_default' => $request->has('is_default') ? $request->is_default : false,
            'is_system' => false,
        ]);

        return redirect()->route('admin.whiteboard-statuses.index')
            ->with('success', 'Status created successfully.');
    }

    /**
     * Update the specified status.
     */
    public function update(Request $request, $id)
    {
        $status = WhiteboardCardStatus::findOrFail($id);
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'color' => 'required|string|max:50',
            'is_default' => 'boolean',
        ]);

        // If this is set as default, unset any existing default
        if ($request->has('is_default') && $request->is_default) {
            WhiteboardCardStatus::where('is_default', true)
                ->where('id', '!=', $status->id)
                ->update(['is_default' => false]);
        }

        $status->update([
            'name' => $validated['name'],
            'color' => $validated['color'],
            'is_default' => $request->has('is_default') ? $request->is_default : false,
        ]);

        return redirect()->route('admin.whiteboard-statuses.index')
            ->with('success', 'Status updated successfully.');
    }

    /**
     * Remove the specified status.
     */
    public function destroy($id)
    {
        $status = WhiteboardCardStatus::findOrFail($id);
        
        // Prevent deletion of system statuses
        if ($status->is_system) {
            return redirect()->route('admin.whiteboard-statuses.index')
                ->with('error', 'System statuses cannot be deleted.');
        }

        // Check if the status is in use
        $cardsCount = $status->cards()->count();
        if ($cardsCount > 0) {
            return redirect()->route('admin.whiteboard-statuses.index')
                ->with('error', "Cannot delete status that is used by {$cardsCount} cards.");
        }

        $status->delete();

        return redirect()->route('admin.whiteboard-statuses.index')
            ->with('success', 'Status deleted successfully.');
    }

    /**
     * Update the order of statuses.
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'order' => 'required|array',
            'order.*' => 'integer|exists:whiteboard_card_statuses,id',
        ]);

        foreach ($request->order as $position => $statusId) {
            WhiteboardCardStatus::where('id', $statusId)
                ->update(['order' => $position + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * List all statuses for API.
     */
    public function list()
    {
        $statuses = WhiteboardCardStatus::orderBy('order')->get();
        return response()->json($statuses);
    }
}
