<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectStatus;
use App\Models\User;
use App\Models\Font;
use Illuminate\Http\Request;

class ProjectsController extends Controller
{
    public function index()
    {
        try {
            // Only load essential data initially
            $projects = Project::with([
                'fonts:id,name', 
                'users:id,name,surname,login'
            ])->paginate(50);
            
            $statuses = ProjectStatus::all();
            $users = User::select('id', 'name', 'surname', 'login')->get();
            $fonts = Font::select('id', 'name')->get();
            
            $needsUpdateCount = User::where('needs_update', true)->count();
            $newUsersCount = User::where('created_at', '>=', now()->subDays(7))->count();
            
            return view('admin.projects', compact(
                'projects', 'statuses', 'users', 'fonts', 
                'needsUpdateCount', 'newUsersCount'
            ));
        } catch (\Exception $e) {
            return view('admin.projects', ['error' => $e->getMessage()]);
        }
    }
} 