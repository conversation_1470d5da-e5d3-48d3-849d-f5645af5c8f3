<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Font;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Response;
use ZipArchive;
use Illuminate\Support\Facades\File;

class DatatablePreviewController extends Controller
{
    /**
     * Display a preview of the datatable component with font data.
     *
     * @return \Illuminate\View\View
     */
    public function __invoke(Request $request)
    {
        // Define the columns for the datatable with alignment and width properties
        $columns = [
            [
                'key' => 'id', 
                'label' => 'ID', 
                'visible' => true,
                'align' => 'center',
                'width' => '10',
                'icon' => 'tos tos-good-id',
                'sortable' => true
            ],
            [
                'key' => 'name', 
                'label' => 'Font Name', 
                'visible' => true,
                'align' => 'start',
                'icon' => 'tos tos-article_name',
                'sortable' => true
            ],
            [
                'key' => 'hash', 
                'label' => 'Hash Key', 
                'visible' => true,
                'align' => 'start',
                'icon' => 'tos tos-key',
                'sortable' => false
            ],
            [
                'key' => 'css_class', 
                'label' => 'CSS Class', 
                'visible' => true,
                'align' => 'end',
                'icon' => 'tos tos-code',
                'sortable' => false
            ],
            [
                'key' => 'order_nr', 
                'label' => 'Order', 
                'visible' => true,
                'align' => 'center',
                'icon' => 'tos tos-sort-by',
                'sortable' => true
            ],
        ];

        // Fetch font data from the database
        $fonts = Font::paginate(10);

        // Render the preview view with the data
        return view('admin.datatable-preview', [
            'fonts' => $fonts,
            'columns' => $columns,
        ]);
    }

    /**
     * Download the Vue component file
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadComponent()
    {
        $path = resource_path('js/components/Shared/DatatableComponent.vue');
        return Response::download($path, 'DatatableComponent.vue');
    }

    /**
     * Download the example blade file
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadBlade()
    {
        // Create a simplified version of the blade file for download
        $content = $this->getExampleBladeContent();
        
        return Response::make($content, 200, [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => 'attachment; filename="datatable-example.blade.php"',
        ]);
    }

    /**
     * Download the controller file
     *
     * @return \Illuminate\Http\Response
     */
    public function downloadController()
    {
        // Create a simplified version of the controller for download
        $content = $this->getExampleControllerContent();
        
        return Response::make($content, 200, [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => 'attachment; filename="DatatableController.php"',
        ]);
    }

    /**
     * Download a complete package with all necessary files
     * 
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadPackage()
    {
        $zipFileName = storage_path('app/datatable-package.zip');
        
        // Create a new zip archive
        $zip = new ZipArchive();
        if ($zip->open($zipFileName, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            abort(500, 'Could not create zip file');
        }
        
        // Add Vue component
        $componentPath = resource_path('js/components/Shared/DatatableComponent.vue');
        $zip->addFile($componentPath, 'DatatableComponent.vue');
        
        // Add example blade content
        $zip->addFromString('datatable-example.blade.php', $this->getExampleBladeContent());
        
        // Add example controller content
        $zip->addFromString('DatatableController.php', $this->getExampleControllerContent());
        
        // Add TOS font files
        $fontFiles = ['tos.woff2', 'tos.woff', 'tos.ttf', 'tos.svg', 'tos.eot'];
        foreach ($fontFiles as $fontFile) {
            $fontPath = resource_path('fonts-icons/' . $fontFile);
            if (File::exists($fontPath)) {
                $zip->addFile($fontPath, 'fonts/' . $fontFile);
            }
        }
        
        // Add TOS SCSS file
        $scssPath = resource_path('sass/tos.scss');
        if (File::exists($scssPath)) {
            $zip->addFile($scssPath, 'sass/tos.scss');
        }
        
        // Add README file with installation instructions
        $zip->addFromString('README.md', $this->getReadmeContent());
        
        $zip->close();
        
        return Response::download($zipFileName, 'datatable-package.zip', [
            'Content-Type' => 'application/zip',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Get example blade content
     *
     * @return string
     */
    private function getExampleBladeContent()
    {
        return <<<'EOT'
@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Datatable Example</h3>
                </div>
                <div class="card-body">
                    <!-- Include TOS font CSS if not already included in your layout -->
                    <style>
                        @font-face {
                            font-family: 'tos';
                            src: url('{{ asset('fonts/tos.woff2') }}') format('woff2'),
                                 url('{{ asset('fonts/tos.woff') }}') format('woff'),
                                 url('{{ asset('fonts/tos.ttf') }}') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                            font-display: block;
                        }

                        [class^="tos-"], [class*=" tos-"] {
                            font-family: 'tos' !important;
                            speak: never;
                            font-style: normal;
                            font-weight: normal;
                            font-variant: normal;
                            text-transform: none;
                            line-height: 1;
                            vertical-align: -.125em;
                            text-rendering: auto;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                        }

                        .tos-cells-setup-outline:before {
                            content: "\eb1e";
                        }
                        
                        .tos-hidden:before {
                            content: "\eb2b";
                        }
                    </style>

                    <datatable-component 
                        :columns="{{ json_encode($columns) }}" 
                        :data="{{ json_encode($data->items()) }}"
                        :pagination="{{ json_encode($data) }}"
                        @page-change="handlePageChange"
                    >
                        <template #actions="{ item }">
                            <a :href="'/items/' + item.id" class="btn btn-sm btn-info me-1">View</a>
                            <a :href="'/items/' + item.id + '/edit'" class="btn btn-sm btn-primary me-1">Edit</a>
                        </template>
                    </datatable-component>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    const app = new Vue({
        el: '#app',
        data: {
            columns: @json($columns),
            data: @json($data)
        },
        methods: {
            handlePageChange(url) {
                window.location.href = url;
            }
        }
    });
</script>
@endpush
EOT;
    }

    /**
     * Get example controller content
     *
     * @return string
     */
    private function getExampleControllerContent()
    {
        return <<<'EOT'
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\YourModel;

class DatatableController extends Controller
{
    /**
     * Display a listing of the resource with datatable component.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Define the columns for the datatable
        $columns = [
            ['key' => 'id', 'label' => 'ID', 'visible' => true],
            ['key' => 'name', 'label' => 'Name', 'visible' => true],
            ['key' => 'created_at', 'label' => 'Created At', 'visible' => true],
            ['key' => 'updated_at', 'label' => 'Updated At', 'visible' => false],
        ];

        // Fetch paginated data from the database
        $data = YourModel::paginate(10);

        // Render the view with the data
        return view('your-view', [
            'data' => $data,
            'columns' => $columns,
        ]);
    }
}
EOT;
    }

    /**
     * Get README content with installation instructions
     *
     * @return string
     */
    private function getReadmeContent()
    {
        return <<<'EOT'
# Datatable Component Package

This package contains a reusable Vue datatable component with the following features:
- Column visibility toggling
- Responsive design (mobile-friendly)
- Pagination support
- Custom cell rendering with slots
- Row styling customization

## Installation Instructions

1. **Copy the Vue Component:**
   - Copy `DatatableComponent.vue` to your project's components directory
   - Register the component in your Vue app

2. **Install Font Icons:**
   - Copy the font files from the `fonts` directory to your public assets
   - Copy the `tos.scss` file to your SCSS directory
   - Import the SCSS file in your main stylesheet

3. **Example Usage:**
   - See `datatable-example.blade.php` for an example of how to use the component
   - See `DatatableController.php` for an example controller implementation

## Required Dependencies

- Vue.js (v2 or v3)
- Bootstrap 5.1 or higher

## Additional Notes

- Make sure to properly reference the font files in your CSS based on your project's asset structure
- The component requires proper escaping of Vue expressions in Blade templates using the @ symbol
EOT;
    }
} 