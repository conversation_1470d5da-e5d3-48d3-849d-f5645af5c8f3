<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProjectStatusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $statuses = ProjectStatus::orderBy('sort_order')->get();
        $projectCounts = [];
        
        // Count projects for each status
        foreach ($statuses as $status) {
            $projectCounts[$status->id] = Project::where('status', $status->slug)->count();
        }
        
        $needsUpdateCount = config('vermont.needs_update_count', 0);
        $newUsersCount = config('vermont.new_users_count', 0);
        
        return view('admin.project-statuses.index', compact('statuses', 'projectCounts', 'needsUpdateCount', 'newUsersCount'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // We'll handle creation in a modal, no need for separate view
        return redirect()->route('admin.project-statuses.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'color' => 'required|string|max:20',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'integer'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        ProjectStatus::create([
            'name' => $request->name,
            'color' => $request->color,
            'icon' => $request->icon,
            'is_active' => true,
            'sort_order' => $request->sort_order ?? 0
        ]);

        return redirect()->route('admin.project-statuses.index')
            ->with('success', 'Status projektu bol úspešne vytvorený.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return redirect()->route('admin.project-statuses.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // We'll handle editing in a modal, no need for separate view
        return redirect()->route('admin.project-statuses.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $status = ProjectStatus::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'color' => 'required|string|max:20',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'integer'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Store the old slug for project updates if needed
        $oldSlug = $status->slug;
        
        $status->name = $request->name;
        $status->color = $request->color;
        $status->icon = $request->icon;
        $status->is_active = true;
        $status->sort_order = $request->sort_order ?? 0;
        $status->save();
        
        // If the slug changed, update all projects with this status
        if ($oldSlug !== $status->slug) {
            Project::where('status', $oldSlug)->update(['status' => $status->slug]);
        }

        return redirect()->route('admin.project-statuses.index')
            ->with('success', 'Status projektu bol úspešne aktualizovaný.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $status = ProjectStatus::findOrFail($id);
        
        // Check if any projects are using this status
        $projectCount = Project::where('status', $status->slug)->count();
        if ($projectCount > 0) {
            return back()->with('error', "Nemožno vymazať status, pretože je používaný v $projectCount projektoch.");
        }
        
        $status->delete();
        
        return redirect()->route('admin.project-statuses.index')
            ->with('success', 'Status projektu bol úspešne odstránený.');
    }
    
    /**
     * Update the order of statuses.
     */
    public function updateOrder(Request $request)
    {
        $statusOrder = $request->input('statusOrder', []);
        
        foreach ($statusOrder as $index => $statusId) {
            $status = ProjectStatus::find($statusId);
            if ($status) {
                $status->sort_order = $index;
                $status->save();
            }
        }
        
        return response()->json(['success' => true]);
    }
}
