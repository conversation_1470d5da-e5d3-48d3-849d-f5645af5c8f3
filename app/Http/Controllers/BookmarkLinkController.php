<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BookmarkLink;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

class BookmarkLinkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $data = $request->validate([
            'bookmark_id' => 'required|exists:bookmarks,id',
            'label' => 'required|string|max:255',
            'url' => 'required|url|max:255',
        ]);
        // Ensure the bookmark belongs to the authenticated user
        $bookmark = Auth::user()->bookmarks()->findOrFail($data['bookmark_id']);
        $link = $bookmark->links()->create($data);
        return response()->json($link, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BookmarkLink $link): JsonResponse
    {
        // Ensure the link's bookmark belongs to the authenticated user
        if ($link->bookmark->user_id !== Auth::id()) {
            return response()->json(['message' => 'Forbidden'], 403);
        }
        $data = $request->validate([
            'label' => 'required|string|max:255',
            'url' => 'required|url|max:255',
        ]);
        $link->update($data);
        return response()->json($link);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BookmarkLink $link): JsonResponse
    {
        // Ensure the link's bookmark belongs to the authenticated user
        if ($link->bookmark->user_id !== Auth::id()) {
            return response()->json(['message' => 'Forbidden'], 403);
        }
        $link->delete();
        return response()->json(null, 204);
    }
}
