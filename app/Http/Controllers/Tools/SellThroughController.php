<?php

namespace App\Http\Controllers\Tools;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class SellThroughController extends Controller
{
    public function __invoke(Request $request)
    {
        $data = $this->parseCsv();
        return view('tools.sell_through', $data);
    }

    public function downloadBlade()
    {
        $bladeContent = $this->getBladeTemplateContent();
        
        return Response::make($bladeContent, 200, [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => 'attachment; filename="sell_through_table.blade.php"',
        ]);
    }

    public function downloadVue()
    {
        $vueContent = $this->getVueComponentContent();
        
        return Response::make($vueContent, 200, [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => 'attachment; filename="SellThroughProgress.vue"',
        ]);
    }

    private function getBladeTemplateContent()
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sell Through Table</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        body {
            display: flex;
            flex-direction: column;
        }
        main {
            flex-grow: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .table-container-wrapper {
            flex-grow: 1;
            overflow-y: hidden;
            display: flex;
            flex-direction: column;
        }
        .table-container {
            flex-grow: 1;
            overflow: auto;
        }

        .table th, .table td {
            white-space: nowrap;
            padding: .35rem .4rem;
            background-color: rgba(255, 255, 255, 1.0) !important;
        }

        .table thead th,
        .table tfoot th {
            position: sticky;
            z-index: 2;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table thead th {
            top: 0;
            font-weight: 600;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .table tfoot th {
            bottom: 0;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .sticky-col,
        .sticky-col-right {
            position: sticky;
            z-index: 1;
        }

        .table thead .sticky-col, .table thead .sticky-col-right,
        .table tfoot .sticky-col, .table tfoot .sticky-col-right {
            z-index: 3;
            background-color: rgba(255, 255, 255, 1.0) !important;
        }
        
        .table thead.table-dark .sticky-col, .table thead.table-dark .sticky-col-right,
        .table tfoot.table-dark .sticky-col, .table tfoot.table-dark .sticky-col-right {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .sticky-col, .sticky-col-right {
            background-color: rgba(255, 255, 255, 1.0) !important;
        }

        .table-dark .sticky-col,
        .table-dark .sticky-col-right {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }
        
        .sticky-col.table-dark,
        .sticky-col-right.table-dark {
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }

        .table-dark th, .table-dark td, .table.table-dark {
            border-color: #495057;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .table-info .sticky-col,
        .table-info .sticky-col-right {
            background-color: rgba(209, 236, 241, 1.0) !important;
        }

        .table-striped tbody tr:nth-child(odd) .sticky-col,
        .table-striped tbody tr:nth-child(odd) .sticky-col-right {
            background-color: rgba(248, 249, 250, 1.0) !important;
        }

        .table-hover tbody tr:hover .sticky-col,
        .table-hover tbody tr:hover .sticky-col-right {
            background-color: rgba(233, 236, 239, 1.0) !important;
        }

        .group-total-row td {
            position: sticky;
            z-index: 1;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .group-total-row .sticky-col,
        .group-total-row .sticky-col-right {
            z-index: 2;
            background-color: rgba(52, 58, 64, 1.0) !important;
        }

        .sticky-col-1 { width: 150px; left: 0px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-2 { width: 80px;  left: 150px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-3 { width: 200px; left: 230px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-4 { width: 80px;  left: 430px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-5 { width: 80px;  left: 510px; background-color: rgba(255, 255, 255, 1.0) !important; }
        .sticky-col-6 { width: 80px;  left: 590px; background-color: rgba(255, 255, 255, 1.0) !important; }

        .sticky-col-right-1 { width: 300px; right: 0px; background-color: rgba(255, 255, 255, 1.0) !important; }
    </style>
</head>
<body>
<div id="app">
    <div class="container-fluid mt-4">
        <div class="row table-container-wrapper position-relative">
            <div class="col-12 h-100">
                <div class="table-container h-100">
                    <table class="table table-sm table-bordered table-hover mb-0 text-center align-middle">
                        <thead class="table-dark text-white fw-normal small">
                            <tr>
                                <th class="sticky-col sticky-col-1 fw-normal">Product</th>
                                @foreach (array_slice($header, 6, -4) as $th)
                                    <th><i class="tos tos-center me-1 opacity-50"></i>{{ $th }}</th>
                                @endforeach
                                <th class="sticky-col-right sticky-col-right-1">Sell-Through</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($groups as $groupName => $group)
                                @foreach ($group[\'rows\'] as $row)
                                    <tr>
                                        <td class="sticky-col sticky-col-1 text-start p-0">
                                            <div style="padding: 0.35rem 0.4rem; min-width: 340px; max-width: 420px;">
                                                <div class="fw-bold text-truncate" style="max-width: 320px; margin: 0.1rem 0;">{{ $row[\'Article\'] }}</div>
                                                <div class="fw-bold text-muted">{{ $row[\'Product Name\'] }} &mdash; {{ $row[\'Order Group\'] }}</div>               
                                                <div class="d-flex justify-content-between text-muted">
                                                    <small class="text-muted">Colour:<span class="text-dark fw-bold ms-1">{{ $row[\'Colour\'] }}</span> &bull; First delivery:<span class="text-dark fw-bold ms-1">{{ $row[\'First Delivery\'] }}</span></small>
                                                    <span class="fw-bold"><span class="bg-secondary text-white rounded-1 px-1">{{ $row[\'Price\'] }}</span></span>
                                                </div>
                                            </div>
                                        </td>
                                        @foreach (array_slice($row, 6, -4) as $value)
                                            <td>{{ $value }}</td>
                                        @endforeach
                                        <td class="sticky-col-right sticky-col-right-1 p-0">
                                            @php
                                                $sales = (int) $row[\'Sales\'];
                                                $order = (int) $row[\'Order\'];
                                                $stock = (int) $row[\'Stock\'];
                                            @endphp
                                            <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}"></sell-through-progress>
                                        </td>
                                    </tr>
                                @endforeach
                                @if (!empty($group[\'total\']))
                                    <tr class="group-total-row">
                                        <td class="sticky-col sticky-col-1 text-start table-dark fw-bold">{{ $group[\'total\'][\'Order Group\'] }}</td>
                                        <td class="sticky-col sticky-col-2 table-dark"></td>
                                        <td class="sticky-col sticky-col-3 table-dark"></td>
                                        <td class="sticky-col sticky-col-4 table-dark"></td>
                                        <td class="sticky-col sticky-col-5 table-dark"></td>
                                        <td class="sticky-col sticky-col-6 table-dark"></td>
                                        @foreach (array_slice($group[\'total\'], 6, -4) as $value)
                                            <td class="table-dark fw-bold">{{ $value }}</td>
                                        @endforeach
                                        <td class="sticky-col-right sticky-col-right-1 table-dark p-0">
                                            @php
                                                $sales = (int) $group[\'total\'][\'Sales\'];
                                                $order = (int) $group[\'total\'][\'Order\'];
                                                $stock = (int) $group[\'total\'][\'Stock\'];
                                            @endphp
                                            <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}" :dark="true"></sell-through-progress>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                        <tfoot class="table-dark mb-5">
                            <tr>
                                <th class="sticky-col sticky-col-1 text-start" colspan="6">{{ $grandTotal[\'Order Group\'] }}</th>
                                @foreach (array_slice($grandTotal, 6, -4) as $value)
                                    <th>{{ $value }}</th>
                                @endforeach
                                <th class="sticky-col-right sticky-col-right-1 p-0">
                                    @php
                                        $sales = (int) str_replace(\' \', \'\', $grandTotal[\'Sales\']);
                                        $order = (int) str_replace(\' \', \'\', $grandTotal[\'Order\']);
                                        $stock = (int) str_replace(\' \', \'\', $grandTotal[\'Stock\']);
                                    @endphp
                                    <sell-through-progress :sales="{{ $sales }}" :order="{{ $order }}" :stock="{{ $stock }}" :dark="true"></sell-through-progress>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<script>
    const { createApp } = Vue;
    
    // Vue component definition would go here
    // You would need to include the SellThroughProgress component
    
    createApp({
        // Your Vue app configuration
    }).mount(\'#app\');
</script>
</body>
</html>';
    }

    private function getVueComponentContent()
    {
        return '<template>
  <div class="progress-wrapper" :class="{ \'dark-mode\': dark }">
    <div class="stats">
      <div class="stat-item">
        <span class="label">Sales</span>
        <span class="value">{{ formattedSales }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
      <div class="stat-item">
        <span class="label">Stock</span>
        <span class="value">{{ formattedStock }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
      <div class="stat-item">
        <span class="label">Order</span>
        <span class="value">{{ formattedOrder }} <span class="text-muted fw-normal">pcs</span></span>
      </div>
    </div>
    <div class="progress-bar-container">
      <div class="progress">
        <div
          class="progress-bar"
          role="progressbar"
          :style="progressBarStyle"
          :aria-valuenow="percentage"
          aria-valuemin="0"
          aria-valuemax="100"
        ></div>
      </div>
      <span class="percentage-label">{{ percentage }}%</span>
    </div>
  </div>
</template>

<script>
export default {
    name: "SellThroughProgress",
    props: {
        sales: {
            type: [Number, String],
            required: true,
        },
        order: {
            type: [Number, String],
            required: true,
        },
        stock: {
            type: [Number, String],
            required: true,
        },
        dark: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        percentage() {
            const sales = parseFloat(String(this.sales).replace(/[,\\s]/g, \'\'));
            const order = parseFloat(String(this.order).replace(/[,\\s]/g, \'\'));

            if (isNaN(sales) || isNaN(order) || order === 0) {
                return 0;
            }
            return Math.round((sales / order) * 100);
        },
        progressBarStyle() {
            const p = this.percentage;
            let color;
            if (p <= 5) {
                color = \'#f63a0f\';
            } else if (p <= 25) {
                color = \'#f27011\';
            } else if (p <= 50) {
                color = \'#f2b01e\';
            } else if (p <= 75) {
                color = \'#f2d31b\';
            } else {
                color = \'#86e01e\';
            }
            return {
                width: p + \'%\',
                backgroundColor: color,
            };
        },
        formattedSales() {
            const sales = parseFloat(String(this.sales).replace(/[,\\s]/g, \'\'));
            if (isNaN(sales)) return \'0\';
            return sales.toLocaleString();
        },
        formattedOrder() {
            const order = parseFloat(String(this.order).replace(/[,\\s]/g, \'\'));
            if (isNaN(order)) return \'0\';
            return order.toLocaleString();
        },
        formattedStock() {
            const stock = parseFloat(String(this.stock).replace(/[,\\s]/g, \'\'));
            if (isNaN(stock)) return \'0\';
            return stock.toLocaleString();
        }
    }
};
</script>

<style scoped>
.progress-wrapper {
  width: 100%;
  padding: 0.4rem 0.5rem;
  background: rgba(255,255,255,0.92);
  border-radius: 6px;
  border: 1px solid #f1f1f1;
  min-height: 2.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.3rem;
}
.dark-mode.progress-wrapper {
  background: rgba(30,30,30,0.92);
  border: 0px solid #eee;
}
.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  gap: 0.8rem;
  align-items: center;
}
.stat-item {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-item .label {
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.02em;
  line-height: 1.2;
  margin-bottom: 0.1rem;
}
.stat-item .value {
  font-weight: 600;
  color: #212529;
  line-height: 1.2;
}
.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  height: 1rem;
}
.percentage-label {
  font-weight: 600;
  min-width: 35px;
  text-align: right;
  color: #212529;
  line-height: 1.2;
}

/* Dark Mode Styles */
.dark-mode .stat-item .label {
  color: #adb5bd;
}
.dark-mode .stat-item .value,
.dark-mode .percentage-label {
  color: #f8f9fa;
}

/* Progress Bar Styles */
.progress {
  flex-grow: 1;
  padding: 4px;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
  overflow: hidden;
  height: 1.5rem!important;
}

.progress-bar {
  height: 16px;
  border-radius: 4px;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
  transition: 0.4s linear;
  transition-property: width, background-color;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.25), inset 0 1px rgba(255, 255, 255, 0.1);
}
</style>';
    }

    private function parseCsv()
    {
        $csvPath = resource_path('views/GANT_HOME_FW24_Sell thru till 2025-2-28_24_00_.csv');
        $data = [];
        $header = [];
        $groups = [];
        $grandTotal = [];

        if (($handle = fopen($csvPath, "r")) !== FALSE) {
            // Read header
            if (($raw_header = fgetcsv($handle, 1000, ";")) !== FALSE) {
                // Trim whitespace and remove BOM if present
                $raw_header[0] = preg_replace('/^\x{FEFF}/u', '', $raw_header[0]);
                $header = array_map('trim', $raw_header);
            }

            $currentGroup = null;
            $currentRows = [];

            while (($row = fgetcsv($handle, 1000, ";")) !== FALSE) {
                // Skip empty lines
                if (empty($row) || (isset($row[0]) && is_null($row[0]))) {
                    continue;
                }

                $associatedRow = [];
                foreach ($header as $index => $key) {
                    $associatedRow[$key] = $row[$index] ?? null;
                }
                
                $groupName = (string) ($associatedRow['Order Group'] ?? '');

                if ($groupName === 'Grand Total') {
                    $grandTotal = $associatedRow;
                } elseif (str_contains($groupName, 'Total')) {
                    if ($currentGroup) {
                        $groups[$currentGroup]['rows'] = $currentRows;
                        $groups[$currentGroup]['total'] = $associatedRow;
                    }
                    $currentRows = [];
                    $currentGroup = null;
                }
                else {
                    if ($groupName !== $currentGroup) {
                        if ($currentGroup !== null) {
                             $groups[$currentGroup]['rows'] = $currentRows;
                        }
                        $currentRows = [];
                        $currentGroup = $groupName;
                        if (!empty($currentGroup)) {
                            $groups[$currentGroup] = ['rows' => [], 'total' => []];
                        }
                    }
                    if (!empty($currentGroup)) {
                        $currentRows[] = $associatedRow;
                    }
                }
            }
             if ($currentGroup !== null && !empty($currentRows)) {
                $groups[$currentGroup]['rows'] = $currentRows;
            }

            fclose($handle);
        }

        return [
            'header' => $header,
            'groups' => $groups,
            'grandTotal' => $grandTotal,
        ];
    }
} 