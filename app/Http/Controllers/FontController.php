<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Font;
use App\Models\FontIcon;
use App\Models\Icon;

class FontController extends Controller
{
    public function index()
    {
        $fonts = Font::withCount('icons')->withMax('icons', 'updated_at')->orderBy('order_nr')->get();
        return $fonts;
    }
    
    public function getIcons()
    {
        $allIcons = [];
        $fonts = Font::all();
        
        foreach ($fonts as $font) {
            $fontIcons = $font->icons_data ?? [];
            
            foreach ($fontIcons as $icon) {
                $allIcons[] = [
                    'id' => $icon['css_class'] ?? '',
                    'css_class' => $icon['css_class'] ?? '',
                    'css_content' => $icon['css_content'] ?? '',
                    'tags' => $icon['tags'] ?? [],
                    'font' => [
                        'id' => $font->id,
                        'name' => $font->name,
                        'css_class' => $font->css_class,
                    ]
                ];
            }
        }
        
        return response()->json($allIcons);
    }

    public function getScssContent($name)
    {
        $scssPath = resource_path("sass/{$name}.scss");
        
        if (!file_exists($scssPath)) {
            return response()->json(['error' => 'SCSS file not found'], 404);
        }

        return response(file_get_contents($scssPath))
            ->header('Content-Type', 'text/plain');
    }

    public function getIconsByFont(string $font)
    {
        $fontModel = Font::where('name', $font)->first();
        
        if (!$fontModel) {
            return response()->json([]);
        }
        
        $icons = [];
        $fontIcons = $fontModel->icons_data ?? [];
        
        foreach ($fontIcons as $icon) {
            $icons[] = [
                'id' => $icon['css_class'] ?? '',
                'css_class' => $icon['css_class'] ?? '',
                'css_content' => $icon['css_content'] ?? '',
                'tags' => $icon['tags'] ?? [],
                'font' => [
                    'id' => $fontModel->id,
                    'name' => $fontModel->name,
                    'css_class' => $fontModel->css_class,
                ]
            ];
        }
        
        return response()->json($icons);
    }
    
    /**
     * Update tags for a specific icon in a font
     *
     * @param Request $request
     * @param string|int $fontIdentifier - Can be a font ID or name
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateIconTags(Request $request, $fontIdentifier)
    {
        $request->validate([
            'css_class' => 'required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'string'
        ]);

        // Determine if the identifier is a numeric ID or a name/string
        $font = is_numeric($fontIdentifier) 
            ? Font::find($fontIdentifier) 
            : Font::where('name', $fontIdentifier)->orWhere('css_class', $fontIdentifier)->first();
        
        if (!$font) {
            return response()->json([
                'success' => false,
                'message' => "Font with identifier '{$fontIdentifier}' not found"
            ], 404);
        }

        $cssClass = $request->input('css_class');
        $tags = $request->input('tags');

        try {
            $font->updateIcon($cssClass, ['tags' => $tags]);
            
            return response()->json([
                'success' => true,
                'message' => 'Icon tags updated successfully',
                'tags' => $tags
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update icon tags: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lookup a font by name and return its ID
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function lookupByName(Request $request)
    {
        $request->validate([
            'name' => 'required|string'
        ]);

        $name = $request->input('name');
        
        // Try to find the font by exact name first
        $font = Font::where('name', $name)->first();
        
        // If no exact match, try to find by css_class
        if (!$font) {
            $font = Font::where('css_class', $name)->first();
        }
        
        // If still no match, try by hash
        if (!$font) {
            $font = Font::where('hash', $name)->first();
        }
        
        // Last resort - try case-insensitive match
        if (!$font) {
            $font = Font::where('name', 'LIKE', $name)
                      ->orWhere('css_class', 'LIKE', $name)
                      ->orWhere('hash', 'LIKE', $name)
                      ->first();
        }
        
        if (!$font) {
            // For debugging, get a list of all fonts
            $allFonts = Font::select('id', 'name', 'css_class', 'hash')->get();
            
            return response()->json([
                'error' => 'Font not found',
                'requested' => $name,
                'available_fonts' => $allFonts
            ], 404);
        }
        
        return response()->json([
            'id' => $font->id,
            'name' => $font->name,
            'css_class' => $font->css_class,
            'hash' => $font->hash
        ]);
    }
}
