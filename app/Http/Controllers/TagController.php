<?php

namespace App\Http\Controllers;

use App\Models\Font;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TagController extends Controller
{
    /**
     * Get all unique tags with usage count across all icons
     */
    public function getAllTags(): JsonResponse
    {
        try {
            Log::info('TagController.getAllTags: Starting tag collection');
            
            $tags = [];
            
            // Get all fonts
            $fonts = Font::all();
            
            // Iterate through each font and count tag occurrences
            foreach ($fonts as $font) {
                if (isset($font->icons_data) && is_array($font->icons_data)) {
                    foreach ($font->icons_data as $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            foreach ($icon['tags'] as $tag) {
                                if (!isset($tags[$tag])) {
                                    $tags[$tag] = 0;
                                }
                                $tags[$tag]++;
                            }
                        }
                    }
                }
            }
            
            // Format for response
            $formattedTags = [];
            foreach ($tags as $tag => $count) {
                $formattedTags[] = [
                    'name' => $tag,
                    'count' => $count
                ];
            }
            
            // Sort by tag name
            usort($formattedTags, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            Log::info('TagController.getAllTags: Found ' . count($formattedTags) . ' tags');
            return response()->json($formattedTags);
            
        } catch (\Exception $e) {
            Log::error('TagController.getAllTags error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Get simple array of all unique tags (for autocomplete)
     */
    public function getTagList(): JsonResponse
    {
        try {
            Log::info('TagController.getTagList: Starting tag list collection');
            $tags = [];
            
            // Get all fonts
            $fonts = Font::all();
            
            // Iterate through each font
            foreach ($fonts as $font) {
                if (isset($font->icons_data) && is_array($font->icons_data)) {
                    foreach ($font->icons_data as $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            foreach ($icon['tags'] as $tag) {
                                if (!in_array($tag, $tags)) {
                                    $tags[] = $tag;
                                }
                            }
                        }
                    }
                }
            }
            
            sort($tags);
            
            Log::info('TagController.getTagList: Found ' . count($tags) . ' unique tags');
            return response()->json($tags);
            
        } catch (\Exception $e) {
            Log::error('TagController.getTagList error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Rename a tag across all icons
     */
    public function renameTag(Request $request): JsonResponse
    {
        try {
            // Log the raw request data for debugging
            Log::info("TagController.renameTag: Raw input received", [
                'all' => $request->all(),
                'oldName' => $request->input('oldName'),
                'oldName_type' => gettype($request->input('oldName')),
                'newName' => $request->input('newName'),
                'newName_type' => gettype($request->input('newName')),
            ]);
            
            $validated = $request->validate([
                'oldName' => 'required|string',
                'newName' => 'required|string',
            ]);
            
            $oldName = $validated['oldName'];
            $newName = $validated['newName'];
            
            Log::info("TagController.renameTag: Renaming tag '{$oldName}' to '{$newName}'");
            
            $updatedCount = 0;
            $fonts = Font::all();
            
            foreach ($fonts as $font) {
                $updated = false;
                $iconsData = $font->icons_data;
                
                if (isset($iconsData) && is_array($iconsData)) {
                    foreach ($iconsData as $key => $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            $tagIndex = array_search($oldName, $icon['tags']);
                            
                            if ($tagIndex !== false) {
                                // Replace old tag with new tag if new tag doesn't already exist
                                if (!in_array($newName, $icon['tags'])) {
                                    $iconsData[$key]['tags'][$tagIndex] = $newName;
                                } else {
                                    // Otherwise just remove the old tag
                                    unset($iconsData[$key]['tags'][$tagIndex]);
                                    $iconsData[$key]['tags'] = array_values($iconsData[$key]['tags']); // Re-index array
                                }
                                
                                $updated = true;
                                $updatedCount++;
                            }
                        }
                    }
                }
                
                if ($updated) {
                    $font->icons_data = $iconsData; // Assign the entire array back
                    $font->save();
                }
            }
            
            Log::info("TagController.renameTag: Updated {$updatedCount} icons");
            return response()->json([
                'success' => true,
                'message' => "Renamed tag '{$oldName}' to '{$newName}' in {$updatedCount} icons."
            ]);
            
        } catch (\Exception $e) {
            Log::error('TagController.renameTag error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error renaming tag: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Delete a tag from all icons
     */
    public function deleteTag(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string',
            ]);
            
            $tagName = $validated['name'];
            Log::info("TagController.deleteTag: Deleting tag '{$tagName}'");
            
            $updatedCount = 0;
            $fonts = Font::all();
            
            foreach ($fonts as $font) {
                $updated = false;
                $iconsData = $font->icons_data;
                
                if (isset($iconsData) && is_array($iconsData)) {
                    foreach ($iconsData as $key => $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags'])) {
                            $tagIndex = array_search($tagName, $icon['tags']);
                            
                            if ($tagIndex !== false) {
                                unset($iconsData[$key]['tags'][$tagIndex]);
                                $iconsData[$key]['tags'] = array_values($iconsData[$key]['tags']); // Re-index array
                                $updated = true;
                                $updatedCount++;
                            }
                        }
                    }
                }
                
                if ($updated) {
                    $font->icons_data = $iconsData; // Assign the entire array back
                    $font->save();
                }
            }
            
            Log::info("TagController.deleteTag: Removed tag from {$updatedCount} icons");
            return response()->json([
                'success' => true,
                'message' => "Removed tag '{$tagName}' from {$updatedCount} icons."
            ]);
            
        } catch (\Exception $e) {
            Log::error('TagController.deleteTag error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting tag: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index()
    {
        // Example implementation - adjust to your data structure
        $tags = []; // Your tag collection
        $tagCounts = []; // To track usage count
        
        // Example using fonts with icons_data
        $fonts = Font::all();
        foreach ($fonts as $font) {
            if (isset($font->icons_data['tags'])) {
                foreach ($font->icons_data['tags'] as $tag) {
                    if (!isset($tagCounts[$tag])) {
                        $tagCounts[$tag] = 0;
                    }
                    $tagCounts[$tag]++;
                }
            }
        }
        
        // Format for the frontend
        foreach ($tagCounts as $name => $count) {
            $tags[] = [
                'name' => $name,
                'count' => $count
            ];
        }
        
        return response()->json($tags);
    }
    
    public function create(Request $request)
    {
        // Validate request
        $request->validate([
            'name' => 'required|string|max:255'
        ]);
        
        // Here you might just want to check if it exists
        // since tags often don't have their own table
        
        return response()->json([
            'success' => true,
            'message' => 'Tag created successfully.'
        ]);
    }
    
    public function rename(Request $request)
    {
        $request->validate([
            'oldName' => 'required|string',
            'newName' => 'required|string|max:255'
        ]);
        
        // Updating icons_data in Font model
        $updatedCount = 0;
        $fonts = Font::all();
        
        foreach ($fonts as $font) {
            $updated = false;
            $iconsData = $font->icons_data;
            
            if (isset($iconsData) && is_array($iconsData)) {
                foreach ($iconsData as $key => $icon) {
                    if (isset($icon['tags']) && is_array($icon['tags'])) {
                        $tagIndex = array_search($request->oldName, $icon['tags']);
                        
                        if ($tagIndex !== false) {
                            // Replace old tag with new tag if new tag doesn't already exist
                            if (!in_array($request->newName, $icon['tags'])) {
                                $iconsData[$key]['tags'][$tagIndex] = $request->newName;
                            } else {
                                // Otherwise just remove the old tag
                                unset($iconsData[$key]['tags'][$tagIndex]);
                                $iconsData[$key]['tags'] = array_values($iconsData[$key]['tags']); // Re-index array
                            }
                            
                            $updated = true;
                            $updatedCount++;
                        }
                    }
                }
            }
            
            if ($updated) {
                $font->icons_data = $iconsData; // Assign the entire array back
                $font->save();
            }
        }
        
        Log::info("rename: Updated {$updatedCount} icons");
        return response()->json([
            'success' => true,
            'message' => "Renamed tag '{$request->oldName}' to '{$request->newName}' in {$updatedCount} icons."
        ]);
    }
    
    public function delete(Request $request)
    {
        $request->validate([
            'name' => 'required|string'
        ]);
        
        // Removing tag from icons_data
        $updatedCount = 0;
        $fonts = Font::all();
        
        foreach ($fonts as $font) {
            $updated = false;
            $iconsData = $font->icons_data;
            
            if (isset($iconsData) && is_array($iconsData)) {
                foreach ($iconsData as $key => $icon) {
                    if (isset($icon['tags']) && is_array($icon['tags'])) {
                        $tagIndex = array_search($request->name, $icon['tags']);
                        
                        if ($tagIndex !== false) {
                            unset($iconsData[$key]['tags'][$tagIndex]);
                            $iconsData[$key]['tags'] = array_values($iconsData[$key]['tags']); // Re-index array
                            $updated = true;
                            $updatedCount++;
                        }
                    }
                }
            }
            
            if ($updated) {
                $font->icons_data = $iconsData; // Assign the entire array back
                $font->save();
            }
        }
        
        Log::info("delete: Removed tag from {$updatedCount} icons");
        return response()->json([
            'success' => true,
            'message' => "Removed tag '{$request->name}' from {$updatedCount} icons."
        ]);
    }

    /**
     * Get detailed usage information for a specific tag
     */
    public function getTagUsage(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string',
            ]);
            
            $tagName = $validated['name'];
            Log::info("TagController.getTagUsage: Getting usage for tag '{$tagName}'");
            
            $usage = [
                'tagName' => $tagName,
                'totalCount' => 0,
                'fontCount' => 0,
                'fontDistribution' => []
            ];
            
            $fonts = Font::all();
            $fontCounts = [];
            
            foreach ($fonts as $font) {
                $fontTagCount = 0;
                $iconsData = $font->icons_data;
                
                if (isset($iconsData) && is_array($iconsData)) {
                    foreach ($iconsData as $icon) {
                        if (isset($icon['tags']) && is_array($icon['tags']) && in_array($tagName, $icon['tags'])) {
                            $fontTagCount++;
                            $usage['totalCount']++;
                        }
                    }
                }
                
                if ($fontTagCount > 0) {
                    $fontCounts[] = [
                        'name' => $font->name,
                        'hash' => $font->hash,
                        'css_class' => $font->css_class,
                        'count' => $fontTagCount
                    ];
                }
            }
            
            // Sort fonts by count (descending) then name
            usort($fontCounts, function($a, $b) {
                if ($a['count'] === $b['count']) {
                    return strcmp($a['name'], $b['name']);
                }
                return $b['count'] - $a['count'];
            });
            
            $usage['fontCount'] = count($fontCounts);
            $usage['fontDistribution'] = $fontCounts;
            
            return response()->json($usage);
            
        } catch (\Exception $e) {
            Log::error('TagController.getTagUsage error: ' . $e->getMessage());
            return response()->json([
                'error' => 'Error getting tag usage: ' . $e->getMessage()
            ], 500);
        }
    }
} 