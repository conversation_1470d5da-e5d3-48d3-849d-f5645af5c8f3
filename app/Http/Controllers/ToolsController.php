<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Support\Facades\File;

class ToolsController extends Controller
{
    public function download($filename)
    {
        $filePaths = [
            'eshop-resizer' => resource_path('tools/eshop-resize-photoshop/eshop-image-resize.jsx'),
            'insta-resizer' => resource_path('tools/insta-resize-photoshop/insta-image-resize.jsx')
        ];

        if (!isset($filePaths[$filename]) || !file_exists($filePaths[$filename])) {
            abort(404);
        }
        
        // Generate a timestamp suffix in format YYYYMMDD_HHMMSS
        $timestamp = date('Ymd_His');
        
        // Get the original filename without path
        $originalFilename = basename($filePaths[$filename]);
        
        // Insert timestamp before the file extension
        $parts = pathinfo($originalFilename);
        $newFilename = $parts['filename'] . '_' . $timestamp . '.' . $parts['extension'];
        
        return response()->download($filePaths[$filename], $newFilename);
    }
    
    /**
     * Download SVG file
     *
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadSvg($filename)
    {
        $filePath = public_path('img/tools/svg-animate/' . $filename);
        
        if (!file_exists($filePath)) {
            abort(404, 'SVG file not found');
        }
        
        // Generate a timestamp suffix in format YYYYMMDD_HHMMSS
        $timestamp = date('Ymd_His');
        
        // Insert timestamp before the file extension
        $parts = pathinfo($filename);
        $newFilename = $parts['filename'] . '_' . $timestamp . '.' . $parts['extension'];
        
        return response()->download($filePath, $newFilename);
    }
    
    /**
     * Get SVGs from a specific folder
     *
     * @param string $folderPath
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSvgsFromFolder($folderPath)
    {
        $path = public_path('img/svg/' . $folderPath);
        
        if (!File::exists($path)) {
            return response()->json([], 404);
        }
        
        $svgs = [];
        $files = File::files($path);
        
        foreach ($files as $file) {
            if (strtolower($file->getExtension()) === 'svg') {
                $svgContent = File::get($file->getPathname());
                $svgs[] = [
                    'name' => $file->getFilename(),
                    'url' => asset('img/svg/' . $folderPath . '/' . $file->getFilename()),
                    'content' => $svgContent
                ];
            }
        }
        
        return response()->json($svgs);
    }

    // QR Code Generator
    public function qrGeneratorIndex()
    {
        return view('tools.qr.qr-generator');
    }

    public function qrGeneratorDocumentation()
    {
        return view('tools.qr.qr-generator-documentation');
    }

    // SVG Optimizer
    public function svgOptimizerIndex()
    {
        return view('tools.svg-optimizer.index');
    }

    public function svgOptimizerDocumentation()
    {
        return view('tools.svg-optimizer.documentation');
    }

    // Eshop Resizer Documentation
    public function eshopResizerDocumentation()
    {
        return view('tools.eshop-resize-photoshop.eshop-resizer-documentation');
    }
    
    // Insta Resizer Documentation
    public function instaResizerDocumentation()
    {
        return view('tools.insta-resize-photoshop.insta-resizer-documentation');
    }

    // SVG Animation Documentation
    public function svgAnimationDocumentation()
    {
        return view('tools.svg-animation.svg-animation-documentation');
    }

    // OrderGroup Image Documentation
    public function ordergroupImageDocumentation()
    {
        return view('tools.ordergroup-image.ordergroup-image-documentation');
    }
} 