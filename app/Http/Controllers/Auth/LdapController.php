<?php

namespace App\Http\Controllers\Auth;

class LdapController
{
    private $connection;
    private ?string $masterServerUrl;
    private ?int $masterServerPort;
    private ?string $masterAdminName;
    private ?string $masterAdminPassword;
    private ?string $masterUserBase;

    private ?string $slaveServerUrl;
    private ?int $slaveServerPort;
    private ?string $slaveAdminName;
    private ?string $slaveAdminPassword;
    private ?string $slaveUserBase;

    private $userBase = '';
    private $user = [];

    /**
     * Vytvor pripojenie na LDAP Server
     */
    public function __construct()
    {
        $this->setMasterAndSlaveConfigValues();

        //pripojenie na mastera
        try {
            $ldap_conn = ldap_connect($this->masterServerUrl, $this->masterServerPort);

            ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
            // ldap_start_tls($ldap_conn);

            //otestuj ci je pripojenie funkcne
            $result = @ldap_bind($ldap_conn, $this->masterAdminName, $this->masterAdminPassword);
        } catch (\Throwable $t) {
            if ($t->getMessage() === 'Call to undefined function App\Http\Controllers\Auth\ldap_connect()') {
                throw new \Exception("You dont have installed ldap php extension", 101);
            };
        }

        $this->userBase = $this->masterUserBase;

        if (!$result) { //nie je funkcne, skus rovnako so slavom
            $ldap_conn = ldap_connect($this->slaveServerUrl, $this->slaveServerPort);
            ldap_set_option($ldap_conn, LDAP_OPT_PROTOCOL_VERSION, 3);
            $result = @ldap_bind($ldap_conn, $this->slaveAdminName, $this->slaveAdminPassword);
            $this->userBase = $this->slaveUserBase;
        }

        if ($result) {
            $this->connection = $ldap_conn;
        } else {
            throw new \Exception("Error: Couldn't bind to server with supplied credentials!", 100);
        }
    }

    /**
     * Otestuj, ci su spravne prihlasovacie Udaje pouzivatela
     */
    public function login($username, $password)
    {
        if (($user = $this->searchUsername($username)) === false) {
            return false;
        }

        //Authenticate with the newly found DN and user-provided password
        $auth_status = @ldap_bind($this->connection, $user['dn'], $password);
        if ($auth_status === false) {
            return false;                    //Couldn't bind to LDAP as user!
        }

        //Uspesne prihlaseny - moznost vratit si true alebo
        //pole $user s uzivatelovymi udajmi ['dn' = ..., 'uid' = ..., 'name' = ...]
        return $user;
    }

    /**
     * Zisti ci pouzivatel s danym UID uz existuje
     */
    public function searchUsername($username)
    {
        $query = "(&(uid=" . $username . ")(objectClass=account))";

        $search_status = @ldap_search(
            $this->connection,
            $this->userBase,
            $query,
            ['dn', 'uid', 'description']
        );
        if ($search_status === false) {
            return false;
            //die("Search on LDAP failed");
        }

        // Pull the search results
        $result = @ldap_get_entries($this->connection, $search_status);
        if ($result === false) {
            return false;
            //die("Couldn't pull search results from LDAP");
        }

        if ((int)@$result['count'] > 0) {
            // Definitely pulled something, we don't check here
            //     for this example if it's more results than 1,
            //     although you should.
            $user['dn'] = $result[0]['dn'];
            $user['uid'] = $result[0]['uid'][0];
            $user['name'] = $result[0]['description'][0];
        } else {
            return false;
        }

        if (trim((string)$user['dn']) == '') {
            return false;
            //die("Empty DN. Something is wrong.");
        }

        return $user;
    }

    private function setMasterAndSlaveConfigValues(): void
    {
        $this->masterServerUrl = config('vermont.ldap_auth.master_server.url');
        $this->masterServerPort = config('vermont.ldap_auth.master_server.port');
        $this->masterAdminName = config('vermont.ldap_auth.master_server.admin_name');
        $this->masterAdminPassword = config('vermont.ldap_auth.master_server.admin_pass');
        $this->masterUserBase = config('vermont.ldap_auth.master_server.user_base');

        $this->slaveServerUrl = config('vermont.ldap_auth.slave_server.url');
        $this->slaveServerPort = config('vermont.ldap_auth.slave_server.port');
        $this->slaveAdminName = config('vermont.ldap_auth.slave_server.admin_name');
        $this->slaveAdminPassword = config('vermont.ldap_auth.slave_server.admin_pass');
        $this->slaveUserBase = config('vermont.ldap_auth.slave_server.user_base');
    }
}
