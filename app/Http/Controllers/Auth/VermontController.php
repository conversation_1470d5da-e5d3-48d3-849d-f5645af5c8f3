<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;

class VermontController extends Controller
{
    protected $redirectTo = '/';

    public function __construct()
    {
    }

    public function showLoginForm() : View|RedirectResponse
    {
        if(auth()->check()){
            return to_route('home');
        }

        return view('auth.login');
    }

    public function login(Request $request) : \Illuminate\Http\RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|min:8',
        ]);

        if(!empty(request()->input('redirectTo'))) {
            $this->redirectTo = request()->input('redirectTo');
        }

        if (! $validator->fails()) {
            $login = strtolower(trim($request->login));
            $dbuser = User::where('login', $login)->first();

            try {
                $ldap = new LdapController();
                $ldapuser = $ldap->login($login, $request->password);
            } catch (\Throwable $t){
                if(!in_array($t->getCode(), [100,101]) || config('app.env') !== 'local'){
                    return redirect()->back()->withErrors(['login' => $t->getMessage()]);
                }

                $ldapuser = ['name' => 'Local '.$login];
            }

            if (empty($ldapuser)) {
                return redirect()->back()->withErrors(['login' =>'Non-existing Ldap account or Incorrect login or password!']);
            }

            if (empty($dbuser)) {
                $ldapname = self::getLdapName($ldapuser['name']);
                $dbuser = User::create([
                    'login' => $login,
                    'surname' => $ldapname['surname'],
                    'name' => $ldapname['name'],
                    'active' => 1,
                ]);

                Auth::login($dbuser);

                return redirect()->to($this->redirectTo);
            } else {
                if ($dbuser->active !== 1) {
                    return redirect()->back()->withErrors(['login' =>'Inactive account!']);
                }

                $ldapname = self::getLdapName($ldapuser['name']);

                User::where('id', $dbuser->id)
                    ->update([
                        'login' => $login,
                        'surname' => $ldapname['surname'],
                        'name' => $ldapname['name'],
                        'last_login_at' => now()
                    ]);

                Auth::login($dbuser);

                return redirect()->intended($this->redirectTo);
            }
        } else {
            return redirect()->back()->withErrors($validator);
        }
    }

    /**
     * Ziskanie Mena a priezviska z LDAPu
     * @param  [type] $fullname [description]
     * @return [type]           [description]
     */
    private static function getLdapName(string $fullname) : array
    {
        $array = explode(' ', trim($fullname));
        if (count($array) < 2) {
            $return['surname'] = $fullname;
            $return['name'] = '';
        } elseif (count($array) == 2) {
            $return['surname'] = $array[0];
            $return['name'] = $array[1];
        } else {
            $return['surname'] = $array[0];
            $return['name'] = substr($fullname, strlen($array[0]));
        }

        return $return;
    }

    public function logout() : \Illuminate\Http\RedirectResponse
    {
        Session::flush();
        Auth::logout();

        if(!empty(request()->input('redirectTo'))){
            return redirect()->to(request()->input('redirectTo'));
        }

        return redirect()->route('login');
    }
}
