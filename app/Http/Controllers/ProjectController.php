<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    /**
     * Display the project details.
     *
     * @param Project $project
     * @return \Illuminate\View\View
     */
    public function show(Project $project)
    {
        // Check if the user has access to this project
        $user = Auth::user();
        if (!$user->projects->contains($project->id)) {
            return redirect()->route('profile')->with('error', 'Nemáte prístup k tomuto projektu.');
        }
        
        // Load project with relationships
        $project->load(['users', 'fonts']);
        
        return view('projects.show', compact('project'));
    }
} 