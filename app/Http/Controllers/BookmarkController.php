<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Bookmark;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BookmarkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try {
            $bookmarks = Bookmark::where('user_id', Auth::id())->with('links')->get();
            return response()->json($bookmarks);
        } catch (\Throwable $e) {
            Log::error('Error fetching bookmarks', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'icon_class' => 'nullable|string|max:255',
            ]);
            $bookmark = Bookmark::create(array_merge($data, ['user_id' => Auth::id()]));
            return response()->json($bookmark, 201);
        } catch (\Throwable $e) {
            Log::error('Error creating bookmark', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Bookmark $bookmark): JsonResponse
    {
        if ($bookmark->user_id !== Auth::id()) {
            return response()->json(['message' => 'Forbidden'], 403);
        }
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'icon_class' => 'nullable|string|max:255',
        ]);
        $bookmark->update($data);
        return response()->json($bookmark);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Bookmark $bookmark): JsonResponse
    {
        if ($bookmark->user_id !== Auth::id()) {
            return response()->json(['message' => 'Forbidden'], 403);
        }
        $bookmark->delete();
        return response()->json(null, 204);
    }
}
