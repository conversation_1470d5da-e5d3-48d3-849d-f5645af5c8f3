<?php

namespace App\Http\Controllers;

use App\Models\Font;
use App\Models\FontIcon;
use App\Models\Icon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IconController extends Controller
{
    public function getIcons(Request $request): JsonResponse
    {
        $query = FontIcon::query()
            ->with('font')
            ->select(['id', 'font_id', 'css_class', 'css_content']);

        if ($request->has('font')) {
            $query->whereHas('font', function ($q) use ($request) {
                $q->where('hash', $request->font);
            });
        }

        $icons = $query->get();

        return response()->json([
            'icons' => $icons->map(function ($icon) {
                return [
                    'id' => $icon->id,
                    'fontName' => $icon->font->hash,
                    'cssClass' => $icon->css_class,
                    'cssContent' => $icon->css_content,
                ];
            })
        ]);
    }

    public function getAllIcons()
    {
        $icons = Icon::with('font')->get();
        return response()->json($icons);
    }

    public function getIconsByFont($fontName)
    {
        $font = Font::where('name', $fontName)->first();
        
        if (!$font) {
            return response()->json([]);
        }
        
        $icons = Icon::where('font_id', $font->id)->get();
        return response()->json($icons);
    }

    public function getIconsByFontId($id)
    {
        $icons = Icon::where('font_id', $id)->get();
        return response()->json($icons);
    }

    public function updateIconTags(Request $request, $fontId)
    {
        $validated = $request->validate([
            'css_class' => 'required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'string'
        ]);

        $icon = Icon::where('font_id', $fontId)
                   ->where('css_class', $validated['css_class'])
                   ->first();

        if (!$icon) {
            return response()->json([
                'success' => false,
                'message' => 'Icon not found'
            ], 404);
        }

        $icon->tags = $validated['tags'];
        $icon->save();

        return response()->json(['success' => true]);
    }
}
