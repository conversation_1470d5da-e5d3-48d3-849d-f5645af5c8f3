<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class FontDownloadController extends Controller
{
    public function download($filename)
    {
        $filename = preg_replace('/[^a-zA-Z0-9-_]/', '', $filename); // Sanitize filename
        $path = resource_path("fonts-icons/{$filename}.woff2"); // Ensure the filename ends with .woff2

        if (!file_exists($path)) {
            Log::error("File not found at path: {$path}");
            return abort(404, 'File not found.');
        }

        return response()->download($path);
    }
}

