<?php

namespace App\Http\Controllers;

use App\Models\WhiteboardCard;
use App\Models\WhiteboardCardLink;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class WhiteboardCardController extends Controller
{
    /**
     * Check if the authenticated user is an admin.
     */
    protected function isAdmin(): bool
    {
        // First check the database flag
        if (Auth::user()->is_admin) {
            return true;
        }
        
        // Fallback to config check if database flag is false
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        return in_array(Auth::user()->login, $adminLogins);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get filter status from request (active, archived, all)
        $status = $request->get('status', 'active');
        
        // Query builder
        $query = WhiteboardCard::with('additionalLinks', 'user', 'project', 'assignedUsers', 'fonts')
            ->orderBy('card_date', 'desc');
            
        // If user is not admin, only show admin-created cards
        if (!$this->isAdmin()) {
            // Get admin user IDs
            $adminUserIds = \App\Models\User::where('is_admin', true)->pluck('id')->toArray();
            
            // Also include admin users from config
            $adminLogins = Config::get('vermont.admin_user_logins', []);
            $adminUsersByLogin = \App\Models\User::whereIn('login', $adminLogins)->pluck('id')->toArray();
            
            // Combine the admin user IDs
            $allAdminUserIds = array_merge($adminUserIds, $adminUsersByLogin);
            
            // Only show cards created by admin users
            $query->whereIn('user_id', $allAdminUserIds);
        }
        
        // Apply status filter
        if ($status === 'active') {
            $query->where('is_active', true);
        } elseif ($status === 'archived') {
            $query->where('is_active', false);
        }
        
        $cards = $query->get();
        
        return response()->json($cards);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if user is admin
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        // Log the entire request data for debugging
        info('Create request for new card');
        info('Request data: ' . json_encode($request->all()));
        
        // Handle the case where user_ids is present but empty
        if ($request->has('user_ids') && empty($request->user_ids)) {
            // If an empty array is submitted, we'll keep it as an empty array
            info('Empty user_ids array received in request');
        } else if (!$request->has('user_ids')) {
            // If user_ids is not present in the request, we'll default to empty array
            info('No user_ids field in request, defaulting to empty array');
            $request->merge(['user_ids' => []]);
        } else {
            // Otherwise, we have user_ids in the request
            info('user_ids in request: ' . json_encode($request->user_ids));
        }
        
        // Get valid status slugs
        $validStatuses = \App\Models\WhiteboardCardStatus::pluck('slug')->toArray();
        
        $validated = $request->validate([
            'headline' => 'required|string|max:255',
            'description' => 'nullable|string',
            'link' => 'nullable|url|max:255',
            'link_target' => 'nullable|string|in:_blank,_self',
            'card_date' => 'required|date',
            'deadline' => 'nullable|date',
            'is_active' => 'boolean',
            'status' => 'nullable|string|in:' . implode(',', $validStatuses),
            'project_id' => 'nullable|exists:projects,id',
            'image' => 'nullable|string|max:255',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
            'font_ids' => 'nullable|array',
            'font_ids.*' => 'exists:fonts,id',
            'additional_links' => 'nullable|array',
            'additional_links.*.title' => 'required|string|max:255',
            'additional_links.*.url' => 'required|url|max:255',
            'additional_links.*.image' => 'nullable|string|max:255',
        ]);
        
        // Log validation result
        info('Validated data: ' . json_encode($validated));
        
        // Get default status if not provided
        $defaultStatus = \App\Models\WhiteboardCard::getDefaultStatus();
        
        // Create card
        $card = WhiteboardCard::create([
            'user_id' => Auth::id(),
            'headline' => $validated['headline'],
            'description' => $validated['description'],
            'link' => $validated['link'],
            'link_target' => $validated['link_target'],
            'card_date' => $validated['card_date'],
            'deadline' => $validated['deadline'],
            'is_active' => $validated['is_active'] ?? true,
            'status' => $validated['status'] ?? $defaultStatus,
            'project_id' => $validated['project_id'],
            'image' => $validated['image'],
        ]);
        
        info('Created new card with ID: ' . $card->id);
        
        // Create additional links if any
        if (isset($validated['additional_links']) && is_array($validated['additional_links'])) {
            foreach ($validated['additional_links'] as $link) {
                WhiteboardCardLink::create([
                    'whiteboard_card_id' => $card->id,
                    'title' => $link['title'],
                    'url' => $link['url'],
                    'image' => $link['image'],
                ]);
            }
        }
        
        // Process user assignments - use [] as default rather than null
        $userIds = isset($validated['user_ids']) ? $validated['user_ids'] : [];
        info('Processing user_ids: ' . json_encode($userIds));
        $userSyncResult = $this->safeDetachAndAttach(
            $card, 
            'assignedUsers', 
            $userIds, 
            \App\Models\User::class, 
            'Users sync (create)'
        );
        
        if (!$userSyncResult) {
            info('WARNING: User sync failed for new card ID: ' . $card->id);
        }
        
        // Process font assignments - use [] as default rather than null
        $fontIds = isset($validated['font_ids']) ? $validated['font_ids'] : [];
        info('Processing font_ids: ' . json_encode($fontIds));
        $fontSyncResult = $this->safeDetachAndAttach(
            $card, 
            'fonts', 
            $fontIds, 
            \App\Models\Font::class, 
            'Fonts sync (create)'
        );
        
        if (!$fontSyncResult) {
            info('WARNING: Font sync failed for new card ID: ' . $card->id);
        }
        
        // Make sure to refresh the card from the database to get the latest data
        $card = WhiteboardCard::with(['additionalLinks', 'user', 'project', 'assignedUsers', 'fonts'])
            ->findOrFail($card->id);
            
        // Verify the assigned users were loaded
        if (!$card->relationLoaded('assignedUsers')) {
            info('WARNING: assignedUsers relation not loaded after create for card ID: ' . $card->id);
            $card->load('assignedUsers');
        }
        
        // Add explicit check for assigned users
        $assignedUsers = $card->assignedUsers()->get();
        info('FINAL ASSIGNED USERS via relation: ' . json_encode($assignedUsers));
        
        // Construct a response that explicitly includes the relationships
        $response = $card->toArray();
        $response['debug_info'] = [
            'assigned_users_count' => $assignedUsers->count(),
            'assigned_user_ids' => $assignedUsers->pluck('id')->toArray()
        ];
        
        // Log the final state
        info('New card created: ' . json_encode($card->toArray()));
        
        return response()->json($response, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(WhiteboardCard $card)
    {
        // Check if user is admin or if card was created by an admin
        $isAdmin = $this->isAdmin();
        $cardCreatedByAdmin = ($card->user && 
            ($card->user->is_admin || in_array($card->user->login, Config::get('vermont.admin_user_logins', []))));
        
        if (!$isAdmin && !$cardCreatedByAdmin) {
            abort(403, 'Unauthorized action.');
        }
        
        // Load relationships
        $card->load('additionalLinks', 'user', 'project', 'assignedUsers', 'fonts');
        
        return response()->json($card);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WhiteboardCard $card)
    {
        // Only admins can update cards
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        // Log the entire request data for debugging
        info('Update request for card ID: ' . $card->id);
        info('Request data: ' . json_encode($request->all()));
        
        // Handle the case where user_ids is present but empty
        if ($request->has('user_ids') && empty($request->user_ids)) {
            // If an empty array is submitted, we'll keep it as an empty array
            info('Empty user_ids array received in request');
        } else if (!$request->has('user_ids')) {
            // If user_ids is not present in the request, we'll default to empty array
            info('No user_ids field in request, defaulting to empty array');
            $request->merge(['user_ids' => []]);
        } else {
            // Otherwise, we have user_ids in the request
            info('user_ids in request: ' . json_encode($request->user_ids));
        }
        
        // Get valid status slugs
        $validStatuses = \App\Models\WhiteboardCardStatus::pluck('slug')->toArray();
        
        $validated = $request->validate([
            'headline' => 'required|string|max:255',
            'description' => 'nullable|string',
            'link' => 'nullable|url|max:255',
            'link_target' => 'nullable|string|in:_blank,_self',
            'card_date' => 'required|date',
            'deadline' => 'nullable|date',
            'is_active' => 'boolean',
            'status' => 'nullable|string|in:' . implode(',', $validStatuses),
            'project_id' => 'nullable|exists:projects,id',
            'image' => 'nullable|string|max:255',
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
            'font_ids' => 'nullable|array',
            'font_ids.*' => 'exists:fonts,id',
            'additional_links' => 'nullable|array',
            'additional_links.*.id' => 'nullable|exists:whiteboard_card_links,id',
            'additional_links.*.title' => 'required|string|max:255',
            'additional_links.*.url' => 'required|url|max:255',
            'additional_links.*.image' => 'nullable|string|max:255',
        ]);
        
        // Log validation result
        info('Validated data: ' . json_encode($validated));
        
        // Log the is_active state specifically
        $is_active_value = $validated['is_active'] ?? $card->is_active;
        info('is_active before update: ' . ($card->is_active ? 'true' : 'false'));
        info('is_active in request: ' . (isset($validated['is_active']) ? ($validated['is_active'] ? 'true' : 'false') : 'not set'));
        info('is_active to be applied: ' . ($is_active_value ? 'true' : 'false'));
        
        // Update card
        $card->update([
            'headline' => $validated['headline'],
            'description' => $validated['description'],
            'link' => $validated['link'],
            'link_target' => $validated['link_target'],
            'card_date' => $validated['card_date'],
            'deadline' => $validated['deadline'],
            'is_active' => $is_active_value,
            'status' => $validated['status'],
            'project_id' => $validated['project_id'],
            'image' => $validated['image'],
        ]);
        
        $card->refresh();
        info('is_active after update: ' . ($card->is_active ? 'true' : 'false'));
        
        // Update additional links
        if (isset($validated['additional_links']) && is_array($validated['additional_links'])) {
            // First, get all existing link IDs
            $existingLinkIds = $card->additionalLinks->pluck('id')->toArray();
            $updatedLinkIds = [];
            
            foreach ($validated['additional_links'] as $link) {
                if (isset($link['id'])) {
                    // Update existing link
                    WhiteboardCardLink::where('id', $link['id'])
                        ->update([
                            'title' => $link['title'],
                            'url' => $link['url'],
                            'image' => $link['image'],
                        ]);
                    $updatedLinkIds[] = $link['id'];
                } else {
                    // Create new link
                    $newLink = WhiteboardCardLink::create([
                        'whiteboard_card_id' => $card->id,
                        'title' => $link['title'],
                        'url' => $link['url'],
                        'image' => $link['image'],
                    ]);
                    $updatedLinkIds[] = $newLink->id;
                }
            }
            
            // Delete links that weren't in the update
            $linksToDelete = array_diff($existingLinkIds, $updatedLinkIds);
            if (!empty($linksToDelete)) {
                WhiteboardCardLink::whereIn('id', $linksToDelete)->delete();
            }
        }
        
        // Process user assignments - use [] as default rather than null
        $userIds = isset($validated['user_ids']) ? $validated['user_ids'] : [];
        info('Processing user_ids: ' . json_encode($userIds));
        $userSyncResult = $this->safeDetachAndAttach(
            $card, 
            'assignedUsers', 
            $userIds, 
            \App\Models\User::class, 
            'Users sync'
        );
        
        if (!$userSyncResult) {
            info('WARNING: User sync failed for card ID: ' . $card->id);
        }
        
        // Process font assignments - use [] as default rather than null
        $fontIds = isset($validated['font_ids']) ? $validated['font_ids'] : [];
        info('Processing font_ids: ' . json_encode($fontIds));
        $fontSyncResult = $this->safeDetachAndAttach(
            $card, 
            'fonts', 
            $fontIds, 
            \App\Models\Font::class, 
            'Fonts sync'
        );
        
        if (!$fontSyncResult) {
            info('WARNING: Font sync failed for card ID: ' . $card->id);
        }
        
        // Make sure to refresh the card from the database to get the latest data
        $card = WhiteboardCard::with(['additionalLinks', 'user', 'project', 'assignedUsers', 'fonts'])
            ->findOrFail($card->id);
            
        // Verify the assigned users were loaded
        if (!$card->relationLoaded('assignedUsers')) {
            info('WARNING: assignedUsers relation not loaded after update for card ID: ' . $card->id);
            $card->load('assignedUsers');
        }
        
        // Add explicit check for assigned users
        $assignedUsers = $card->assignedUsers()->get();
        info('FINAL ASSIGNED USERS via relation: ' . json_encode($assignedUsers));
        
        // Construct a response that explicitly includes the relationships
        $response = $card->toArray();
        $response['debug_info'] = [
            'assigned_users_count' => $assignedUsers->count(),
            'assigned_user_ids' => $assignedUsers->pluck('id')->toArray()
        ];
        
        // Log the final state
        info('Card after update: ' . json_encode($card->toArray()));
        
        return response()->json($response);
    }

    /**
     * Toggle the card active status.
     */
    public function toggleStatus(WhiteboardCard $card)
    {
        // Only admins can toggle card status
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        // Toggle the active status
        $card->is_active = !$card->is_active;
        
        // Update the status field based on is_active
        if ($card->is_active) {
            $activeStatus = \App\Models\WhiteboardCardStatus::where('is_default', true)->first();
            $card->status = $activeStatus ? $activeStatus->slug : \App\Models\WhiteboardCard::DEFAULT_STATUS;
        } else {
            $archivedStatus = \App\Models\WhiteboardCardStatus::where('slug', 'archived')->first();
            $card->status = $archivedStatus ? $archivedStatus->slug : 'archived';
        }
        
        $card->save();
        
        return response()->json([
            'id' => $card->id,
            'is_active' => $card->is_active,
            'status' => $card->status,
            'message' => 'Card status toggled successfully.'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WhiteboardCard $card)
    {
        // Only admins can delete cards
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $card->delete();
        
        return response()->json(null, 204);
    }

    /**
     * Get all projects for dropdown in the card form.
     */
    public function getProjects()
    {
        // Check if user is admin
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $projects = \App\Models\Project::orderBy('name')->get(['id', 'name']);
        
        return response()->json($projects);
    }
    
    /**
     * Get all users for dropdown in the card form.
     */
    public function getUsers()
    {
        // Check if user is admin
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $users = \App\Models\User::orderBy('name')->get(['id', 'name', 'email']);
        
        return response()->json($users);
    }
    
    /**
     * Get all fonts for dropdown in the card form.
     */
    public function getFonts()
    {
        // Check if user is admin
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $fonts = \App\Models\Font::orderBy('name')->get(['id', 'name']);
        
        return response()->json($fonts);
    }
    
    /**
     * Get all available logo images for the card form.
     */
    public function getLogoImages()
    {
        // Check if user is admin
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $images = [];
        
        // Get logos from the logos directory - SVG files
        $logosPath = public_path('img/logos');
        if (is_dir($logosPath)) {
            $logoFiles = array_values(array_filter(scandir($logosPath), function($item) use ($logosPath) {
                return !is_dir($logosPath . '/' . $item) && pathinfo($item, PATHINFO_EXTENSION) === 'svg';
            }));
            
            foreach ($logoFiles as $file) {
                $images[] = [
                    'name' => pathinfo($file, PATHINFO_FILENAME),
                    'path' => $file,
                    'type' => 'logo'
                ];
            }
        }
        
        // Get logos from the category directory - only PNG files
        $categoryPath = public_path('img/category');
        if (is_dir($categoryPath)) {
            $categoryFiles = array_values(array_filter(scandir($categoryPath), function($item) use ($categoryPath) {
                return !is_dir($categoryPath . '/' . $item) && pathinfo($item, PATHINFO_EXTENSION) === 'png';
            }));
            
            foreach ($categoryFiles as $file) {
                $images[] = [
                    'name' => pathinfo($file, PATHINFO_FILENAME),
                    'path' => 'logos/category/' . $file,
                    'type' => 'category'
                ];
            }
        }
        
        return response()->json($images);
    }
    
    /**
     * Render the whiteboard view.
     */
    public function whiteboard()
    {
        // Everyone can see the whiteboard, but only admins can manage it
        $isAdmin = $this->isAdmin();
        
        return view('main.whiteboard', compact('isAdmin'));
    }

    /**
     * Helper method to safely detach and reattach related models.
     */
    private function safeDetachAndAttach($card, $relationName, $ids, $modelClass, $logPrefix = '')
    {
        // Log for debugging
        info($logPrefix . ' for card ID: ' . $card->id);
        info($logPrefix . ' IDs received: ' . json_encode($ids));
        
        try {
            // Convert all IDs to integers and filter out any invalid values
            $intIds = array_filter(array_map(function($id) {
                $intVal = filter_var($id, FILTER_VALIDATE_INT);
                return $intVal !== false ? (int)$id : null;
            }, $ids));
            
            info($logPrefix . ' IDs after filtering: ' . json_encode($intIds));
            
            // Validate that all IDs exist in the database
            $validIds = [];
            foreach ($intIds as $id) {
                // Skip null or zero values
                if (!$id) {
                    continue;
                }
                
                // Fully qualify the table name to avoid ambiguous column name issues
                $tableName = (new $modelClass)->getTable();
                $exists = $modelClass::from($tableName)->where("$tableName.id", $id)->exists();
                
                if ($exists) {
                    $validIds[] = $id;
                    info($logPrefix . ' valid ID: ' . $id);
                } else {
                    info($logPrefix . ' invalid or non-existent ID: ' . $id);
                }
            }
            
            info($logPrefix . ' final valid IDs for sync: ' . json_encode($validIds));
            
            // Add direct SQL debugging
            if ($relationName === 'assignedUsers') {
                // Check existing records in pivot table
                $existingPivotRecords = DB::table('whiteboard_card_user')
                    ->where('whiteboard_card_id', $card->id)
                    ->get();
                    
                info('EXISTING PIVOT RECORDS before sync: ' . json_encode($existingPivotRecords));
                
                // Execute the sync
                $card->$relationName()->sync($validIds);
                
                // Check if the sync actually worked
                $newPivotRecords = DB::table('whiteboard_card_user')
                    ->where('whiteboard_card_id', $card->id)
                    ->get();
                    
                info('NEW PIVOT RECORDS after sync: ' . json_encode($newPivotRecords));
            } else {
                // Use sync instead of detach/attach for better reliability
                $method = $relationName;
                $card->$method()->sync($validIds);
            }
            
            // Force reload the relationship
            $card->load($relationName);
            
            // When verifying what was synced, specify the column explicitly to avoid ambiguity
            $synced = $card->$relationName()
                ->select($card->$relationName()->getRelated()->getTable() . '.id')
                ->pluck($card->$relationName()->getRelated()->getTable() . '.id')
                ->toArray();
            
            info($logPrefix . ' synced IDs confirmed from DB: ' . json_encode($synced));
            
            return true;
        } catch (\Exception $e) {
            info($logPrefix . ' error: ' . $e->getMessage());
            info($logPrefix . ' stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Change the status of the specified card.
     */
    public function changeStatus(Request $request, WhiteboardCard $card)
    {
        // Only admins can change card status
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        // Get valid status slugs
        $validStatuses = \App\Models\WhiteboardCardStatus::pluck('slug')->toArray();
        
        $validated = $request->validate([
            'status' => 'required|string|in:' . implode(',', $validStatuses),
        ]);
        
        // Update the status
        $card->status = $validated['status'];
        
        // Get the status model
        $statusModel = \App\Models\WhiteboardCardStatus::where('slug', $validated['status'])->first();
        
        // Update is_active based on status - only 'active' status cards are considered active
        $card->is_active = ($statusModel && $statusModel->is_default);
        
        $card->save();
        
        return response()->json([
            'status' => $card->status,
            'is_active' => $card->is_active,
            'message' => 'Card status updated successfully.'
        ]);
    }

    /**
     * Reorder cards within the same status.
     */
    public function reorderCards(Request $request)
    {
        // Only admins can reorder cards
        if (!$this->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }
        
        $validated = $request->validate([
            'status' => 'required|string',
            'cards' => 'required|array',
            'cards.*.id' => 'required|exists:whiteboard_cards,id',
            'cards.*.order' => 'required|integer|min:1',
        ]);
        
        // Process the reordering in a database transaction
        DB::beginTransaction();
        
        try {
            // Update each card's order
            foreach ($validated['cards'] as $cardData) {
                WhiteboardCard::where('id', $cardData['id'])
                    ->update(['order' => $cardData['order']]);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Cards reordered successfully.',
                'status' => $validated['status']
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => 'Error reordering cards: ' . $e->getMessage()
            ], 500);
        }
    }
}
