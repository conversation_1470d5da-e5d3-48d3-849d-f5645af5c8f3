<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LanguageController extends Controller
{
    public function switchLang($locale)
    {
        Log::info("Language switch requested to: " . $locale);
        if (in_array($locale, ['en', 'sk'])) {
            session()->put('locale', $locale);
            Log::info("Language switched to: " . $locale);
        } else {
            Log::info("Invalid locale requested: " . $locale);
        }
        return redirect()->back();
    }
}
