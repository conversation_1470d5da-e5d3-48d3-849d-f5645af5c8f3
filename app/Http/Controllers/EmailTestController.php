<?php

namespace App\Http\Controllers;

use App\Mail\TestEmail;
use App\Mail\WelcomeEmail;
use App\Mail\NotificationEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

class EmailTestController extends Controller
{
    /**
     * Show the email testing dashboard.
     */
    public function index()
    {
        return view('emails.test-dashboard');
    }

    /**
     * Send a test email.
     */
    public function sendTestEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'name' => 'nullable|string',
            'message' => 'nullable|string',
        ]);

        $data = [
            'name' => $request->input('name', Auth::user()->name),
            'message' => $request->input('message', 'This is a test message.')
        ];

        Mail::to($request->email)->send(new TestEmail($data));

        return back()->with('success', 'Test email sent successfully!');
    }

    /**
     * Send a welcome email.
     */
    public function sendWelcomeEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        // Use the authenticated user or create a temporary user object
        $user = Auth::user() ?? (object) [
            'name' => $request->input('name', 'Test User'),
            'email' => $request->email
        ];

        Mail::to($request->email)->send(new WelcomeEmail($user));

        return back()->with('success', 'Welcome email sent successfully!');
    }

    /**
     * Send a notification email.
     */
    public function sendNotificationEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'title' => 'required|string',
            'message' => 'required|string',
            'action_text' => 'nullable|string',
            'action_url' => 'nullable|string|url',
        ]);

        Mail::to($request->email)->send(new NotificationEmail(
            $request->title,
            $request->message,
            $request->action_text,
            $request->action_url
        ));

        return back()->with('success', 'Notification email sent successfully!');
    }
} 