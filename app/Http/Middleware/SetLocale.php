<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        if (session()->has('locale')) {
            $locale = session()->get('locale');
            app()->setLocale($locale);
            Log::info("Setting locale to: " . $locale);
        } else {
            $defaultLocale = config('app.locale', 'en');
            app()->setLocale($defaultLocale);
            Log::info("No locale in session, using default: " . $defaultLocale);
        }
        return $next($request);
    }
}
