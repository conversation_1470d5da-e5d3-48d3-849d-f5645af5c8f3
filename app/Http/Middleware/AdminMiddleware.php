<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        Log::info('AdminMiddleware running', [
            'user_authenticated' => auth()->check(),
            'user_login' => auth()->check() ? auth()->user()->login : 'not_authenticated',
            'admin_logins' => config('vermont.admin_user_logins', []),
        ]);

        // Check if user is authenticated
        if (!auth()->check()) {
            Log::warning('User not authenticated in AdminMiddleware');
            return redirect()->route('login')
                ->with('error', 'You must be logged in to access the admin area.');
        }
        
        // Check if user is an admin
        $userLogin = auth()->user()->login;
        $adminLogins = config('vermont.admin_user_logins', []);
        
        if (empty($adminLogins)) {
            Log::error('Admin logins array is empty in config');
        }

        if (!in_array($userLogin, $adminLogins)) {
            Log::warning("User {$userLogin} tried to access admin area but is not in admin list", [
                'user_login' => $userLogin,
                'admin_logins' => $adminLogins
            ]);
            
            return redirect()->route('hello')
                ->with('error', 'You do not have permission to access the admin area.');
        }
        
        Log::info("User {$userLogin} granted admin access");
        return $next($request);
    }
} 