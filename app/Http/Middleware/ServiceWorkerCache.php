<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ServiceWorkerCache
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // For POST/PUT/DELETE requests, prevent caching
        if (!$request->isMethod('GET')) {
            $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
            $response->headers->set('Service-Worker-Allowed', 'false');
        }

        // For AJAX/JSON requests, ensure proper content type
        if ($request->ajax() || $request->wantsJson()) {
            $response->headers->set('Content-Type', 'application/json');
            $response->headers->set('X-Content-Type-Options', 'nosniff');
        }

        return $response;
    }
} 