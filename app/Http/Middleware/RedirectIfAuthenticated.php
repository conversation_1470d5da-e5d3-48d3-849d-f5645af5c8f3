<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    public function handle(Request $request, Closure $next, ...$guards)
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // Get user's preferred landing page, default to projectassets if not set
                $user = Auth::guard($guard)->user();
                $defaultPage = $user->default_page ?? 'projectassets';
                
                // Map the page preferences to routes
                $routes = [
                    'projectassets' => 'projectassets',
                    'profile' => 'profile',
                    'tools' => 'tools'
                ];
                
                return redirect()->route($routes[$defaultPage] ?? 'projectassets');
            }
        }

        return $next($request);
    }
} 