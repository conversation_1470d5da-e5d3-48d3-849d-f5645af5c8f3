<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class WhiteboardCard extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'headline',
        'description',
        'link',
        'link_target',
        'card_date',
        'deadline',
        'is_active',
        'status', // Status slug
        'project_id',
        'image'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'card_date' => 'date',
        'deadline' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Default status
     */
    const DEFAULT_STATUS = 'active';

    /**
     * The relationships that should be eager loaded.
     *
     * @var array
     */
    protected $with = ['assignedUsers', 'additionalLinks', 'fonts', 'project', 'cardStatus'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['assigned_users_count', 'fonts_count'];

    /**
     * Get the user that owns the card.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the additional links for the card.
     */
    public function additionalLinks(): HasMany
    {
        return $this->hasMany(WhiteboardCardLink::class);
    }

    /**
     * Get the project associated with the card.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the users associated with the card.
     */
    public function assignedUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'whiteboard_card_user', 'whiteboard_card_id', 'user_id');
    }

    /**
     * Get the fonts associated with the card.
     */
    public function fonts(): BelongsToMany
    {
        return $this->belongsToMany(Font::class, 'whiteboard_card_font', 'whiteboard_card_id', 'font_id');
    }

    /**
     * Get the number of assigned users.
     */
    public function getAssignedUsersCountAttribute()
    {
        return $this->assignedUsers()->count();
    }

    /**
     * Get the number of associated fonts.
     */
    public function getFontsCountAttribute()
    {
        return $this->fonts()->count();
    }

    /**
     * Get the status record associated with this card.
     */
    public function cardStatus(): BelongsTo
    {
        return $this->belongsTo(WhiteboardCardStatus::class, 'status', 'slug');
    }

    /**
     * Get all available statuses.
     */
    public static function getStatuses(): array
    {
        return WhiteboardCardStatus::orderBy('order')->get()->pluck('name', 'slug')->toArray();
    }
    
    /**
     * Get the default status slug.
     */
    public static function getDefaultStatus(): string
    {
        $defaultStatus = WhiteboardCardStatus::where('is_default', true)->first();
        return $defaultStatus ? $defaultStatus->slug : self::DEFAULT_STATUS;
    }
}
