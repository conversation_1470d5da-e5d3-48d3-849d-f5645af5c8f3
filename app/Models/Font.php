<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Auth;

class Font extends Model
{
    protected $guarded = [];

    protected $casts = [
        'icons_data' => 'array',
        'active' => 'boolean',
    ];

    protected $appends = ['icons_count', 'icons_max_updated_at', 'new_icons_count'];

    /**
     * Get the users that have access to this font.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'font_user');
    }

    /**
     * Get the whiteboard cards associated with this font.
     */
    public function whiteboardCards(): BelongsToMany
    {
        return $this->belongsToMany(WhiteboardCard::class, 'whiteboard_card_font', 'font_id', 'whiteboard_card_id');
    }

    public function getIconsCountAttribute()
    {
        return is_array($this->icons_data) ? count($this->icons_data) : 0;
    }

    public function getIconsMaxUpdatedAtAttribute()
    {
        if (!is_array($this->icons_data) || empty($this->icons_data)) {
            return null;
        }

        $maxUpdated = null;
        foreach ($this->icons_data as $icon) {
            $updated = $icon['updated_at'] ?? null;
            if ($updated && (!$maxUpdated || $updated > $maxUpdated)) {
                $maxUpdated = $updated;
            }
        }

        return $maxUpdated;
    }

    public function getNewIconsCountAttribute()
    {
        if (!Auth::check()) {
            return 0;
        }

        // Since we removed the user_font_views table, we'll need to implement this
        // differently. For now, return 0 or implement a new way to track new icons
        return 0;
    }

    /**
     * Get all icons for this font.
     *
     * @return array
     */
    public function getIcons()
    {
        return $this->icons_data ?? [];
    }

    /**
     * Add an icon to the font.
     *
     * @param array $iconData
     * @return void
     */
    public function addIcon(array $iconData)
    {
        $icons = $this->icons_data ?? [];
        $icons[] = array_merge($iconData, [
            'created_at' => now()->toDateTimeString(),
            'updated_at' => now()->toDateTimeString(),
        ]);
        $this->icons_data = $icons;
        $this->save();
    }

    /**
     * Update an icon in the font.
     *
     * @param string $cssClass
     * @param array $iconData
     * @return void
     */
    public function updateIcon(string $cssClass, array $iconData)
    {
        $icons = $this->icons_data ?? [];
        $updated = false;
        
        foreach ($icons as $key => $icon) {
            if ($icon['css_class'] === $cssClass) {
                // Initialize tags array if it doesn't exist
                if (!isset($icon['tags'])) {
                    $icon['tags'] = [];
                }
                
                // Handle specific case for tags
                if (isset($iconData['tags'])) {
                    $icon['tags'] = $iconData['tags'];
                }
                
                // Update other icon properties
                $icons[$key] = array_merge($icon, $iconData, [
                    'updated_at' => now()->toDateTimeString(),
                ]);
                
                $updated = true;
                break;
            }
        }
        
        // If the icon doesn't exist yet, create it
        if (!$updated && isset($iconData['css_class'])) {
            // Make sure tags is an array
            if (!isset($iconData['tags'])) {
                $iconData['tags'] = [];
            }
            
            $icons[] = array_merge($iconData, [
                'created_at' => now()->toDateTimeString(),
                'updated_at' => now()->toDateTimeString(),
            ]);
        }
        
        $this->icons_data = $icons;
        $this->save();
        
        // Update the tag count in the cache
        \Cache::forget('font_'.$this->id.'_tag_count');
    }

    /**
     * Remove an icon from the font.
     *
     * @param string $cssClass
     * @return void
     */
    public function removeIcon(string $cssClass)
    {
        $icons = $this->icons_data ?? [];
        $this->icons_data = array_filter($icons, function($icon) use ($cssClass) {
            return $icon['css_class'] !== $cssClass;
        });
        $this->save();
    }

    /**
     * Initialize tags array for all icons that don't have it
     * 
     * @return int Number of icons updated
     */
    public function initializeIconTags()
    {
        $icons = $this->icons_data ?? [];
        $updatedCount = 0;
        
        foreach ($icons as $key => $icon) {
            if (!isset($icon['tags'])) {
                $icons[$key]['tags'] = [];
                $updatedCount++;
            } else if (!is_array($icon['tags'])) {
                $icons[$key]['tags'] = [];
                $updatedCount++;
            }
        }
        
        if ($updatedCount > 0) {
            $this->icons_data = $icons;
            $this->save();
        }
        
        return $updatedCount;
    }
}
