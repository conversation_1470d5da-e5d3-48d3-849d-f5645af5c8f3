<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FontIcon extends Model
{
    protected $guarded = [];

    public function font(): BelongsTo
    {
        return $this->belongsTo(Font::class, 'font_id');
    }

    /**
     * Get the users that have access to this icon.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'font_icon_user', 'font_icon_id', 'user_id');
    }
}
