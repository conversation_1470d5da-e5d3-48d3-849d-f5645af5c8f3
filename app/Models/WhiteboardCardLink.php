<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhiteboardCardLink extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'whiteboard_card_id',
        'title',
        'url',
        'image'
    ];

    /**
     * Get the card that the link belongs to.
     */
    public function card(): BelongsTo
    {
        return $this->belongsTo(WhiteboardCard::class, 'whiteboard_card_id');
    }
}
