<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasAvatar;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Bookmark;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasAvatar;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'login',
        'name',
        'surname',
        'birthday',
        'email',
        'password',
        'default_page',
        'is_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'custom_greetings' => 'array',
        'birthday' => 'date',
    ];

    /**
     * The relationships that should be loaded with the model.
     *
     * @var array
     */
    protected $with = ['projects', 'fonts'];

    /**
     * Get the projects that belong to the user.
     */
    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_user');
    }

    /**
     * Get the fonts that belong to the user.
     */
    public function fonts(): BelongsToMany
    {
        return $this->belongsToMany(Font::class, 'font_user');
    }

    /**
     * Get the whiteboard cards that belong to the user (created by this user).
     */
    public function whiteboardCards()
    {
        return $this->hasMany(WhiteboardCard::class);
    }

    /**
     * Get the whiteboard cards that are assigned to this user.
     */
    public function assignedWhiteboardCards(): BelongsToMany
    {
        return $this->belongsToMany(WhiteboardCard::class, 'whiteboard_card_user', 'user_id', 'whiteboard_card_id');
    }

    /**
     * Get the bookmarks that belong to the user.
     */
    public function bookmarks(): HasMany
    {
        return $this->hasMany(Bookmark::class);
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->is_admin;
    }
}
