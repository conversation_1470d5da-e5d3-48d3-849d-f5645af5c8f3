<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChangelogEntry extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'version',
        'title',
        'description',
        'release_date',
        'changes',
        'image_path',
        'is_major',
        'order'
    ];

    protected $casts = [
        'release_date' => 'date',
        'changes' => 'array',
        'is_major' => 'boolean',
        'order' => 'integer'
    ];
}
