<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use App\Models\Font;

class FontHelper
{
    /**
     * Get all fonts with their icons.
     *
     * @param  bool  $activeOnly  Only return active fonts
     * @param  int  $cacheTime  Cache time in seconds (default: 1 hour)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllFonts(bool $activeOnly = true, int $cacheTime = 3600)
    {
        $cacheKey = 'fonts.' . ($activeOnly ? 'active' : 'all');
        
        return Cache::remember($cacheKey, $cacheTime, function () use ($activeOnly) {
            $query = Font::query();
            
            if ($activeOnly) {
                $query->where('active', true);
            }
            
            return $query->orderBy('order_nr')->get();
        });
    }

    /**
     * Get font icons from the JSON data.
     *
     * @param  \App\Models\Font  $font
     * @return array
     */
    public static function getFontIcons(Font $font)
    {
        if (!$font->icons_data) {
            return [];
        }

        return json_decode($font->icons_data, true) ?? [];
    }

    /**
     * Clear font cache.
     *
     * @return void
     */
    public static function clearCache()
    {
        Cache::forget('fonts.active');
        Cache::forget('fonts.all');
    }
}