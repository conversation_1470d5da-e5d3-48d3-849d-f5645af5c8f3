<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class SyncAdminStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize the is_admin database field with the config values for all users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Synchronizing admin status for all users...');
        
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        $this->info('Admin logins in config: ' . implode(', ', $adminLogins));
        
        $users = User::all();
        $updatedCount = 0;
        
        foreach ($users as $user) {
            $isAdminInConfig = in_array($user->login, $adminLogins);
            
            if ($user->is_admin !== $isAdminInConfig) {
                $user->is_admin = $isAdminInConfig;
                $user->save();
                
                $status = $isAdminInConfig ? 'admin' : 'non-admin';
                $this->line("Updated user {$user->login} to {$status}");
                $updatedCount++;
            }
        }
        
        $this->info("Completed! {$updatedCount} users were updated out of {$users->count()} total users.");
        
        return Command::SUCCESS;
    }
}
