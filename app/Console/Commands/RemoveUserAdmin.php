<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class RemoveUserAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:remove {login : The login of the user to remove admin rights from}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove admin rights from a specific user by login ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $login = $this->argument('login');
        
        // Find the user
        $user = User::where('login', $login)->first();
        
        if (!$user) {
            $this->error("User with login '{$login}' not found.");
            return Command::FAILURE;
        }
        
        // Check if user is an admin in the config
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        
        if (!in_array($login, $adminLogins)) {
            $this->info("User '{$login}' is not in the admin list in the config file.");
        } else {
            // Remove user from admin list in the config
            $this->info("Removing user '{$login}' from admin list in config file...");
            
            // Get the file path
            $configPath = config_path('vermont.php');
            
            if (!File::exists($configPath)) {
                $this->error("Config file vermont.php not found.");
                return Command::FAILURE;
            }
            
            $configContent = File::get($configPath);
            
            // Find and remove the user from the admin_user_logins array
            $patterns = [
                "/'admin_user_logins'\s*=>\s*\[(.*?){$login}(.*?)\]/s",
                "/'{$login}',?\s*(?:\\/\\/.*?)?(\n|\r\n)/"
            ];
            
            $replacements = [
                function ($matches) use ($login) {
                    // Get the existing content
                    $before = $matches[1];
                    $after = $matches[2];
                    
                    // Remove the login entry
                    $cleanBefore = preg_replace("/'{$login}',?\s*(?:\/\/.*?)?(\n|\r\n)/", '', $before);
                    $cleanAfter = preg_replace("/'{$login}',?\s*(?:\/\/.*?)?(\n|\r\n)/", '', $after);
                    
                    return "'admin_user_logins' => [" . $cleanBefore . $cleanAfter . "]";
                }
            ];
            
            $newConfigContent = preg_replace_callback($patterns[0], $replacements[0], $configContent);
            
            if ($newConfigContent !== $configContent) {
                File::put($configPath, $newConfigContent);
                $this->info("Removed user '{$login}' from admin_user_logins in config/vermont.php");
            } else {
                $this->error("Could not find and remove '{$login}' from admin_user_logins in the config file.");
                return Command::FAILURE;
            }
        }
        
        // Update database
        $user->is_admin = false;
        $user->save();
        
        $this->info("Admin rights removed from user '{$login}' in the database.");
        $this->info("Remember to clear config cache with 'php artisan config:clear' if running in production.");
        
        return Command::SUCCESS;
    }
}
