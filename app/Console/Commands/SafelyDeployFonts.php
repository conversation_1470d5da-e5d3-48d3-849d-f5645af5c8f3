<?php

namespace App\Console\Commands;

use App\Models\Font;
use Database\Seeders\FontSeeder;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class SafelyDeployFonts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:safely-deploy-fonts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Safely deploy the fonts table and its data, ensuring no data loss in production';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting safe deployment of fonts table...');

        // Step 1: Check if table exists and create it if it doesn't
        if (!Schema::hasTable('fonts')) {
            $this->info('Creating fonts table from scratch...');
            Schema::create('fonts', function (Blueprint $table) {
                $table->id();
                $table->string('hash', 20)->nullable();
                $table->string('name');
                $table->string('path')->nullable();
                $table->string('css_class');
                $table->string('route_name', 255)->nullable();
                $table->string('icon', 255)->nullable();
                $table->string('webfont', 999)->nullable();
                $table->unsignedInteger('order_nr')->default(1);
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
            $this->info('Fonts table created successfully!');
        } else {
            $this->info('Fonts table already exists, checking for missing columns...');
            
            // Step 2: Add any missing columns
            Schema::table('fonts', function (Blueprint $table) {
                if (!Schema::hasColumn('fonts', 'hash')) {
                    $this->info('Adding hash column...');
                    $table->string('hash', 20)->nullable()->after('id');
                }
                if (!Schema::hasColumn('fonts', 'name')) {
                    $this->info('Adding name column...');
                    $table->string('name')->after('hash');
                }
                if (!Schema::hasColumn('fonts', 'path')) {
                    $this->info('Adding path column...');
                    $table->string('path')->nullable()->after('name');
                }
                if (!Schema::hasColumn('fonts', 'css_class')) {
                    $this->info('Adding css_class column...');
                    $table->string('css_class')->after('path');
                }
                if (!Schema::hasColumn('fonts', 'route_name')) {
                    $this->info('Adding route_name column...');
                    $table->string('route_name', 255)->nullable()->after('css_class');
                }
                if (!Schema::hasColumn('fonts', 'icon')) {
                    $this->info('Adding icon column...');
                    $table->string('icon', 255)->nullable()->after('route_name');
                }
                if (!Schema::hasColumn('fonts', 'webfont')) {
                    $this->info('Adding webfont column...');
                    $table->string('webfont', 999)->nullable()->after('icon');
                }
                if (!Schema::hasColumn('fonts', 'order_nr')) {
                    $this->info('Adding order_nr column...');
                    $table->unsignedInteger('order_nr')->default(1)->after('webfont');
                }
                if (!Schema::hasColumn('fonts', 'active')) {
                    $this->info('Adding active column...');
                    $table->boolean('active')->default(true)->after('order_nr');
                }
            });
            
            $this->info('Column check completed!');
        }

        // Step 3: Run the font seeder to ensure all fonts are updated...
        $this->info('Running font seeder to ensure all fonts are updated...');
        try {
            $seeder = new FontSeeder();
            $seeder->run();
            $this->info('Font seeder completed successfully!');
            
            $fontCount = Font::count();
            $this->info("Total fonts in database: {$fontCount}");
            
            $orphanedFonts = Font::whereNotIn('hash', array_column($seeder->getFonts(), 'hash'))->get();
            if ($orphanedFonts->count() > 0) {
                $this->warn("Found {$orphanedFonts->count()} fonts in the database that are not in the seeder:");
                foreach ($orphanedFonts as $font) {
                    $this->line("- {$font->name} (hash: {$font->hash})");
                }
                
                if ($this->confirm('Do you want to remove these fonts?')) {
                    Font::whereNotIn('hash', array_column($seeder->getFonts(), 'hash'))->delete();
                    $this->info('Orphaned fonts removed successfully.');
                } else {
                    $this->info('Orphaned fonts kept in the database.');
                }
            }
        } catch (\Exception $e) {
            $this->error('Error running font seeder: ' . $e->getMessage());
            return 1;
        }

        $this->info('Font deployment completed successfully!');
        return 0;
    }
}
