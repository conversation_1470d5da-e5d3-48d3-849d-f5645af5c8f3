<?php

namespace App\Console\Commands;

use App\Models\Font;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\LazyCollection;

class ParseFontScssFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'font:parse-files {--font=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parse font icons';

    public function handle(): void
    {
        if($this->option('font')) {
            $fonts = Font::where('id', $this->option('font'))->get();
        }else{
            $fonts = Font::all();
        }

        if(!$fonts->count()) {
            $this->error('No fonts in Database');
            return;
        }

        foreach($fonts as $font) {
            $this->parseFont($font);
        }
    }

    private function parseFont(Font $font): void
    {
        $this->info('Font: '.$font->name);

        $file = resource_path('sass/'.$font->path);

        if(!file_exists($file)) {
            $this->error('-- Font file "'.$font->path.'" does not exists!');
            return;
        }

        $this->info('-- Parsing font file');

        $iconsCollection = $this->getIconsCollectionFromFontFile($file, $font);

        $this->info('-- Nr. of found icons in font: '.$iconsCollection->count());

        $this->updateFontIcons($font, $iconsCollection);
    }

    private function getIconsCollectionFromFontFile(string $file, Font $font):Collection
    {
        $now = now()->toDateTimeString();
        $iconsCollection = collect();

        LazyCollection::make(function () use ($file) {
            $handle = fopen($file, 'r');

            while (($line = fgets($handle)) !== false) {
                yield $line;
            }
        })->each(function($line) use (&$iconsCollection, $font, $now) {
            if($line === PHP_EOL) {
                return false;
            }

            $explodedByColon = explode(':', $line);

            if(count($explodedByColon) !== 2) {
                return true;
            }

            if($explodedByColon[0][0] !== '$') {
                return true;
            }

            $cssContentExploded = explode(';', $explodedByColon[1]);

            $icon = [
                'css_class' => substr($explodedByColon[0], 1),
                'css_content' => str_replace([' ', '"'], '', $cssContentExploded[0]),
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $iconsCollection->add($icon);

            return true;
        });

        return $iconsCollection;
    }

    private function updateFontIcons(Font $font, Collection $iconsCollection)
    {
        $now = $iconsCollection->first()['updated_at'];
        $currentIcons = collect($font->icons_data ?? []);

        // Update or add new icons
        $updatedIcons = $iconsCollection->map(function($newIcon) use ($currentIcons) {
            $existingIcon = $currentIcons->firstWhere('css_class', $newIcon['css_class']);
            
            if ($existingIcon) {
                // Preserve all existing icon data, especially tags
                // Only update the css_content and updated_at timestamp
                $newIcon = array_merge(
                    $existingIcon,
                    [
                        'css_content' => $newIcon['css_content'],
                        'updated_at' => $newIcon['updated_at']
                    ]
                );
                
                // If content hasn't changed, also preserve the original created_at
                if ($existingIcon['css_content'] === $newIcon['css_content']) {
                    $newIcon['created_at'] = $existingIcon['created_at'];
                }
            }
            
            return $newIcon;
        })->toArray();

        // Update the font with new icons
        $font->icons_data = $updatedIcons;
        $font->save();

        $this->info('-- Updated icons for font: ' . $font->name);
    }
}
