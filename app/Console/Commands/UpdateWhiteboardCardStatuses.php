<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WhiteboardCard;
use App\Models\WhiteboardCardStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateWhiteboardCardStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whiteboard:update-card-statuses 
                            {--reset : Reset all cards to default status}
                            {--inactive-to-archived : Move inactive cards to archived status}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update whiteboard card statuses based on their is_active field';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting whiteboard card status update...');
        
        // Get total count of cards for logging
        $totalCards = DB::table('whiteboard_cards')->count();
        $this->info("Found {$totalCards} whiteboard cards");
        
        if ($this->option('reset')) {
            // Get the default status
            $defaultStatus = WhiteboardCardStatus::where('is_default', true)->first();
            $defaultSlug = $defaultStatus ? $defaultStatus->slug : WhiteboardCard::DEFAULT_STATUS;
            
            // Reset all cards to default status
            $updatedCount = DB::table('whiteboard_cards')
                ->update(['status' => $defaultSlug]);
                
            $this->info("Reset {$updatedCount} cards to '{$defaultSlug}' status");
            Log::info("Reset {$updatedCount} whiteboard cards to '{$defaultSlug}' status");
        } elseif ($this->option('inactive-to-archived')) {
            // Update only inactive cards to archived status
            $inactiveCount = DB::table('whiteboard_cards')
                ->where('is_active', false)
                ->update(['status' => 'archived']);
                
            $this->info("Updated {$inactiveCount} inactive cards to 'archived' status");
            Log::info("Updated {$inactiveCount} inactive whiteboard cards to 'archived' status");
        } else {
            // Standard update based on is_active field
            // Update active cards
            $activeCount = DB::table('whiteboard_cards')
                ->where('is_active', true)
                ->update(['status' => 'active']);
            
            $this->info("Updated {$activeCount} active cards to 'active' status");
            
            // Update inactive cards
            $inactiveCount = DB::table('whiteboard_cards')
                ->where('is_active', false)
                ->update(['status' => 'archived']);
            
            $this->info("Updated {$inactiveCount} inactive cards to 'archived' status");
            
            // Log the results
            Log::info("Whiteboard card statuses updated: {$activeCount} active, {$inactiveCount} archived");
        }
        
        $this->info('Whiteboard card statuses updated successfully!');
        
        return Command::SUCCESS;
    }
}
