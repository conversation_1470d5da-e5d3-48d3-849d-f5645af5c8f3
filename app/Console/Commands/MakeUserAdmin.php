<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class MakeUserAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:make {login : The login of the user to make admin}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make a specific user an admin by login ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $login = $this->argument('login');
        
        // Find the user
        $user = User::where('login', $login)->first();
        
        if (!$user) {
            $this->error("User with login '{$login}' not found.");
            return Command::FAILURE;
        }
        
        // Check if user is already an admin in the config
        $adminLogins = Config::get('vermont.admin_user_logins', []);
        
        if (in_array($login, $adminLogins)) {
            $this->info("User '{$login}' is already in the admin list in the config file.");
        } else {
            // Add user to admin list in the config
            $this->info("Adding user '{$login}' to admin list in config file...");
            
            // Get the file path
            $configPath = config_path('vermont.php');
            
            if (!File::exists($configPath)) {
                $this->error("Config file vermont.php not found.");
                return Command::FAILURE;
            }
            
            $configContent = File::get($configPath);
            
            // Find the admin_user_logins array
            $pattern = "/'admin_user_logins'\s*=>\s*\[(.*?)\]/s";
            if (preg_match($pattern, $configContent, $matches)) {
                $currentAdminList = $matches[1];
                $newAdminList = $currentAdminList . ($currentAdminList ? ',' : '') . "\n        '{$login}', // Added by MakeUserAdmin command";
                $newConfigContent = str_replace($matches[0], "'admin_user_logins' => [$newAdminList]", $configContent);
                
                File::put($configPath, $newConfigContent);
                $this->info("Added user '{$login}' to admin_user_logins in config/vermont.php");
            } else {
                $this->error("Could not find admin_user_logins array in config file.");
                return Command::FAILURE;
            }
        }
        
        // Update database
        $user->is_admin = true;
        $user->save();
        
        $this->info("User '{$login}' has been successfully made an admin in the database.");
        $this->info("Remember to clear config cache with 'php artisan config:clear' if running in production.");
        
        return Command::SUCCESS;
    }
}
