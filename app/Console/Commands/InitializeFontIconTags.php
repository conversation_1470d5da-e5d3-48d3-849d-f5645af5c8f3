<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Font;

class InitializeFontIconTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fonts:initialize-tags';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize tags array for all icons in all fonts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing tags for all fonts...');
        
        $fonts = Font::all();
        $totalUpdated = 0;
        
        foreach ($fonts as $font) {
            $this->info("Processing font: {$font->name}");
            $updatedCount = $font->initializeIconTags();
            $totalUpdated += $updatedCount;
            $this->info("- Updated {$updatedCount} icons in {$font->name}");
        }
        
        $this->info("Completed! Total icons updated: {$totalUpdated}");
        
        return 0;
    }
} 