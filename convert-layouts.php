<?php

$directory = __DIR__ . '/resources/views';
$files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));

foreach ($files as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $content = file_get_contents($file->getPathname());
        
        // Skip if file doesn't use x-application-layout
        if (strpos($content, '<x-application-layout>') === false) {
            continue;
        }
        
        // Replace the component tags with extends
        $content = str_replace('<x-application-layout>', "@extends('layouts.application')\n\n@section('content')", $content);
        $content = str_replace('</x-application-layout>', '@endsection', $content);
        
        // Save the file
        file_put_contents($file->getPathname(), $content);
        
        echo "Converted: " . $file->getPathname() . "\n";
    }
}

echo "Done!\n"; 