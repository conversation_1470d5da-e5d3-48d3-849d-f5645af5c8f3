#!/bin/bash

# Exit script if any command fails
set -e

echo "Starting deployment process..."

# Step 1: Pull the latest changes
echo "Pulling latest changes from repository..."
git pull origin main

# Step 2: Install/update dependencies
echo "Installing/updating composer dependencies..."
composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

# Step 3: Put the application in maintenance mode
echo "Putting application in maintenance mode..."
php artisan down --refresh=15

# Step 4: Clear caches
echo "Clearing application caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Step 5: Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Step 6: Specifically run the admin:sync command to ensure admin roles are synchronized
echo "Synchronizing admin roles..."
php artisan admin:sync

# Step 7: Optimize the application
echo "Optimizing application..."
php artisan optimize

# Step 8: Restart Queue workers (if using Laravel queues)
echo "Restarting queue workers..."
php artisan queue:restart

# Step 9: Take the application out of maintenance mode
echo "Taking application out of maintenance mode..."
php artisan up

echo "Deployment completed successfully!" 