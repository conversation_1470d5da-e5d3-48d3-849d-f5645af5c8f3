#!/bin/bash

# Exit script if any command fails
set -e

echo "Starting deployment process with Laravel Sail..."

# Step 1: Pull the latest changes
echo "Pulling latest changes from repository..."
git pull origin main

# Step 2: Install/update dependencies
echo "Installing/updating composer dependencies..."
./vendor/bin/sail composer install

# Step 3: Put the application in maintenance mode
echo "Putting application in maintenance mode..."
./vendor/bin/sail artisan down --refresh=15

# Step 4: Clear caches
echo "Clearing application caches..."
./vendor/bin/sail artisan config:clear
./vendor/bin/sail artisan route:clear
./vendor/bin/sail artisan view:clear
./vendor/bin/sail artisan cache:clear

# Step 5: Run database migrations
echo "Running database migrations..."
./vendor/bin/sail artisan migrate --force

# Step 6: Specifically run the admin:sync command to ensure admin roles are synchronized
echo "Synchronizing admin roles..."
./vendor/bin/sail artisan admin:sync

# Step 7: Optimize the application
echo "Optimizing application..."
./vendor/bin/sail artisan optimize

# Step 8: Restart Queue workers (if using Lara<PERSON> queues)
echo "Restarting queue workers..."
./vendor/bin/sail artisan queue:restart

# Step 9: Take the application out of maintenance mode
echo "Taking application out of maintenance mode..."
./vendor/bin/sail artisan up

echo "Deployment completed successfully!" 