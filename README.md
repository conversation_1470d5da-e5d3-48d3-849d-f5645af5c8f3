
<div align="center">
  <img src="public/img/git.png" alt="Design Dev Logo" width="750"/>
  <p>A Design.vermont-dev.eu</p>
</div>

## 🌟 Overview
A modern web application built with Laravel, featuring a robust frontend architecture with custom JavaScript plugins and UI components.

## 🛠️ Requirements
-  PHP >= 8.0
-  Laravel Framework
-  Node.js & NPM
-  Modern web browser with JavaScript enabled

## 📚 Custom Font & Icon Implementation Guide

### 🔤 Font Setup

#### 1. Create Font Face
First, define your font face in CSS/SCSS:

```scss
@font-face {
    font-family: 'MyFont';
    src: url('/fonts/myfont.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
}
```

### 2. Define Base Font Class
Create a base class with essential properties.

## 🗂️ File Structure
```
resources/
├── sass/
│   └── your-font.scss     # SCSS definitions
├── views/
│   └── icons/            # Icon display templates
public/
└── assets/
    └── icons/
        └── your-font/
            ├── your-font.woff2
            └── style.css
```

## ⭐ Best Practices

### 1. 📝 Naming Convention
- Use consistent prefix for all icons
- Keep names descriptive and lowercase
- Use hyphens for spaces

### 2. 🔢 Unicode Values
- Start from `\ea01` and increment sequentially
- Maintain unique values for each icon
- Document used values

### 3. ♿ Accessibility
- Include `aria-hidden` attribute
- Provide text alternatives
- Use semantic HTML

### 4. ⚡ Performance
- Use WOFF2 format for better compression
- Implement proper caching
- Load icons asynchronously when possible

## 🔤 Custom Typeface Implementation

### Gant Modern V2 CE
Our primary typeface with multiple weights and features. Reference implementation:

```css
@font-face {
    font-family: 'Gant-Modern-bold';
    src: url("../fonts-typeface/GantModernV2-Bold.woff2") format('woff2');
    font-weight: 900;
    display: swap;
}
```

### Tailwind CSS Integration
Add custom fonts to your Tailwind configuration:

```javascript
module.exports = {
  theme: {
    extend: {
      fontFamily: {
        'gant-modern-bold': ['Gant-Modern-bold', 'sans-serif'],
        'gant-modern-medium': ['Gant-Modern-medium', 'sans-serif'],
        'gant-modern-regular': ['Gant-Modern-regular', 'sans-serif'],
        'gant-modern-light': ['Gant-Modern-light', 'sans-serif'],
      }
    }
  }
}
```

### Usage in Templates
```html
<h1 class="gant-modern-bold text-5xl lg:text-6xl xl:text-8xl leading-none tracking-tighter">
    Your Heading
</h1>
<p class="gant-modern-regular">
    Regular text content
</p>
```

### Font Features
The typeface includes advanced OpenType features:

```css
:root {
    --gant-modern-v2-bold-aalt: "aalt" off;
    --gant-modern-v2-bold-case: "case" off;
    --gant-modern-v2-bold-frac: "frac" off;
    --gant-modern-v2-bold-ordn: "ordn" off;
    --gant-modern-v2-bold-salt: "salt" off;
    --gant-modern-v2-bold-ss01: "ss01" off;
    --gant-modern-v2-bold-sups: "sups" off;
    --gant-modern-v2-bold-cpsp: "cpsp" off;
}
```

### Performance Optimization
1. **Font Display Strategy**
   - Use `font-display: swap` for better performance
   - Implement preloading for critical fonts
   - Optimize with WOFF2 format

2. **File Structure**
```
fonts-typeface/
├── GantModernV2CE/
│   ├── GantModernV2CE-Bold.woff2
│   ├── GantModernV2CE-Medium.woff2
│   ├── GantModernV2CE-Regular.woff2
│   └── GantModernV2CE-Light.woff2
└── GantSerif/
    └── GantSerif-MediumCondensed.woff2
```

### Browser Support
- Modern browsers: Full support with OpenType features
- Legacy browsers: Basic font-family fallback
- IE11: System font stack fallback

### Best Practices
1. Always specify fallback fonts
2. Use appropriate font-weight values
3. Implement progressive loading
4. Consider subsetting for different languages
5. Monitor performance metrics

## 👥 Credits
Built and maintained by Vermont Development Team

## 📄 License
© 2024 Vermont Development. All rights reserved.
