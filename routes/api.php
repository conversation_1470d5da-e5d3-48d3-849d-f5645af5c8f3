<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\IconController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\FontController;

Route::post('/locale', function (Request $request) {
    $locale = $request->input('locale');
    
    if (!in_array($locale, ['en', 'sk'])) {
        return response()->json(['success' => false, 'message' => 'Invalid locale'], 400);
    }

    session(['locale' => $locale]);
    App::setLocale($locale);

    return response()->json(['success' => true]);
});

Route::middleware('auth')->group(function () {
    Route::post('/profile', [ProfileController::class, 'update'])->name('profile.update');
});

// API routes for admin data
Route::middleware(['auth'])->prefix('admin')->group(function () {
    Route::get('/users', 'Admin\Api\UsersController@index');
    Route::get('/projects', 'Admin\Api\ProjectsController@index');
});

// Get all icons
Route::get('/font-icons', [IconController::class, 'getAllIcons']);

// Get icons for a specific font
Route::get('/font-icons/{fontName}', [IconController::class, 'getIconsByFont']);

// Get icons for a specific font by ID
Route::get('/fonts/{id}/icons', [IconController::class, 'getIconsByFontId']);

// Update icon tags
Route::post('/fonts/{id}/update-icon-tags', [IconController::class, 'updateIconTags']);

// Tag management endpoints
// Temporarily remove auth middleware to diagnose the issue
Route::get('/tags', [TagController::class, 'getAllTags']);
Route::get('/tags/list', [TagController::class, 'getTagList']);
Route::post('/tags/rename', [TagController::class, 'renameTag']);
Route::post('/tags/delete', [TagController::class, 'deleteTag']);
Route::post('/tags/create', [TagController::class, 'create']);
Route::post('/tags/usage', [TagController::class, 'getTagUsage']);

// Font lookup by name
Route::get('/fonts/lookup', [FontController::class, 'lookupByName']); 