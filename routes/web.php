<?php

use App\Http\Controllers\FontController;
use App\Http\Controllers\ClassesController;
use App\Http\Controllers\IconController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\UserFontViewController;
use App\Http\Controllers\FontDownloadController;
use App\Http\Controllers\EmailTestController;
use Illuminate\Support\Facades\Route;
use App\Models\Font;
use App\Http\Controllers\ToolsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\DatatablePreviewController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\Tools\SellThroughController;
use App\Http\Controllers\WhiteboardCardController;
use App\Http\Controllers\BookmarkController;
use App\Http\Controllers\BookmarkLinkController;

// Public routes
Route::get('language/{locale}', [LanguageController::class, 'switchLang'])->name('language.switch');
Route::view('/errors/404', 'errors.404')->name('errors.404');
Route::view('/errors/500', 'errors.500')->name('errors.500');

// Public icon search route
Route::get('/iconsearch', function () {
    return view('icons.iconsearch');
})->name('iconsearch');

// Public icon routes - already outside auth middleware
Route::get('/font-icons', [FontController::class, 'getIcons']);
Route::get('/font-icons/{font}', [FontController::class, 'getIconsByFont']);

// Protected routes
Route::group(['middleware' => ['auth']], function(){

    // Direct database routes for testing
    Route::get('/admin/database/export', [App\Http\Controllers\Admin\DatabaseController::class, 'export'])->name('admin.database.export');
    Route::post('/admin/database/import', [App\Http\Controllers\Admin\DatabaseController::class, 'import'])->name('admin.database.import');

    Route::get('/', function () {
        try {
            // Use a simpler query that's more compatible with different DB drivers
            $fonts = Font::select('*')->orderBy('order_nr')->get();
            return view('main.hello', compact('fonts'));
        } catch (\Exception $e) {
            \Log::error("Error in hello route: " . $e->getMessage());
            return view('main.hello', ['fonts' => []]);
        }
    })->name('hello');
   
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
    Route::post('/profile', [ProfileController::class, 'update'])->name('profile.update');

    // Sell through tool
    Route::get('/tools/sell-through', SellThroughController::class)->name('tools.sell-through');
    Route::get('/tools/sell-through/download/blade', [SellThroughController::class, 'downloadBlade'])->name('tools.sell-through.download.blade');
    Route::get('/tools/sell-through/download/vue', [SellThroughController::class, 'downloadVue'])->name('tools.sell-through.download.vue');

    // Datatable component preview route
    Route::get('/datatable-preview', DatatablePreviewController::class)->name('datatable.preview');
    Route::get('/datatable-preview/download/component', [DatatablePreviewController::class, 'downloadComponent'])->name('datatable.download.component');
    Route::get('/datatable-preview/download/blade', [DatatablePreviewController::class, 'downloadBlade'])->name('datatable.download.blade');
    Route::get('/datatable-preview/download/controller', [DatatablePreviewController::class, 'downloadController'])->name('datatable.download.controller');
    Route::get('/datatable-preview/download/package', [DatatablePreviewController::class, 'downloadPackage'])->name('datatable.download.package');

    // Update font icon tags
    Route::post('/fonts/{fontIdentifier}/update-icon-tags', [FontController::class, 'updateIconTags'])->name('fonts.update-icon-tags');

    Route::get('/new', function () {
        $fonts = Font::withCount('icons')->withMax('icons', 'updated_at')->orderBy('order_nr')->get();// Fetch all fonts
        return view('new', compact('fonts'));
    })->name('new');

    Route::get('/projectassets', function () {
        $fonts = Font::all();
        return view('main.projectassets', compact('fonts'));
    })->name('projectassets');

    // Add a route for viewing a specific font's icons
    Route::get('/projectassets/{font}', function (Font $font) {
        return view('main.icon-font', compact('font'));
    })->name('projectassets.font');

    Route::get('/animations', function () {
        return view('main.animations');
    })->name('animations');

    Route::get('/code', function () {
        return view('main.code');
    })->name('code');

    Route::get('/tools', function () {
        return view('main.tools');
    })->name('tools');
    
    // Whiteboard routes
    Route::get('/whiteboard', [WhiteboardCardController::class, 'whiteboard'])->name('whiteboard');

    // Admin-only API routes for whiteboard cards
    Route::middleware(['auth'])->prefix('api')->group(function () {
        Route::get('/whiteboard-cards', [WhiteboardCardController::class, 'index']);
        Route::post('/whiteboard-cards', [WhiteboardCardController::class, 'store']);
        Route::get('/whiteboard-cards/{card}', [WhiteboardCardController::class, 'show']);
        Route::put('/whiteboard-cards/{card}', [WhiteboardCardController::class, 'update']);
        Route::delete('/whiteboard-cards/{card}', [WhiteboardCardController::class, 'destroy']);
        Route::patch('/whiteboard-cards/{card}/toggle-status', [WhiteboardCardController::class, 'toggleStatus']);
        Route::patch('/whiteboard-cards/{card}/status', [WhiteboardCardController::class, 'changeStatus']);
        Route::post('/whiteboard-cards/reorder', [WhiteboardCardController::class, 'reorderCards']);
        Route::get('/projects-for-whiteboard', [WhiteboardCardController::class, 'getProjects']);
        Route::get('/users-for-whiteboard', [WhiteboardCardController::class, 'getUsers']);
        Route::get('/fonts-for-whiteboard', [WhiteboardCardController::class, 'getFonts']);
        Route::get('/logos-for-whiteboard', [WhiteboardCardController::class, 'getLogoImages']);
        
        // Bookmark API routes
        Route::get('/bookmarks', [BookmarkController::class, 'index']);
        Route::post('/bookmarks', [BookmarkController::class, 'store']);
        Route::put('/bookmarks/{bookmark}', [BookmarkController::class, 'update']);
        Route::delete('/bookmarks/{bookmark}', [BookmarkController::class, 'destroy']);

        // Bookmark Link API routes
        Route::post('/bookmark-links', [BookmarkLinkController::class, 'store']);
        Route::put('/bookmark-links/{link}', [BookmarkLinkController::class, 'update']);
        Route::delete('/bookmark-links/{link}', [BookmarkLinkController::class, 'destroy']);
    });

    // Email testing routes
    Route::controller(EmailTestController::class)->prefix('emails')->name('emails.')->group(function () {
        Route::get('/test', 'index')->name('test');
        Route::post('/send/test', 'sendTestEmail')->name('send.test');
        Route::post('/send/welcome', 'sendWelcomeEmail')->name('send.welcome');
        Route::post('/send/notification', 'sendNotificationEmail')->name('send.notification');
    });

    $iconRoutes = [
        'claims', 'eshop', 'matrix', 'og', 'cdb', 'retail', 'tos', 'wms', 'category', 'design', 'hrms'
    ];

    foreach ($iconRoutes as $icon) {
        // API endpoint for JSON data
        Route::get("/api/{$icon}_icons", function () use ($icon) {
            try {
                $font = \App\Models\Font::where('hash', $icon)->first();
                if (!$font) {
                    return response()->json(['error' => 'Font not found'], 404);
                }
                
                $icons = collect($font->icons_data ?? [])->map(function ($icon) use ($font) {
                    return [
                        'id' => $icon['id'] ?? null,
                        'css_class' => $icon['css_class'] ?? null,
                        'css_content' => $icon['css_content'] ?? null,
                        'tags' => $icon['tags'] ?? [],
                        'font' => [
                            'name' => $font->name,
                            'css_class' => $font->css_class
                        ]
                    ];
                });

                return response()->json($icons);
            } catch (\Exception $e) {
                return response()->json(['error' => $e->getMessage()], 500);
            }
        })->name("api.{$icon}_icons");
        
        // Route for blade template - use the specific blade template for each icon type
        Route::get("/{$icon}_icons", function () use ($icon) {
            try {
                $font = \App\Models\Font::where('hash', $icon)->first();
                if (!$font) {
                    abort(404, 'Font not found');
                }
                
                return view("icons.{$icon}_icons", compact('font'));
            } catch (\Exception $e) {
                \Log::error("Error in {$icon}_icons route: " . $e->getMessage());
                abort(500, 'An error occurred while loading the icons');
            }
        })->name("{$icon}_icons");
    }


    Route::get('/classes', [App\Http\Controllers\ClassesController::class, 'index'])->name('classes');
    Route::get('/download-scss', [App\Http\Controllers\ClassesController::class, 'downloadScss'])->name('download-scss');
    Route::get('/playground', [App\Http\Controllers\PlaygroundController::class, 'index'])->name('playground');

    Route::group(['prefix' => 'admin', 'as' => 'admin.'], function(){
        Route::controller(\App\Http\Controllers\AdminController::class)->group(function () {
            Route::get('/', 'index')->name('index');
            Route::get('/update-font-icons/{font}', 'updateFontIcons')->name('update_font_icons');
            Route::get('/run-font-seeder', 'runFontSeeder')->name('run_font_seeder');
            Route::get('/svg-animation', 'svgAnimation')->name('svg-animation');
            Route::get('/fonts/{font}/manage', 'fontIconManagement')->name('fonts.manage');
            Route::get('/tags', 'tagManager')->name('tags-manager');
            
            // User management routes
            Route::get('/users', 'users')->name('users');
            Route::get('/users/{user}', 'userDetail')->name('users.detail');
            Route::patch('/users/{user}/toggle-status', 'toggleUserStatus')->name('users.toggle-status');
            Route::delete('/users/{user}/delete', 'deleteUser')->name('users.delete');
            Route::patch('/users/{user}/update-role', 'updateUserRole')->name('users.update-role');
            Route::patch('/users/{user}/greetings', 'updateUserGreetings')->name('users.update-greetings');
            Route::patch('/users/{user}/update-email', 'updateUserEmail')->name('users.update-email');
            Route::patch('/users/{user}/update-birthday', 'updateUserBirthday')->name('users.update-birthday');
            
            // User projects and icons management routes
            Route::post('/users/{user}/projects', [UserController::class, 'addProject'])->name('users.projects.add');
            Route::delete('/users/{user}/projects/{project}', [UserController::class, 'removeProject'])->name('users.projects.remove');
            Route::post('/users/{user}/fonts', [UserController::class, 'addFont'])->name('users.fonts.add');
            Route::delete('/users/{user}/fonts/{font}', [UserController::class, 'removeFont'])->name('users.fonts.remove');
            
            // New admin routes
            Route::get('/typography', 'typography')->name('typography');
            Route::get('/assets', 'assets')->name('assets');
            Route::get('/tools', 'tools')->name('tools');
            Route::get('/code', 'code')->name('code');
            Route::get('/animations', 'animations')->name('animations');
            Route::get('/icons', 'icons')->name('icons');
            Route::get('/activities', 'activities')->name('activities');
        });

        // Changelog management routes
        Route::controller(\App\Http\Controllers\Admin\ChangelogController::class)->group(function () {
            Route::get('/changelog', 'index')->name('changelog');
            Route::get('/changelog/create', 'create')->name('changelog.create');
            Route::post('/changelog', 'store')->name('changelog.store');
            Route::get('/changelog/{post}', 'show')->name('changelog.show');
            Route::put('/changelog/{post}', 'update')->name('changelog.update');
            Route::delete('/changelog/{post}', 'destroy')->name('changelog.destroy');
        });
        

        // Project management routes
        Route::controller(\App\Http\Controllers\Admin\ProjectController::class)->group(function () {
            Route::get('/projects', 'index')->name('projects');
            Route::get('/projects/create', 'create')->name('projects.create');
            Route::post('/projects', 'store')->name('projects.store');
            Route::put('/projects/{project}', 'update')->name('projects.update');
            Route::delete('/projects/{project}', 'destroy')->name('projects.destroy');
            Route::get('/projects/{project}', 'show')->name('projects.show');
        });
    });

    Route::post('/fonts/{fontIdentifier}/view', [UserFontViewController::class, 'updateView'])->name('fonts.update-view');
    
    // Font documentation routes
    Route::get('/fonts/gant', function () {
        return view('components.fonts.gant-fonts');
    })->name('fonts.gant');

    Route::get('/fonts/nunito', function () {
        return view('components.fonts.nunito-sans');
    })->name('fonts.nunito');

    Route::get('/download_font/{filename}', [FontDownloadController::class, 'download'])->name('download_font');
    
    Route::get('/tools/download/{filename}', [ToolsController::class, 'download'])->name('tools.download');
    
    // SVG download route
    Route::get('/svg/download/{filename}', [ToolsController::class, 'downloadSvg'])->name('svg.download');
    
    // Tool Routes - individual definitions
    Route::get('/tools/eshop-resizer', function () {
        return view('tools.eshop-resize-photoshop.eshop-resizer');
    })->name('tools.eshop-resizer');
    Route::get('/tools/eshop-resizer/documentation', [ToolsController::class, 'eshopResizerDocumentation'])->name('tools.eshop-resizer.documentation');

    Route::get('/tools/insta-resizer', function () {
        return view('tools.insta-resize-photoshop.insta-resizer');
    })->name('tools.insta-resizer');
    Route::get('/tools/insta-resizer/documentation', [ToolsController::class, 'instaResizerDocumentation'])->name('tools.insta-resizer.documentation');

    Route::get('/tools/svg-animation', function () {
        return view('tools.svg-animation.svg-animation');
    })->name('tools.svg-animation');
    Route::get('/tools/svg-animation/documentation', [ToolsController::class, 'svgAnimationDocumentation'])->name('tools.svg-animation.documentation');

    Route::get('/tools/ordergroup-image', function () {
        return view('tools.ordergroup-image.ordergroup-image');
    })->name('tools.ordergroup-image');
    Route::get('/tools/ordergroup-image/documentation', [ToolsController::class, 'ordergroupImageDocumentation'])->name('tools.ordergroup-image.documentation');

    Route::get('/tools/qr-generator', [ToolsController::class, 'qrGeneratorIndex'])->name('tools.qr-generator');
    Route::get('/tools/qr-generator/documentation', [ToolsController::class, 'qrGeneratorDocumentation'])->name('tools.qr-generator.documentation');

    Route::get('/tools/barcode-generator', function () {
        return view('tools.barcode-generator.barcode-generator');
    })->name('tools.barcode-generator');
    Route::get('/tools/barcode-generator/documentation', function () {
        return view('tools.barcode-generator.barcode-generator-documentation');
    })->name('tools.barcode-generator.documentation');

    Route::get('/tools/svg-optimizer', [ToolsController::class, 'svgOptimizerIndex'])->name('tools.svg-optimizer');
    Route::get('/tools/svg-optimizer/documentation', [ToolsController::class, 'svgOptimizerDocumentation'])->name('tools.svg-optimizer.documentation');

    Route::get('/changelog', [App\Http\Controllers\ChangelogController::class, 'index'])->name('changelog');

    // Public project details route
    Route::get('/projects/{project}', [ProjectController::class, 'show'])
        ->name('projects.show');
});

// Protect API routes
Route::middleware(['auth'])->group(function () {
    Route::get('/fonts', [FontController::class, 'index']);
    Route::get('/api/icons', [IconController::class, 'getIcons'])->name('api.icons');
    Route::get('/api/scss/{name}', [FontController::class, 'getScssContent'])->name('scss.content');
    Route::get('/api/svgs/{folder}', [ToolsController::class, 'getSvgsFromFolder'])->name('api.svgs');
    
    // QR Code Generator API routes
    Route::post('/api/qr-generator/generate', [App\Http\Controllers\QrGeneratorController::class, 'generate'])->name('api.qr.generate');
    Route::post('/api/qr-generator/download', [App\Http\Controllers\QrGeneratorController::class, 'download'])->name('api.qr.download');
});

// Admin routes - using AdminMiddleware class directly
Route::middleware(['auth', \App\Http\Middleware\AdminMiddleware::class])
    ->prefix('admin')
    ->name('admin.')
    ->group(function () {
        // Admin routes
        Route::get('/', [App\Http\Controllers\AdminController::class, 'index'])->name('index');
        Route::get('/users', [App\Http\Controllers\AdminController::class, 'users'])->name('users');
        Route::get('/icons', [App\Http\Controllers\AdminController::class, 'icons'])->name('icons');
        Route::get('/update-font-icons/{font}', [App\Http\Controllers\AdminController::class, 'updateFontIcons'])->name('update_font_icons');
        Route::get('/run-font-seeder', [App\Http\Controllers\AdminController::class, 'runFontSeeder'])->name('run_font_seeder');
        Route::get('/initialize-font-tags', [App\Http\Controllers\AdminController::class, 'initializeFontTags'])->name('initialize_font_tags');
        
        // Project management routes
        Route::controller(\App\Http\Controllers\Admin\ProjectController::class)->group(function () {
            Route::get('/projects', 'index')->name('projects');
            Route::get('/projects/create', 'create')->name('projects.create');
            Route::post('/projects', 'store')->name('projects.store');
            Route::put('/projects/{project}', 'update')->name('projects.update');
            Route::delete('/projects/{project}', 'destroy')->name('projects.destroy');
            Route::get('/projects/{project}', 'show')->name('projects.show');
        });
        
        // Project Status management routes
        Route::controller(\App\Http\Controllers\Admin\ProjectStatusController::class)->group(function () {
            Route::get('/project-statuses', 'index')->name('project-statuses.index');
            Route::post('/project-statuses', 'store')->name('project-statuses.store');
            Route::put('/project-statuses/{id}', 'update')->name('project-statuses.update');
            Route::delete('/project-statuses/{id}', 'destroy')->name('project-statuses.destroy');
            Route::post('/project-statuses/order', 'updateOrder')->name('project-statuses.order');
        });

        // Tag management
        Route::get('/tags', [App\Http\Controllers\AdminController::class, 'tagManager'])->name('tags');

        // Database management routes
        Route::get('/database/export', [App\Http\Controllers\Admin\DatabaseController::class, 'export'])->name('database.export');
        Route::post('/database/import', [App\Http\Controllers\Admin\DatabaseController::class, 'import'])->name('database.import');

        // Whiteboard Status management routes
        Route::get('/whiteboard-statuses', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'index'])
            ->name('whiteboard-statuses.index');
        Route::post('/whiteboard-statuses', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'store'])
            ->name('whiteboard-statuses.store');
        Route::put('/whiteboard-statuses/{id}', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'update'])
            ->name('whiteboard-statuses.update');
        Route::delete('/whiteboard-statuses/{id}', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'destroy'])
            ->name('whiteboard-statuses.destroy');
        Route::post('/whiteboard-statuses/order', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'updateOrder'])
            ->name('whiteboard-statuses.order');
    });

// Add a route in your routes/web.php file:
Route::post('/icons/{id}/update-tags', [IconController::class, 'updateTags']);

// Add tag management API routes
Route::middleware(['auth'])->prefix('api')->group(function () {
    Route::get('/tags', [TagController::class, 'getAllTags']);
    Route::get('/tags/list', [TagController::class, 'getTagList']);
    Route::post('/tags/rename', [TagController::class, 'renameTag']);
    Route::post('/tags/delete', [TagController::class, 'deleteTag']);
    Route::post('/tags/usage', [TagController::class, 'getTagUsage']);
});

// Add this route definition
Route::get('/icons/search', 'App\Http\Controllers\IconController@search')->name('icons.search');

// Add routes for all your other icon pages...

// Debug routes
Route::get('/debug/font-icons', function () {
    return view('debug.font-icons');
})->name('debug.font-icons');

// API endpoint for whiteboard statuses
Route::middleware(['auth'])->prefix('api')->group(function () {
    Route::get('/whiteboard-statuses', [\App\Http\Controllers\Admin\WhiteboardCardStatusController::class, 'list']);
});

require __DIR__.'/auth.php';
