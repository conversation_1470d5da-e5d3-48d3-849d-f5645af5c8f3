<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'login' => 'test',
            'name' => 'Test',
            'surname' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'active' => true,
        ]);

        // Run Font seeder
        $this->call(FontSeeder::class);
        
        // Run Admin Status seeder to sync admin status
        $this->call(AdminStatusSeeder::class);
        
        // Update whiteboard card statuses
        $this->call(UpdateWhiteboardCardStatusesSeeder::class);
    }
}
