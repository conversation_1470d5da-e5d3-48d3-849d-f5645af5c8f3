<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\WhiteboardCard;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateWhiteboardCardStatusesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * Maps existing whiteboard cards to appropriate statuses based on is_active field:
     * - active cards (is_active=true) -> 'active' status
     * - inactive cards (is_active=false) -> 'archived' status
     */
    public function run(): void
    {
        // Get total count of cards for logging
        $totalCards = DB::table('whiteboard_cards')->count();
        $this->command->info("Updating status for {$totalCards} whiteboard cards");
        
        // Update active cards
        $activeCount = DB::table('whiteboard_cards')
            ->where('is_active', true)
            ->update(['status' => 'active']);
        
        $this->command->info("Updated {$activeCount} active cards");
        
        // Update inactive cards
        $inactiveCount = DB::table('whiteboard_cards')
            ->where('is_active', false)
            ->update(['status' => 'archived']);
        
        $this->command->info("Updated {$inactiveCount} inactive cards");
        
        // Log the results
        Log::info("Whiteboard card statuses updated: {$activeCount} active, {$inactiveCount} archived");
        
        $this->command->info('Whiteboard card statuses updated successfully!');
    }
}
