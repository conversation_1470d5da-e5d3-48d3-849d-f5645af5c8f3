<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Models\User;

class AdminStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if the column exists before proceeding
        if (Schema::hasColumn('users', 'is_admin')) {
            $message = 'Running admin:sync command to synchronize admin status...';
            
            // Show message in console if command property is available, otherwise log it
            if (isset($this->command)) {
                $this->command->info($message);
            } else {
                Log::info($message);
            }
            
            Artisan::call('admin:sync');
            
            // Display the output from the admin:sync command
            $output = Artisan::output();
            
            if (isset($this->command)) {
                $this->command->info($output);
            } else {
                Log::info('Admin sync command output: ' . $output);
            }
            
            // Alternative method using direct implementation
            $message = 'Directly synchronizing admin status...';
            
            if (isset($this->command)) {
                $this->command->info($message);
            } else {
                Log::info($message);
            }
            
            $adminLogins = Config::get('vermont.admin_user_logins', []);
            $updatedCount = 0;
            
            User::chunk(100, function ($users) use (&$updatedCount, $adminLogins) {
                foreach ($users as $user) {
                    $isAdminInConfig = in_array($user->login, $adminLogins);
                    
                    if ($user->is_admin !== $isAdminInConfig) {
                        $user->is_admin = $isAdminInConfig;
                        $user->save();
                        $updatedCount++;
                    }
                }
            });
            
            $message = "Updated {$updatedCount} users with correct admin status.";
            
            if (isset($this->command)) {
                $this->command->info($message);
            } else {
                Log::info($message);
            }
        } else {
            $message = 'The is_admin column does not exist in the users table. Please run the migration that adds this column first.';
            
            if (isset($this->command)) {
                $this->command->error($message);
            } else {
                Log::error($message);
            }
        }
    }
}
