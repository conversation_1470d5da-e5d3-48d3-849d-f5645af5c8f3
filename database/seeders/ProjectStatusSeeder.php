<?php

namespace Database\Seeders;

use App\Models\ProjectStatus;
use Illuminate\Database\Seeder;

class ProjectStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            [
                'name' => 'Development',
                'color' => '#6366F1', // indigo-500
                'icon' => 'design-code',
                'sort_order' => 1,
            ],
            [
                'name' => 'Stage',
                'color' => '#3B82F6', // blue-500
                'icon' => 'design-lab',
                'sort_order' => 2,
            ],
            [
                'name' => 'Produkcia',
                'color' => '#10B981', // green-500
                'icon' => 'design-monitor-check',
                'sort_order' => 3,
            ],
            [
                'name' => 'Refactor',
                'color' => '#8B5CF6', // purple-500
                'icon' => 'design-swap',
                'sort_order' => 4,
            ],
            [
                'name' => 'Nechytat',
                'color' => '#FBBF24', // yellow-500
                'icon' => 'design-hand-block',
                'sort_order' => 5,
            ],
            [
                'name' => 'Netrap sa',
                'color' => '#6B7280', // gray-500
                'icon' => 'design-emotion-sad',
                'sort_order' => 6,
            ],
            [
                'name' => 'Urgent',
                'color' => '#EF4444', // red-500
                'icon' => 'wms-alert',
                'sort_order' => 7,
            ],
        ];

        foreach ($statuses as $status) {
            ProjectStatus::updateOrCreate(
                ['name' => $status['name']],
                $status
            );
        }
    }
}
