<?php

namespace Database\Seeders;

use App\Models\Font;
use Illuminate\Database\Seeder;

class FontSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    protected $fonts = [
        [
            'hash' => 'wms',
            'name' => 'WMS',
            'path' => 'wms.scss',
            'css_class' => 'wms',
            'route_name' => 'wms_icons',
            'icon' => 'design design-font-wms  dark:design-font-wms-invert',
            'webfont' => 'https://webfontapp.com/s/463a6073-d28a-41e4-8032-a3a3f55cb9b2',
            'order_nr' => 1,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'tos',
            'name' => 'TOS',
            'path' => 'tos.scss',
            'css_class' => 'tos',
            'route_name' => 'tos_icons',
            'webfont' => 'https://webfontapp.com/s/94121992-4040-483a-a2c2-7103fd71cc31',
            'order_nr' => 8,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'claims',
            'name' => 'Claims',
            'path' => 'claims.scss',
            'css_class' => 'claims',
            'route_name' => 'claims_icons',
            'webfont' => 'https://webfontapp.com/s/664b83c3-d9de-44c7-9ea7-3d201254e265',
            'order_nr' => 2,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'retail',
            'name' => 'Retail',
            'path' => 'retail.scss',
            'css_class' => 'retail',
            'route_name' => 'retail_icons',
            'webfont' => 'https://webfontapp.com/s/a5f31d7c-e581-4562-9ad2-af26f7129469',
            'order_nr' => 7,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'matrix',
            'name' => 'Matrix',
            'path' => 'matrix.scss',
            'css_class' => 'matrix',
            'route_name' => 'matrix_icons',
            'webfont' => 'https://webfontapp.com/s/48d36853-0e12-469b-9b2d-bcfd6e335557',
            'order_nr' => 3,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'og',
            'name' => 'Order Group',
            'path' => 'og.scss',
            'css_class' => 'ordergroup',
            'route_name' => 'og_icons',
            'icon' => 'design design-font-ordergorup  dark:design-font-ordergorup-invert',
            'webfont' => 'https://webfontapp.com/s/b720f326-945b-4fc8-901e-e0206d45f353',
            'order_nr' => 5,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'eshop',
            'name' => 'Eshop',
            'path' => 'eshop.scss',
            'css_class' => 'vermont-icon',
            'route_name' => 'eshop_icons',
            'webfont' => 'https://webfontapp.com/s/c8fac8aa-e2d9-4004-a9a9-bf59de798e53',
            'order_nr' => 9,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'cdb',
            'name' => 'Central Database',
            'path' => 'cdb.scss',
            'css_class' => 'cdb',
            'route_name' => 'cdb_icons',
            'webfont' => 'https://webfontapp.com/s/ebe69728-84db-4cc6-a1e1-a68f904bac44',
            'order_nr' => 6,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'design',
            'name' => 'Design',
            'path' => 'design.scss',
            'css_class' => 'design',
            'route_name' => 'design_icons',
            'webfont' => 'https://webfontapp.com/s/f226e0ff-b3cf-4986-8372-0b5a177db3bc',
            'order_nr' => 4,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'category',
            'name' => 'Category',
            'path' => 'category.scss',
            'css_class' => 'vermont-category',
            'route_name' => 'category_icons',
            'webfont' => 'https://webfontapp.com/s/1owdvc',
            'order_nr' => 10,
            'active' => true,
            'icons_data' => [],
        ],
        [
            'hash' => 'hrms',
            'name' => 'hrms',
            'path' => 'hrms.scss',
            'css_class' => 'hrms',
            'route_name' => 'hrms_icons',
            'webfont' => 'https://webfontapp.com/s/67f3108c-aadf-4053-bde5-eb948b1d265a',
            'order_nr' => 11,
            'active' => true,
            'icons_data' => [],
        ],
    ];

    public function getFonts()
    {
        return $this->fonts;
    }

    public function run(): void
    {
        /***************************************************/
        /** HASH NIKDY NEMENIT, PODLA NEHO SA AKTUALIZUJE **/
        /***************************************************/

        // Keep track of font hashes for cleanup
        $fontHashes = collect($this->fonts)->pluck('hash')->toArray();

        foreach ($this->fonts as $fontData) {
            // Save existing icons_data if font exists
            $existingFont = Font::where('hash', $fontData['hash'])->first();
            
            if ($existingFont && !empty($existingFont->icons_data)) {
                // If the font data has icons_data, we need to merge them properly
                if (!empty($fontData['icons_data'])) {
                    // Create a map of existing icons by css_class for faster lookup
                    $existingIcons = collect($existingFont->icons_data)->keyBy('css_class');
                    
                    // Process each new icon, preserving tags from existing icons
                    $mergedIcons = collect($fontData['icons_data'])->map(function($newIcon) use ($existingIcons) {
                        // If this icon already exists
                        if (isset($newIcon['css_class']) && $existingIcons->has($newIcon['css_class'])) {
                            $existingIcon = $existingIcons[$newIcon['css_class']];
                            
                            // Preserve tags from the existing icon
                            if (!isset($newIcon['tags']) && isset($existingIcon['tags'])) {
                                $newIcon['tags'] = $existingIcon['tags'];
                            }
                            
                            // Preserve created_at if content hasn't changed
                            if (isset($existingIcon['created_at']) && 
                                isset($newIcon['css_content']) && 
                                isset($existingIcon['css_content']) && 
                                $newIcon['css_content'] === $existingIcon['css_content']) {
                                $newIcon['created_at'] = $existingIcon['created_at'];
                            }
                        }
                        
                        // Ensure every icon has a tags array
                        if (!isset($newIcon['tags'])) {
                            $newIcon['tags'] = [];
                        }
                        
                        return $newIcon;
                    })->toArray();
                    
                    // Add any existing icons that aren't in the new data to preserve them
                    // This ensures we don't lose icons that might have been customized
                    $existingIcons->each(function($existingIcon, $cssClass) use (&$mergedIcons, $fontData) {
                        // Check if this icon exists in the merged collection already
                        $exists = false;
                        foreach ($mergedIcons as $icon) {
                            if (isset($icon['css_class']) && $icon['css_class'] === $cssClass) {
                                $exists = true;
                                break;
                            }
                        }
                        
                        // If not found in the new data, add it to preserve it
                        if (!$exists) {
                            $mergedIcons[] = $existingIcon;
                        }
                    });
                    
                    // Update the font data with the merged icons
                    $fontData['icons_data'] = $mergedIcons;
                } else {
                    // No new icons, just preserve existing ones
                    $fontData['icons_data'] = $existingFont->icons_data;
                }
            } else if (empty($fontData['icons_data']) && $existingFont && !empty($existingFont->icons_data)) {
                // Backward compatibility with the old code
                $fontData['icons_data'] = $existingFont->icons_data;
            }
            
            // Make sure all icons have a tags array for consistency
            if (!empty($fontData['icons_data'])) {
                foreach ($fontData['icons_data'] as $key => $icon) {
                    if (!isset($fontData['icons_data'][$key]['tags'])) {
                        $fontData['icons_data'][$key]['tags'] = [];
                    }
                }
            }
            
            Font::updateOrCreate(
                ['hash' => $fontData['hash']],
                $fontData
            );
        }
        
        // Optional: Remove fonts that are no longer in the seeder
        // Uncomment if you want to automatically remove fonts that aren't in the seeder anymore
        // Font::whereNotIn('hash', $fontHashes)->delete();
    }
}
