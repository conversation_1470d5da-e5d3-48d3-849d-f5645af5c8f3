<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('bookmark_links')) {
            Schema::create('bookmark_links', function (Blueprint $table) {
                $table->id();
                $table->foreignId('bookmark_id')->constrained()->onDelete('cascade');
                $table->string('label');
                $table->string('url');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookmark_links');
    }
};
