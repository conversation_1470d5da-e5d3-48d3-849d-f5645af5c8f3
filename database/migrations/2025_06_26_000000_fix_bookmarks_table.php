<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookmarks', function (Blueprint $table) {
            if (!Schema::hasColumn('bookmarks', 'title')) {
                $table->string('title')->after('user_id');
            }
            if (!Schema::hasColumn('bookmarks', 'icon_class')) {
                $table->string('icon_class')->nullable()->after('title');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookmarks', function (Blueprint $table) {
            if (Schema::hasColumn('bookmarks', 'icon_class')) {
                $table->dropColumn('icon_class');
            }
            if (Schema::hasColumn('bookmarks', 'title')) {
                $table->dropColumn('title');
            }
        });
    }
}; 