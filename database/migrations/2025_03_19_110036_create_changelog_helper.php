<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create app/Helpers directory if it doesn't exist
        if (!File::exists(app_path('Helpers'))) {
            File::makeDirectory(app_path('Helpers'));
        }

        // Create ChangelogHelper.php
        $helperContent = <<<'EOD'
<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class ChangelogHelper
{
    /**
     * Get all changelog entries.
     *
     * @param  bool  $sorted  Sort by release date and order (default: true)
     * @param  int  $cacheTime  Cache time in seconds (default: 1 hour)
     * @return \Illuminate\Support\Collection
     */
    public static function getAllEntries(bool $sorted = true, int $cacheTime = 3600)
    {
        return Cache::remember('changelog.all', $cacheTime, function () use ($sorted) {
            $jsonPath = config('changelog.json_path', storage_path('app/changelog.json'));
            
            if (!file_exists($jsonPath)) {
                return collect([]);
            }
            
            $entries = collect(json_decode(file_get_contents($jsonPath), true));
            
            if ($sorted) {
                $entries = $entries->sortByDesc('release_date')
                                  ->sortByDesc('order');
            }
            
            return $entries;
        });
    }
    
    /**
     * Get a specific changelog entry by version.
     *
     * @param  string  $version  The version to find
     * @return array|null
     */
    public static function getEntryByVersion(string $version)
    {
        return static::getAllEntries()->firstWhere('version', $version);
    }
    
    /**
     * Get major changelog entries.
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getMajorEntries()
    {
        return static::getAllEntries()->filter(function ($entry) {
            return $entry['is_major'] ?? false;
        });
    }
    
    /**
     * Get entries from a specific year.
     *
     * @param  int  $year  The year to filter by
     * @return \Illuminate\Support\Collection
     */
    public static function getEntriesByYear(int $year)
    {
        return static::getAllEntries()->filter(function ($entry) use ($year) {
            $releaseDate = Carbon::parse($entry['release_date']);
            return $releaseDate->year === $year;
        });
    }
    
    /**
     * Clear changelog cache.
     *
     * @return void
     */
    public static function clearCache()
    {
        Cache::forget('changelog.all');
    }
}
EOD;

        File::put(app_path('Helpers/ChangelogHelper.php'), $helperContent);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the created file
        File::delete(app_path('Helpers/ChangelogHelper.php'));
    }
};
