<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Export all changelog entries to a JSON file
        $changelogEntries = DB::table('changelog_entries')
            ->orderBy('release_date', 'desc')
            ->orderBy('order', 'desc')
            ->get();
        
        if ($changelogEntries->count() > 0) {
            // Convert the collection to an array
            $entriesArray = json_decode(json_encode($changelogEntries), true);
            
            // Save to a JSON file
            Storage::disk('local')->put('changelog.json', json_encode($entriesArray, JSON_PRETTY_PRINT));
            
            // Create a config file with instructions for accessing the changelog data
            $configContent = <<<'EOD'
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Changelog Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for the changelog system.
    | The changelog data is stored in storage/app/changelog.json
    |
    */
    
    // Path to the changelog JSON file
    'json_path' => storage_path('app/changelog.json'),
    
    // Cache duration for changelog data (in seconds)
    'cache_duration' => 3600, // 1 hour
];
EOD;
            
            // Save the config file
            if (!file_exists(config_path('changelog.php'))) {
                file_put_contents(config_path('changelog.php'), $configContent);
            }
        }

        // Drop the changelog_entries table
        Schema::dropIfExists('changelog_entries');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the changelog_entries table
        Schema::create('changelog_entries', function (Blueprint $table) {
            $table->id();
            $table->string('version');
            $table->string('title');
            $table->text('description')->nullable();
            $table->date('release_date');
            $table->json('changes')->nullable(); // Array of changes
            $table->string('image_path')->nullable();
            $table->boolean('is_major')->default(false);
            $table->integer('order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        // Attempt to restore data from the JSON file
        if (Storage::disk('local')->exists('changelog.json')) {
            $jsonData = Storage::disk('local')->get('changelog.json');
            $entriesArray = json_decode($jsonData, true);
            
            foreach ($entriesArray as $entry) {
                // Remove any fields that don't exist in the table schema
                unset($entry['id']); // Let the database assign new IDs
                
                // Insert into the database
                DB::table('changelog_entries')->insert([
                    'version' => $entry['version'],
                    'title' => $entry['title'],
                    'description' => $entry['description'] ?? null,
                    'release_date' => $entry['release_date'],
                    'changes' => $entry['changes'] ?? null,
                    'image_path' => $entry['image_path'] ?? null,
                    'is_major' => $entry['is_major'] ?? false,
                    'order' => $entry['order'] ?? 0,
                    'created_at' => $entry['created_at'] ?? now(),
                    'updated_at' => $entry['updated_at'] ?? now(),
                    'deleted_at' => $entry['deleted_at'] ?? null,
                ]);
            }
        }
    }
};
