<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    // Track whether we created the table or just modified it
    protected $createdTable = false;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('fonts')) {
            Schema::create('fonts', function (Blueprint $table) {
                $table->id();
                $table->string('hash', 20)->nullable();
                $table->string('name');
                $table->string('path')->nullable();
                $table->string('css_class');
                $table->string('route_name', 255)->nullable();
                $table->string('icon', 255)->nullable();
                $table->string('webfont', 999)->nullable();
                $table->unsignedInteger('order_nr')->default(1);
                $table->boolean('active')->default(true);
                $table->timestamps();
            });
            
            $this->createdTable = true;
        } else {
            // If the table exists, add any missing columns
            Schema::table('fonts', function (Blueprint $table) {
                if (!Schema::hasColumn('fonts', 'hash')) {
                    $table->string('hash', 20)->nullable()->after('id');
                }
                if (!Schema::hasColumn('fonts', 'name')) {
                    $table->string('name')->after('hash');
                }
                if (!Schema::hasColumn('fonts', 'path')) {
                    $table->string('path')->nullable()->after('name');
                }
                if (!Schema::hasColumn('fonts', 'css_class')) {
                    $table->string('css_class')->after('path');
                }
                if (!Schema::hasColumn('fonts', 'route_name')) {
                    $table->string('route_name', 255)->nullable()->after('css_class');
                }
                if (!Schema::hasColumn('fonts', 'icon')) {
                    $table->string('icon', 255)->nullable()->after('route_name');
                }
                if (!Schema::hasColumn('fonts', 'webfont')) {
                    $table->string('webfont', 999)->nullable()->after('icon');
                }
                if (!Schema::hasColumn('fonts', 'order_nr')) {
                    $table->unsignedInteger('order_nr')->default(1)->after('webfont');
                }
                if (!Schema::hasColumn('fonts', 'active')) {
                    $table->boolean('active')->default(true)->after('order_nr');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // In a production environment, it's safer not to drop tables
        // If you need to rollback, you should manually handle this based on your specific needs
    }
};
