<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add a JSON column to fonts table to store icons data
        Schema::table('fonts', function (Blueprint $table) {
            $table->json('icons_data')->nullable()->after('webfont');
        });

        // Migrate data from font_icons to fonts
        $fonts = DB::table('fonts')->get();
        foreach ($fonts as $font) {
            $icons = DB::table('font_icons')
                ->where('font_id', $font->id)
                ->get(['css_class', 'css_content']);
            
            if ($icons->count() > 0) {
                DB::table('fonts')
                    ->where('id', $font->id)
                    ->update(['icons_data' => json_encode($icons)]);
            }
        }

        // Drop the font_icons table
        Schema::dropIfExists('font_icons');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the font_icons table
        Schema::create('font_icons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('font_id')->nullable()->constrained('fonts');
            $table->string('css_class')->nullable();
            $table->string('css_content')->nullable();
            $table->timestamps();
        });

        // Migrate data from fonts back to font_icons
        $fonts = DB::table('fonts')->whereNotNull('icons_data')->get();
        foreach ($fonts as $font) {
            $iconsData = json_decode($font->icons_data);
            
            if (is_array($iconsData)) {
                foreach ($iconsData as $icon) {
                    DB::table('font_icons')->insert([
                        'font_id' => $font->id,
                        'css_class' => $icon->css_class,
                        'css_content' => $icon->css_content,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Remove the icons_data column
        Schema::table('fonts', function (Blueprint $table) {
            $table->dropColumn('icons_data');
        });
    }
};
