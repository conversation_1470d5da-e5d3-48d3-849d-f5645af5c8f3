<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whiteboard_card_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('color')->default('#6B7280'); // Default gray color
            $table->integer('order')->default(0);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_system')->default(false); // Cannot be deleted if true
            $table->timestamps();
        });

        // Insert default statuses
        DB::table('whiteboard_card_statuses')->insert([
            [
                'name' => 'Active',
                'slug' => 'active',
                'color' => '#10B981', // Green
                'order' => 1,
                'is_default' => true,
                'is_system' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Completed',
                'slug' => 'completed',
                'color' => '#F59E0B', // Yellow
                'order' => 2,
                'is_default' => false,
                'is_system' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Archived',
                'slug' => 'archived',
                'color' => '#6B7280', // Gray
                'order' => 3,
                'is_default' => false,
                'is_system' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whiteboard_card_statuses');
    }
};
