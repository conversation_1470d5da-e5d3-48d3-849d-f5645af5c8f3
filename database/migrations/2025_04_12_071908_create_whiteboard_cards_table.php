<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whiteboard_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('headline');
            $table->text('description')->nullable();
            $table->string('link')->nullable();
            $table->date('card_date');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
        
        // Optional: Additional links table for multiple links per card
        Schema::create('whiteboard_card_links', function (Blueprint $table) {
            $table->id();
            $table->foreignId('whiteboard_card_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('url');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whiteboard_card_links');
        Schema::dropIfExists('whiteboard_cards');
    }
};
