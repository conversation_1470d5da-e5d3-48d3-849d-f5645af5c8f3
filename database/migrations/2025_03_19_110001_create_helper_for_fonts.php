<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create app/Helpers directory if it doesn't exist
        if (!File::exists(app_path('Helpers'))) {
            File::makeDirectory(app_path('Helpers'));
        }

        // Create FontHelper.php
        $helperContent = <<<'EOD'
<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use App\Models\Font;

class FontHelper
{
    /**
     * Get all fonts with their icons.
     *
     * @param  bool  $activeOnly  Only return active fonts
     * @param  int  $cacheTime  Cache time in seconds (default: 1 hour)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAllFonts(bool $activeOnly = true, int $cacheTime = 3600)
    {
        $cacheKey = 'fonts.' . ($activeOnly ? 'active' : 'all');
        
        return Cache::remember($cacheKey, $cacheTime, function () use ($activeOnly) {
            $query = Font::query();
            
            if ($activeOnly) {
                $query->where('active', true);
            }
            
            return $query->orderBy('order_nr')->get();
        });
    }

    /**
     * Get font icons from the JSON data.
     *
     * @param  \App\Models\Font  $font
     * @return array
     */
    public static function getFontIcons(Font $font)
    {
        if (!$font->icons_data) {
            return [];
        }

        return json_decode($font->icons_data, true) ?? [];
    }

    /**
     * Clear font cache.
     *
     * @return void
     */
    public static function clearCache()
    {
        Cache::forget('fonts.active');
        Cache::forget('fonts.all');
    }
}
EOD;

        File::put(app_path('Helpers/FontHelper.php'), $helperContent);

        // Create FontServiceProvider.php
        $providerContent = <<<'EOD'
<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class FontServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Register the FontHelper in the container
        $this->app->singleton('font', function ($app) {
            return new \App\Helpers\FontHelper();
        });
    }
}
EOD;

        File::put(app_path('Providers/FontServiceProvider.php'), $providerContent);

        // Add the service provider to the config/app.php providers array
        $appConfig = File::get(config_path('app.php'));
        if (!Str::contains($appConfig, 'App\Providers\FontServiceProvider::class')) {
            $appConfig = preg_replace(
                '/\'providers\' => \[\s+?(.+?)\s+?\],/s',
                '\'providers\' => [$1' . PHP_EOL . '        App\Providers\FontServiceProvider::class,' . PHP_EOL . '    ],',
                $appConfig
            );
            File::put(config_path('app.php'), $appConfig);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the created files
        File::delete(app_path('Helpers/FontHelper.php'));
        File::delete(app_path('Providers/FontServiceProvider.php'));

        // Remove the service provider from config/app.php
        $appConfig = File::get(config_path('app.php'));
        $appConfig = str_replace('        App\Providers\FontServiceProvider::class,' . PHP_EOL, '', $appConfig);
        File::put(config_path('app.php'), $appConfig);
    }
};
