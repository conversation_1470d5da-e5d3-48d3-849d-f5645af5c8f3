<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('user_font_views');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('user_font_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('font_id')->constrained()->onDelete('cascade');
            $table->integer('last_icons_count')->default(0);
            $table->timestamp('last_viewed_at');
            $table->timestamps();

            $table->unique(['user_id', 'font_id']);
        });
    }
};
