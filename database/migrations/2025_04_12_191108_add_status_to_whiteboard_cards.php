<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\WhiteboardCard;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('whiteboard_cards', function (Blueprint $table) {
            $table->string('status')->default(WhiteboardCard::DEFAULT_STATUS)->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('whiteboard_cards', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
