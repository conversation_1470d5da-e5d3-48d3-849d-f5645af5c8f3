<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('whiteboard_cards', function (Blueprint $table) {
            $table->string('image')->default('link.svg')->after('project_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('whiteboard_cards', function (Blueprint $table) {
            $table->dropColumn('image');
        });
    }
};
