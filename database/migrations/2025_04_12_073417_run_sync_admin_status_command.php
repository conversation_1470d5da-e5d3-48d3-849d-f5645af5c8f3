<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // This migration assumes that the is_admin column has already been added to the users table
        // Check if the column exists before proceeding
        if (Schema::hasColumn('users', 'is_admin')) {
            // Option 1: Run the admin:sync artisan command
            Log::info('Running admin:sync command to synchronize admin status...');
            Artisan::call('admin:sync');
            
            // Log the output from the admin:sync command
            $output = Artisan::output();
            Log::info('Admin sync command output: ' . $output);
            
            // Option 2: Direct implementation (as fallback)
            Log::info('Directly synchronizing admin status...');
            $adminLogins = Config::get('vermont.admin_user_logins', []);
            $updatedCount = 0;
            
            User::chunk(100, function ($users) use (&$updatedCount, $adminLogins) {
                foreach ($users as $user) {
                    $isAdminInConfig = in_array($user->login, $adminLogins);
                    
                    if ($user->is_admin !== $isAdminInConfig) {
                        $user->is_admin = $isAdminInConfig;
                        $user->save();
                        $updatedCount++;
                        
                        Log::info("Admin status synchronized for user {$user->login}", [
                            'user_id' => $user->id,
                            'login' => $user->login,
                            'is_admin' => $isAdminInConfig
                        ]);
                    }
                }
            });
            
            Log::info("Updated {$updatedCount} users with correct admin status.");
        } else {
            Log::error('The is_admin column does not exist in the users table. Please run the migration that adds this column first.');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse anything, as we're just running a command
    }
};
