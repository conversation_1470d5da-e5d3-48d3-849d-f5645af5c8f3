<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('font_icons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('font_id')->nullable()->constrained('fonts');
            $table->string('css_class')->nullable();
            $table->string('css_content')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('font_icons');
    }
};
