import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './resources/**/*.vue',
        './resources/views/**/*.blade.php',
    ],
    darkMode: 'class',
    theme: {
        extend: {
            fontFamily: {
                sans: ['Gant-Modern-regular', ...defaultTheme.fontFamily.sans],
                'gant-regular': ['Gant-Modern-regular', ...defaultTheme.fontFamily.sans],
                'gant-medium': ['Gant-Modern-medium', ...defaultTheme.fontFamily.sans],
                'gant-bold': ['Gant-Modern-bold', ...defaultTheme.fontFamily.sans],
                'gant-light': ['Gant-Modern-light', ...defaultTheme.fontFamily.sans],
                'gant-serif': ['Gant-Serif', ...defaultTheme.fontFamily.serif],
            },
            fontSize: {
                'xs': '.75rem',
                'sm': '.875rem',
                'tiny': '.875rem',
                'base': '1rem',
                'lg': '1.125rem',
                'xl': '1.25rem',
                '2xl': '1.5rem',
                '3xl': '1.875rem',
                '4xl': '2.25rem',
                '5xl': '3rem',
                '6xl': '4rem',
                '7xl': '5rem',
                '8xl': '8rem',
                '9xl': '12rem',
                '10xl': '18rem',
                '11xl': '24rem',
                '12xl': '36rem',
                '13xl': '48rem',
            },
            fontWeight: {
                thin: '100',
                hairline: '100',
                extralight: '200',
                light: '300',
                normal: '400',
                medium: '500',
                semibold: '600',
                bold: '700',
                extrabold: '800',
                'extra-bold': '800',
                black: '900',
            },
            colors: {
                gray: {
                    ...defaultTheme.colors.gray,
                    900: '#1a202c',
                }
            },
            borderColor: {
                'input-light': '#e5e7eb',
                'input-dark': '#374151',
            }
        },
    },

    plugins: [forms],
};
