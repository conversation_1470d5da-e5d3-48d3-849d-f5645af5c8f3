<?php

return [
    'ldap_auth' => [
        'master_server' => [
            'url' => env('LDAP_MASTER_URL', 'ldaps://mail.vermontservices.eu'),
            'port' => env('LDAP_MASTER_PORT', 636),
            'admin_name' => env('LDAP_MASTER_ADMIN_NAME', 'cn=rouser,dc=intranet,dc=vermont,dc=eu'),
            'admin_pass' => env('LDAP_MASTER_ADMIN_PASS', ''),
            'user_base' => env('LDAP_MASTER_USER_BASE', 'ou=people,dc=intranet,dc=vermont,dc=eu'),
        ],
        'slave_server' => [
            'url' => env('LDAP_SLAVE_URL', 'ldap://ldap.vermont.eu'),
            'port' => env('LDAP_SLAVE_PORT', 389),
            'admin_name' => env('LDAP_SLAVE_ADMIN_NAME', 'cn=ro,dc=intranet,dc=vermont,dc=eu'),
            'admin_pass' => env('LDAP_SLAVE_ADMIN_PASS', ''),
            'user_base' => env('LDAP_SLAVE_USER_BASE', 'ou=people,dc=intranet,dc=vermont,dc=eu'),
        ],
    ],

    'admin_user_logins' => [
        'u4431', // Marek Nový
        'u1182', // Matuš Markúsek
        'u4154', //  Tibor Csicsay 
        'u3660', // Tomáš Mikuláš
        'u4345', // Petra Horňáková 
    ],
    'developer_user_logins' => [

    ],
    'dev_user_logins' => [
        'u4346', //  Dávid Soóky
        'u3603', // Miroslav Bačkovský
        'u3660', // Tomáš Mikuláš
        'u4345', // Petra Horňáková 
        'u3985', //  Marek Koršepa
        'u1182', // Matuš Markúsek
    ],
    'tibi_user_logins' => [
        'u4154', //  Tibor Csicsay 
    ],
    'pm_user_logins' => [
        'u3658', // Patrik Polcer
        'u1007', // Peto Krajca
        'u4544', //  Martin machcac
    ],   
];
